## **Equipment Management UI Design**

### **1. Company-Level Equipment Management**

#### **Main Equipment Dashboard**
```
┌─────────────────────────────────────────────────────────────┐
│ Equipment Management Dashboard                              │
├─────────────────────────────────────────────────────────────┤
│ 📊 Summary Cards                                           │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐           │
│ │Total    │ │On-Site  │ │In       │ │Overdue  │           │
│ │Equipment│ │Equipment│ │Maintenance│ │Inspections│         │
│ │ 1,247   │ │  892    │ │  156    │ │   23    │           │
│ └─────────┘ └─────────┘ └─────────┘ └─────────┘           │
├─────────────────────────────────────────────────────────────┤
│ 🔍 Search & Filters                                        │
│ [Search equipment...] [Category ▼] [Status ▼] [Site ▼]     │
├─────────────────────────────────────────────────────────────┤
│ 📋 Equipment Table                                         │
│ ┌─────┬─────────────┬──────────┬─────────┬─────────┬──────┐ │
│ │ID   │Name         │Category  │Status   │Location │Actions│ │
│ ├─────┼─────────────┼──────────┼─────────┼─────────┼──────┤ │
│ │E001 │Excavator-01 │Heavy     │Active   │Site A   │[View]│ │
│ │E002 │Crane-05     │Lifting   │Maintenance│Site B │[View]│ │
│ │E003 │Generator-12 │Power     │Available│Company  │[View]│ │
│ └─────┴─────────────┴──────────┴─────────┴─────────┴──────┘ │
└─────────────────────────────────────────────────────────────┘
```

#### **Equipment Detail View (VSCode-style)**
```
┌─────────────────────────────────────────────────────────────┐
│ Equipment Explorer                    │ Equipment Details    │
├─────────────────────────────────────┼───────────────────────┤
│ �� Active Equipment                  │ �� Equipment Info     │
│ ├── �� Heavy Machinery              │ • Name: Excavator-01  │
│ │   ├── �� Excavator-01             │ • Category: Heavy     │
│ │   ├── �� Crane-05                 │ • Model: CAT 320D     │
│ │   └── 🔧 Bulldozer-03             │ • Serial: CAT123456   │
│ ├── �� Lifting Equipment            │ • Status: Active      │
│ │   ├── �� Forklift-12              │ • Current Site: A     │
│ │   └── 🔧 Hoist-08                 │ • Last Inspection:    │
│ ├── 📁 Power Equipment              │   2024-01-15         │
│ │   ├── �� Generator-12             │ • Next Due: 2024-02-15│
│ │   └── 🔧 Welder-25                │                       │
│ └── �� Hand Tools                   │ �� Usage Stats        │
│     ├── 🔧 Drill-45                 │ • Hours This Month: 120│
│     └── 🔧 Saw-67                   │ • Utilization: 85%    │
│                                     │ • Maintenance Cost:   │
│ �� Maintenance Required              │   $2,450             │
│ ├── �� Excavator-01 (Due: 2 days)   │                       │
│ └── 🔧 Generator-12 (Overdue)       │ �� Documents          │
│                                     │ • Manual.pdf          │
│ �� Inspections Due                   │ • Warranty.pdf       │
│ ├── 🔧 Crane-05 (Due: 1 day)        │ • Inspection-2024-01 │
│ └── 🔧 Forklift-12 (Due: 3 days)    │                       │
└─────────────────────────────────────┴───────────────────────┘
```

### **2. Site-Level Equipment Management**

#### **Site Equipment Dashboard**
```
┌─────────────────────────────────────────────────────────────┐
│ Site A - Equipment Management                              │
├─────────────────────────────────────────────────────────────┤
│ 📊 Site Equipment Summary                                  │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐           │
│ │Assigned │ │In Use   │ │Available│ │Maintenance│          │
│ │   45    │ │   32    │ │   13    │ │    8     │           │
│ └─────────┘ └─────────┘ └─────────┘ └─────────┘           │
├─────────────────────────────────────────────────────────────┤
│ 🔄 Quick Actions                                           │
│ [Import from Company] [Assign Equipment] [Schedule Inspection]│
├─────────────────────────────────────────────────────────────┤
│ 📋 Equipment by Area                                       │
│ ┌─────────────┬─────────────┬─────────────┬─────────────┐   │
│ │Construction │ Storage     │ Office      │ Utility     │   │
│ │    12       │     8       │     3       │     5       │   │
│ └─────────────┴─────────────┴─────────────┴─────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### **3. Equipment Detail Modal/Page**

#### **Equipment Profile with Tabs**
```
┌─────────────────────────────────────────────────────────────┐
│ Excavator-01 (CAT 320D)                    [Edit] [Close]   │
├─────────────────────────────────────────────────────────────┤
│ [Overview] [Inspections] [Maintenance] [Documents] [History]│
├─────────────────────────────────────────────────────────────┤
│ 📋 Basic Information                                       │
│ • Equipment ID: E001                                       │
│ • Category: Heavy Machinery                                │
│ • Model: CAT 320D                                          │
│ • Serial Number: CAT123456                                 │
│ • Purchase Date: 2022-03-15                                │
│ • Warranty Expiry: 2025-03-15                              │
│ • Current Status: Active                                   │
│ • Current Location: Site A - Construction Area             │
│ • Assigned To: John Smith (Site Engineer)                  │
│                                                             │
│ 📊 Usage Statistics                                        │
│ • Total Hours: 2,450                                       │
│ • Hours This Month: 120                                    │
│ • Utilization Rate: 85%                                    │
│ • Last Inspection: 2024-01-15                              │
│ • Next Inspection Due: 2024-02-15                          │
│                                                             │
│ ⚠️ Alerts & Notifications                                  │
│ • Maintenance due in 2 days                                │
│ • Inspection overdue by 1 day                              │
└─────────────────────────────────────────────────────────────┘
```

## **Database Structure**

### **Core Equipment Tables**

#### **1. Equipment Master Table**
```sql
CREATE TABLE equipment (
    id VARCHAR(50) PRIMARY KEY,
    company_id VARCHAR(50) NOT NULL,
    equipment_number VARCHAR(50) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    category_id VARCHAR(50) NOT NULL,
    subcategory_id VARCHAR(50),
    manufacturer VARCHAR(100),
    model VARCHAR(100),
    serial_number VARCHAR(100),
    year_manufactured INT,
    purchase_date DATE,
    purchase_price DECIMAL(12,2),
    current_value DECIMAL(12,2),
    warranty_expiry DATE,
    status ENUM('active', 'inactive', 'retired', 'disposed') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_company_id (company_id),
    INDEX idx_category_id (category_id),
    INDEX idx_status (status),
    INDEX idx_equipment_number (equipment_number)
);
```

#### **2. Equipment Categories**
```sql
CREATE TABLE equipment_categories (
    id VARCHAR(50) PRIMARY KEY,
    company_id VARCHAR(50) NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_id VARCHAR(50) NULL,
    inspection_interval_days INT DEFAULT 30,
    maintenance_interval_hours INT DEFAULT 500,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (parent_id) REFERENCES equipment_categories(id),
    INDEX idx_company_id (company_id),
    INDEX idx_parent_id (parent_id)
);
```

#### **3. Equipment Site Assignments**
```sql
CREATE TABLE equipment_site_assignments (
    id VARCHAR(50) PRIMARY KEY,
    equipment_id VARCHAR(50) NOT NULL,
    site_id VARCHAR(50) NOT NULL,
    assigned_by VARCHAR(50) NOT NULL,
    assigned_to VARCHAR(50) NULL, -- Worker assigned to equipment
    assigned_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    unassigned_date TIMESTAMP NULL,
    status ENUM('active', 'inactive', 'transferred') DEFAULT 'active',
    notes TEXT,
    
    FOREIGN KEY (equipment_id) REFERENCES equipment(id),
    INDEX idx_equipment_id (equipment_id),
    INDEX idx_site_id (site_id),
    INDEX idx_assigned_to (assigned_to),
    INDEX idx_status (status)
);
```

#### **4. Equipment Inspections**
```sql
CREATE TABLE equipment_inspections (
    id VARCHAR(50) PRIMARY KEY,
    equipment_id VARCHAR(50) NOT NULL,
    site_id VARCHAR(50) NOT NULL,
    inspection_type ENUM('routine', 'pre_use', 'post_incident', 'annual', 'compliance') NOT NULL,
    inspector_id VARCHAR(50) NOT NULL,
    inspection_date TIMESTAMP NOT NULL,
    next_inspection_due TIMESTAMP NOT NULL,
    status ENUM('passed', 'failed', 'conditional', 'pending') NOT NULL,
    overall_condition ENUM('excellent', 'good', 'fair', 'poor', 'critical') NOT NULL,
    total_score DECIMAL(5,2),
    max_score DECIMAL(5,2),
    notes TEXT,
    recommendations TEXT,
    corrective_actions_required BOOLEAN DEFAULT FALSE,
    follow_up_date DATE NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (equipment_id) REFERENCES equipment(id),
    INDEX idx_equipment_id (equipment_id),
    INDEX idx_site_id (site_id),
    INDEX idx_inspector_id (inspector_id),
    INDEX idx_inspection_date (inspection_date),
    INDEX idx_next_due (next_inspection_due)
);
```

#### **5. Equipment Inspection Items**
```sql
CREATE TABLE equipment_inspection_items (
    id VARCHAR(50) PRIMARY KEY,
    inspection_id VARCHAR(50) NOT NULL,
    item_name VARCHAR(255) NOT NULL,
    item_category VARCHAR(100),
    condition ENUM('excellent', 'good', 'fair', 'poor', 'critical') NOT NULL,
    score DECIMAL(3,1) NOT NULL,
    max_score DECIMAL(3,1) NOT NULL,
    notes TEXT,
    requires_attention BOOLEAN DEFAULT FALSE,
    corrective_action TEXT,
    
    FOREIGN KEY (inspection_id) REFERENCES equipment_inspections(id) ON DELETE CASCADE,
    INDEX idx_inspection_id (inspection_id),
    INDEX idx_requires_attention (requires_attention)
);
```

#### **6. Equipment Maintenance**
```sql
CREATE TABLE equipment_maintenance (
    id VARCHAR(50) PRIMARY KEY,
    equipment_id VARCHAR(50) NOT NULL,
    site_id VARCHAR(50) NOT NULL,
    maintenance_type ENUM('preventive', 'corrective', 'emergency', 'scheduled') NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    scheduled_date TIMESTAMP NOT NULL,
    completed_date TIMESTAMP NULL,
    performed_by VARCHAR(50) NULL,
    status ENUM('scheduled', 'in_progress', 'completed', 'cancelled') DEFAULT 'scheduled',
    priority ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    estimated_hours DECIMAL(5,2),
    actual_hours DECIMAL(5,2),
    cost DECIMAL(10,2),
    parts_used TEXT,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (equipment_id) REFERENCES equipment(id),
    INDEX idx_equipment_id (equipment_id),
    INDEX idx_site_id (site_id),
    INDEX idx_scheduled_date (scheduled_date),
    INDEX idx_status (status),
    INDEX idx_priority (priority)
);
```

#### **7. Equipment Documents**
```sql
CREATE TABLE equipment_documents (
    id VARCHAR(50) PRIMARY KEY,
    equipment_id VARCHAR(50) NOT NULL,
    document_type ENUM('manual', 'warranty', 'inspection', 'maintenance', 'certificate', 'other') NOT NULL,
    title VARCHAR(255) NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT,
    mime_type VARCHAR(100),
    uploaded_by VARCHAR(50) NOT NULL,
    uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expiry_date DATE NULL,
    is_active BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (equipment_id) REFERENCES equipment(id),
    INDEX idx_equipment_id (equipment_id),
    INDEX idx_document_type (document_type),
    INDEX idx_uploaded_at (uploaded_at)
);
```

#### **8. Equipment Usage Tracking**
```sql
CREATE TABLE equipment_usage (
    id VARCHAR(50) PRIMARY KEY,
    equipment_id VARCHAR(50) NOT NULL,
    site_id VARCHAR(50) NOT NULL,
    worker_id VARCHAR(50) NULL,
    usage_date DATE NOT NULL,
    start_time TIMESTAMP NOT NULL,
    end_time TIMESTAMP NULL,
    total_hours DECIMAL(5,2) NOT NULL,
    usage_type ENUM('operation', 'maintenance', 'testing', 'training') DEFAULT 'operation',
    project_code VARCHAR(50) NULL,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (equipment_id) REFERENCES equipment(id),
    INDEX idx_equipment_id (equipment_id),
    INDEX idx_site_id (site_id),
    INDEX idx_worker_id (worker_id),
    INDEX idx_usage_date (usage_date)
);
```

### **Supporting Tables**

#### **9. Equipment Transfer History**
```sql
CREATE TABLE equipment_transfers (
    id VARCHAR(50) PRIMARY KEY,
    equipment_id VARCHAR(50) NOT NULL,
    from_site_id VARCHAR(50) NULL,
    to_site_id VARCHAR(50) NOT NULL,
    transfer_date TIMESTAMP NOT NULL,
    transferred_by VARCHAR(50) NOT NULL,
    reason VARCHAR(255),
    notes TEXT,
    status ENUM('pending', 'in_transit', 'completed', 'cancelled') DEFAULT 'pending',
    
    FOREIGN KEY (equipment_id) REFERENCES equipment(id),
    INDEX idx_equipment_id (equipment_id),
    INDEX idx_transfer_date (transfer_date)
);
```

#### **10. Equipment Alerts/Notifications**
```sql
CREATE TABLE equipment_alerts (
    id VARCHAR(50) PRIMARY KEY,
    equipment_id VARCHAR(50) NOT NULL,
    alert_type ENUM('inspection_due', 'maintenance_due', 'warranty_expiry', 'overdue', 'critical') NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    priority ENUM('low', 'medium', 'high', 'critical') NOT NULL,
    due_date TIMESTAMP NULL,
    is_read BOOLEAN DEFAULT FALSE,
    is_resolved BOOLEAN DEFAULT FALSE,
    resolved_by VARCHAR(50) NULL,
    resolved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (equipment_id) REFERENCES equipment(id),
    INDEX idx_equipment_id (equipment_id),
    INDEX idx_alert_type (alert_type),
    INDEX idx_due_date (due_date),
    INDEX idx_is_read (is_read)
);
```

## **Key Features Implementation**

### **1. Equipment Import to Site**
```typescript
interface EquipmentImportRequest {
  equipmentIds: string[];
  siteId: string;
  assignedTo?: string;
  notes?: string;
}

// UI: Checkbox list of available equipment with search/filter
```

### **2. Equipment Summary Dashboard**
```typescript
interface EquipmentSummary {
  totalEquipment: number;
  onSiteEquipment: number;
  inMaintenance: number;
  overdueInspections: number;
  utilizationRate: number;
  maintenanceCost: number;
}
```

### **3. Inspection Workflow**
```typescript
interface InspectionWorkflow {
  scheduleInspection: (equipmentId: string, type: InspectionType) => void;
  conductInspection: (inspectionId: string, items: InspectionItem[]) => void;
  generateReport: (inspectionId: string) => InspectionReport;
  scheduleFollowUp: (inspectionId: string, followUpDate: Date) => void;
}
```

This structure gives you:
- **Company-level equipment registry** with site assignments
- **Comprehensive inspection tracking** with detailed item scoring
- **Maintenance scheduling and tracking** with cost management
- **Document management** for manuals, warranties, certificates
- **Usage tracking** for utilization analysis
- **Alert system** for proactive management
- **Transfer history** for equipment movement tracking

The UI follows the VSCode-inspired design you mentioned, making it familiar and efficient for technical users while remaining accessible to field workers.