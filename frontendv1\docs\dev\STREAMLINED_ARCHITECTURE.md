# Streamlined GraphQL-Ready Architecture

## Overview

The application has been streamlined to use a centralized mock data system that simulates a real GraphQL backend. All components now fetch data through GraphQL-compatible hooks that will seamlessly transition to a real GraphQL backend.

## Architecture Components

### 1. Mock GraphQL Client (`src/services/mockGraphQLClient.ts`)

**Purpose**: Simulates a real GraphQL backend with proper async operations and data management.

**Features**:
- Singleton pattern for consistent state management
- Simulated network delays for realistic UX
- In-memory data store that mimics backend state
- Full CRUD operations for all entities
- Proper error handling and response formatting

**Key Methods**:
- `getWorkers(siteId?)` - Fetch workers with optional site filtering
- `getWorkerById(id)` - Fetch individual worker details
- `createWorker(input)` - Create new worker
- `updateWorker(id, input)` - Update existing worker
- `uploadWorkerPhoto(workerId, photo)` - Handle photo uploads with Hikvision simulation
- `recordTrainingCompletion(input)` - Record training completions
- `createToolboxSession(input)` - Create toolbox talk sessions

### 2. Graph<PERSON> Hooks (`src/hooks/useGraphQL.ts`)

**Purpose**: Provide React hooks that mirror real GraphQL hooks but use mock data.

**Features**:
- Consistent API with Apollo Client hooks
- Loading states and error handling
- Automatic refetching and cache management
- Type-safe operations

**Key Hooks**:
- `useWorkers(siteId?)` - Query workers
- `useWorker(id)` - Query individual worker
- `useCreateWorker()` - Mutation for creating workers
- `useUploadWorkerPhoto()` - Mutation for photo uploads
- `useTrainings()` - Query all trainings
- `useToolboxSessions()` - Query toolbox sessions
- `useCreateToolboxSession()` - Mutation for creating sessions

### 3. Centralized Mock Data (`src/data/mockData.ts`)

**Purpose**: Single source of truth for all mock data.

**Entities**:
- Workers with complete backend-aligned structure
- Trades and Skills with audit trails
- Trainings with validity periods and status
- Training History with completion tracking
- Toolbox Sessions with attendance records

### 4. Enhanced Components

#### Worker Management
- **WorkerDirectory**: Uses `useWorkers()` hook with loading states and error handling
- **WorkerProfile**: Uses `useWorker()` hook with comprehensive data display
- **PhotoUpload**: Integrated with GraphQL mutations for photo management

#### Training Management
- **TrainingCompletion**: Records training completions with scores
- **TrainingExpiryTracker**: Monitors certification expiry dates
- **BulkTrainingAssignment**: Assigns trainings to multiple workers

#### Toolbox Talk Management
- **ToolboxSessionForm**: Creates new toolbox talk sessions
- **ToolboxSessionList**: Displays and manages existing sessions
- **ToolboxTalks**: Main component integrated with Safety page

#### Common Components
- **LoadingSpinner**: Reusable loading states
- **TableSkeleton**: Skeleton loading for tables
- **AuditTrail**: Displays audit information
- **GraphQLErrorHandler**: Comprehensive error handling

## Data Flow

### Query Flow
1. Component calls GraphQL hook (e.g., `useWorkers()`)
2. Hook calls MockGraphQLClient method
3. Client simulates network delay
4. Client returns data from in-memory store
5. Hook updates component state with loading/error/data

### Mutation Flow
1. Component calls mutation hook (e.g., `useCreateWorker()`)
2. Hook provides mutate function
3. Component calls mutate with variables
4. Client processes mutation and updates in-memory store
5. Related queries are automatically refetched

## Key Features

### 1. GraphQL Compatibility
- All hooks mirror Apollo Client API
- Easy transition to real GraphQL backend
- Proper loading states and error handling
- Optimistic updates and cache management

### 2. Realistic Data Simulation
- Network delays for authentic UX
- Proper error scenarios
- State persistence during session
- Audit trail generation

### 3. Type Safety
- Full TypeScript integration
- Proper interface definitions
- Type-safe hook parameters and returns

### 4. Enhanced UX
- Skeleton loading states
- Comprehensive error handling
- Loading indicators
- Optimistic updates

## Migration Path to Real GraphQL

### Step 1: Replace Mock Client
Replace `mockGraphQLClient` with real Apollo Client:

```typescript
// Before
import { mockGraphQLClient } from '../services/mockGraphQLClient';

// After
import { useQuery, useMutation } from '@apollo/client';
```

### Step 2: Update Hooks
Replace mock hooks with real Apollo hooks:

```typescript
// Before
export const useWorkers = (siteId?: string): MockQueryResult<{ workers: Worker[] }> => {
  // Mock implementation
};

// After
export const useWorkers = (siteId?: string) => {
  return useQuery(GET_WORKERS, {
    variables: { siteId },
    errorPolicy: 'all',
  });
};
```

### Step 3: Update Components
No changes needed in components - they already use the hook API correctly.

## File Structure

```
src/
├── services/
│   └── mockGraphQLClient.ts          # Mock GraphQL backend
├── hooks/
│   └── useGraphQL.ts                 # GraphQL-compatible hooks
├── data/
│   └── mockData.ts                   # Centralized mock data
├── components/
│   ├── common/
│   │   ├── LoadingSpinner.tsx        # Loading components
│   │   ├── AuditTrail.tsx           # Audit information
│   │   └── GraphQLErrorHandler.tsx   # Error handling
│   ├── workers/
│   │   └── PhotoUpload.tsx          # Photo management
│   ├── training/
│   │   ├── TrainingCompletion.tsx   # Training completion
│   │   ├── TrainingExpiryTracker.tsx # Expiry monitoring
│   │   └── BulkTrainingAssignment.tsx # Bulk operations
│   └── toolbox/
│       ├── ToolboxSessionForm.tsx   # Session creation
│       └── ToolboxSessionList.tsx   # Session management
├── pages/
│   ├── WorkerDirectory.tsx          # Worker listing
│   └── WorkerProfile.tsx            # Worker details
└── graphql/
    ├── queries.ts                   # GraphQL queries (ready for real backend)
    └── mutations.ts                 # GraphQL mutations (ready for real backend)
```

## Benefits

1. **Seamless Transition**: Easy migration to real GraphQL backend
2. **Realistic Development**: Proper async patterns and error handling
3. **Type Safety**: Full TypeScript integration throughout
4. **Better UX**: Loading states and error handling
5. **Maintainable**: Centralized data management
6. **Testable**: Isolated mock client for testing

## Next Steps

1. **Testing**: Add comprehensive tests for all hooks and components
2. **Real Backend**: Replace mock client with Apollo Client
3. **Optimization**: Add query optimization and caching strategies
4. **Monitoring**: Add error tracking and performance monitoring
