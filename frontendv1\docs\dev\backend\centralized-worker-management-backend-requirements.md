# Centralized Worker Management Backend Requirements

## Overview

This document outlines the backend changes required to support the redesigned centralized worker management system. The frontend has been updated to implement a company-level worker database with site assignment tracking, and the backend needs to be modified to support this new architecture.

## Database Schema Changes

### 1. Enhanced Company Workers Table

The existing workers table needs to be enhanced to support company-level management:

```sql
-- Enhanced company_workers table
ALTER TABLE workers ADD COLUMN IF NOT EXISTS employee_number VARCHAR(50) UNIQUE;
ALTER TABLE workers ADD COLUMN IF NOT EXISTS compliance_status VARCHAR(20) DEFAULT 'pending_training';
ALTER TABLE workers ADD COLUMN IF NOT EXISTS current_site_id VARCHAR(50);
ALTER TABLE workers ADD COLUMN IF NOT EXISTS total_site_assignments INTEGER DEFAULT 0;
ALTER TABLE workers ADD COLUMN IF NOT EXISTS last_assignment_date TIMESTAMP;
ALTER TABLE workers ADD COLUMN IF NOT EXISTS hire_date TIMESTAMP;
ALTER TABLE workers ADD COLUMN IF NOT EXISTS termination_date TIMESTAMP;
ALTER TABLE workers ADD COLUMN IF NOT EXISTS rehire_eligible BOOLEAN DEFAULT true;
ALTER TABLE workers ADD COLUMN IF NOT EXISTS performance_rating DECIMAL(3,2) DEFAULT 0.0;
ALTER TABLE workers ADD COLUMN IF NOT EXISTS notes TEXT;

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_workers_employee_number ON workers(employee_number);
CREATE INDEX IF NOT EXISTS idx_workers_compliance_status ON workers(compliance_status);
CREATE INDEX IF NOT EXISTS idx_workers_current_site_id ON workers(current_site_id);
```

### 2. Site Worker Assignments Table

Create a new table to track worker assignments to sites:

```sql
CREATE TABLE IF NOT EXISTS site_worker_assignments (
    id SERIAL PRIMARY KEY,
    worker_id INTEGER NOT NULL REFERENCES workers(id) ON DELETE CASCADE,
    site_id VARCHAR(50) NOT NULL,
    role VARCHAR(100) NOT NULL,
    start_date TIMESTAMP NOT NULL,
    end_date TIMESTAMP,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    assignment_type VARCHAR(20) NOT NULL DEFAULT 'initial',
    assignment_reason TEXT,
    assigned_by VARCHAR(100),
    hourly_rate DECIMAL(10,2),
    overtime_rate DECIMAL(10,2),
    total_hours_worked DECIMAL(10,2) DEFAULT 0.0,
    average_hours_per_day DECIMAL(5,2) DEFAULT 0.0,
    attendance_rate DECIMAL(5,2) DEFAULT 0.0,
    safety_incidents INTEGER DEFAULT 0,
    quality_rating DECIMAL(3,2) DEFAULT 0.0,
    performance_notes TEXT,
    transferred_from_site VARCHAR(50),
    transferred_to_site VARCHAR(50),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by VARCHAR(100),
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100)
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_site_assignments_worker_id ON site_worker_assignments(worker_id);
CREATE INDEX IF NOT EXISTS idx_site_assignments_site_id ON site_worker_assignments(site_id);
CREATE INDEX IF NOT EXISTS idx_site_assignments_status ON site_worker_assignments(status);
```

### 3. Worker Training Compliance Table

Create a table to track training compliance at the worker level:

```sql
CREATE TABLE IF NOT EXISTS worker_training_compliance (
    id SERIAL PRIMARY KEY,
    worker_id INTEGER NOT NULL REFERENCES workers(id) ON DELETE CASCADE,
    training_id INTEGER NOT NULL,
    training_name VARCHAR(200) NOT NULL,
    trade_id INTEGER,
    trade_name VARCHAR(100),
    compliance_status VARCHAR(20) NOT NULL DEFAULT 'pending_training',
    is_required BOOLEAN DEFAULT false,
    is_mandatory BOOLEAN DEFAULT false,
    completion_date TIMESTAMP,
    expiry_date TIMESTAMP,
    renewal_due_date TIMESTAMP,
    certificate_url VARCHAR(500),
    last_assessment_date TIMESTAMP,
    next_assessment_due TIMESTAMP,
    blocking_site_assignment BOOLEAN DEFAULT false,
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_training_compliance_worker_id ON worker_training_compliance(worker_id);
CREATE INDEX IF NOT EXISTS idx_training_compliance_status ON worker_training_compliance(compliance_status);
CREATE INDEX IF NOT EXISTS idx_training_compliance_blocking ON worker_training_compliance(blocking_site_assignment);
```

## GraphQL Schema Updates

### 1. Enhanced Worker Type

```graphql
type CompanyWorker {
  id: Int!
  tenantId: String!
  name: String!
  company: String!
  nationalId: String!
  phoneNumber: String!
  email: String!
  dateOfBirth: String!
  gender: String!
  mpesaNumber: String
  inductionDate: String
  medicalCheckDate: String
  profilePictureFile: File
  profilePictureUrl: String
  age: Int
  trainingsCompleted: Int
  manHours: Int
  rating: Float
  status: String!
  
  # Company-level specific fields
  employeeNumber: String!
  complianceStatus: TrainingComplianceStatus!
  currentSiteId: String
  totalSiteAssignments: Int!
  lastAssignmentDate: String
  hireDate: String!
  terminationDate: String
  rehireEligible: Boolean!
  performanceRating: Float!
  notes: String
  
  # Related data
  trades: [Trade!]!
  skills: [Skill!]!
  trainings: [Training!]!
  
  # Site assignments
  siteAssignments: [SiteWorkerAssignment!]!
  
  # Training compliance
  trainingCompliance: [WorkerTrainingCompliance!]!
  
  createdAt: String!
  createdBy: String!
  updatedAt: String!
  updatedBy: String!
  isDeleted: Boolean!
}

enum TrainingComplianceStatus {
  COMPLIANT
  PENDING_TRAINING
  NON_COMPLIANT
  EXPIRED
}

type SiteWorkerAssignment {
  id: Int!
  workerId: Int!
  siteId: String!
  role: String!
  startDate: String!
  endDate: String
  status: String!
  assignmentType: String!
  assignmentReason: String
  assignedBy: String
  hourlyRate: Float
  overtimeRate: Float
  totalHoursWorked: Float
  averageHoursPerDay: Float
  attendanceRate: Float
  safetyIncidents: Int
  qualityRating: Float
  performanceNotes: String
  transferredFromSite: String
  transferredToSite: String
  createdAt: String!
  createdBy: String!
}

type WorkerTrainingCompliance {
  id: String!
  workerId: Int!
  trainingId: Int!
  trainingName: String!
  tradeId: Int
  tradeName: String
  complianceStatus: TrainingComplianceStatus!
  isRequired: Boolean!
  isMandatory: Boolean!
  completionDate: String
  expiryDate: String
  renewalDueDate: String
  certificateUrl: String
  lastAssessmentDate: String
  nextAssessmentDue: String
  blockingSiteAssignment: Boolean!
  notes: String
  createdAt: String!
  updatedAt: String!
}

type SiteWorkerSummary {
  workerId: Int!
  workerName: String!
  employeeNumber: String!
  currentRole: String!
  assignmentDate: String!
  assignmentStatus: String!
  primaryTrade: String!
  complianceStatus: TrainingComplianceStatus!
  isOnSite: Boolean!
  lastCheckIn: String
  hoursWorkedToday: Float
  hoursWorkedThisWeek: Float
  attendanceRate: Float!
  safetyScore: Float!
  photoUrl: String
}

type CompanyWorkerStats {
  totalWorkers: Int!
  activeWorkers: Int!
  workersOnSites: Int!
  availableWorkers: Int!
  compliantWorkers: Int!
  nonCompliantWorkers: Int!
  workersNeedingTraining: Int!
  workersByTrade: JSON!
  workersBySite: JSON!
  averageExperience: Float!
  averagePerformanceRating: Float!
  totalManHours: Int!
  averageHourlyRate: Float!
}
```

### 2. New Queries

```graphql
extend type Query {
  # Company-level worker management
  allCompanyWorkers: [CompanyWorker!]!
  companyWorkerById(id: Int!): CompanyWorker
  companyWorkerStats: CompanyWorkerStats!
  
  # Site-specific worker queries
  siteWorkers(siteId: String!): [SiteWorkerSummary!]!
  availableWorkersForSite(siteId: String!): [CompanyWorker!]!
  
  # Worker assignment queries
  workerAssignmentHistory(workerId: Int!): [SiteWorkerAssignment!]!
  siteWorkerAssignments(siteId: String!): [SiteWorkerAssignment!]!
}
```

### 3. New Mutations

```graphql
extend type Mutation {
  # Company worker management
  createCompanyWorker(input: CreateCompanyWorkerInput!): CompanyWorker!
  updateCompanyWorker(id: Int!, input: UpdateCompanyWorkerInput!): CompanyWorker!
  
  # Site assignment management
  assignWorkerToSite(input: AssignWorkerToSiteInput!): SiteWorkerAssignment!
  updateWorkerSiteAssignment(id: Int!, input: UpdateSiteAssignmentInput!): SiteWorkerAssignment!
  bulkImportWorkersToSite(input: BulkImportWorkersInput!): BulkImportResult!
  
  # Training compliance management
  updateWorkerTrainingCompliance(input: UpdateTrainingComplianceInput!): WorkerTrainingCompliance!
}

input CreateCompanyWorkerInput {
  name: String!
  company: String!
  nationalId: String!
  phoneNumber: String!
  email: String!
  dateOfBirth: String!
  gender: String!
  employeeNumber: String!
  hireDate: String!
  tradeIds: [Int!]!
  skillIds: [Int!]
  siteId: String # If creating from site context
  role: String # If creating from site context
}

input AssignWorkerToSiteInput {
  workerId: Int!
  siteId: String!
  role: String!
  assignmentType: String!
  assignmentReason: String!
  hourlyRate: Float
  overtimeRate: Float
}

input BulkImportWorkersInput {
  siteId: String!
  workerIds: [Int!]!
  defaultRole: String!
  assignmentReason: String!
}

type BulkImportResult {
  successCount: Int!
  failureCount: Int!
  errors: [String!]!
  assignments: [SiteWorkerAssignment!]!
}
```

## Implementation Requirements

### 1. Data Migration

Create migration scripts to:
1. Add new columns to existing workers table
2. Create new tables for site assignments and training compliance
3. Migrate existing site-specific worker data to the new centralized structure
4. Generate employee numbers for existing workers
5. Create initial site assignments for workers currently assigned to sites

### 2. Business Logic Updates

1. **Worker Creation**: Always create workers at company level, optionally assign to site
2. **Site Assignment**: Implement assignment workflow with compliance checking
3. **Training Compliance**: Automatic compliance status calculation based on trade requirements
4. **Data Synchronization**: Ensure real-time updates between company and site levels

### 3. Performance Considerations

1. **Indexing**: Add appropriate indexes for frequently queried fields
2. **Caching**: Implement caching for worker statistics and compliance status
3. **Pagination**: Add pagination support for large worker lists
4. **Bulk Operations**: Optimize bulk import/export operations

### 4. Security and Permissions

1. **Tenant Isolation**: Ensure workers are properly isolated by tenant
2. **Role-Based Access**: Implement proper permissions for company vs site level access
3. **Audit Trail**: Track all changes to worker assignments and compliance status

## Testing Requirements

1. **Unit Tests**: Test all new GraphQL resolvers and business logic
2. **Integration Tests**: Test end-to-end worker management workflows
3. **Performance Tests**: Test with large datasets (1000+ workers)
4. **Migration Tests**: Verify data migration scripts work correctly

## Deployment Plan

1. **Phase 1**: Deploy database schema changes
2. **Phase 2**: Deploy new GraphQL schema and resolvers
3. **Phase 3**: Run data migration scripts
4. **Phase 4**: Update frontend to use new endpoints
5. **Phase 5**: Deprecate old worker management endpoints

This backend implementation will support the enhanced centralized worker management system while maintaining backward compatibility during the transition period.
