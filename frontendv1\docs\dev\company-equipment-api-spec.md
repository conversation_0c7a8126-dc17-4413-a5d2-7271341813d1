# Company Equipment Management API Specification

## Overview

This document defines the API endpoints for the company-level equipment management system, including equipment registration, site assignments, transfers, compliance tracking, and multi-site operations.

## Core API Endpoints

### 1. Company Equipment Registry

#### Create Equipment
```http
POST /api/company/equipment
Content-Type: application/json

{
  "name": "Caterpillar Excavator 320D",
  "category": "heavy-machinery",
  "subcategory": "excavators",
  "manufacturer": "Caterpillar",
  "model": "320D",
  "serialNumber": "CAT320D2024001",
  "yearOfManufacture": 2024,
  "purchaseDate": "2024-01-15",
  "purchasePrice": 150000,
  "specifications": {
    "Engine Power": "122 kW",
    "Operating Weight": "20,300 kg",
    "Bucket Capacity": "1.2 m³"
  },
  "operatingParameters": {
    "maxCapacity": "20 tons",
    "fuelType": "Diesel",
    "operatingWeight": "20,300 kg"
  },
  "safetyStandards": ["ISO 6165", "CE Marking"],
  "certificationRequirements": [
    {
      "name": "Annual Safety Inspection",
      "issuingAuthority": "Local Safety Authority",
      "validityPeriod": 12,
      "isRequired": true
    }
  ],
  "requiredTrainingIds": ["heavy-machinery-cert", "excavator-operation"],
  "operatorLicenseRequired": true,
  "maintenanceSchedule": {
    "dailyChecks": ["fluid-levels", "visual-inspection"],
    "weeklyMaintenance": ["lubrication", "filter-check"],
    "monthlyMaintenance": ["hydraulic-system", "engine-service"],
    "annualMaintenance": ["complete-overhaul"]
  },
  "expectedLifespanHours": 10000,
  "expectedLifespanYears": 10,
  "documents": [
    {
      "documentType": "manual",
      "title": "Operator Manual",
      "fileUrl": "https://storage/manuals/cat320d-manual.pdf"
    },
    {
      "documentType": "warranty",
      "title": "Manufacturer Warranty",
      "fileUrl": "https://storage/warranties/cat320d-warranty.pdf",
      "expiryDate": "2027-01-15"
    }
  ]
}

Response:
{
  "id": "eq-master-001",
  "equipmentNumber": "EQ-HM-2024-001",
  "companyId": "company-123",
  "name": "Caterpillar Excavator 320D",
  "overallStatus": "active",
  "isAvailableForAssignment": true,
  "createdAt": "2024-01-15T10:00:00Z",
  "complianceStatus": {
    "overallStatus": "compliant",
    "nextAssessmentDue": "2024-07-15T00:00:00Z"
  }
}
```

#### Get Company Equipment List
```http
GET /api/company/equipment?category={category}&status={status}&available={boolean}&page={page}&limit={limit}

Response:
{
  "equipment": [
    {
      "id": "eq-master-001",
      "equipmentNumber": "EQ-HM-2024-001",
      "name": "Caterpillar Excavator 320D",
      "category": "heavy-machinery",
      "manufacturer": "Caterpillar",
      "model": "320D",
      "overallStatus": "active",
      "isAvailableForAssignment": true,
      "currentAssignments": [
        {
          "siteId": "site-001",
          "siteName": "Downtown Construction",
          "assignmentDate": "2024-01-20",
          "assignmentType": "permanent"
        }
      ],
      "complianceStatus": {
        "overallStatus": "compliant",
        "expiringCertifications": 0,
        "overdueInspections": 0
      },
      "utilizationMetrics": {
        "totalHours": 1250,
        "averageHoursPerMonth": 125,
        "utilizationRate": 78
      }
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 45,
    "totalPages": 3
  }
}
```

#### Get Equipment Details
```http
GET /api/company/equipment/{equipmentId}

Response:
{
  "id": "eq-master-001",
  "equipmentNumber": "EQ-HM-2024-001",
  "companyId": "company-123",
  "name": "Caterpillar Excavator 320D",
  "category": "heavy-machinery",
  "manufacturer": "Caterpillar",
  "model": "320D",
  "serialNumber": "CAT320D2024001",
  "yearOfManufacture": 2024,
  "purchaseDate": "2024-01-15",
  "purchasePrice": 150000,
  "currentBookValue": 135000,
  "specifications": {
    "Engine Power": "122 kW",
    "Operating Weight": "20,300 kg"
  },
  "safetyStandards": ["ISO 6165", "CE Marking"],
  "overallStatus": "active",
  "isAvailableForAssignment": false,
  "currentAssignments": [
    {
      "id": "assignment-001",
      "siteId": "site-001",
      "siteName": "Downtown Construction",
      "assignmentDate": "2024-01-20",
      "assignmentType": "permanent",
      "currentStatus": "in-use",
      "currentCondition": "good",
      "hoursUsedAtSite": 1250,
      "locationOnSite": "Zone A - Foundation Area"
    }
  ],
  "complianceStatus": {
    "overallStatus": "compliant",
    "lastAssessmentDate": "2024-01-15",
    "nextAssessmentDue": "2024-07-15",
    "certifications": [
      {
        "certificationId": "cert-001",
        "name": "Annual Safety Inspection",
        "status": "valid",
        "expiryDate": "2025-01-15",
        "daysUntilExpiry": 45
      }
    ],
    "inspections": [
      {
        "inspectionType": "monthly",
        "status": "up-to-date",
        "lastInspectionDate": "2024-12-01",
        "nextInspectionDue": "2025-01-01"
      }
    ]
  },
  "performanceMetrics": {
    "totalOperatingHours": 1250,
    "averageHoursPerMonth": 125,
    "utilizationRate": 78,
    "totalMaintenanceCost": 15420,
    "costPerOperatingHour": 12.34,
    "availabilityRate": 95
  },
  "documents": [
    {
      "id": "doc-001",
      "documentType": "manual",
      "title": "Operator Manual",
      "fileUrl": "https://storage/manuals/cat320d-manual.pdf",
      "uploadedAt": "2024-01-15T10:00:00Z"
    }
  ],
  "lifecycleEvents": [
    {
      "id": "event-001",
      "eventType": "purchase",
      "eventDate": "2024-01-15",
      "description": "Equipment registered in company inventory",
      "location": "Company Headquarters"
    }
  ]
}
```

### 2. Site Equipment Assignment

#### Assign Equipment to Site
```http
POST /api/sites/{siteId}/equipment/assign
Content-Type: application/json

{
  "companyEquipmentId": "eq-master-001",
  "assignmentType": "permanent",
  "assignmentReason": "Required for foundation work",
  "expectedReturnDate": null,
  "siteEquipmentNumber": "SITE-EQ-001",
  "locationOnSite": "Zone A - Foundation Area",
  "gpsCoordinates": {
    "latitude": 40.7128,
    "longitude": -74.0060
  }
}

Response:
{
  "assignmentId": "assignment-001",
  "companyEquipmentId": "eq-master-001",
  "siteId": "site-001",
  "assignmentDate": "2024-01-20T10:00:00Z",
  "assignmentStatus": "active",
  "currentStatus": {
    "id": "available",
    "name": "Available",
    "isAvailableForAssignment": true,
    "color": "green"
  },
  "complianceCheck": {
    "status": "passed",
    "issues": []
  }
}
```

#### Get Site Equipment
```http
GET /api/sites/{siteId}/equipment?status={status}&category={category}

Response:
{
  "siteEquipment": [
    {
      "assignmentId": "assignment-001",
      "companyEquipment": {
        "id": "eq-master-001",
        "equipmentNumber": "EQ-HM-2024-001",
        "name": "Caterpillar Excavator 320D",
        "category": "heavy-machinery",
        "manufacturer": "Caterpillar",
        "model": "320D"
      },
      "siteEquipmentNumber": "SITE-EQ-001",
      "assignmentDate": "2024-01-20",
      "assignmentType": "permanent",
      "currentStatus": {
        "id": "in-use",
        "name": "In Use",
        "isAvailableForAssignment": false,
        "color": "blue"
      },
      "currentCondition": "good",
      "currentAssignee": {
        "workerId": "worker-001",
        "workerName": "John Mwangi",
        "assignedDate": "2024-01-25"
      },
      "locationOnSite": "Zone A - Foundation Area",
      "hoursUsedAtSite": 1250,
      "lastInspectionDate": "2024-12-01",
      "nextInspectionDue": "2025-01-01",
      "complianceStatus": "compliant"
    }
  ]
}
```

#### Update Site Equipment
```http
PUT /api/sites/{siteId}/equipment/{assignmentId}
Content-Type: application/json

{
  "currentStatus": {
    "id": "maintenance",
    "name": "Under Maintenance",
    "isAvailableForAssignment": false,
    "color": "yellow"
  },
  "currentCondition": "fair",
  "locationOnSite": "Maintenance Bay",
  "hoursUsedAtSite": 1275,
  "lastMaintenanceDate": "2024-12-15",
  "maintenanceNotes": "Hydraulic system service completed",
  "updatedBy": "user-123"
}

Response:
{
  "assignmentId": "assignment-001",
  "updated": true,
  "syncedToCompany": true,
  "lifecycleEventCreated": "event-045",
  "complianceStatusUpdated": false
}
```

### 3. Equipment Transfer Management

#### Create Transfer Request
```http
POST /api/equipment/transfer/request
Content-Type: application/json

{
  "companyEquipmentId": "eq-master-001",
  "fromSiteId": "site-001",
  "toSiteId": "site-002",
  "requestReason": "Urgent requirement for excavation work at Site 002",
  "urgency": "high",
  "proposedTransferDate": "2024-12-25",
  "proposedReturnDate": "2025-01-15",
  "estimatedTransportCost": 500
}

Response:
{
  "transferRequestId": "transfer-001",
  "status": "pending",
  "approvals": [
    {
      "approverRole": "site-manager",
      "approverId": "user-site-001-manager",
      "approverName": "Sarah Johnson",
      "approvalStatus": "pending"
    },
    {
      "approverRole": "site-manager",
      "approverId": "user-site-002-manager",
      "approverName": "Mike Chen",
      "approvalStatus": "pending"
    },
    {
      "approverRole": "equipment-manager",
      "approverId": "user-equipment-manager",
      "approverName": "David Wilson",
      "approvalStatus": "pending"
    }
  ],
  "estimatedApprovalTime": "2-3 business days"
}
```

#### Get Transfer Requests
```http
GET /api/equipment/transfer/requests?status={status}&siteId={siteId}&equipmentId={equipmentId}

Response:
{
  "transferRequests": [
    {
      "id": "transfer-001",
      "companyEquipment": {
        "id": "eq-master-001",
        "equipmentNumber": "EQ-HM-2024-001",
        "name": "Caterpillar Excavator 320D"
      },
      "fromSite": {
        "id": "site-001",
        "name": "Downtown Construction"
      },
      "toSite": {
        "id": "site-002",
        "name": "Westlands Project"
      },
      "requestedBy": "John Smith",
      "requestedAt": "2024-12-20T10:00:00Z",
      "requestReason": "Urgent requirement for excavation work",
      "urgency": "high",
      "status": "pending",
      "proposedTransferDate": "2024-12-25",
      "approvalProgress": {
        "totalApprovers": 3,
        "approvedCount": 1,
        "pendingCount": 2,
        "rejectedCount": 0
      }
    }
  ]
}
```

#### Approve/Reject Transfer Request
```http
PUT /api/equipment/transfer/requests/{transferRequestId}/approve
Content-Type: application/json

{
  "approvalStatus": "approved",
  "comments": "Approved for urgent project requirements",
  "conditions": [
    "Equipment must be returned by January 15, 2025",
    "Daily inspection reports required"
  ]
}

Response:
{
  "transferRequestId": "transfer-001",
  "approvalUpdated": true,
  "overallStatus": "approved",
  "readyForExecution": true,
  "nextSteps": [
    "Schedule transport",
    "Prepare transfer documentation",
    "Conduct pre-transfer inspection"
  ]
}
```

#### Execute Transfer
```http
POST /api/equipment/transfer/requests/{transferRequestId}/execute
Content-Type: application/json

{
  "actualTransferDate": "2024-12-25T09:00:00Z",
  "transportMethod": "Low-bed trailer",
  "transportCost": 450,
  "transferredBy": "user-transport-001",
  "receivedBy": "user-site-002-supervisor",
  "conditionBeforeTransfer": "good",
  "conditionAfterTransfer": "good",
  "transferDamages": [],
  "photos": [
    "https://storage/transfers/transfer-001-before-1.jpg",
    "https://storage/transfers/transfer-001-after-1.jpg"
  ],
  "notes": "Transfer completed successfully without issues"
}

Response:
{
  "transferRequestId": "transfer-001",
  "newAssignmentId": "assignment-045",
  "transferCompleted": true,
  "transferHistoryId": "history-001",
  "lifecycleEventId": "event-078",
  "notifications": {
    "fromSiteNotified": true,
    "toSiteNotified": true,
    "equipmentManagerNotified": true
  }
}
```

### 4. Compliance Management

#### Get Equipment Compliance Status
```http
GET /api/company/equipment/{equipmentId}/compliance

Response:
{
  "equipmentId": "eq-master-001",
  "overallStatus": "compliant",
  "lastAssessmentDate": "2024-12-01T10:00:00Z",
  "nextAssessmentDue": "2024-12-31T23:59:59Z",
  "certifications": [
    {
      "certificationId": "cert-001",
      "name": "Annual Safety Inspection",
      "status": "valid",
      "currentCertificateId": "cert-doc-001",
      "expiryDate": "2025-01-15T00:00:00Z",
      "daysUntilExpiry": 25,
      "issuingAuthority": "Local Safety Authority"
    },
    {
      "certificationId": "cert-002",
      "name": "Operator License Verification",
      "status": "expiring-soon",
      "currentCertificateId": "cert-doc-002",
      "expiryDate": "2024-12-31T00:00:00Z",
      "daysUntilExpiry": 10,
      "issuingAuthority": "Department of Transport"
    }
  ],
  "inspections": [
    {
      "inspectionType": "daily",
      "status": "up-to-date",
      "lastInspectionDate": "2024-12-19T08:00:00Z",
      "nextInspectionDue": "2024-12-20T08:00:00Z"
    },
    {
      "inspectionType": "monthly",
      "status": "due",
      "lastInspectionDate": "2024-11-15T10:00:00Z",
      "nextInspectionDue": "2024-12-15T10:00:00Z",
      "daysOverdue": 5
    }
  ],
  "blockingIssues": [
    {
      "issueType": "overdue-inspection",
      "description": "Monthly safety inspection is 5 days overdue",
      "severity": "medium",
      "blocksOperation": false,
      "recommendedAction": "Schedule inspection within 2 days"
    }
  ],
  "complianceScore": 85,
  "riskLevel": "low"
}
```

#### Update Equipment Compliance
```http
PUT /api/company/equipment/{equipmentId}/compliance
Content-Type: application/json

{
  "certificationUpdates": [
    {
      "certificationId": "cert-002",
      "newCertificateId": "cert-doc-003",
      "issueDate": "2024-12-20",
      "expiryDate": "2025-12-20",
      "documentUrl": "https://storage/certificates/cert-doc-003.pdf"
    }
  ],
  "inspectionUpdates": [
    {
      "inspectionType": "monthly",
      "completionDate": "2024-12-20T14:00:00Z",
      "inspectorId": "inspector-001",
      "inspectorName": "Jane Wilson",
      "result": "pass",
      "findings": "All systems operational, minor wear on hydraulic seals noted",
      "nextInspectionDue": "2025-01-20T14:00:00Z",
      "documentUrl": "https://storage/inspections/inspection-report-001.pdf"
    }
  ],
  "updatedBy": "user-compliance-manager"
}

Response:
{
  "equipmentId": "eq-master-001",
  "complianceUpdated": true,
  "newOverallStatus": "compliant",
  "resolvedIssues": [
    "overdue-inspection-monthly"
  ],
  "newComplianceScore": 95,
  "sitesSynced": [
    "site-001",
    "site-002"
  ],
  "notificationsSent": {
    "siteManagers": true,
    "equipmentOperators": true,
    "complianceTeam": true
  }
}
```

#### Get Company Compliance Dashboard
```http
GET /api/company/equipment/compliance/dashboard

Response:
{
  "overallMetrics": {
    "totalEquipment": 145,
    "compliantEquipment": 132,
    "nonCompliantEquipment": 8,
    "expiringCertifications": 15,
    "overdueInspections": 5,
    "complianceRate": 91.0,
    "riskScore": 15
  },
  "certificationStatus": [
    {
      "certificationType": "Annual Safety Inspection",
      "totalRequired": 145,
      "valid": 130,
      "expiringSoon": 10,
      "expired": 5
    }
  ],
  "inspectionStatus": [
    {
      "inspectionType": "monthly",
      "totalRequired": 145,
      "upToDate": 135,
      "due": 7,
      "overdue": 3
    }
  ],
  "riskCategories": [
    {
      "category": "high-risk",
      "equipmentCount": 3,
      "primaryIssues": ["expired-safety-cert", "overdue-critical-inspection"]
    },
    {
      "category": "medium-risk",
      "equipmentCount": 12,
      "primaryIssues": ["expiring-certifications", "due-inspections"]
    }
  ],
  "upcomingDeadlines": [
    {
      "equipmentId": "eq-master-001",
      "equipmentName": "Caterpillar Excavator 320D",
      "deadlineType": "certification-expiry",
      "deadline": "2024-12-31T00:00:00Z",
      "daysRemaining": 10,
      "priority": "high"
    }
  ]
}
```

### 5. Multi-Site Operations

#### Get Equipment Locations
```http
GET /api/company/equipment/locations

Response:
{
  "equipmentLocations": [
    {
      "equipmentId": "eq-master-001",
      "equipmentNumber": "EQ-HM-2024-001",
      "name": "Caterpillar Excavator 320D",
      "currentLocation": {
        "type": "site",
        "siteId": "site-001",
        "siteName": "Downtown Construction",
        "locationOnSite": "Zone A - Foundation Area",
        "gpsCoordinates": {
          "latitude": 40.7128,
          "longitude": -74.0060
        },
        "assignmentDate": "2024-01-20",
        "assignmentType": "permanent"
      },
      "status": "in-use",
      "condition": "good",
      "lastUpdated": "2024-12-19T15:30:00Z"
    }
  ],
  "summary": {
    "totalEquipment": 145,
    "assignedToSites": 132,
    "inTransit": 3,
    "atHeadquarters": 8,
    "underMaintenance": 2
  }
}
```

#### Get Equipment History
```http
GET /api/company/equipment/{equipmentId}/history?eventType={eventType}&startDate={startDate}&endDate={endDate}

Response:
{
  "equipmentId": "eq-master-001",
  "lifecycleEvents": [
    {
      "id": "event-001",
      "eventType": "purchase",
      "eventDate": "2024-01-15T00:00:00Z",
      "description": "Equipment registered in company inventory",
      "location": "Company Headquarters",
      "performedBy": "John Smith",
      "cost": 150000,
      "costCategory": "purchase"
    },
    {
      "id": "event-002",
      "eventType": "deployment",
      "eventDate": "2024-01-20T10:00:00Z",
      "description": "Equipment assigned to site: Downtown Construction",
      "location": "site-001",
      "performedBy": "Site Manager",
      "conditionBefore": "excellent",
      "conditionAfter": "excellent"
    },
    {
      "id": "event-003",
      "eventType": "maintenance",
      "eventDate": "2024-03-15T14:00:00Z",
      "description": "500-hour maintenance service completed",
      "location": "site-001",
      "performedBy": "James Ochieng",
      "cost": 2000,
      "costCategory": "maintenance",
      "conditionBefore": "good",
      "conditionAfter": "excellent"
    }
  ],
  "transferHistory": [
    {
      "id": "transfer-history-001",
      "fromSite": {
        "id": "site-001",
        "name": "Downtown Construction"
      },
      "toSite": {
        "id": "site-002",
        "name": "Westlands Project"
      },
      "transferDate": "2024-06-01T09:00:00Z",
      "returnDate": "2024-06-30T17:00:00Z",
      "transferDuration": 29,
      "transferCost": 450,
      "utilizationAtToSite": 240,
      "transferRating": 4
    }
  ],
  "utilizationSummary": {
    "totalOperatingHours": 1250,
    "siteUtilization": [
      {
        "siteId": "site-001",
        "siteName": "Downtown Construction",
        "hoursUsed": 1010,
        "utilizationRate": 80.8,
        "performanceRating": 4.2
      },
      {
        "siteId": "site-002",
        "siteName": "Westlands Project",
        "hoursUsed": 240,
        "utilizationRate": 19.2,
        "performanceRating": 4.5
      }
    ]
  }
}
```

#### Get Equipment Utilization Analytics
```http
GET /api/company/equipment/utilization?period={period}&groupBy={groupBy}

Response:
{
  "period": "last-12-months",
  "overallMetrics": {
    "totalEquipment": 145,
    "averageUtilizationRate": 72.5,
    "totalOperatingHours": 125000,
    "totalMaintenanceCost": 450000,
    "costPerOperatingHour": 3.6,
    "averageAvailabilityRate": 94.2
  },
  "utilizationByCategory": [
    {
      "category": "heavy-machinery",
      "equipmentCount": 25,
      "averageUtilizationRate": 85.2,
      "totalHours": 45000,
      "costPerHour": 12.5
    },
    {
      "category": "power-tools",
      "equipmentCount": 80,
      "averageUtilizationRate": 65.8,
      "totalHours": 35000,
      "costPerHour": 2.1
    }
  ],
  "utilizationBySite": [
    {
      "siteId": "site-001",
      "siteName": "Downtown Construction",
      "equipmentCount": 45,
      "averageUtilizationRate": 78.5,
      "totalHours": 35000,
      "performanceRating": 4.2
    }
  ],
  "underutilizedEquipment": [
    {
      "equipmentId": "eq-master-045",
      "name": "Concrete Mixer CM-350",
      "utilizationRate": 25.5,
      "recommendedAction": "Consider transfer to high-demand site"
    }
  ],
  "highPerformers": [
    {
      "equipmentId": "eq-master-001",
      "name": "Caterpillar Excavator 320D",
      "utilizationRate": 95.2,
      "availabilityRate": 98.5,
      "performanceScore": 96.8
    }
  ]
}
```

## Error Handling

### Common Error Responses

#### Equipment Not Available
```json
{
  "error": "EQUIPMENT_NOT_AVAILABLE",
  "message": "Equipment is not available for assignment",
  "details": {
    "equipmentId": "eq-master-001",
    "currentStatus": "under-maintenance",
    "availableDate": "2024-12-25T00:00:00Z",
    "blockingIssues": [
      "scheduled-maintenance-in-progress"
    ]
  }
}
```

#### Compliance Issues
```json
{
  "error": "COMPLIANCE_VIOLATION",
  "message": "Equipment cannot be assigned due to compliance issues",
  "details": {
    "equipmentId": "eq-master-001",
    "complianceStatus": "non-compliant",
    "blockingIssues": [
      {
        "issueType": "expired-certification",
        "description": "Safety certification expired on 2024-12-01",
        "severity": "critical",
        "blocksOperation": true
      }
    ],
    "requiredActions": [
      "Renew safety certification",
      "Complete compliance inspection"
    ]
  }
}
```

#### Transfer Approval Required
```json
{
  "error": "TRANSFER_APPROVAL_REQUIRED",
  "message": "Equipment transfer requires additional approvals",
  "details": {
    "transferRequestId": "transfer-001",
    "pendingApprovals": [
      {
        "approverRole": "equipment-manager",
        "approverName": "David Wilson",
        "estimatedApprovalTime": "1-2 business days"
      }
    ],
    "canProceedWithoutApproval": false
  }
}
```

## Integration Considerations

### 1. Real-time Synchronization
- WebSocket connections for live equipment status updates
- Event-driven architecture for compliance status changes
- Automatic sync between company and site-level data

### 2. Mobile Application Support
- Offline capability for equipment inspections
- QR code scanning for equipment identification
- Photo upload for condition documentation

### 3. External System Integration
- ERP system integration for financial data
- Maintenance management system connectivity
- Compliance management platform integration

### 4. Reporting and Analytics
- Automated compliance reports
- Equipment utilization dashboards
- Predictive maintenance analytics
- Cost optimization recommendations

This API specification provides a comprehensive framework for managing company-owned equipment across multiple construction sites while maintaining strict compliance, optimizing utilization, and providing complete visibility into equipment lifecycle and performance.