# Company-Level Equipment Management System

## Overview

This document outlines the redesigned equipment management system that establishes company-level ownership of equipment with site assignment capabilities. The system addresses compliance, safety, multi-site operations, and comprehensive equipment lifecycle management.

## Current Implementation Analysis

### What's Good (Keep)
1. **Comprehensive Equipment Types**: Good separation between general equipment and PPE
2. **Detailed Equipment Master Data**: Rich metadata including specifications, safety standards, and maintenance intervals
3. **Status Tracking**: Equipment status management with availability indicators
4. **Maintenance Work Orders**: Structured maintenance workflow with cost tracking
5. **PPE Lifespan Tracking**: Individual PPE lifespan monitoring per worker
6. **Inspection Framework**: Equipment inspection templates and tracking
7. **Assignment Tracking**: Worker and project assignment capabilities

### What Needs Improvement (Drop/Redesign)
1. **Site-Centric Ownership**: Equipment is currently owned by sites, not company
2. **Limited Multi-Site Capability**: No cross-site equipment movement tracking
3. **Fragmented Compliance Data**: Safety and compliance data scattered across components
4. **No Central Equipment Registry**: Missing company-wide equipment visibility
5. **Limited Document Management**: Insufficient equipment documentation tracking
6. **No Equipment Transfer Workflow**: Missing site-to-site transfer processes
7. **Weak Audit Trail**: Limited tracking of equipment history across sites

## New System Architecture

### 1. Company-Level Equipment Ownership

```typescript
// Company Equipment Master - Central Registry
interface CompanyEquipmentMaster {
  // Core Identification
  id: string;
  companyId: string; // Tenant/Company ownership
  equipmentNumber: string; // Company-wide unique identifier
  name: string;
  category: EquipmentCategory;
  subcategory?: string;
  
  // Physical Specifications
  manufacturer: string;
  model: string;
  serialNumber: string;
  yearOfManufacture: number;
  
  // Financial Information
  purchaseDate: Date;
  purchasePrice: number;
  currentBookValue: number;
  depreciationMethod: 'straight-line' | 'declining-balance' | 'units-of-production';
  depreciationRate: number;
  residualValue: number;
  
  // Technical Specifications
  specifications: Record<string, string>;
  operatingParameters: {
    maxCapacity?: string;
    powerRequirements?: string;
    fuelType?: string;
    operatingWeight?: string;
    dimensions?: {
      length: number;
      width: number;
      height: number;
      unit: 'mm' | 'cm' | 'm' | 'in' | 'ft';
    };
  };
  
  // Compliance & Safety
  safetyStandards: string[];
  certificationRequirements: CertificationRequirement[];
  requiredTrainingIds: string[];
  operatorLicenseRequired: boolean;
  inspectionRequirements: InspectionRequirement[];
  
  // Maintenance Configuration
  maintenanceSchedule: MaintenanceSchedule;
  expectedLifespanHours?: number;
  expectedLifespanYears?: number;
  
  // Documentation
  documents: EquipmentDocument[];
  images: string[];
  manuals: DocumentReference[];
  warranties: WarrantyInfo[];
  
  // Status & Availability
  overallStatus: 'active' | 'retired' | 'disposed' | 'under-repair';
  isAvailableForAssignment: boolean;
  
  // Audit Trail
  createdAt: Date;
  createdBy: string;
  updatedAt: Date;
  updatedBy: string;
}

// Site Equipment Assignment - Links company equipment to sites
interface SiteEquipmentAssignment {
  id: string;
  companyEquipmentId: string;
  siteId: string;
  
  // Assignment Details
  assignmentDate: Date;
  expectedReturnDate?: Date;
  actualReturnDate?: Date;
  assignmentReason: string;
  assignmentType: 'permanent' | 'temporary' | 'project-specific';
  
  // Site-Specific Configuration
  siteEquipmentNumber?: string; // Site-specific identifier
  locationOnSite?: string;
  gpsCoordinates?: { latitude: number; longitude: number };
  
  // Current Status at Site
  currentStatus: SiteEquipmentStatus;
  currentCondition: EquipmentCondition;
  currentAssignee?: WorkerAssignment;
  
  // Site-Specific Tracking
  hoursUsedAtSite: number;
  lastInspectionDate?: Date;
  nextInspectionDue?: Date;
  lastMaintenanceDate?: Date;
  nextMaintenanceDue?: Date;
  
  // Transfer Information
  transferredFromSite?: string;
  transferDate?: Date;
  transferReason?: string;
  transferApprovedBy?: string;
  
  // Assignment Status
  assignmentStatus: 'active' | 'completed' | 'transferred' | 'returned';
  
  // Audit Trail
  assignedBy: string;
  assignedAt: Date;
  updatedAt: Date;
  updatedBy: string;
}
```

### 2. Enhanced Compliance & Safety Tracking

```typescript
interface CertificationRequirement {
  id: string;
  name: string;
  issuingAuthority: string;
  validityPeriod: number; // in months
  isRequired: boolean;
  applicableRegions: string[];
  renewalNotificationDays: number;
}

interface InspectionRequirement {
  id: string;
  inspectionType: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'annual' | 'pre-use' | 'post-incident';
  frequency: number; // in days
  inspectionStandard: string;
  requiredCertifications: string[];
  isRegulatory: boolean;
  gracePeriodDays: number;
}

interface EquipmentDocument {
  id: string;
  documentType: 'certificate' | 'manual' | 'warranty' | 'inspection-report' | 'maintenance-record' | 'compliance-doc';
  title: string;
  documentNumber?: string;
  issuedBy?: string;
  issueDate?: Date;
  expiryDate?: Date;
  fileUrl: string;
  fileSize: number;
  mimeType: string;
  isActive: boolean;
  complianceRelated: boolean;
  accessLevel: 'public' | 'internal' | 'restricted';
  uploadedBy: string;
  uploadedAt: Date;
}

interface ComplianceStatus {
  equipmentId: string;
  overallStatus: 'compliant' | 'non-compliant' | 'expiring-soon' | 'pending-review';
  lastAssessmentDate: Date;
  nextAssessmentDue: Date;
  
  certifications: Array<{
    certificationId: string;
    status: 'valid' | 'expired' | 'expiring-soon' | 'not-applicable';
    currentCertificateId?: string;
    expiryDate?: Date;
    daysUntilExpiry?: number;
  }>;
  
  inspections: Array<{
    inspectionType: string;
    status: 'up-to-date' | 'due' | 'overdue';
    lastInspectionDate?: Date;
    nextInspectionDue?: Date;
    daysOverdue?: number;
  }>;
  
  blockingIssues: Array<{
    issueType: 'expired-certification' | 'overdue-inspection' | 'safety-concern';
    description: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    blocksOperation: boolean;
  }>;
}
```

### 3. Multi-Site Equipment Transfer System

```typescript
interface EquipmentTransferRequest {
  id: string;
  companyEquipmentId: string;
  
  // Transfer Details
  fromSiteId: string;
  toSiteId: string;
  requestedBy: string;
  requestedAt: Date;
  requestReason: string;
  urgency: 'low' | 'medium' | 'high' | 'critical';
  
  // Proposed Timeline
  proposedTransferDate: Date;
  proposedReturnDate?: Date;
  estimatedTransportCost?: number;
  
  // Approval Workflow
  status: 'pending' | 'approved' | 'rejected' | 'in-transit' | 'completed' | 'cancelled';
  approvals: Array<{
    approverRole: 'site-manager' | 'equipment-manager' | 'operations-manager';
    approverId: string;
    approverName: string;
    approvalStatus: 'pending' | 'approved' | 'rejected';
    approvalDate?: Date;
    comments?: string;
  }>;
  
  // Transfer Execution
  actualTransferDate?: Date;
  transportMethod?: string;
  transportCost?: number;
  transferredBy?: string;
  receivedBy?: string;
  
  // Condition Assessment
  conditionBeforeTransfer?: EquipmentCondition;
  conditionAfterTransfer?: EquipmentCondition;
  transferDamages?: string[];
  
  // Documentation
  transferDocuments: string[]; // Document IDs
  photos: string[];
  notes?: string;
}

interface EquipmentTransferHistory {
  id: string;
  companyEquipmentId: string;
  transferRequestId: string;
  
  // Transfer Summary
  fromSite: SiteInfo;
  toSite: SiteInfo;
  transferDate: Date;
  returnDate?: Date;
  
  // Performance Metrics
  transferDuration: number; // in days
  utilizationAtFromSite: number; // hours used
  utilizationAtToSite: number; // hours used
  transferCost: number;
  
  // Impact Assessment
  downtimeHours: number;
  productivityImpact?: string;
  costBenefit?: number;
  
  // Lessons Learned
  transferRating: 1 | 2 | 3 | 4 | 5;
  feedback?: string;
  improvementSuggestions?: string[];
}
```

### 4. Comprehensive Equipment Lifecycle Management

```typescript
interface EquipmentLifecycleEvent {
  id: string;
  companyEquipmentId: string;
  eventType: 'purchase' | 'deployment' | 'maintenance' | 'inspection' | 'transfer' | 'incident' | 'upgrade' | 'retirement' | 'disposal';
  eventDate: Date;
  
  // Event Details
  description: string;
  location: string; // Site or facility
  performedBy: string;
  authorizedBy?: string;
  
  // Financial Impact
  cost?: number;
  costCategory?: 'maintenance' | 'repair' | 'upgrade' | 'transport' | 'compliance';
  
  // Condition Impact
  conditionBefore?: EquipmentCondition;
  conditionAfter?: EquipmentCondition;
  
  // Compliance Impact
  complianceStatus?: 'improved' | 'maintained' | 'degraded';
  certificationsAffected?: string[];
  
  // Documentation
  documents: string[];
  photos: string[];
  
  // Next Actions
  followUpRequired: boolean;
  followUpDate?: Date;
  followUpDescription?: string;
}

interface EquipmentPerformanceMetrics {
  companyEquipmentId: string;
  calculationDate: Date;
  
  // Utilization Metrics
  totalOperatingHours: number;
  averageHoursPerMonth: number;
  utilizationRate: number; // percentage
  idleTime: number; // hours
  
  // Financial Metrics
  totalMaintenanceCost: number;
  costPerOperatingHour: number;
  returnOnInvestment: number;
  depreciatedValue: number;
  
  // Reliability Metrics
  meanTimeBetweenFailures: number; // hours
  meanTimeToRepair: number; // hours
  availabilityRate: number; // percentage
  
  // Compliance Metrics
  complianceScore: number; // 0-100
  overdueInspections: number;
  expiredCertifications: number;
  safetyIncidents: number;
  
  // Site Performance
  siteUtilization: Array<{
    siteId: string;
    siteName: string;
    hoursUsed: number;
    utilizationRate: number;
    performanceRating: number;
  }>;
}
```

## Implementation Workflow

### 1. Equipment Registration Process

```typescript
// Step 1: Create Company Equipment Master
const registerEquipment = async (equipmentData: CreateEquipmentRequest) => {
  // 1. Generate unique equipment number
  const equipmentNumber = await generateEquipmentNumber(equipmentData.companyId, equipmentData.category);
  
  // 2. Create equipment master record
  const equipment = await createCompanyEquipment({
    ...equipmentData,
    equipmentNumber,
    overallStatus: 'active',
    isAvailableForAssignment: true
  });
  
  // 3. Upload and process documents
  if (equipmentData.documents?.length) {
    await processEquipmentDocuments(equipment.id, equipmentData.documents);
  }
  
  // 4. Initialize compliance tracking
  await initializeComplianceTracking(equipment.id);
  
  // 5. Create initial lifecycle event
  await createLifecycleEvent({
    companyEquipmentId: equipment.id,
    eventType: 'purchase',
    eventDate: equipmentData.purchaseDate,
    description: 'Equipment registered in company inventory',
    location: 'Company Headquarters',
    performedBy: equipmentData.createdBy
  });
  
  return equipment;
};

// Step 2: Site Assignment Process
const assignEquipmentToSite = async (assignmentData: CreateSiteAssignmentRequest) => {
  // 1. Validate equipment availability
  const equipment = await getCompanyEquipment(assignmentData.companyEquipmentId);
  if (!equipment.isAvailableForAssignment) {
    throw new Error('Equipment is not available for assignment');
  }
  
  // 2. Check compliance status
  const complianceStatus = await getEquipmentComplianceStatus(assignmentData.companyEquipmentId);
  if (complianceStatus.overallStatus === 'non-compliant') {
    throw new Error('Equipment is not compliant and cannot be assigned');
  }
  
  // 3. Create site assignment
  const assignment = await createSiteEquipmentAssignment({
    ...assignmentData,
    assignmentStatus: 'active',
    currentStatus: { id: 'available', name: 'Available', isAvailableForAssignment: true, color: 'green' },
    currentCondition: 'good'
  });
  
  // 4. Update equipment availability
  await updateCompanyEquipment(assignmentData.companyEquipmentId, {
    isAvailableForAssignment: assignmentData.assignmentType === 'permanent' ? false : true
  });
  
  // 5. Initialize site-specific tracking
  await initializeSiteTracking(assignment.id);
  
  // 6. Create lifecycle event
  await createLifecycleEvent({
    companyEquipmentId: assignmentData.companyEquipmentId,
    eventType: 'deployment',
    eventDate: assignmentData.assignmentDate,
    description: `Equipment assigned to site: ${assignmentData.siteId}`,
    location: assignmentData.siteId,
    performedBy: assignmentData.assignedBy
  });
  
  return assignment;
};
```

### 2. Site-to-Site Transfer Process

```typescript
const initiateEquipmentTransfer = async (transferData: CreateTransferRequest) => {
  // 1. Validate current assignment
  const currentAssignment = await getCurrentSiteAssignment(transferData.companyEquipmentId);
  if (!currentAssignment || currentAssignment.siteId !== transferData.fromSiteId) {
    throw new Error('Equipment is not currently assigned to the specified source site');
  }
  
  // 2. Check equipment status
  if (currentAssignment.currentStatus.id === 'maintenance' || currentAssignment.currentStatus.id === 'damaged') {
    throw new Error('Equipment cannot be transferred while under maintenance or damaged');
  }
  
  // 3. Create transfer request
  const transferRequest = await createTransferRequest({
    ...transferData,
    status: 'pending',
    approvals: await initializeApprovalWorkflow(transferData)
  });
  
  // 4. Notify approvers
  await notifyTransferApprovers(transferRequest.id);
  
  return transferRequest;
};

const executeEquipmentTransfer = async (transferRequestId: string, executionData: TransferExecutionData) => {
  const transferRequest = await getTransferRequest(transferRequestId);
  
  // 1. Validate all approvals received
  const allApproved = transferRequest.approvals.every(a => a.approvalStatus === 'approved');
  if (!allApproved) {
    throw new Error('Transfer cannot be executed without all required approvals');
  }
  
  // 2. Update current site assignment
  await updateSiteEquipmentAssignment(transferRequest.fromSiteId, transferRequest.companyEquipmentId, {
    assignmentStatus: 'completed',
    actualReturnDate: executionData.actualTransferDate,
    conditionOnReturn: executionData.conditionBeforeTransfer
  });
  
  // 3. Create new site assignment
  const newAssignment = await createSiteEquipmentAssignment({
    companyEquipmentId: transferRequest.companyEquipmentId,
    siteId: transferRequest.toSiteId,
    assignmentDate: executionData.actualTransferDate,
    assignmentReason: `Transfer from site ${transferRequest.fromSiteId}`,
    assignmentType: 'temporary',
    transferredFromSite: transferRequest.fromSiteId,
    transferDate: executionData.actualTransferDate,
    assignedBy: executionData.transferredBy
  });
  
  // 4. Update transfer request
  await updateTransferRequest(transferRequestId, {
    status: 'completed',
    actualTransferDate: executionData.actualTransferDate,
    conditionBeforeTransfer: executionData.conditionBeforeTransfer,
    conditionAfterTransfer: executionData.conditionAfterTransfer,
    transferredBy: executionData.transferredBy,
    receivedBy: executionData.receivedBy
  });
  
  // 5. Create transfer history record
  await createTransferHistory({
    companyEquipmentId: transferRequest.companyEquipmentId,
    transferRequestId: transferRequestId,
    fromSite: await getSite(transferRequest.fromSiteId),
    toSite: await getSite(transferRequest.toSiteId),
    transferDate: executionData.actualTransferDate,
    transferCost: executionData.transportCost || 0
  });
  
  // 6. Create lifecycle events
  await createLifecycleEvent({
    companyEquipmentId: transferRequest.companyEquipmentId,
    eventType: 'transfer',
    eventDate: executionData.actualTransferDate,
    description: `Equipment transferred from ${transferRequest.fromSiteId} to ${transferRequest.toSiteId}`,
    location: transferRequest.toSiteId,
    performedBy: executionData.transferredBy,
    cost: executionData.transportCost
  });
  
  return newAssignment;
};
```

### 3. Compliance Synchronization

```typescript
const syncComplianceAcrossSites = async (companyEquipmentId: string) => {
  // 1. Get all current site assignments
  const siteAssignments = await getActiveSiteAssignments(companyEquipmentId);
  
  // 2. Get latest compliance status from company level
  const companyCompliance = await getEquipmentComplianceStatus(companyEquipmentId);
  
  // 3. Update each site assignment with current compliance
  for (const assignment of siteAssignments) {
    await updateSiteEquipmentCompliance(assignment.id, {
      complianceStatus: companyCompliance.overallStatus,
      lastComplianceCheck: new Date(),
      blockingIssues: companyCompliance.blockingIssues,
      nextComplianceReview: companyCompliance.nextAssessmentDue
    });
    
    // 4. If equipment becomes non-compliant, restrict usage
    if (companyCompliance.overallStatus === 'non-compliant') {
      const criticalIssues = companyCompliance.blockingIssues.filter(i => i.blocksOperation);
      if (criticalIssues.length > 0) {
        await updateSiteEquipmentAssignment(assignment.id, {
          currentStatus: { 
            id: 'compliance-hold', 
            name: 'Compliance Hold', 
            isAvailableForAssignment: false, 
            color: 'red' 
          }
        });
        
        // Notify site managers
        await notifySiteManager(assignment.siteId, {
          type: 'equipment-compliance-issue',
          equipmentId: companyEquipmentId,
          issues: criticalIssues
        });
      }
    }
  }
};

const updateEquipmentFromSite = async (siteAssignmentId: string, updateData: SiteEquipmentUpdate) => {
  const assignment = await getSiteEquipmentAssignment(siteAssignmentId);
  
  // 1. Update site assignment
  await updateSiteEquipmentAssignment(siteAssignmentId, updateData);
  
  // 2. Sync critical updates back to company equipment
  const companyUpdates: Partial<CompanyEquipmentMaster> = {};
  
  if (updateData.hoursUsedAtSite) {
    // Update total operating hours
    const currentEquipment = await getCompanyEquipment(assignment.companyEquipmentId);
    companyUpdates.totalOperatingHours = (currentEquipment.totalOperatingHours || 0) + updateData.hoursUsedAtSite;
  }
  
  if (updateData.currentCondition && updateData.currentCondition !== assignment.currentCondition) {
    // Update overall condition if it's worse
    const conditionHierarchy = { 'excellent': 5, 'good': 4, 'fair': 3, 'poor': 2, 'damaged': 1 };
    const currentEquipment = await getCompanyEquipment(assignment.companyEquipmentId);
    
    if (conditionHierarchy[updateData.currentCondition] < conditionHierarchy[currentEquipment.currentCondition]) {
      companyUpdates.currentCondition = updateData.currentCondition;
    }
  }
  
  if (updateData.lastMaintenanceDate) {
    companyUpdates.lastMaintenanceDate = updateData.lastMaintenanceDate;
    companyUpdates.nextMaintenanceDue = calculateNextMaintenanceDate(
      updateData.lastMaintenanceDate,
      assignment.companyEquipment.maintenanceSchedule
    );
  }
  
  if (Object.keys(companyUpdates).length > 0) {
    await updateCompanyEquipment(assignment.companyEquipmentId, {
      ...companyUpdates,
      updatedAt: new Date(),
      updatedBy: updateData.updatedBy
    });
  }
  
  // 3. Create lifecycle event for significant changes
  if (updateData.currentCondition || updateData.lastMaintenanceDate) {
    await createLifecycleEvent({
      companyEquipmentId: assignment.companyEquipmentId,
      eventType: updateData.lastMaintenanceDate ? 'maintenance' : 'inspection',
      eventDate: new Date(),
      description: `Equipment updated at site: ${assignment.siteId}`,
      location: assignment.siteId,
      performedBy: updateData.updatedBy,
      conditionBefore: assignment.currentCondition,
      conditionAfter: updateData.currentCondition
    });
  }
};
```

## API Endpoints

### Company Equipment Management
```http
# Company Equipment Registry
GET /api/company/equipment
POST /api/company/equipment
GET /api/company/equipment/{equipmentId}
PUT /api/company/equipment/{equipmentId}
DELETE /api/company/equipment/{equipmentId}

# Equipment Documents
POST /api/company/equipment/{equipmentId}/documents
GET /api/company/equipment/{equipmentId}/documents
DELETE /api/company/equipment/{equipmentId}/documents/{documentId}

# Compliance Management
GET /api/company/equipment/{equipmentId}/compliance
PUT /api/company/equipment/{equipmentId}/compliance
GET /api/company/equipment/compliance/dashboard
```

### Site Assignment Management
```http
# Site Assignments
GET /api/sites/{siteId}/equipment
POST /api/sites/{siteId}/equipment/assign
PUT /api/sites/{siteId}/equipment/{assignmentId}
DELETE /api/sites/{siteId}/equipment/{assignmentId}

# Equipment Transfers
POST /api/equipment/transfer/request
GET /api/equipment/transfer/requests
PUT /api/equipment/transfer/requests/{requestId}/approve
POST /api/equipment/transfer/requests/{requestId}/execute
```

### Multi-Site Operations
```http
# Cross-Site Visibility
GET /api/company/equipment/locations
GET /api/company/equipment/{equipmentId}/history
GET /api/company/equipment/utilization
GET /api/company/equipment/performance-metrics
```

## Key Benefits

### 1. **Centralized Ownership**
- Single source of truth for all company equipment
- Unified equipment numbering and identification
- Comprehensive equipment lifecycle tracking

### 2. **Enhanced Compliance**
- Real-time compliance status across all sites
- Automated compliance synchronization
- Proactive compliance issue identification

### 3. **Multi-Site Flexibility**
- Seamless equipment transfers between sites
- Optimized equipment utilization across projects
- Reduced equipment redundancy and costs

### 4. **Comprehensive Tracking**
- Complete equipment history and audit trail
- Performance metrics and utilization analytics
- Predictive maintenance capabilities

### 5. **Improved Safety**
- Consistent safety standard enforcement
- Real-time equipment condition monitoring
- Automated safety compliance alerts

This system provides a robust foundation for managing equipment across multiple construction sites while maintaining strict compliance and safety standards, optimizing utilization, and providing comprehensive visibility into equipment performance and lifecycle costs.