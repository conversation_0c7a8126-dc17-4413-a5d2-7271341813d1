# Hikvision Attendance System Integration

## Overview

The attendance system integrates with Hikvision face recognition devices deployed at each construction site. Workers are enrolled with their face templates and access levels, allowing automatic attendance tracking through facial recognition at site entry/exit points.

## System Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Main App      │    │   Site-Level     │    │   Hikvision     │
│   (Company DB)  │◄──►│   Attendance     │◄──►│   Device        │
│                 │    │   Module         │    │   (Face Recog)  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
        │                        │                        │
        │                        │                        │
    ┌───▼────┐              ┌────▼────┐              ┌────▼────┐
    │Worker  │              │Daily    │              │Face     │
    │Master  │              │Attendance│              │Templates│
    │Database│              │Records  │              │& Events │
    └────────┘              └─────────┘              └─────────┘
```

## Hikvision Device Integration Workflow

### 1. Worker Enrollment to Device

```typescript
// When worker is assigned to site
const enrollWorkerToHikvisionDevice = async (siteId: string, workerId: string) => {
  // 1. Get site's Hikvision devices
  const devices = await getHikvisionDevicesForSite(siteId);
  
  // 2. Get worker information
  const worker = await getWorkerWithPhoto(workerId);
  
  // 3. Check compliance before enrollment
  const compliance = await checkWorkerCompliance(workerId);
  if (!compliance.eligible) {
    throw new Error('Worker not compliant - cannot enroll to device');
  }
  
  // 4. Enroll to each device at the site
  for (const device of devices) {
    try {
      // Create user on Hikvision device
      const deviceUserId = await hikvisionAPI.createUser({
        deviceIP: device.device_ip,
        credentials: device.access_credentials,
        userData: {
          name: `${worker.first_name} ${worker.last_name}`,
          employeeNo: worker.employee_number,
          userType: 'normal',
          accessLevel: determineAccessLevel(worker.trades)
        }
      });
      
      // Upload face template
      await hikvisionAPI.uploadFaceTemplate({
        deviceIP: device.device_ip,
        userId: deviceUserId,
        faceImage: worker.photo_url
      });
      
      // Record sync status
      await createDeviceUserSync({
        hikvision_device_id: device.id,
        worker_id: workerId,
        device_user_id: deviceUserId,
        face_template_uploaded: true,
        sync_status: 'synced',
        access_level: determineAccessLevel(worker.trades)
      });
      
    } catch (error) {
      // Log failed sync
      await createDeviceUserSync({
        hikvision_device_id: device.id,
        worker_id: workerId,
        sync_status: 'failed',
        sync_error_message: error.message
      });
    }
  }
};

// Determine access level based on worker role
const determineAccessLevel = (trades: WorkerTrade[]): number => {
  // Access levels: 1=Basic Worker, 2=Supervisor, 3=Manager, 4=Admin
  const hasManagementTrade = trades.some(t => 
    ['Management', 'Supervisors', 'HSE Personnel'].includes(t.trade_name)
  );
  
  if (hasManagementTrade) return 3; // Manager level
  return 1; // Basic worker level
};
```

### 2. Real-time Event Processing from Hikvision Device

```typescript
// Webhook endpoint to receive events from Hikvision device
app.post('/api/hikvision/events', async (req, res) => {
  const eventData = req.body;
  
  try {
    // 1. Log raw event
    await logDeviceEvent({
      hikvision_device_id: eventData.deviceId,
      event_type: eventData.eventType,
      event_timestamp: new Date(eventData.timestamp),
      device_event_id: eventData.eventId,
      face_recognition_confidence: eventData.confidence,
      raw_event_data: eventData,
      processed: false
    });
    
    // 2. Process the event
    await processHikvisionEvent(eventData);
    
    res.status(200).json({ status: 'received' });
  } catch (error) {
    console.error('Error processing Hikvision event:', error);
    res.status(500).json({ error: 'Processing failed' });
  }
});

// Process different types of events
const processHikvisionEvent = async (eventData: HikvisionEvent) => {
  const device = await getHikvisionDeviceByDeviceId(eventData.deviceId);
  if (!device) {
    throw new Error('Unknown device');
  }
  
  switch (eventData.eventType) {
    case 'ACCESS_GRANTED':
      await processAccessGranted(device, eventData);
      break;
    case 'ACCESS_DENIED':
      await processAccessDenied(device, eventData);
      break;
    case 'FACE_RECOGNIZED':
      await processFaceRecognized(device, eventData);
      break;
    case 'UNKNOWN_FACE':
      await processUnknownFace(device, eventData);
      break;
  }
};

// Process successful face recognition and site access
const processAccessGranted = async (device: HikvisionDevice, eventData: HikvisionEvent) => {
  // 1. Find worker by device user ID
  const userSync = await getDeviceUserSync(device.id, eventData.userId);
  if (!userSync) {
    console.warn('Access granted for unknown user:', eventData.userId);
    return;
  }
  
  // 2. Check if this is check-in or check-out
  const today = new Date().toISOString().split('T')[0];
  const existingAttendance = await getDailyAttendance(device.site_id, userSync.worker_id, today);
  
  if (!existingAttendance || !existingAttendance.check_in_time) {
    // This is a check-in
    await recordCheckIn({
      site_id: device.site_id,
      worker_id: userSync.worker_id,
      hikvision_device_id: device.device_id,
      check_in_time: new Date(eventData.timestamp).toTimeString().split(' ')[0],
      attendance_date: today,
      face_recognition_confidence: eventData.confidence,
      device_event_id: eventData.eventId,
      status: 'present'
    });
    
    // Send real-time notification to site admin
    await notifySiteAdmin(device.site_id, {
      type: 'worker_checked_in',
      worker_name: userSync.worker.name,
      time: eventData.timestamp,
      device_location: device.location_description
    });
    
  } else if (!existingAttendance.check_out_time) {
    // This is a check-out
    const checkOutTime = new Date(eventData.timestamp).toTimeString().split(' ')[0];
    const totalHours = calculateHours(existingAttendance.check_in_time, checkOutTime);
    
    await updateAttendanceRecord(existingAttendance.id, {
      check_out_time: checkOutTime,
      total_hours: totalHours,
      status: 'completed'
    });
    
    // Send check-out notification
    await notifySiteAdmin(device.site_id, {
      type: 'worker_checked_out',
      worker_name: userSync.worker.name,
      time: eventData.timestamp,
      total_hours: totalHours,
      device_location: device.location_description
    });
  }
  
  // 3. Mark event as processed
  await markEventProcessed(eventData.eventId);
};
```

### 3. Site-Level Attendance Module UI

```typescript
// Site Attendance Dashboard Component
interface SiteAttendanceDashboard {
  siteId: string;
  selectedDate: Date;
}

const SiteAttendanceDashboard: React.FC<SiteAttendanceDashboard> = ({ siteId, selectedDate }) => {
  const [attendanceData, setAttendanceData] = useState<DailyAttendanceData | null>(null);
  const [realTimeEvents, setRealTimeEvents] = useState<AttendanceEvent[]>([]);
  
  // Real-time updates via WebSocket
  useEffect(() => {
    const ws = new WebSocket(`ws://api/sites/${siteId}/attendance/live`);
    
    ws.onmessage = (event) => {
      const attendanceEvent = JSON.parse(event.data);
      setRealTimeEvents(prev => [attendanceEvent, ...prev.slice(0, 9)]); // Keep last 10 events
      
      // Update attendance data if it's for today
      if (isToday(selectedDate)) {
        refreshAttendanceData();
      }
    };
    
    return () => ws.close();
  }, [siteId]);
  
  return (
    <div className="site-attendance-dashboard">
      {/* Site Summary Cards */}
      <div className="attendance-summary">
        <SummaryCard 
          title="Present Today" 
          value={attendanceData?.summary.present || 0}
          color="green"
        />
        <SummaryCard 
          title="Absent" 
          value={attendanceData?.summary.absent || 0}
          color="red"
        />
        <SummaryCard 
          title="Late Arrivals" 
          value={attendanceData?.summary.late_arrivals || 0}
          color="orange"
        />
        <SummaryCard 
          title="Total Hours" 
          value={attendanceData?.summary.total_hours || 0}
          color="blue"
        />
      </div>
      
      {/* Real-time Events Feed */}
      <div className="real-time-events">
        <h3>Live Activity</h3>
        {realTimeEvents.map(event => (
          <div key={event.id} className="event-item">
            <span className="time">{formatTime(event.timestamp)}</span>
            <span className="worker">{event.worker_name}</span>
            <span className={`action ${event.type}`}>
              {event.type === 'check_in' ? 'Checked In' : 'Checked Out'}
            </span>
            <span className="location">{event.device_location}</span>
          </div>
        ))}
      </div>
      
      {/* Detailed Attendance Table */}
      <AttendanceTable 
        attendanceRecords={attendanceData?.records || []}
        onUpdateRecord={handleUpdateRecord}
      />
      
      {/* Device Status Panel */}
      <DeviceStatusPanel siteId={siteId} />
    </div>
  );
};

// Attendance Table Component
const AttendanceTable: React.FC<{
  attendanceRecords: AttendanceRecord[];
  onUpdateRecord: (recordId: string, updates: Partial<AttendanceRecord>) => void;
}> = ({ attendanceRecords, onUpdateRecord }) => {
  return (
    <div className="attendance-table">
      <table>
        <thead>
          <tr>
            <th>Employee #</th>
            <th>Name</th>
            <th>Trade</th>
            <th>Check In</th>
            <th>Check Out</th>
            <th>Total Hours</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          {attendanceRecords.map(record => (
            <tr key={record.id}>
              <td>{record.worker.employee_number}</td>
              <td>{record.worker.name}</td>
              <td>{record.worker.primary_trade}</td>
              <td>
                {record.check_in_time || (
                  <ManualTimeEntry 
                    onSave={(time) => onUpdateRecord(record.id, { check_in_time: time })}
                  />
                )}
              </td>
              <td>
                {record.check_out_time || (
                  record.check_in_time && (
                    <ManualTimeEntry 
                      onSave={(time) => onUpdateRecord(record.id, { check_out_time: time })}
                    />
                  )
                )}
              </td>
              <td>{record.total_hours?.toFixed(2) || '-'}</td>
              <td>
                <StatusBadge status={record.status} />
              </td>
              <td>
                <AttendanceActions record={record} onUpdate={onUpdateRecord} />
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
```

### 4. Device Management & Sync Operations

```typescript
// Sync all workers to Hikvision devices when site assignment changes
const syncSiteWorkersToDevices = async (siteId: string) => {
  // 1. Get all active workers assigned to site
  const siteWorkers = await getActiveSiteWorkers(siteId);
  
  // 2. Get all Hikvision devices for the site
  const devices = await getHikvisionDevicesForSite(siteId);
  
  // 3. Sync each worker to each device
  const syncPromises = [];
  
  for (const worker of siteWorkers) {
    for (const device of devices) {
      syncPromises.push(syncWorkerToDevice(worker.id, device.id));
    }
  }
  
  const results = await Promise.allSettled(syncPromises);
  
  // 4. Report sync results
  const successful = results.filter(r => r.status === 'fulfilled').length;
  const failed = results.filter(r => r.status === 'rejected').length;
  
  return {
    total_operations: results.length,
    successful,
    failed,
    details: results
  };
};

// Remove worker from all site devices when assignment ends
const removeWorkerFromSiteDevices = async (siteId: string, workerId: string) => {
  const devices = await getHikvisionDevicesForSite(siteId);
  
  for (const device of devices) {
    try {
      // Get device user sync record
      const userSync = await getDeviceUserSync(device.id, workerId);
      if (userSync && userSync.device_user_id) {
        // Remove from Hikvision device
        await hikvisionAPI.deleteUser({
          deviceIP: device.device_ip,
          userId: userSync.device_user_id
        });
        
        // Update sync status
        await updateDeviceUserSync(userSync.id, {
          sync_status: 'removed',
          last_sync_attempt: new Date()
        });
      }
    } catch (error) {
      console.error(`Failed to remove worker from device ${device.device_name}:`, error);
    }
  }
};

// Health check for Hikvision devices
const checkDeviceHealth = async (deviceId: string) => {
  const device = await getHikvisionDevice(deviceId);
  
  try {
    // Ping device
    const response = await hikvisionAPI.getDeviceInfo({
      deviceIP: device.device_ip,
      credentials: device.access_credentials
    });
    
    // Update device status
    await updateHikvisionDevice(deviceId, {
      status: 'active',
      last_sync: new Date()
    });
    
    return {
      status: 'healthy',
      device_info: response,
      last_check: new Date()
    };
    
  } catch (error) {
    // Mark device as inactive
    await updateHikvisionDevice(deviceId, {
      status: 'inactive',
      last_sync: new Date()
    });
    
    return {
      status: 'unhealthy',
      error: error.message,
      last_check: new Date()
    };
  }
};
```

## API Endpoints for Site-Level Attendance

### Real-time Attendance Data
```http
GET /api/sites/{site_id}/attendance/live
WebSocket connection for real-time attendance updates

GET /api/sites/{site_id}/attendance/daily?date=2024-02-20
Response: Daily attendance summary and detailed records

POST /api/sites/{site_id}/attendance/manual-entry
Manual attendance entry for device failures or exceptions
```

### Device Management
```http
GET /api/sites/{site_id}/devices
List all Hikvision devices for a site

POST /api/sites/{site_id}/devices/{device_id}/sync-workers
Sync all site workers to specific device

GET /api/sites/{site_id}/devices/health-check
Check health status of all site devices
```

## Key Features

1. **Automatic Enrollment**: Workers are automatically enrolled to site devices when assigned
2. **Real-time Processing**: Face recognition events are processed immediately
3. **Fallback Manual Entry**: Site admins can manually enter attendance if devices fail
4. **Device Health Monitoring**: Continuous monitoring of device connectivity and status
5. **Compliance Integration**: Only compliant workers are enrolled to devices
6. **Multi-device Support**: Sites can have multiple entry/exit points with separate devices
7. **Audit Trail**: Complete log of all device events and attendance records

This integration provides a seamless bridge between Hikvision face recognition hardware and your worker management system, with a dedicated site-level attendance module for daily operations management.