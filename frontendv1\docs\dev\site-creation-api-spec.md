# Site Creation API Specification

## Overview

This document defines the API endpoints and data structures for the site creation process, including validation, templates, and post-creation setup workflows.

## Core API Endpoints

### 1. Site Creation Wizard Endpoints

#### Initialize Site Creation Session
```http
POST /api/sites/creation/initialize
Content-Type: application/json

{
  "tenantId": "string",
  "templateId": "string", // optional
  "createdBy": "string"
}

Response:
{
  "sessionId": "string",
  "expiresAt": "2024-02-20T18:00:00Z",
  "currentStep": "basic-info",
  "template": SiteTemplate | null,
  "availableTemplates": SiteTemplate[]
}
```

#### Save Step Data
```http
PUT /api/sites/creation/{sessionId}/step/{stepId}
Content-Type: application/json

{
  "data": StepData,
  "isComplete": boolean,
  "validationOverrides": ValidationOverride[]
}

Response:
{
  "status": "saved" | "validation_failed",
  "validationErrors": ValidationError[],
  "nextStep": "string" | null,
  "canProceed": boolean,
  "warnings": Warning[]
}
```

#### Get Step Data
```http
GET /api/sites/creation/{sessionId}/step/{stepId}

Response:
{
  "stepId": "string",
  "data": StepData,
  "isComplete": boolean,
  "validationStatus": "valid" | "invalid" | "pending",
  "lastModified": "2024-02-20T15:30:00Z"
}
```

#### Validate Step
```http
POST /api/sites/creation/{sessionId}/step/{stepId}/validate
Content-Type: application/json

{
  "data": StepData,
  "skipWarnings": boolean
}

Response:
{
  "isValid": boolean,
  "errors": ValidationError[],
  "warnings": Warning[],
  "suggestions": Suggestion[]
}
```

### 2. Site Creation Finalization

#### Create Site
```http
POST /api/sites/creation/{sessionId}/finalize
Content-Type: application/json

{
  "confirmOverrides": string[], // List of validation override IDs
  "additionalNotes": "string"
}

Response:
{
  "siteId": "string",
  "status": "created" | "pending_approval" | "failed",
  "creationTasks": CreationTask[],
  "setupTasks": SetupTask[],
  "estimatedSetupTime": "string",
  "nextSteps": NextStep[]
}
```

#### Get Creation Status
```http
GET /api/sites/{siteId}/creation-status

Response:
{
  "siteId": "string",
  "creationStatus": "initializing" | "setting_up" | "ready" | "failed",
  "completedTasks": CreationTask[],
  "pendingTasks": CreationTask[],
  "failedTasks": CreationTask[],
  "overallProgress": number, // 0-100
  "estimatedCompletion": "2024-02-20T18:00:00Z"
}
```

### 3. Template Management

#### Get Available Templates
```http
GET /api/sites/templates?category={category}&tenantId={tenantId}

Response:
{
  "templates": [
    {
      "id": "string",
      "name": "string",
      "category": "residential" | "commercial" | "industrial" | "infrastructure",
      "description": "string",
      "isDefault": boolean,
      "customizations": TemplateCustomization[],
      "usage": {
        "timesUsed": number,
        "lastUsed": "2024-02-20T15:30:00Z",
        "averageSetupTime": "string"
      }
    }
  ]
}
```

#### Create Custom Template
```http
POST /api/sites/templates
Content-Type: application/json

{
  "name": "string",
  "category": "string",
  "description": "string",
  "baseTemplateId": "string", // optional
  "configuration": TemplateConfiguration,
  "isPublic": boolean
}

Response:
{
  "templateId": "string",
  "status": "created",
  "validationResults": ValidationResult[]
}
```

### 4. Validation Services

#### Validate Site Code Uniqueness
```http
GET /api/sites/validate/code/{siteCode}?tenantId={tenantId}

Response:
{
  "isUnique": boolean,
  "suggestions": string[], // Alternative codes if not unique
  "conflicts": ConflictInfo[]
}
```

#### Validate Permit Numbers
```http
POST /api/sites/validate/permits
Content-Type: application/json

{
  "permits": [
    {
      "type": "string",
      "number": "string",
      "issuingAuthority": "string"
    }
  ]
}

Response:
{
  "validationResults": [
    {
      "permitType": "string",
      "permitNumber": "string",
      "isValid": boolean,
      "status": "active" | "expired" | "pending" | "invalid",
      "expiryDate": "2024-12-31T00:00:00Z",
      "warnings": string[]
    }
  ]
}
```

#### Validate Stakeholder Information
```http
POST /api/sites/validate/stakeholders
Content-Type: application/json

{
  "stakeholders": ProjectStakeholders
}

Response:
{
  "validationResults": {
    "client": StakeholderValidationResult,
    "architect": StakeholderValidationResult,
    "contractor": StakeholderValidationResult,
    "projectManager": StakeholderValidationResult
  },
  "overallStatus": "valid" | "invalid" | "warnings"
}
```

## Data Structures

### Site Creation Session
```typescript
interface SiteCreationSession {
  sessionId: string;
  tenantId: string;
  createdBy: string;
  createdAt: string;
  expiresAt: string;
  currentStep: string;
  completedSteps: string[];
  stepData: Record<string, any>;
  templateId?: string;
  status: 'active' | 'completed' | 'expired' | 'cancelled';
  validationOverrides: ValidationOverride[];
}
```

### Validation Error
```typescript
interface ValidationError {
  field: string;
  code: string;
  message: string;
  severity: 'error' | 'warning' | 'info';
  canOverride: boolean;
  overrideReason?: string;
  suggestions?: string[];
}
```

### Creation Task
```typescript
interface CreationTask {
  id: string;
  name: string;
  description: string;
  category: 'database' | 'integration' | 'configuration' | 'notification';
  status: 'pending' | 'in_progress' | 'completed' | 'failed' | 'skipped';
  startedAt?: string;
  completedAt?: string;
  duration?: number; // in seconds
  error?: string;
  dependencies: string[]; // Task IDs that must complete first
  retryCount: number;
  maxRetries: number;
}
```

### Setup Task
```typescript
interface SetupTask {
  id: string;
  name: string;
  description: string;
  category: 'immediate' | 'within_24h' | 'within_1week' | 'ongoing';
  priority: 'low' | 'medium' | 'high' | 'critical';
  assignedTo?: string;
  dueDate?: string;
  estimatedDuration: string;
  instructions: string[];
  resources: string[];
  status: 'pending' | 'in_progress' | 'completed' | 'overdue';
}
```

### Site Template
```typescript
interface SiteTemplate {
  id: string;
  name: string;
  category: 'residential' | 'commercial' | 'industrial' | 'infrastructure' | 'mixed-use';
  description: string;
  version: string;
  isDefault: boolean;
  isPublic: boolean;
  createdBy: string;
  createdAt: string;
  
  // Template Configuration
  defaultValues: {
    basicInfo: Partial<SiteCreationBasicInfo>;
    specifications: Partial<SitePhysicalSpecs>;
    safety: Partial<SafetyConfiguration>;
    phases: Partial<ProjectPhases>;
  };
  
  // Required Fields Override
  requiredFields: string[];
  optionalFields: string[];
  hiddenFields: string[];
  
  // Validation Rules Override
  customValidations: CustomValidationRule[];
  
  // Default Resources
  defaultEquipment: EquipmentTemplate[];
  defaultTrainings: TrainingTemplate[];
  defaultRoles: RoleTemplate[];
  
  // Usage Statistics
  usage: {
    timesUsed: number;
    lastUsed?: string;
    averageSetupTime: number; // in minutes
    successRate: number; // 0-1
  };
}
```

## Step-by-Step API Flow

### 1. Site Creation Initialization
```mermaid
sequenceDiagram
    participant Client
    participant API
    participant DB
    participant TemplateService

    Client->>API: POST /api/sites/creation/initialize
    API->>DB: Create creation session
    API->>TemplateService: Get available templates
    TemplateService-->>API: Return templates
    API-->>Client: Session ID + templates
```

### 2. Step Data Collection
```mermaid
sequenceDiagram
    participant Client
    participant API
    participant ValidationService
    participant DB

    loop For each step
        Client->>API: PUT /api/sites/creation/{sessionId}/step/{stepId}
        API->>ValidationService: Validate step data
        ValidationService-->>API: Validation results
        API->>DB: Save step data
        API-->>Client: Validation results + next step
    end
```

### 3. Site Creation Finalization
```mermaid
sequenceDiagram
    participant Client
    participant API
    participant SiteService
    participant TaskQueue
    participant NotificationService

    Client->>API: POST /api/sites/creation/{sessionId}/finalize
    API->>SiteService: Create site record
    SiteService->>DB: Insert site data
    SiteService->>TaskQueue: Queue setup tasks
    SiteService->>NotificationService: Send notifications
    API-->>Client: Site created + setup tasks
```

## Error Handling

### Common Error Responses

#### Validation Failed
```json
{
  "error": "VALIDATION_FAILED",
  "message": "Site creation data validation failed",
  "details": {
    "step": "basic-info",
    "errors": [
      {
        "field": "name",
        "code": "REQUIRED",
        "message": "Site name is required"
      },
      {
        "field": "code",
        "code": "DUPLICATE",
        "message": "Site code already exists",
        "suggestions": ["SITE-001-A", "SITE-001-B"]
      }
    ]
  }
}
```

#### Session Expired
```json
{
  "error": "SESSION_EXPIRED",
  "message": "Site creation session has expired",
  "details": {
    "sessionId": "sess-123",
    "expiredAt": "2024-02-20T18:00:00Z",
    "canRestore": true,
    "restoreUntil": "2024-02-21T18:00:00Z"
  }
}
```

#### Creation Failed
```json
{
  "error": "SITE_CREATION_FAILED",
  "message": "Failed to create site due to system error",
  "details": {
    "failedTasks": [
      {
        "taskId": "create-database-schema",
        "error": "Database connection timeout",
        "canRetry": true
      }
    ],
    "rollbackStatus": "completed",
    "supportTicket": "TICKET-789"
  }
}
```

## Integration Endpoints

### Hikvision Device Setup
```http
POST /api/sites/{siteId}/hikvision/setup
Content-Type: application/json

{
  "devices": HikvisionDeviceConfig[],
  "defaultSettings": HikvisionSettings,
  "testConnection": boolean
}

Response:
{
  "setupResults": [
    {
      "deviceId": "string",
      "status": "success" | "failed" | "warning",
      "message": "string",
      "connectionTest": boolean
    }
  ],
  "overallStatus": "success" | "partial" | "failed"
}
```

### Worker Enrollment Preparation
```http
POST /api/sites/{siteId}/workers/prepare-enrollment
Content-Type: application/json

{
  "expectedWorkerCount": number,
  "trainingRequirements": string[],
  "complianceChecks": string[]
}

Response:
{
  "enrollmentSetup": {
    "trainingSchedules": TrainingSchedule[],
    "complianceChecklists": ComplianceChecklist[],
    "deviceCapacity": DeviceCapacity[]
  },
  "readyForWorkers": boolean,
  "estimatedReadyDate": "2024-02-25T00:00:00Z"
}
```

### Document Structure Creation
```http
POST /api/sites/{siteId}/documents/initialize
Content-Type: application/json

{
  "documentCategories": string[],
  "retentionPolicies": RetentionPolicy[],
  "accessControls": AccessControl[]
}

Response:
{
  "documentStructure": {
    "rootFolderId": "string",
    "categoryFolders": CategoryFolder[],
    "templateDocuments": TemplateDocument[]
  },
  "setupComplete": boolean
}
```

## Monitoring and Analytics

### Site Creation Metrics
```http
GET /api/sites/creation/metrics?period={period}&tenantId={tenantId}

Response:
{
  "period": "string",
  "metrics": {
    "totalSitesCreated": number,
    "averageCreationTime": number, // in minutes
    "successRate": number, // 0-1
    "commonFailureReasons": FailureReason[],
    "templateUsage": TemplateUsageStats[],
    "stepCompletionRates": StepCompletionStats[]
  }
}
```

### Creation Performance
```http
GET /api/sites/creation/performance?siteId={siteId}

Response:
{
  "siteId": "string",
  "creationTimeline": {
    "initiated": "2024-02-20T10:00:00Z",
    "dataCollectionCompleted": "2024-02-20T11:30:00Z",
    "validationCompleted": "2024-02-20T11:45:00Z",
    "siteCreated": "2024-02-20T12:00:00Z",
    "setupCompleted": "2024-02-20T14:00:00Z"
  },
  "taskPerformance": TaskPerformance[],
  "bottlenecks": Bottleneck[],
  "recommendations": string[]
}
```

This API specification provides a comprehensive framework for implementing the site creation process with proper validation, error handling, and integration capabilities.