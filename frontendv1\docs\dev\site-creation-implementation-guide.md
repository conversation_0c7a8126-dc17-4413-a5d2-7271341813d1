# Site Creation Implementation Guide

## Overview

This guide provides practical implementation details for the site creation process, including UI components, validation logic, database operations, and integration workflows.

## Frontend Implementation

### 1. Site Creation Wizard Component

```typescript
// SiteCreationWizard.tsx
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useSiteCreation } from '../hooks/useSiteCreation';

interface SiteCreationWizardProps {
  tenantId: string;
  templateId?: string;
}

const SiteCreationWizard: React.FC<SiteCreationWizardProps> = ({ 
  tenantId, 
  templateId 
}) => {
  const navigate = useNavigate();
  const {
    session,
    currentStep,
    stepData,
    validationErrors,
    isLoading,
    initializeSession,
    saveStepData,
    validateStep,
    finalizeSiteCreation
  } = useSiteCreation();

  const [formData, setFormData] = useState<any>({});
  const [showValidationErrors, setShowValidationErrors] = useState(false);

  useEffect(() => {
    initializeSession(tenantId, templateId);
  }, [tenantId, templateId]);

  const handleStepSubmit = async (stepId: string, data: any) => {
    try {
      setShowValidationErrors(false);
      
      // Validate step data
      const validationResult = await validateStep(stepId, data);
      
      if (!validationResult.isValid) {
        setShowValidationErrors(true);
        return;
      }
      
      // Save step data
      await saveStepData(stepId, data);
      
      // Move to next step
      if (validationResult.nextStep) {
        navigate(`/sites/create/step/${validationResult.nextStep}`);
      }
    } catch (error) {
      console.error('Step submission failed:', error);
    }
  };

  const handleFinalize = async () => {
    try {
      const result = await finalizeSiteCreation();
      
      if (result.status === 'created') {
        navigate(`/sites/${result.siteId}/setup-progress`);
      } else {
        // Handle creation failure
        console.error('Site creation failed:', result);
      }
    } catch (error) {
      console.error('Site finalization failed:', error);
    }
  };

  if (isLoading) {
    return <SiteCreationLoader />;
  }

  return (
    <div className="site-creation-wizard">
      <WizardHeader 
        currentStep={currentStep}
        totalSteps={10}
        sessionId={session?.sessionId}
      />
      
      <WizardProgress 
        completedSteps={session?.completedSteps || []}
        currentStep={currentStep}
      />
      
      <StepContent
        stepId={currentStep}
        data={stepData[currentStep]}
        validationErrors={showValidationErrors ? validationErrors : []}
        onSubmit={(data) => handleStepSubmit(currentStep, data)}
        onBack={() => navigate(-1)}
      />
      
      {currentStep === 'review' && (
        <FinalReview
          sessionData={stepData}
          onConfirm={handleFinalize}
          onEdit={(stepId) => navigate(`/sites/create/step/${stepId}`)}
        />
      )}
    </div>
  );
};
```

### 2. Step Components

```typescript
// BasicInfoStep.tsx
import React from 'react';
import { useForm } from 'react-hook-form';
import { SiteCreationBasicInfo } from '../types/siteCreation';

interface BasicInfoStepProps {
  initialData?: Partial<SiteCreationBasicInfo>;
  validationErrors: ValidationError[];
  onSubmit: (data: SiteCreationBasicInfo) => void;
  onBack: () => void;
}

const BasicInfoStep: React.FC<BasicInfoStepProps> = ({
  initialData,
  validationErrors,
  onSubmit,
  onBack
}) => {
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<SiteCreationBasicInfo>({
    defaultValues: initialData
  });

  const projectType = watch('projectType');

  // Auto-generate site code based on name and type
  const generateSiteCode = (name: string, type: string) => {
    const namePrefix = name.substring(0, 3).toUpperCase();
    const typePrefix = type.substring(0, 3).toUpperCase();
    const timestamp = Date.now().toString().slice(-4);
    return `${namePrefix}-${typePrefix}-${timestamp}`;
  };

  const handleNameChange = (name: string) => {
    if (projectType && !initialData?.code) {
      const generatedCode = generateSiteCode(name, projectType);
      setValue('code', generatedCode);
    }
  };

  return (
    <div className="step-content">
      <div className="step-header">
        <h2>Basic Site Information</h2>
        <p>Provide the fundamental details about your construction site</p>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Site Identification */}
        <div className="form-section">
          <h3>Site Identification</h3>
          
          <div className="form-group">
            <label htmlFor="name">Site Name *</label>
            <input
              {...register('name', { 
                required: 'Site name is required',
                minLength: { value: 3, message: 'Name must be at least 3 characters' }
              })}
              onChange={(e) => handleNameChange(e.target.value)}
              className={errors.name ? 'error' : ''}
            />
            {errors.name && <span className="error-message">{errors.name.message}</span>}
          </div>

          <div className="form-group">
            <label htmlFor="code">Site Code *</label>
            <input
              {...register('code', { 
                required: 'Site code is required',
                pattern: { 
                  value: /^[A-Z0-9-]+$/, 
                  message: 'Code must contain only uppercase letters, numbers, and hyphens' 
                }
              })}
              className={errors.code ? 'error' : ''}
            />
            {errors.code && <span className="error-message">{errors.code.message}</span>}
            <small>Unique identifier for this site (e.g., WES-COM-2024)</small>
          </div>

          <div className="form-group">
            <label htmlFor="projectType">Project Type *</label>
            <select
              {...register('projectType', { required: 'Project type is required' })}
              className={errors.projectType ? 'error' : ''}
            >
              <option value="">Select project type</option>
              <option value="residential">Residential</option>
              <option value="commercial">Commercial</option>
              <option value="industrial">Industrial</option>
              <option value="infrastructure">Infrastructure</option>
              <option value="mixed-use">Mixed Use</option>
            </select>
            {errors.projectType && <span className="error-message">{errors.projectType.message}</span>}
          </div>
        </div>

        {/* Location Information */}
        <div className="form-section">
          <h3>Location Information</h3>
          
          <div className="form-group">
            <label htmlFor="location.address">Address *</label>
            <textarea
              {...register('location.address', { required: 'Address is required' })}
              rows={3}
              className={errors.location?.address ? 'error' : ''}
            />
            {errors.location?.address && <span className="error-message">{errors.location.address.message}</span>}
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="location.city">City *</label>
              <input
                {...register('location.city', { required: 'City is required' })}
                className={errors.location?.city ? 'error' : ''}
              />
              {errors.location?.city && <span className="error-message">{errors.location.city.message}</span>}
            </div>

            <div className="form-group">
              <label htmlFor="location.region">Region/State *</label>
              <input
                {...register('location.region', { required: 'Region is required' })}
                className={errors.location?.region ? 'error' : ''}
              />
              {errors.location?.region && <span className="error-message">{errors.location.region.message}</span>}
            </div>
          </div>

          <CoordinatesPicker
            onCoordinatesChange={(coords) => {
              setValue('location.coordinates.latitude', coords.latitude);
              setValue('location.coordinates.longitude', coords.longitude);
            }}
            initialCoordinates={initialData?.location?.coordinates}
          />
        </div>

        {/* Timeline */}
        <div className="form-section">
          <h3>Project Timeline</h3>
          
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="plannedStartDate">Planned Start Date *</label>
              <input
                type="date"
                {...register('plannedStartDate', { required: 'Start date is required' })}
                min={new Date().toISOString().split('T')[0]}
                className={errors.plannedStartDate ? 'error' : ''}
              />
              {errors.plannedStartDate && <span className="error-message">{errors.plannedStartDate.message}</span>}
            </div>

            <div className="form-group">
              <label htmlFor="plannedEndDate">Planned End Date *</label>
              <input
                type="date"
                {...register('plannedEndDate', { required: 'End date is required' })}
                className={errors.plannedEndDate ? 'error' : ''}
              />
              {errors.plannedEndDate && <span className="error-message">{errors.plannedEndDate.message}</span>}
            </div>
          </div>
        </div>

        {/* Validation Errors Display */}
        {validationErrors.length > 0 && (
          <ValidationErrorsDisplay errors={validationErrors} />
        )}

        {/* Navigation */}
        <div className="step-navigation">
          <button type="button" onClick={onBack} className="btn-secondary">
            Back
          </button>
          <button type="submit" className="btn-primary">
            Next: Stakeholders
          </button>
        </div>
      </form>
    </div>
  );
};
```

### 3. Custom Hooks

```typescript
// useSiteCreation.ts
import { useState, useCallback } from 'react';
import { siteCreationAPI } from '../services/siteCreationAPI';

interface UseSiteCreationReturn {
  session: SiteCreationSession | null;
  currentStep: string;
  stepData: Record<string, any>;
  validationErrors: ValidationError[];
  isLoading: boolean;
  initializeSession: (tenantId: string, templateId?: string) => Promise<void>;
  saveStepData: (stepId: string, data: any) => Promise<void>;
  validateStep: (stepId: string, data: any) => Promise<ValidationResult>;
  finalizeSiteCreation: () => Promise<SiteCreationResult>;
}

export const useSiteCreation = (): UseSiteCreationReturn => {
  const [session, setSession] = useState<SiteCreationSession | null>(null);
  const [currentStep, setCurrentStep] = useState<string>('basic-info');
  const [stepData, setStepData] = useState<Record<string, any>>({});
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const initializeSession = useCallback(async (tenantId: string, templateId?: string) => {
    setIsLoading(true);
    try {
      const response = await siteCreationAPI.initializeSession({
        tenantId,
        templateId,
        createdBy: 'current-user-id' // Get from auth context
      });
      
      setSession(response);
      setCurrentStep(response.currentStep);
      
      // If template is used, populate initial data
      if (response.template) {
        setStepData(response.template.defaultValues);
      }
    } catch (error) {
      console.error('Failed to initialize site creation session:', error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const saveStepData = useCallback(async (stepId: string, data: any) => {
    if (!session) return;
    
    setIsLoading(true);
    try {
      const response = await siteCreationAPI.saveStepData(session.sessionId, stepId, {
        data,
        isComplete: true
      });
      
      setStepData(prev => ({ ...prev, [stepId]: data }));
      
      if (response.nextStep) {
        setCurrentStep(response.nextStep);
      }
      
      setValidationErrors(response.validationErrors || []);
    } catch (error) {
      console.error('Failed to save step data:', error);
    } finally {
      setIsLoading(false);
    }
  }, [session]);

  const validateStep = useCallback(async (stepId: string, data: any): Promise<ValidationResult> => {
    if (!session) throw new Error('No active session');
    
    const response = await siteCreationAPI.validateStep(session.sessionId, stepId, {
      data,
      skipWarnings: false
    });
    
    setValidationErrors(response.errors || []);
    return response;
  }, [session]);

  const finalizeSiteCreation = useCallback(async (): Promise<SiteCreationResult> => {
    if (!session) throw new Error('No active session');
    
    setIsLoading(true);
    try {
      const response = await siteCreationAPI.finalizeSiteCreation(session.sessionId, {
        confirmOverrides: [],
        additionalNotes: ''
      });
      
      return response;
    } catch (error) {
      console.error('Failed to finalize site creation:', error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [session]);

  return {
    session,
    currentStep,
    stepData,
    validationErrors,
    isLoading,
    initializeSession,
    saveStepData,
    validateStep,
    finalizeSiteCreation
  };
};
```

## Backend Implementation

### 1. Site Creation Service

```typescript
// SiteCreationService.ts
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { SiteCreationSession, Site } from '../entities';
import { ValidationService } from './ValidationService';
import { TaskQueueService } from './TaskQueueService';

@Injectable()
export class SiteCreationService {
  constructor(
    @InjectRepository(SiteCreationSession)
    private sessionRepository: Repository<SiteCreationSession>,
    @InjectRepository(Site)
    private siteRepository: Repository<Site>,
    private validationService: ValidationService,
    private taskQueueService: TaskQueueService
  ) {}

  async initializeSession(dto: InitializeSessionDto): Promise<SiteCreationSessionResponse> {
    // Create new session
    const session = this.sessionRepository.create({
      tenantId: dto.tenantId,
      createdBy: dto.createdBy,
      currentStep: 'basic-info',
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
      status: 'active'
    });

    await this.sessionRepository.save(session);

    // Get available templates
    const templates = await this.getAvailableTemplates(dto.tenantId);
    
    // Apply template if specified
    let templateData = null;
    if (dto.templateId) {
      templateData = await this.applyTemplate(session.id, dto.templateId);
    }

    return {
      sessionId: session.id,
      expiresAt: session.expiresAt,
      currentStep: session.currentStep,
      template: templateData,
      availableTemplates: templates
    };
  }

  async saveStepData(sessionId: string, stepId: string, dto: SaveStepDataDto): Promise<SaveStepResponse> {
    const session = await this.getActiveSession(sessionId);
    
    // Validate step data
    const validationResult = await this.validationService.validateStep(stepId, dto.data);
    
    if (!validationResult.isValid && !dto.validationOverrides?.length) {
      return {
        status: 'validation_failed',
        validationErrors: validationResult.errors,
        canProceed: false
      };
    }

    // Save step data
    session.stepData = {
      ...session.stepData,
      [stepId]: dto.data
    };

    // Mark step as completed
    if (dto.isComplete && !session.completedSteps.includes(stepId)) {
      session.completedSteps.push(stepId);
    }

    // Determine next step
    const nextStep = this.getNextStep(stepId, session.completedSteps);
    if (nextStep) {
      session.currentStep = nextStep;
    }

    await this.sessionRepository.save(session);

    return {
      status: 'saved',
      validationErrors: validationResult.errors,
      nextStep,
      canProceed: true,
      warnings: validationResult.warnings
    };
  }

  async finalizeSiteCreation(sessionId: string, dto: FinalizeSiteCreationDto): Promise<SiteCreationResult> {
    const session = await this.getActiveSession(sessionId);
    
    // Final validation
    const finalValidation = await this.validationService.validateAllSteps(session.stepData);
    if (!finalValidation.isValid) {
      throw new Error('Site creation validation failed');
    }

    // Create site record
    const site = await this.createSiteFromSession(session);
    
    // Queue setup tasks
    const setupTasks = await this.queueSetupTasks(site.id, session.stepData);
    
    // Mark session as completed
    session.status = 'completed';
    await this.sessionRepository.save(session);

    return {
      siteId: site.id,
      status: 'created',
      creationTasks: setupTasks.immediate,
      setupTasks: setupTasks.deferred,
      estimatedSetupTime: this.calculateSetupTime(setupTasks),
      nextSteps: this.generateNextSteps(site.id)
    };
  }

  private async createSiteFromSession(session: SiteCreationSession): Promise<Site> {
    const { basicInfo, stakeholders, specifications, regulatory, organization, phases } = session.stepData;

    const site = this.siteRepository.create({
      // Basic Information
      tenantId: session.tenantId,
      name: basicInfo.name,
      code: basicInfo.code,
      projectType: basicInfo.projectType,
      location: basicInfo.location,
      plannedStartDate: basicInfo.plannedStartDate,
      plannedEndDate: basicInfo.plannedEndDate,
      status: 'planning',
      
      // Stakeholders
      projectManager: stakeholders.projectManager,
      client: stakeholders.client,
      contractor: stakeholders.mainContractor,
      
      // Specifications
      siteArea: specifications.siteArea,
      buildingFootprint: specifications.buildingFootprint,
      floors: specifications.floors,
      
      // Regulatory
      buildingPermit: regulatory.buildingPermit,
      permits: regulatory.additionalPermits,
      
      // Organization
      siteCommittee: organization.committee,
      emergencyContacts: organization.emergencyContacts,
      
      // Timeline
      projectPhases: phases.phases,
      
      // Metadata
      createdBy: session.createdBy,
      createdAt: new Date()
    });

    return await this.siteRepository.save(site);
  }

  private async queueSetupTasks(siteId: string, stepData: any): Promise<{ immediate: CreationTask[], deferred: SetupTask[] }> {
    const immediateTasks: CreationTask[] = [
      {
        id: 'create-database-schema',
        name: 'Create Site Database Schema',
        category: 'database',
        dependencies: []
      },
      {
        id: 'setup-hikvision-devices',
        name: 'Configure Hikvision Devices',
        category: 'integration',
        dependencies: ['create-database-schema']
      },
      {
        id: 'create-user-permissions',
        name: 'Setup User Access Permissions',
        category: 'configuration',
        dependencies: ['create-database-schema']
      },
      {
        id: 'initialize-compliance-tracking',
        name: 'Initialize Compliance Tracking',
        category: 'configuration',
        dependencies: ['create-database-schema']
      }
    ];

    const deferredTasks: SetupTask[] = [
      {
        id: 'conduct-site-assessment',
        name: 'Conduct Initial Site Safety Assessment',
        category: 'within_1week',
        priority: 'high',
        estimatedDuration: '4 hours'
      },
      {
        id: 'train-site-personnel',
        name: 'Train Site Personnel on Systems',
        category: 'within_1week',
        priority: 'medium',
        estimatedDuration: '2 days'
      },
      {
        id: 'setup-equipment',
        name: 'Install and Configure Equipment',
        category: 'within_1week',
        priority: 'high',
        estimatedDuration: '3 days'
      }
    ];

    // Queue immediate tasks
    for (const task of immediateTasks) {
      await this.taskQueueService.queueTask({
        ...task,
        siteId,
        payload: stepData
      });
    }

    return { immediate: immediateTasks, deferred: deferredTasks };
  }
}
```

### 2. Validation Service

```typescript
// ValidationService.ts
import { Injectable } from '@nestjs/common';

@Injectable()
export class ValidationService {
  async validateStep(stepId: string, data: any): Promise<ValidationResult> {
    const validators = this.getValidatorsForStep(stepId);
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];

    for (const validator of validators) {
      try {
        const result = await validator.validate(data);
        if (!result.isValid) {
          if (result.severity === 'error') {
            errors.push(...result.errors);
          } else {
            warnings.push(...result.errors);
          }
        }
      } catch (error) {
        errors.push({
          field: 'general',
          code: 'VALIDATION_ERROR',
          message: `Validation failed: ${error.message}`,
          severity: 'error',
          canOverride: false
        });
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions: this.generateSuggestions(stepId, data, errors)
    };
  }

  private getValidatorsForStep(stepId: string): StepValidator[] {
    const validatorMap: Record<string, StepValidator[]> = {
      'basic-info': [
        new RequiredFieldsValidator(['name', 'code', 'projectType']),
        new SiteCodeUniquenessValidator(),
        new DateRangeValidator(['plannedStartDate', 'plannedEndDate']),
        new CoordinatesValidator()
      ],
      'stakeholders': [
        new RequiredFieldsValidator(['client', 'projectManager', 'mainContractor']),
        new EmailValidator(['client.email', 'projectManager.email']),
        new PhoneValidator(['client.phone', 'projectManager.phone']),
        new LicenseValidator(['architect.licenseNumber', 'contractor.licenseNumber'])
      ],
      'specifications': [
        new NumericRangeValidator({
          'siteArea': { min: 100, max: 1000000 },
          'floors': { min: 1, max: 200 },
          'parkingSpaces': { min: 0, max: 10000 }
        }),
        new UtilityProviderValidator()
      ],
      'regulatory': [
        new PermitValidator(),
        new ComplianceValidator(),
        new BuildingCodeValidator()
      ]
    };

    return validatorMap[stepId] || [];
  }
}

// Example Validator Implementation
class SiteCodeUniquenessValidator implements StepValidator {
  async validate(data: any): Promise<ValidationResult> {
    const { code, tenantId } = data;
    
    // Check if site code already exists
    const existingSite = await this.siteRepository.findOne({
      where: { code, tenantId }
    });

    if (existingSite) {
      return {
        isValid: false,
        errors: [{
          field: 'code',
          code: 'DUPLICATE_SITE_CODE',
          message: 'Site code already exists',
          severity: 'error',
          canOverride: false,
          suggestions: this.generateAlternativeCodes(code)
        }]
      };
    }

    return { isValid: true, errors: [] };
  }

  private generateAlternativeCodes(originalCode: string): string[] {
    const timestamp = Date.now().toString().slice(-4);
    return [
      `${originalCode}-A`,
      `${originalCode}-${timestamp}`,
      `${originalCode}-NEW`
    ];
  }
}
```

### 3. Task Queue Implementation

```typescript
// TaskQueueService.ts
import { Injectable } from '@nestjs/common';
import { Queue } from 'bull';
import { InjectQueue } from '@nestjs/bull';

@Injectable()
export class TaskQueueService {
  constructor(
    @InjectQueue('site-creation') private siteCreationQueue: Queue
  ) {}

  async queueTask(task: CreationTaskPayload): Promise<void> {
    await this.siteCreationQueue.add(task.id, task, {
      priority: this.getTaskPriority(task.category),
      attempts: task.maxRetries || 3,
      backoff: {
        type: 'exponential',
        delay: 2000
      }
    });
  }

  private getTaskPriority(category: string): number {
    const priorities = {
      'database': 10,
      'integration': 8,
      'configuration': 6,
      'notification': 4
    };
    return priorities[category] || 5;
  }
}

// Task Processor
@Processor('site-creation')
export class SiteCreationProcessor {
  @Process('create-database-schema')
  async createDatabaseSchema(job: Job<CreationTaskPayload>) {
    const { siteId, payload } = job.data;
    
    try {
      // Create site-specific database tables
      await this.databaseService.createSiteSchema(siteId);
      
      // Initialize with basic data
      await this.databaseService.initializeSiteData(siteId, payload);
      
      job.progress(100);
      return { status: 'completed', message: 'Database schema created successfully' };
    } catch (error) {
      throw new Error(`Database schema creation failed: ${error.message}`);
    }
  }

  @Process('setup-hikvision-devices')
  async setupHikvisionDevices(job: Job<CreationTaskPayload>) {
    const { siteId, payload } = job.data;
    const { technology } = payload;
    
    if (!technology?.hikvisionSetup?.devices?.length) {
      return { status: 'skipped', message: 'No Hikvision devices configured' };
    }

    try {
      for (const device of technology.hikvisionSetup.devices) {
        await this.hikvisionService.configureDevice(siteId, device);
        job.progress((technology.hikvisionSetup.devices.indexOf(device) + 1) / technology.hikvisionSetup.devices.length * 100);
      }
      
      return { status: 'completed', message: 'Hikvision devices configured successfully' };
    } catch (error) {
      throw new Error(`Hikvision setup failed: ${error.message}`);
    }
  }
}
```

## Database Schema

### Site Creation Tables

```sql
-- Site Creation Sessions
CREATE TABLE site_creation_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    created_by UUID NOT NULL,
    current_step VARCHAR(50) NOT NULL,
    completed_steps TEXT[] DEFAULT '{}',
    step_data JSONB DEFAULT '{}',
    template_id UUID,
    status VARCHAR(20) DEFAULT 'active',
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Site Creation Tasks
CREATE TABLE site_creation_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    site_id UUID NOT NULL,
    session_id UUID,
    task_name VARCHAR(100) NOT NULL,
    task_category VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    dependencies TEXT[] DEFAULT '{}',
    payload JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Site Templates
CREATE TABLE site_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    category VARCHAR(50) NOT NULL,
    description TEXT,
    version VARCHAR(20) DEFAULT '1.0',
    is_default BOOLEAN DEFAULT FALSE,
    is_public BOOLEAN DEFAULT FALSE,
    tenant_id UUID,
    created_by UUID NOT NULL,
    default_values JSONB DEFAULT '{}',
    required_fields TEXT[] DEFAULT '{}',
    optional_fields TEXT[] DEFAULT '{}',
    hidden_fields TEXT[] DEFAULT '{}',
    custom_validations JSONB DEFAULT '{}',
    usage_count INTEGER DEFAULT 0,
    last_used TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_site_creation_sessions_tenant ON site_creation_sessions(tenant_id);
CREATE INDEX idx_site_creation_sessions_status ON site_creation_sessions(status);
CREATE INDEX idx_site_creation_tasks_site ON site_creation_tasks(site_id);
CREATE INDEX idx_site_creation_tasks_status ON site_creation_tasks(status);
CREATE INDEX idx_site_templates_category ON site_templates(category);
CREATE INDEX idx_site_templates_tenant ON site_templates(tenant_id);
```

## Testing Strategy

### 1. Unit Tests

```typescript
// SiteCreationService.test.ts
describe('SiteCreationService', () => {
  let service: SiteCreationService;
  let sessionRepository: Repository<SiteCreationSession>;
  let validationService: ValidationService;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        SiteCreationService,
        {
          provide: getRepositoryToken(SiteCreationSession),
          useClass: Repository
        },
        {
          provide: ValidationService,
          useValue: {
            validateStep: jest.fn(),
            validateAllSteps: jest.fn()
          }
        }
      ]
    }).compile();

    service = module.get<SiteCreationService>(SiteCreationService);
    sessionRepository = module.get<Repository<SiteCreationSession>>(getRepositoryToken(SiteCreationSession));
    validationService = module.get<ValidationService>(ValidationService);
  });

  describe('initializeSession', () => {
    it('should create a new site creation session', async () => {
      const dto = {
        tenantId: 'tenant-1',
        createdBy: 'user-1'
      };

      const mockSession = {
        id: 'session-1',
        ...dto,
        currentStep: 'basic-info',
        expiresAt: new Date()
      };

      jest.spyOn(sessionRepository, 'create').mockReturnValue(mockSession as any);
      jest.spyOn(sessionRepository, 'save').mockResolvedValue(mockSession as any);

      const result = await service.initializeSession(dto);

      expect(result.sessionId).toBe('session-1');
      expect(result.currentStep).toBe('basic-info');
      expect(sessionRepository.create).toHaveBeenCalledWith(expect.objectContaining(dto));
    });
  });

  describe('saveStepData', () => {
    it('should save step data and move to next step', async () => {
      const mockSession = {
        id: 'session-1',
        stepData: {},
        completedSteps: []
      };

      jest.spyOn(service as any, 'getActiveSession').mockResolvedValue(mockSession);
      jest.spyOn(validationService, 'validateStep').mockResolvedValue({
        isValid: true,
        errors: [],
        warnings: []
      });
      jest.spyOn(sessionRepository, 'save').mockResolvedValue(mockSession as any);

      const result = await service.saveStepData('session-1', 'basic-info', {
        data: { name: 'Test Site' },
        isComplete: true
      });

      expect(result.status).toBe('saved');
      expect(mockSession.stepData['basic-info']).toEqual({ name: 'Test Site' });
      expect(mockSession.completedSteps).toContain('basic-info');
    });
  });
});
```

### 2. Integration Tests

```typescript
// SiteCreation.integration.test.ts
describe('Site Creation Integration', () => {
  let app: INestApplication;
  let sessionRepository: Repository<SiteCreationSession>;
  let siteRepository: Repository<Site>;

  beforeAll(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule]
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    sessionRepository = app.get<Repository<SiteCreationSession>>(getRepositoryToken(SiteCreationSession));
    siteRepository = app.get<Repository<Site>>(getRepositoryToken(Site));
  });

  describe('Complete Site Creation Flow', () => {
    it('should create a site through the complete wizard flow', async () => {
      // Initialize session
      const initResponse = await request(app.getHttpServer())
        .post('/api/sites/creation/initialize')
        .send({
          tenantId: 'tenant-1',
          createdBy: 'user-1'
        })
        .expect(201);

      const sessionId = initResponse.body.sessionId;

      // Complete basic info step
      await request(app.getHttpServer())
        .put(`/api/sites/creation/${sessionId}/step/basic-info`)
        .send({
          data: {
            name: 'Test Construction Site',
            code: 'TEST-001',
            projectType: 'commercial',
            location: {
              address: '123 Test Street',
              city: 'Test City',
              region: 'Test Region',
              coordinates: { latitude: 40.7128, longitude: -74.0060 }
            },
            plannedStartDate: '2024-03-01',
            plannedEndDate: '2024-12-31'
          },
          isComplete: true
        })
        .expect(200);

      // Complete remaining steps...
      // (Similar requests for stakeholders, specifications, etc.)

      // Finalize site creation
      const finalizeResponse = await request(app.getHttpServer())
        .post(`/api/sites/creation/${sessionId}/finalize`)
        .send({
          confirmOverrides: [],
          additionalNotes: 'Test site creation'
        })
        .expect(201);

      expect(finalizeResponse.body.status).toBe('created');
      expect(finalizeResponse.body.siteId).toBeDefined();

      // Verify site was created in database
      const createdSite = await siteRepository.findOne({
        where: { id: finalizeResponse.body.siteId }
      });

      expect(createdSite).toBeDefined();
      expect(createdSite.name).toBe('Test Construction Site');
      expect(createdSite.code).toBe('TEST-001');
    });
  });
});
```

This implementation guide provides a comprehensive framework for building the site creation process with proper validation, error handling, and testing strategies. The modular approach allows for easy maintenance and extension of functionality as requirements evolve.