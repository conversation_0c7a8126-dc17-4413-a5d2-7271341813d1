# Worker Management Implementation Guide

## Key Implementation Scenarios

### 1. Adding Workers at Company vs Site Level

#### Company Level Worker Addition (Recommended Primary Method)
```typescript
// Add worker to company master database
const addWorkerToCompany = async (workerData: CreateWorkerRequest) => {
  // 1. Create worker in company database
  const worker = await createWorker({
    company_id: currentCompany.id,
    employee_number: generateEmployeeNumber(),
    ...workerData
  });

  // 2. Assign trades and determine required trainings
  const requiredTrainings = await getRequiredTrainingsForTrades(workerData.trades);
  
  // 3. Create training requirement records
  await createTrainingRequirements(worker.id, requiredTrainings);
  
  // 4. Return worker with compliance status
  return {
    worker,
    required_trainings: requiredTrainings,
    compliance_status: 'pending_training'
  };
};
```

#### Site Level Worker Addition (Import from Company Database)
```typescript
// Import existing worker to site
const importWorkerToSite = async (siteId: string, workerId: string) => {
  // 1. Verify worker exists in company database
  const worker = await getWorkerById(workerId);
  if (!worker) throw new Error('Worker not found in company database');

  // 2. Check compliance for site assignment
  const compliance = await checkWorkerCompliance(workerId);
  
  // 3. Create site assignment if eligible
  if (compliance.eligible) {
    await createSiteAssignment({
      site_id: siteId,
      worker_id: workerId,
      assigned_date: new Date(),
      status: 'assigned'
    });
  }
  
  return { worker, compliance };
};

// Add new worker directly at site level (still goes to company DB)
const addWorkerAtSite = async (siteId: string, workerData: CreateWorkerRequest) => {
  // 1. Add to company database first
  const worker = await addWorkerToCompany(workerData);
  
  // 2. Immediately assign to site if compliant
  if (worker.compliance_status === 'compliant') {
    await importWorkerToSite(siteId, worker.worker.id);
  }
  
  return worker;
};
```

### 2. Training Collection and Registration Timing

#### Initial Training Collection (During Onboarding)
```typescript
const collectInitialTrainings = async (workerId: string, certificates: TrainingCertificate[]) => {
  const trainingRecords = [];
  
  for (const cert of certificates) {
    // 1. Validate certificate
    const validation = await validateCertificate(cert);
    if (!validation.valid) {
      throw new Error(`Invalid certificate: ${validation.errors.join(', ')}`);
    }
    
    // 2. Calculate expiry date based on training module
    const trainingModule = await getTrainingModule(cert.training_module_id);
    const expiryDate = trainingModule.validity_period_months 
      ? addMonths(cert.completion_date, trainingModule.validity_period_months)
      : null;
    
    // 3. Store training record
    const record = await createTrainingRecord({
      worker_id: workerId,
      training_module_id: cert.training_module_id,
      certificate_number: cert.certificate_number,
      completion_date: cert.completion_date,
      expiry_date: expiryDate,
      certificate_file_url: cert.file_url,
      training_provider: cert.provider,
      status: expiryDate && expiryDate < new Date() ? 'expired' : 'valid'
    });
    
    trainingRecords.push(record);
  }
  
  // 4. Update worker compliance status
  await updateWorkerComplianceStatus(workerId);
  
  return trainingRecords;
};
```

#### Company-Wide Training Updates
```typescript
const updateCompanyWideTraining = async (trainingSessionData: CompanyTrainingSession) => {
  const { training_module_id, completion_date, participants } = trainingSessionData;
  
  // 1. Get training module details
  const trainingModule = await getTrainingModule(training_module_id);
  
  // 2. Calculate expiry date
  const expiryDate = trainingModule.validity_period_months
    ? addMonths(completion_date, trainingModule.validity_period_months)
    : null;
  
  // 3. Batch update training records
  const updatePromises = participants.map(async (participant) => {
    // Check if worker already has this training
    const existingRecord = await getLatestTrainingRecord(
      participant.worker_id, 
      training_module_id
    );
    
    // Only update if new completion date is more recent
    if (!existingRecord || existingRecord.completion_date < completion_date) {
      return createTrainingRecord({
        worker_id: participant.worker_id,
        training_module_id,
        certificate_number: participant.certificate_number,
        completion_date,
        expiry_date: expiryDate,
        certificate_file_url: participant.certificate_file_url,
        training_provider: trainingSessionData.provider,
        status: 'valid'
      });
    }
  });
  
  // 4. Execute all updates
  const results = await Promise.allSettled(updatePromises);
  
  // 5. Update compliance status for all affected workers
  const workerIds = participants.map(p => p.worker_id);
  await Promise.all(workerIds.map(updateWorkerComplianceStatus));
  
  // 6. Check if any site assignments are now affected
  await checkSiteAssignmentEligibility(workerIds);
  
  return results;
};
```

### 3. Compliance Checking Before Site Assignment

```typescript
const checkSiteAssignmentEligibility = async (siteId: string, workerIds: string[]) => {
  const results = [];
  
  for (const workerId of workerIds) {
    // 1. Get worker's trades
    const workerTrades = await getWorkerTrades(workerId);
    
    // 2. Get required trainings for all worker's trades
    const requiredTrainings = await getRequiredTrainingsForTrades(
      workerTrades.map(t => t.trade_id)
    );
    
    // 3. Get worker's current training records
    const trainingRecords = await getWorkerTrainingRecords(workerId);
    
    // 4. Check each required training
    const trainingStatus = requiredTrainings.map(required => {
      const record = trainingRecords.find(r => 
        r.training_module_id === required.training_module_id
      );
      
      if (!record) {
        return {
          training_module_id: required.training_module_id,
          training_name: required.training_name,
          status: 'missing',
          blocking: true
        };
      }
      
      if (record.expiry_date && record.expiry_date < new Date()) {
        return {
          training_module_id: required.training_module_id,
          training_name: required.training_name,
          status: 'expired',
          expiry_date: record.expiry_date,
          blocking: true
        };
      }
      
      const daysUntilExpiry = record.expiry_date 
        ? Math.ceil((record.expiry_date.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
        : null;
      
      return {
        training_module_id: required.training_module_id,
        training_name: required.training_name,
        status: daysUntilExpiry && daysUntilExpiry <= 30 ? 'expiring_soon' : 'valid',
        expiry_date: record.expiry_date,
        days_until_expiry: daysUntilExpiry,
        blocking: false
      };
    });
    
    // 5. Check medical records
    const medicalRecords = await getWorkerMedicalRecords(workerId);
    const medicalStatus = checkMedicalCompliance(medicalRecords);
    
    // 6. Determine overall eligibility
    const blockingIssues = [
      ...trainingStatus.filter(t => t.blocking),
      ...medicalStatus.filter(m => m.blocking)
    ];
    
    results.push({
      worker_id: workerId,
      eligible: blockingIssues.length === 0,
      training_status: trainingStatus,
      medical_status: medicalStatus,
      blocking_issues: blockingIssues,
      warnings: trainingStatus.filter(t => t.status === 'expiring_soon')
    });
  }
  
  return results;
};
```

### 4. Hikvision Attendance Integration Implementation

```typescript
// Worker enrollment to Hikvision devices when assigned to site
const enrollWorkerToSiteDevices = async (siteId: string, workerId: string) => {
  // 1. Verify worker compliance before enrollment
  const compliance = await checkWorkerCompliance(workerId);
  if (!compliance.eligible) {
    throw new Error(`Worker not compliant: ${compliance.blocking_issues.join(', ')}`);
  }
  
  // 2. Get worker data with photo
  const worker = await getWorkerWithPhoto(workerId);
  if (!worker.photo_url) {
    throw new Error('Worker photo required for face recognition enrollment');
  }
  
  // 3. Get all Hikvision devices for the site
  const devices = await getHikvisionDevicesForSite(siteId);
  const enrollmentResults = [];
  
  // 4. Enroll to each device
  for (const device of devices) {
    try {
      // Create user on Hikvision device
      const deviceUser = await hikvisionAPI.createUser({
        deviceIP: device.device_ip,
        credentials: device.access_credentials,
        userData: {
          name: `${worker.first_name} ${worker.last_name}`,
          employeeNo: worker.employee_number,
          userType: 'normal',
          accessLevel: determineAccessLevel(worker.trades)
        }
      });
      
      // Upload face template
      await hikvisionAPI.uploadFaceTemplate({
        deviceIP: device.device_ip,
        userId: deviceUser.userId,
        faceImage: worker.photo_url
      });
      
      // Record successful sync
      await createDeviceUserSync({
        hikvision_device_id: device.id,
        worker_id: workerId,
        device_user_id: deviceUser.userId,
        face_template_uploaded: true,
        sync_status: 'synced',
        access_level: determineAccessLevel(worker.trades)
      });
      
      enrollmentResults.push({
        device_id: device.device_id,
        device_name: device.device_name,
        status: 'success',
        device_user_id: deviceUser.userId
      });
      
    } catch (error) {
      // Record failed sync
      await createDeviceUserSync({
        hikvision_device_id: device.id,
        worker_id: workerId,
        sync_status: 'failed',
        sync_error_message: error.message
      });
      
      enrollmentResults.push({
        device_id: device.device_id,
        device_name: device.device_name,
        status: 'failed',
        error: error.message
      });
    }
  }
  
  return { enrollment_results: enrollmentResults };
};

// Process Hikvision face recognition events
const processHikvisionEvent = async (eventData: HikvisionEventData) => {
  // 1. Find the device
  const device = await getHikvisionDeviceByDeviceId(eventData.deviceId);
  if (!device) {
    console.warn('Event from unknown device:', eventData.deviceId);
    return;
  }
  
  // 2. Log the raw event
  await logDeviceEvent({
    hikvision_device_id: device.id,
    event_type: eventData.eventType,
    event_timestamp: new Date(eventData.timestamp),
    device_event_id: eventData.eventId,
    face_recognition_confidence: eventData.confidence,
    raw_event_data: eventData,
    processed: false
  });
  
  // 3. Process based on event type
  switch (eventData.eventType) {
    case 'ACCESS_GRANTED':
      await processAccessGranted(device, eventData);
      break;
    case 'ACCESS_DENIED':
      await processAccessDenied(device, eventData);
      break;
    case 'UNKNOWN_FACE':
      await processUnknownFace(device, eventData);
      break;
  }
  
  // 4. Mark event as processed
  await markEventProcessed(eventData.eventId);
};

// Handle successful face recognition and site access
const processAccessGranted = async (device: HikvisionDevice, eventData: HikvisionEventData) => {
  // 1. Find worker by device user ID
  const userSync = await getDeviceUserSyncByDeviceUserId(device.id, eventData.userId);
  if (!userSync) {
    console.warn('Access granted for unknown user:', eventData.userId);
    return;
  }
  
  // 2. Determine if this is check-in or check-out
  const today = new Date().toISOString().split('T')[0];
  const existingAttendance = await getDailyAttendance(device.site_id, userSync.worker_id, today);
  
  const eventTime = new Date(eventData.timestamp).toTimeString().split(' ')[0];
  
  if (!existingAttendance || !existingAttendance.check_in_time) {
    // This is a check-in
    const attendance = await createAttendanceRecord({
      site_id: device.site_id,
      worker_id: userSync.worker_id,
      hikvision_device_id: device.device_id,
      attendance_date: today,
      check_in_time: eventTime,
      face_recognition_confidence: eventData.confidence,
      device_event_id: eventData.eventId,
      status: 'present',
      sync_status: 'synced'
    });
    
    // Send real-time notification via WebSocket
    await broadcastAttendanceEvent(device.site_id, {
      type: 'check_in',
      worker_name: userSync.worker.name,
      employee_number: userSync.worker.employee_number,
      timestamp: eventData.timestamp,
      device_location: device.location_description,
      confidence: eventData.confidence
    });
    
  } else if (!existingAttendance.check_out_time) {
    // This is a check-out
    const totalHours = calculateHours(existingAttendance.check_in_time, eventTime);
    const overtimeHours = Math.max(totalHours - 8, 0);
    
    await updateAttendanceRecord(existingAttendance.id, {
      check_out_time: eventTime,
      total_hours: totalHours,
      overtime_hours: overtimeHours,
      status: 'completed'
    });
    
    // Send check-out notification
    await broadcastAttendanceEvent(device.site_id, {
      type: 'check_out',
      worker_name: userSync.worker.name,
      employee_number: userSync.worker.employee_number,
      timestamp: eventData.timestamp,
      device_location: device.location_description,
      total_hours: totalHours,
      overtime_hours: overtimeHours
    });
  }
};

// Site-level attendance dashboard data
const getSiteDailyAttendance = async (siteId: string, date: string) => {
  // 1. Get all workers assigned to site
  const siteWorkers = await getActiveSiteWorkers(siteId);
  
  // 2. Get attendance records for the date
  const attendanceRecords = await getDailyAttendanceRecords(siteId, date);
  
  // 3. Calculate summary statistics
  const summary = {
    total_assigned: siteWorkers.length,
    present: attendanceRecords.filter(r => r.check_in_time).length,
    absent: siteWorkers.length - attendanceRecords.filter(r => r.check_in_time).length,
    late_arrivals: attendanceRecords.filter(r => 
      r.check_in_time && r.check_in_time > '08:00:00'
    ).length,
    early_departures: attendanceRecords.filter(r => 
      r.check_out_time && r.check_out_time < '17:00:00'
    ).length,
    total_hours: attendanceRecords.reduce((sum, r) => sum + (r.total_hours || 0), 0),
    overtime_hours: attendanceRecords.reduce((sum, r) => sum + (r.overtime_hours || 0), 0)
  };
  
  // 4. Get device status
  const devices = await getHikvisionDevicesForSite(siteId);
  const deviceStatus = await Promise.all(
    devices.map(async (device) => ({
      device_name: device.device_name,
      status: device.status,
      last_event: await getLastDeviceEvent(device.id)
    }))
  );
  
  return {
    site: await getSite(siteId),
    date,
    summary,
    attendance_records: attendanceRecords,
    device_status: deviceStatus
  };
};

// Manual attendance entry for device failures
const createManualAttendanceEntry = async (manualEntryData: ManualAttendanceEntry) => {
  const { site_id, worker_id, attendance_date, check_in_time, check_out_time, reason, entered_by } = manualEntryData;
  
  // 1. Verify worker is assigned to site
  const assignment = await getSiteWorkerAssignment(site_id, worker_id);
  if (!assignment) {
    throw new Error('Worker not assigned to this site');
  }
  
  // 2. Check for existing attendance record
  const existingRecord = await getDailyAttendance(site_id, worker_id, attendance_date);
  if (existingRecord) {
    throw new Error('Attendance record already exists for this date');
  }
  
  // 3. Calculate total hours
  const totalHours = check_out_time ? 
    calculateHours(check_in_time, check_out_time) : null;
  
  // 4. Create manual attendance record
  const attendance = await createAttendanceRecord({
    site_id,
    worker_id,
    attendance_date,
    check_in_time,
    check_out_time,
    total_hours: totalHours,
    overtime_hours: totalHours ? Math.max(totalHours - 8, 0) : null,
    status: check_out_time ? 'completed' : 'present',
    sync_status: 'manual_entry',
    notes: `Manual entry: ${reason}. Entered by: ${entered_by}`
  });
  
  // 5. Log manual entry for audit
  await logManualAttendanceEntry({
    attendance_id: attendance.id,
    entered_by,
    reason,
    timestamp: new Date()
  });
  
  return {
    attendance_id: attendance.id,
    status: 'created',
    total_hours: totalHours,
    requires_approval: true // Manual entries may need supervisor approval
  };
};
```

### 5. Training Management System Features

```typescript
// Training renewal reminder system
const processTrainingRenewals = async () => {
  // 1. Find trainings expiring in next 30 days
  const expiringTrainings = await getExpiringTrainings(30);
  
  // 2. Group by training module for batch scheduling
  const trainingGroups = groupBy(expiringTrainings, 'training_module_id');
  
  // 3. Generate renewal schedules
  for (const [moduleId, trainings] of Object.entries(trainingGroups)) {
    const module = await getTrainingModule(moduleId);
    
    // Create training session proposal
    const sessionProposal = {
      training_module_id: moduleId,
      training_name: module.name,
      participants: trainings.map(t => ({
        worker_id: t.worker_id,
        worker_name: t.worker_name,
        current_expiry: t.expiry_date,
        sites_affected: t.site_assignments
      })),
      recommended_date: addDays(new Date(), 7), // Schedule for next week
      priority: module.category === 'critical' ? 'high' : 'medium'
    };
    
    // Notify training coordinator
    await notifyTrainingCoordinator(sessionProposal);
  }
};

// Certificate validation system
const validateTrainingCertificate = async (certificate: CertificateFile) => {
  const validationResults = {
    valid: true,
    errors: [],
    warnings: []
  };
  
  // 1. File format validation
  if (!certificate.file_url.endsWith('.pdf')) {
    validationResults.errors.push('Certificate must be in PDF format');
  }
  
  // 2. Certificate number format validation
  if (!/^[A-Z]{2,4}-\d{4,6}$/.test(certificate.certificate_number)) {
    validationResults.warnings.push('Certificate number format may be invalid');
  }
  
  // 3. Date validation
  if (certificate.completion_date > new Date()) {
    validationResults.errors.push('Completion date cannot be in the future');
  }
  
  // 4. Duplicate certificate check
  const existingCert = await findCertificateByNumber(certificate.certificate_number);
  if (existingCert && existingCert.worker_id !== certificate.worker_id) {
    validationResults.errors.push('Certificate number already exists for another worker');
  }
  
  // 5. Training provider validation
  const approvedProviders = await getApprovedTrainingProviders();
  if (!approvedProviders.includes(certificate.training_provider)) {
    validationResults.warnings.push('Training provider not in approved list');
  }
  
  validationResults.valid = validationResults.errors.length === 0;
  return validationResults;
};
```

## Key Implementation Tips

### 1. Data Consistency
- Always update worker compliance status after training record changes
- Use database transactions for multi-table operations
- Implement proper foreign key constraints

### 2. Performance Optimization
- Index frequently queried fields (worker_id, site_id, training_module_id, expiry_date)
- Use batch operations for bulk updates
- Implement caching for compliance status checks

### 3. User Experience
- Provide clear feedback on compliance issues
- Show progress indicators for bulk operations
- Implement real-time notifications for critical compliance issues

### 4. Security Considerations
- Validate all file uploads (certificates)
- Implement role-based access control
- Audit all training record modifications
- Secure storage for sensitive documents

This implementation guide provides practical code examples for handling the complex relationships between workers, trainings, sites, and attendance while maintaining data integrity and system performance.