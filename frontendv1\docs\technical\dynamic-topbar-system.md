# Dynamic TopBar System Documentation

## Overview

The TopBar system in the frontend application is designed to provide contextual navigation and actions based on the current page and user context. It dynamically adapts its content, layout, and functionality depending on whether the user is viewing site-level content, creation workflows, or company-level dashboards.

## Architecture

### Core Components

1. **TopBar Component** (`src/components/layout/TopBar.tsx`)
   - Main navigation bar component
   - Handles breadcrumbs, search, user menu, and contextual actions
   - Supports minimal mode for focused workflows

2. **FloatingCard Component** (`src/components/layout/FloatingCard.tsx`)
   - Wrapper component that includes TopBar
   - Provides consistent layout structure
   - Passes TopBar configuration through props

3. **Layout Context** (`src/hooks/useLayoutContext.tsx`)
   - Manages sidebar visibility
   - Determines full-width layout for specific routes

## Dynamic Behavior Patterns

### 1. Site Dashboard Context
**Route Pattern**: `/sites/{siteId}/dashboard`

**TopBar Configuration**:
- **Left Side**: Breadcrumbs showing "Dashboard / [Site Name]"
- **Site Dropdown**: Interactive site switcher in breadcrumb
- **Right Side**: Full action set including:
  - Search functionality
  - Site info button
  - Weather widget
  - Notifications
  - User menu

**Example Usage**:
```tsx
<FloatingCard 
  title={`${site.name} Dashboard`} 
  breadcrumbs={[
    { name: "Dashboard", path: "/" },
    { name: site.name, path: `/sites/${siteId}/dashboard` }
  ]}
>
  {/* Dashboard content */}
</FloatingCard>
```

### 2. Site Sub-Pages Context
**Route Pattern**: `/sites/{siteId}/{module}`

**TopBar Configuration**:
- **Left Side**: Breadcrumbs showing "Dashboard / [Site Name] / [Module]"
- **Site Dropdown**: Available for quick site switching
- **Right Side**: Standard action set (same as dashboard)

**Example Usage**:
```tsx
<FloatingCard 
  title="Worker Directory" 
  breadcrumbs={[
    { name: "Dashboard", path: "/" },
    { name: site.name, path: `/sites/${siteId}/dashboard` },
    { name: "Workers", path: `/sites/${siteId}/workers` }
  ]}
>
  {/* Worker directory content */}
</FloatingCard>
```

### 3. Creation/Edit Workflows Context
**Route Pattern**: `/sites/new`, `/sites/{siteId}/workers/edit/{workerId}`

**TopBar Configuration**:
- **Left Side**: Simple title (e.g., "Create New Site", "Edit Worker")
- **Back Button**: Enabled with custom or default navigation
- **Right Side**: Minimal action set:
  - Save & Close button
  - Auto-save indicator (if applicable)
- **Hidden Elements**: Search, weather, notifications, site info
- **Minimal Mode**: Activated to reduce clutter

**Example Usage**:
```tsx
<FloatingCard
  title="Create New Site"
  layout="custom"
  topBarShowBack={true}
  topBarOnBack={handleExit}
  topBarMinimal={true}
  topBarRightActions={
    <>
      {isSaving && (
        <span className="text-xs text-gray-500 mr-2">
          <Loader2 className="h-3 w-3 mr-1 animate-spin" />
          Auto-saving...
        </span>
      )}
      <button onClick={handleExit}>
        <Save className="h-4 w-4 mr-2" />
        Save and Close
      </button>
    </>
  }
>
  {/* Creation workflow content */}
</FloatingCard>
```

### 4. Company-Level Context
**Route Pattern**: `/`, `/settings`, `/account`

**TopBar Configuration**:
- **Left Side**: Simple title without breadcrumbs
- **Right Side**: Standard action set (no site-specific actions)
- **No Site Dropdown**: Not applicable at company level

## Key Features

### 1. Contextual Site Switching
- **Site Dropdown**: Appears in breadcrumbs when viewing site-specific content
- **Dynamic Site Name**: Shows current site name from context
- **Equivalent Path Mapping**: Maintains current page context when switching sites
- **Visual Indicators**: Shows site health status and current site marker

### 2. Minimal Mode
- **Purpose**: Reduces cognitive load during focused workflows
- **Activated For**: Creation forms, edit workflows, complex multi-step processes
- **Hidden Elements**: Search, weather, notifications, site info
- **Retained Elements**: User menu, essential actions

### 3. Responsive Search
- **Collapsed State**: Shows only search icon
- **Expanded State**: Full search input with animation
- **Auto-collapse**: Closes when clicking outside
- **Placeholder**: "Search across all data..."

### 4. Breadcrumb Intelligence
- **Site Detection**: Automatically identifies site breadcrumbs
- **Interactive Elements**: Site names become dropdowns
- **Path Preservation**: Maintains navigation context

## Implementation Details

### Props Interface

```tsx
interface TopBarProps {
  title: string;
  breadcrumbs?: { name: string; path: string }[];
  showBack?: boolean;
  onBack?: () => void;
  rightActions?: React.ReactNode;
  minimal?: boolean;
}

interface FloatingCardTopBarExtras {
  topBarShowBack?: boolean;
  topBarOnBack?: () => void;
  topBarRightActions?: React.ReactNode;
  topBarMinimal?: boolean;
}
```

### Route-Based Behavior

The system uses several utility functions to determine context:

1. **`extractSiteId(pathname)`**: Extracts site ID from current path
2. **`isSiteBreadcrumb(breadcrumbs, index)`**: Identifies site breadcrumb items
3. **`getEquivalentSitePath(currentPath, targetSiteId)`**: Maps paths between sites
4. **`getNavigationContext(pathname)`**: Determines navigation context

### Layout Integration

The TopBar integrates with the layout system through:

1. **LayoutProvider**: Manages sidebar visibility
2. **SIDEBAR_FREE_ROUTES**: Defines routes that hide sidebar
3. **Full-width calculation**: Adjusts container width based on sidebar state

## Benefits of Dynamic TopBar

### 1. Enhanced User Experience
- **Contextual Actions**: Only shows relevant actions for current context
- **Reduced Clutter**: Minimal mode eliminates distractions during workflows
- **Consistent Navigation**: Maintains familiar patterns across different contexts

### 2. Improved Workflow Efficiency
- **Quick Site Switching**: Seamless navigation between sites
- **Focused Creation**: Minimal distractions during complex workflows
- **Smart Defaults**: Intelligent back navigation and path mapping

### 3. Scalable Architecture
- **Component Reusability**: Single TopBar component handles all contexts
- **Easy Extension**: New contexts can be added through props
- **Maintainable Code**: Centralized logic for navigation behavior

## Usage Guidelines

### When to Use Minimal Mode
- Multi-step creation workflows
- Complex edit forms
- Data entry processes
- Any workflow requiring focused attention

### When to Show Full TopBar
- Dashboard views
- List/directory pages
- Reporting interfaces
- General navigation contexts

### Breadcrumb Best Practices
- Always include "Dashboard" as root for site contexts
- Use descriptive names for each level
- Ensure all breadcrumb links are functional
- Keep breadcrumb depth reasonable (max 4 levels)

## Future Enhancements

### Potential Improvements
1. **Keyboard Navigation**: Add keyboard shortcuts for common actions
2. **Customizable Actions**: Allow modules to register custom actions
3. **Context Persistence**: Remember user preferences per context
4. **Advanced Search**: Add filters and scoped search options
5. **Mobile Optimization**: Improve responsive behavior for mobile devices

### Technical Debt Considerations
1. **State Management**: Consider moving to more centralized state management
2. **Performance**: Optimize re-renders for large site lists
3. **Accessibility**: Enhance keyboard navigation and screen reader support
4. **Testing**: Add comprehensive unit and integration tests

## Conclusion

The dynamic TopBar system successfully provides contextual navigation that adapts to user needs while maintaining consistency across the application. The minimal mode feature significantly improves user experience during complex workflows by reducing cognitive load, while the full mode provides comprehensive navigation capabilities for general use cases.

The architecture is flexible enough to accommodate future requirements while maintaining clean separation of concerns between layout, navigation, and content components.