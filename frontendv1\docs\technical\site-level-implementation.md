# Site Level Implementation Documentation

## Overview

This document outlines the current implementation of site-level functionality in the workforce management application. Site-level features provide project-specific management, operational control, and day-to-day execution capabilities for individual construction sites/projects.

## Architecture Overview

Sites in the application represent individual construction projects or locations that:
- **Belong to a Company/Tenant**: Sites are owned by companies
- **Inherit Master Data**: Sites use company-defined master data with local customization
- **Manage Operations**: Sites handle day-to-day operational activities
- **Report Upward**: Site data aggregates to company-level reporting

## Core Site-Level Features

### 1. Site Dashboard (`/sites/{siteId}/dashboard`)

**Location**: `frontendv1/src/pages/SiteDashboard.tsx`

**Key Metrics Displayed**:
- Workers On-Site (Today): Real-time worker count
- Active Permits: Current work permits in effect
- Open Incidents: Unresolved safety incidents
- Equipment On-Site: Equipment currently assigned to site
- Overdue Trainings: Workers with expired training
- Upcoming Inspections: Scheduled equipment/safety inspections
- Toolbox Talk Attendance: Safety meeting participation rates
- Safe Man Hours: Cumulative safe working hours

**Visualization Tabs**:
- **Trades**: Worker distribution by trade classification
- **Time**: Time tracking and attendance patterns
- **Permits**: Permit status and workflow visualization
- **Equipment**: Equipment utilization and status
- **Hours**: Working hours analysis and overtime tracking

### 2. Site Information Management (`/sites/{siteId}/info`)

**Location**: `frontendv1/src/pages/SiteInfoPage.tsx`

**Site Creation Process**: `frontendv1/docs/dev/site-creation-process.md`

#### 2.1 Core Site Information
- **Basic Identification**: Site name, location, project details
- **Project Management**: Timeline, phases, progress tracking
- **Status Monitoring**: Health status (green/amber/red)
- **Stakeholder Management**: Project managers, site engineers, contacts

#### 2.2 Site Areas Configuration
**Documentation**: `frontendv1/docs/workflows/site-creation-management-workflow.md`

**Area Types**:
- **Construction Areas**: Primary building zones
- **Storage Areas**: Material and equipment storage
- **Office Areas**: Administrative spaces
- **Safety Areas**: Emergency and safety zones
- **Utility Areas**: Infrastructure and services

**Area Properties**:
- Geographic coordinates and boundaries
- Safety level classification (high/medium/low risk)
- Access requirements and restrictions
- PPE requirements by area
- Equipment restrictions and allowances
- Maximum occupancy limits

### 3. Site Worker Management (`/sites/{siteId}/workers`)

**Location**: `frontendv1/src/pages/WorkerDirectory.tsx`

#### 3.1 Worker Assignment and Tracking
- **Site Assignment**: Workers assigned from company master database
- **Daily Attendance**: Real-time check-in/check-out tracking
- **Trade-Based Organization**: Workers grouped by trade classifications
- **Compliance Monitoring**: Training and certification status tracking

#### 3.2 Worker Creation at Site Level
**Component**: `frontendv1/src/components/workers/CreateWorkerForm.tsx`
**Usage Guide**: `frontendv1/src/components/workers/USAGE.md`

**Routes**:
- `/sites/{siteId}/workers/create` - Site-specific worker creation
- `/sites/{siteId}/workers` - Worker directory and management

#### 3.3 Worker Profile Management
**Location**: `frontendv1/src/pages/WorkerProfile.tsx`
- Individual worker details and history
- Training records and certifications
- Site assignment history
- Performance tracking

### 4. Site Equipment Management (`/sites/{siteId}/equipment`)

**Location**: `frontendv1/src/pages/EquipmentPage.tsx`

#### 4.1 Equipment Categories
**General Equipment**: `frontendv1/src/components/equipment/GeneralEquipment.tsx`
- Heavy machinery (excavators, cranes, etc.)
- Tools and small equipment
- Site-specific equipment assignments
- Usage tracking and maintenance scheduling

**Equipment Inspections**: `frontendv1/src/components/equipment/EquipmentInspections.tsx`
- Scheduled inspection management
- Inspection form completion
- Compliance tracking
- Maintenance work order generation

#### 4.2 Site Equipment Features
- **Assignment Tracking**: Equipment assigned to specific workers/areas
- **Status Management**: Available, in-use, maintenance, out-of-service
- **Location Tracking**: Equipment location within site areas
- **Usage Monitoring**: Hours of operation and utilization rates
- **Maintenance Scheduling**: Preventive and corrective maintenance

### 5. Site Task Management (`/sites/{siteId}/tasks`)

**Location**: `frontendv1/src/pages/TasksPage.tsx`

#### 5.1 Task Workflow
- **Task Creation**: `frontendv1/src/pages/NewTaskPage.tsx`
- **Task Requests**: `frontendv1/src/pages/TaskRequestPage.tsx`
- **Task Details**: `frontendv1/src/pages/TaskDetailPage.tsx`
- **Task Review**: `frontendv1/src/pages/ReviewTaskPage.tsx`
- **Task Approval**: `frontendv1/src/pages/ApproveTaskPage.tsx`
- **Task Closure**: `frontendv1/src/pages/CloseTaskPage.tsx`

#### 5.2 Task Management Features
- **Work Planning**: Daily and weekly task planning
- **Resource Assignment**: Worker and equipment allocation
- **Progress Tracking**: Task completion monitoring
- **Quality Control**: Task review and approval workflows
- **Documentation**: Task-related document management

### 6. Site Permit Management (`/sites/{siteId}/permits`)

**Location**: `frontendv1/src/pages/PermitsPage.tsx`

#### 6.1 Permit Types
- **Hot Work Permits**: `frontendv1/src/pages/permits/HotWorkPermitDisplayPage.tsx`
- **Work at Height Permits**: `frontendv1/src/pages/permits/WorkAtHeightPermitDisplayPage.tsx`
- **Excavation Permits**: `frontendv1/src/pages/permits/ExcavationPermitDisplayPage.tsx`
- **Confined Space Permits**: Available in system
- **General Work Permits**: Standard work authorization

#### 6.2 Permit Workflow
- **Permit Request**: Site engineers request work permits
- **HSE Review**: Health, Safety & Environment team review
- **Approval Process**: Multi-level approval workflow
- **Permit Activation**: Work authorization and monitoring
- **Permit Closure**: Work completion and permit closure

### 7. Site Training Management (`/sites/{siteId}/training`)

**Location**: `frontendv1/src/pages/TrainingPage.tsx`

#### 7.1 Training Features
- **Training Schedule**: Site-specific training sessions
- **Competency Matrix**: Worker skill and certification tracking
- **Training Records**: Individual training history
- **Compliance Monitoring**: Training requirement tracking
- **Toolbox Talks**: Daily safety briefings

### 8. Site Time Management (`/sites/{siteId}/time`)

**Location**: `frontendv1/src/pages/TimeManagement.tsx`
**Documentation**: `frontendv1/docs/modules/time-management.md`

#### 8.1 Time Tracking Features
- **Daily Attendance**: Worker check-in/check-out
- **Time Sheets**: Weekly time recording
- **Overtime Management**: Overtime request and approval
- **Shift Patterns**: Flexible shift scheduling
- **Payroll Integration**: Time data for payroll processing

### 9. Site Forms Management (`/sites/{siteId}/forms`)

**Location**: `frontendv1/src/pages/FormsPage.tsx`

#### 9.1 Form Categories
- **Safety Forms**: Incident reports, safety inspections
- **Equipment Forms**: Maintenance logs, inspection checklists
- **Progress Forms**: Daily logs, progress reports
- **Quality Forms**: Quality control inspections

#### 9.2 Form Workflow
- **Form Selection**: Choose from available templates
- **Form Completion**: Mobile-optimized form filling
- **Submission**: Form submission and routing
- **Review Process**: Multi-level review and approval
- **Document Storage**: Completed form archival

### 10. Site Safety Management

#### 10.1 Incident Management
**Types**: `frontendv1/src/components/safety/types/safety.ts`
- **Incident Reporting**: Real-time incident reporting
- **Investigation Workflow**: Structured incident investigation
- **CAPA Management**: Corrective and Preventive Actions
- **Trend Analysis**: Incident pattern analysis

#### 10.2 Safety Observations
- **Observation Reporting**: Safety observation capture
- **Hazard Identification**: Proactive hazard reporting
- **Risk Assessment**: Site-specific risk evaluation
- **Safety Metrics**: Safety performance indicators

## Site Engineer Mobile Application

**Location**: `site-engineer-mobile/`
**Documentation**: `site-engineer-mobile/docs/dev/SITE_ENGINEER_APP_IMPLEMENTATION_GUIDE.md`

### Mobile App Features
- **Dashboard**: Site overview and key metrics
- **My Team**: Worker management and attendance
- **Task Planning**: Mobile task creation and management
- **Permit Requests**: Mobile permit request workflow
- **Reports**: Progress reporting and documentation
- **Weather Integration**: Weather-aware planning
- **Offline Capability**: Offline form completion and sync

## Navigation Structure

### Site-Specific Navigation
**Location**: `frontendv1/src/components/layout/sidebar/SidebarProvider.tsx`

**Site-Level Menu Items**:
1. **Overview** - Site dashboard and details
2. **Workers** - Worker management and attendance
3. **Tasks** - Task planning and execution
4. **Permits** - Work permit management
5. **Training** - Training and competency management
6. **Equipment** - Equipment and PPE management
7. **Time** - Time tracking and attendance
8. **Forms** - Form management and submissions
9. **Safety** - Safety management and reporting

### Context-Aware Features
- **Site Context Detection**: Automatic site identification from URL
- **Dynamic Menu**: Site-specific menu items and permissions
- **Breadcrumb Navigation**: Site-aware navigation breadcrumbs

## Site Creation and Configuration

### Site Creation Workflow
**Documentation**: `frontendv1/docs/workflows/site-creation-management-workflow.md`

#### Creation Steps
1. **Basic Information**: Site name, location, project details
2. **Area Configuration**: Define site areas and boundaries
3. **Safety Requirements**: Set safety standards and requirements
4. **Stakeholder Assignment**: Assign project managers and engineers
5. **Integration Setup**: Configure external system integrations
6. **Training Requirements**: Define site-specific training needs

#### OSM Integration
- **Map Integration**: OpenStreetMap integration for site mapping
- **Coordinate Acquisition**: Interactive map-based coordinate selection
- **Nearby Features**: Automatic detection of nearby infrastructure
- **Offline Capability**: Offline map tiles for remote sites

## Data Flow and Integration

### Master Data Inheritance
1. **Company Master Data**: Sites inherit company-defined master data
2. **Local Customization**: Sites can customize certain aspects
3. **Compliance Enforcement**: Company policies enforced at site level
4. **Data Synchronization**: Real-time sync with company systems

### Site-Specific Data
- **Operational Data**: Day-to-day operational information
- **Performance Metrics**: Site-specific KPIs and metrics
- **Local Configurations**: Site-specific settings and preferences
- **Historical Data**: Site activity and performance history

## Technical Implementation

### Site Context Management
**Hook**: `frontendv1/src/hooks/useSiteContext.ts`
- **Site Detection**: Automatic site identification from URL parameters
- **Context Switching**: Seamless switching between sites
- **State Management**: Site-specific state management

### Component Architecture
- **Site-Aware Components**: Components that adapt to site context
- **Shared Components**: Reusable components across sites
- **Site-Specific Routing**: Dynamic routing based on site context

### Data Types and Interfaces
**Location**: `frontendv1/src/types/index.ts`
- **SiteInfo Interface**: Core site data structure
- **Site Status Types**: Site health and status indicators
- **Site Metrics**: Performance and operational metrics

## Current Limitations and Future Enhancements

### Identified Gaps
1. **Real-Time Collaboration**: Limited real-time collaboration features
2. **Advanced Analytics**: Site-specific predictive analytics needed
3. **Mobile Optimization**: Some features need mobile optimization
4. **Integration Depth**: Deeper integration with external systems needed

### Planned Enhancements
1. **Enhanced Mobile Experience**: Improved mobile application features
2. **Real-Time Updates**: WebSocket-based real-time updates
3. **Advanced Reporting**: Custom site-specific reporting
4. **AI-Powered Insights**: Machine learning-based insights and recommendations

## API Endpoints

### Site Management
```http
GET /api/sites/{siteId}
PUT /api/sites/{siteId}
GET /api/sites/{siteId}/dashboard
GET /api/sites/{siteId}/metrics
```

### Site Operations
```http
GET /api/sites/{siteId}/workers
GET /api/sites/{siteId}/equipment
GET /api/sites/{siteId}/tasks
GET /api/sites/{siteId}/permits
GET /api/sites/{siteId}/forms
```

### Site Reporting
```http
GET /api/sites/{siteId}/reports/daily
GET /api/sites/{siteId}/reports/weekly
GET /api/sites/{siteId}/reports/safety
```

This documentation provides a comprehensive overview of the current site-level implementation. For company-level implementation details, refer to the companion document `company-level-implementation.md`.
