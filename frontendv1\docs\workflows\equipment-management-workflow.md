# Equipment Management Workflow

## Overview
This document outlines the comprehensive equipment management workflow covering company-owned equipment, rented equipment, and subcontractor equipment. The system focuses on safety compliance verification at task creation and approval stages, ensuring all equipment used on-site meets safety standards regardless of ownership.

## 1. Equipment Ownership & Management Structure

### 1.1 Equipment Categories by Ownership

```mermaid
flowchart TD
    A[Equipment Management] --> B[Company-Owned Equipment]
    A --> C[Rented Equipment]
    A --> D[Subcontractor Equipment]
    
    B --> E[Company Equipment Database]
    B --> F[Site Assignment Management]
    B --> G[Cross-Site Transfers]
    
    C --> H[Rental Verification]
    C --> I[Temporary Site Registration]
    C --> J[Rental Period Tracking]
    
    D --> K[Subcontractor Verification]
    D --> L[Compliance Documentation]
    D --> M[Task-Specific Approval]
```

#### Equipment Ownership Types:
```typescript
interface EquipmentOwnership {
  COMPANY_OWNED: {
    description: 'Equipment owned by the company';
    managementLevel: 'company_and_site';
    dataSync: 'bidirectional'; // Site changes sync to company
    complianceSource: 'company_database';
    transferCapability: 'cross_site_transfers_allowed';
  };
  
  RENTED: {
    description: 'Equipment rented for specific period/task';
    managementLevel: 'site_only';
    dataSync: 'none'; // Stays at site level
    complianceSource: 'rental_documentation';
    transferCapability: 'no_transfers';
    rentalPeriod: 'daily' | 'weekly' | 'monthly' | 'project_duration';
  };
  
  SUBCONTRACTOR: {
    description: 'Equipment owned by subcontractors';
    managementLevel: 'site_only';
    dataSync: 'none'; // Stays at site level
    complianceSource: 'subcontractor_documentation';
    transferCapability: 'no_transfers';
    approvalRequired: true;
  };
}
```

## 2. Company-Level Equipment Management

### 2.1 Company Equipment Registration

```mermaid
flowchart TD
    A[New Equipment Purchase] --> B[Register in Company Database]
    B --> C[Generate Equipment Number]
    C --> D[Upload Equipment Documents]
    D --> E[Set Compliance Requirements]
    E --> F[Initialize Maintenance Schedule]
    F --> G[Mark as Available for Assignment]
    G --> H[Company Equipment Ready]
```

#### Company Equipment Registration Process:
```typescript
const registerCompanyEquipment = async (equipmentData: CompanyEquipmentRegistration) => {
  // 1. Generate unique company equipment number
  const equipmentNumber = await generateEquipmentNumber(equipmentData.companyId, equipmentData.category);
  
  // 2. Create company equipment master record
  const equipment = await createCompanyEquipment({
    ...equipmentData,
    equipment_number: equipmentNumber,
    ownership_type: 'company_owned',
    overall_status: 'active',
    is_available_for_assignment: true,
    compliance_status: 'pending_initial_assessment'
  });
  
  // 3. Process equipment documents (manuals, warranties, certificates)
  if (equipmentData.documents?.length > 0) {
    await processEquipmentDocuments(equipment.id, equipmentData.documents);
  }
  
  // 4. Initialize compliance tracking
  await initializeComplianceTracking(equipment.id, equipmentData.safetyStandards);
  
  // 5. Set up maintenance schedule
  await setupMaintenanceSchedule(equipment.id, equipmentData.maintenanceSchedule);
  
  // 6. Create initial lifecycle event
  await createEquipmentLifecycleEvent({
    company_equipment_id: equipment.id,
    event_type: 'registration',
    event_date: new Date(),
    description: 'Equipment registered in company inventory',
    location: 'Company Headquarters',
    performed_by: equipmentData.created_by
  });
  
  return equipment;
};
```

### 2.2 Site Assignment for Company Equipment

```mermaid
flowchart TD
    A[Site Requests Equipment] --> B[Check Equipment Availability]
    B --> C{Equipment Available?}
    C -->|No| D[Show Alternative Options]
    C -->|Yes| E[Verify Compliance Status]
    E --> F{Compliance Valid?}
    F -->|No| G[Block Assignment - Show Issues]
    F -->|Yes| H[Create Site Assignment]
    H --> I[Update Equipment Status]
    I --> J[Sync to Site Database]
    J --> K[Equipment Available at Site]
```

## 3. Site-Level Equipment Management

### 3.1 Task Creation Equipment Selection

```mermaid
flowchart TD
    A[Create New Task] --> B[Define Equipment Requirements]
    B --> C[Equipment Selection Options]
    C --> D[Company Equipment Pool]
    C --> E[Add Rented Equipment]
    C --> F[Add Subcontractor Equipment]
    
    D --> G[Select from Available Company Equipment]
    E --> H[Enter Rental Equipment Details]
    F --> I[Enter Subcontractor Equipment Details]
    
    G --> J[Verify Company Equipment Compliance]
    H --> K[Verify Rental Equipment Documentation]
    I --> L[Verify Subcontractor Equipment Compliance]
    
    J --> M[Equipment Compliance Check]
    K --> M
    L --> M
    
    M --> N{All Equipment Compliant?}
    N -->|No| O[Show Compliance Issues]
    N -->|Yes| P[Approve Equipment for Task]
    P --> Q[Task Ready for Execution]
```

#### Equipment Selection for Tasks:
```typescript
const selectEquipmentForTask = async (taskId: string, equipmentSelections: EquipmentSelection[]) => {
  const taskEquipment = [];
  const complianceIssues = [];
  
  for (const selection of equipmentSelections) {
    let equipmentRecord;
    
    switch (selection.ownership_type) {
      case 'company_owned':
        equipmentRecord = await processCompanyEquipmentSelection(taskId, selection);
        break;
      case 'rented':
        equipmentRecord = await processRentalEquipmentSelection(taskId, selection);
        break;
      case 'subcontractor':
        equipmentRecord = await processSubcontractorEquipmentSelection(taskId, selection);
        break;
    }
    
    // Verify compliance for all equipment types
    const complianceCheck = await verifyEquipmentCompliance(equipmentRecord);
    
    if (!complianceCheck.compliant) {
      complianceIssues.push({
        equipment_id: equipmentRecord.id,
        equipment_name: equipmentRecord.name,
        issues: complianceCheck.issues
      });
    } else {
      taskEquipment.push(equipmentRecord);
    }
  }
  
  if (complianceIssues.length > 0) {
    return {
      success: false,
      compliance_issues: complianceIssues,
      message: 'Some equipment failed compliance verification'
    };
  }
  
  // All equipment compliant - approve for task
  await approveEquipmentForTask(taskId, taskEquipment);
  
  return {
    success: true,
    approved_equipment: taskEquipment,
    task_ready: true
  };
};
```

### 3.2 Rental Equipment Processing

```mermaid
flowchart TD
    A[Add Rental Equipment] --> B[Enter Rental Details]
    B --> C[Upload Rental Documentation]
    C --> D[Verify Insurance Coverage]
    D --> E[Check Safety Certifications]
    E --> F[Validate Inspection Records]
    F --> G{Documentation Complete?}
    G -->|No| H[Request Missing Documents]
    G -->|Yes| I[Create Site Equipment Record]
    I --> J[Set Rental Period Tracking]
    J --> K[Mark as Approved for Use]
```

#### Rental Equipment Registration:
```typescript
const registerRentalEquipment = async (siteId: string, rentalData: RentalEquipmentData) => {
  // 1. Validate rental documentation
  const documentationCheck = await validateRentalDocumentation(rentalData.documents);
  if (!documentationCheck.valid) {
    throw new Error(`Rental documentation incomplete: ${documentationCheck.missing_documents.join(', ')}`);
  }
  
  // 2. Create site-specific equipment record
  const siteEquipment = await createSiteEquipment({
    site_id: siteId,
    equipment_name: rentalData.name,
    equipment_type: rentalData.type,
    ownership_type: 'rented',
    rental_company: rentalData.rental_company,
    rental_start_date: rentalData.rental_start_date,
    rental_end_date: rentalData.rental_end_date,
    rental_cost_per_day: rentalData.daily_rate,
    rental_agreement_number: rentalData.agreement_number,
    
    // Safety & Compliance
    insurance_policy_number: rentalData.insurance_policy,
    insurance_expiry_date: rentalData.insurance_expiry,
    last_inspection_date: rentalData.last_inspection_date,
    next_inspection_due: rentalData.next_inspection_due,
    safety_certifications: rentalData.safety_certifications,
    
    // Status
    equipment_status: 'available',
    compliance_status: 'verified',
    is_approved_for_use: true
  });
  
  // 3. Store rental documentation
  await storeRentalDocuments(siteEquipment.id, rentalData.documents);
  
  // 4. Set up rental period monitoring
  await setupRentalPeriodTracking(siteEquipment.id, {
    start_date: rentalData.rental_start_date,
    end_date: rentalData.rental_end_date,
    daily_rate: rentalData.daily_rate,
    alert_days_before_expiry: 3
  });
  
  return siteEquipment;
};
```

### 3.3 Subcontractor Equipment Processing

```mermaid
flowchart TD
    A[Subcontractor Brings Equipment] --> B[Equipment Declaration Form]
    B --> C[Upload Equipment Certificates]
    C --> D[Verify Insurance Coverage]
    D --> E[Check Operator Certifications]
    E --> F[Validate Safety Compliance]
    F --> G[Site Safety Officer Review]
    G --> H{Approval Decision?}
    H -->|Rejected| I[Equipment Not Approved]
    H -->|Approved| J[Issue Equipment Approval]
    J --> K[Create Site Equipment Record]
    K --> L[Equipment Approved for Use]
```

#### Subcontractor Equipment Approval:
```typescript
const approveSubcontractorEquipment = async (siteId: string, subcontractorData: SubcontractorEquipmentData) => {
  // 1. Validate subcontractor documentation
  const validationResult = await validateSubcontractorEquipment(subcontractorData);
  if (!validationResult.approved) {
    return {
      approved: false,
      rejection_reasons: validationResult.issues,
      required_actions: validationResult.required_actions
    };
  }
  
  // 2. Create site equipment record for subcontractor equipment
  const siteEquipment = await createSiteEquipment({
    site_id: siteId,
    equipment_name: subcontractorData.name,
    equipment_type: subcontractorData.type,
    ownership_type: 'subcontractor',
    subcontractor_company: subcontractorData.subcontractor_company,
    subcontractor_contact: subcontractorData.contact_person,
    
    // Equipment Details
    manufacturer: subcontractorData.manufacturer,
    model: subcontractorData.model,
    serial_number: subcontractorData.serial_number,
    year_of_manufacture: subcontractorData.year_of_manufacture,
    
    // Compliance Documentation
    insurance_policy_number: subcontractorData.insurance_policy,
    insurance_expiry_date: subcontractorData.insurance_expiry,
    safety_certifications: subcontractorData.safety_certifications,
    last_inspection_date: subcontractorData.last_inspection_date,
    operator_certifications: subcontractorData.operator_certifications,
    
    // Approval Details
    approved_by: validationResult.approved_by,
    approval_date: new Date(),
    approval_conditions: validationResult.conditions,
    equipment_status: 'available',
    compliance_status: 'verified',
    is_approved_for_use: true
  });
  
  // 3. Store subcontractor documentation
  await storeSubcontractorDocuments(siteEquipment.id, subcontractorData.documents);
  
  // 4. Create approval record
  await createEquipmentApprovalRecord({
    site_equipment_id: siteEquipment.id,
    approval_type: 'subcontractor_equipment',
    approved_by: validationResult.approved_by,
    approval_date: new Date(),
    conditions: validationResult.conditions,
    valid_until: subcontractorData.project_end_date
  });
  
  return {
    approved: true,
    site_equipment_id: siteEquipment.id,
    approval_conditions: validationResult.conditions,
    valid_until: subcontractorData.project_end_date
  };
};
```

## 4. Safety Compliance Verification

### 4.1 Universal Compliance Checking

```mermaid
flowchart TD
    A[Equipment Compliance Check] --> B{Equipment Type?}
    B -->|Company Owned| C[Check Company Database]
    B -->|Rented| D[Check Rental Documentation]
    B -->|Subcontractor| E[Check Subcontractor Documentation]
    
    C --> F[Verify Certifications]
    D --> F
    E --> F
    
    F --> G[Check Insurance Coverage]
    G --> H[Validate Inspection Records]
    H --> I[Verify Operator Requirements]
    I --> J[Calculate Compliance Score]
    J --> K{Compliance Passed?}
    K -->|Yes| L[Approve for Use]
    K -->|No| M[Block Usage - Show Issues]
```

#### Universal Compliance Verification:
```typescript
const verifyEquipmentCompliance = async (equipment: SiteEquipment): Promise<ComplianceResult> => {
  const complianceChecks = [];
  
  // 1. Insurance verification
  const insuranceCheck = await verifyInsuranceCoverage(equipment);
  complianceChecks.push(insuranceCheck);
  
  // 2. Safety certifications
  const certificationCheck = await verifySafetyCertifications(equipment);
  complianceChecks.push(certificationCheck);
  
  // 3. Inspection records
  const inspectionCheck = await verifyInspectionRecords(equipment);
  complianceChecks.push(inspectionCheck);
  
  // 4. Operator requirements
  const operatorCheck = await verifyOperatorRequirements(equipment);
  complianceChecks.push(operatorCheck);
  
  // 5. Equipment-specific safety standards
  const safetyStandardsCheck = await verifySafetyStandards(equipment);
  complianceChecks.push(safetyStandardsCheck);
  
  // Calculate overall compliance
  const failedChecks = complianceChecks.filter(check => !check.passed);
  const criticalFailures = failedChecks.filter(check => check.severity === 'critical');
  
  const compliant = criticalFailures.length === 0;
  const complianceScore = ((complianceChecks.length - failedChecks.length) / complianceChecks.length) * 100;
  
  return {
    equipment_id: equipment.id,
    compliant: compliant,
    compliance_score: complianceScore,
    checks_performed: complianceChecks,
    failed_checks: failedChecks,
    critical_failures: criticalFailures,
    blocking_issues: criticalFailures.map(f => f.issue),
    recommendations: generateComplianceRecommendations(failedChecks),
    next_review_date: calculateNextReviewDate(equipment, complianceChecks)
  };
};
```

## 5. Data Synchronization & Management

### 5.1 Company Equipment Data Sync

```mermaid
flowchart TD
    A[Site Equipment Update] --> B{Equipment Type?}
    B -->|Company Owned| C[Update Site Record]
    B -->|Rented/Subcontractor| D[Update Site Record Only]
    
    C --> E[Sync Critical Data to Company]
    E --> F[Update Operating Hours]
    F --> G[Update Condition Status]
    G --> H[Update Maintenance Records]
    H --> I[Update Compliance Status]
    I --> J[Create Lifecycle Event]
    
    D --> K[Site-Only Update Complete]
    J --> L[Bidirectional Sync Complete]
```

#### Data Synchronization Process:
```typescript
const syncEquipmentData = async (siteEquipmentId: string, updateData: EquipmentUpdateData) => {
  const siteEquipment = await getSiteEquipment(siteEquipmentId);
  
  // 1. Update site equipment record
  await updateSiteEquipment(siteEquipmentId, updateData);
  
  // 2. Sync to company database if company-owned
  if (siteEquipment.ownership_type === 'company_owned') {
    const companyUpdates = extractCompanyRelevantUpdates(updateData);
    
    if (Object.keys(companyUpdates).length > 0) {
      await updateCompanyEquipment(siteEquipment.company_equipment_id, companyUpdates);
      
      // Create lifecycle event for significant changes
      if (companyUpdates.condition_status || companyUpdates.maintenance_date) {
        await createEquipmentLifecycleEvent({
          company_equipment_id: siteEquipment.company_equipment_id,
          event_type: companyUpdates.maintenance_date ? 'maintenance' : 'condition_update',
          event_date: new Date(),
          description: `Equipment updated at site: ${siteEquipment.site_id}`,
          location: siteEquipment.site_id,
          performed_by: updateData.updated_by,
          condition_before: siteEquipment.condition_status,
          condition_after: updateData.condition_status
        });
      }
    }
  }
  
  return {
    site_updated: true,
    company_synced: siteEquipment.ownership_type === 'company_owned',
    lifecycle_event_created: companyUpdates?.condition_status || companyUpdates?.maintenance_date
  };
};
```

## 6. Equipment Transfer Management

### 6.1 Company Equipment Cross-Site Transfers

```mermaid
flowchart TD
    A[Transfer Request Initiated] --> B[Validate Equipment Ownership]
    B --> C{Company Owned?}
    C -->|No| D[Transfer Not Allowed]
    C -->|Yes| E[Check Current Site Assignment]
    E --> F[Verify Equipment Status]
    F --> G[Create Transfer Request]
    G --> H[Approval Workflow]
    H --> I{All Approvals Received?}
    I -->|No| J[Wait for Approvals]
    I -->|Yes| K[Execute Transfer]
    K --> L[Update Site Assignments]
    L --> M[Create Transfer History]
    M --> N[Transfer Complete]
```

#### Equipment Transfer Process:
```typescript
const initiateEquipmentTransfer = async (transferData: EquipmentTransferRequest) => {
  // 1. Validate equipment can be transferred
  const equipment = await getCompanyEquipment(transferData.company_equipment_id);
  if (!equipment) {
    throw new Error('Equipment not found in company database');
  }

  const currentAssignment = await getCurrentSiteAssignment(transferData.company_equipment_id);
  if (!currentAssignment || currentAssignment.site_id !== transferData.from_site_id) {
    throw new Error('Equipment is not currently assigned to the specified source site');
  }

  // 2. Check equipment status allows transfer
  if (['maintenance', 'damaged', 'compliance-hold'].includes(currentAssignment.equipment_status)) {
    throw new Error(`Equipment cannot be transferred while in ${currentAssignment.equipment_status} status`);
  }

  // 3. Create transfer request
  const transferRequest = await createTransferRequest({
    ...transferData,
    status: 'pending_approval',
    requested_at: new Date(),
    approvals: await initializeTransferApprovals(transferData)
  });

  // 4. Notify approvers
  await notifyTransferApprovers(transferRequest.id);

  return {
    transfer_request_id: transferRequest.id,
    status: 'pending_approval',
    required_approvals: transferRequest.approvals.length,
    estimated_approval_time: '2-3 business days'
  };
};

const executeEquipmentTransfer = async (transferRequestId: string, executionData: TransferExecution) => {
  const transferRequest = await getTransferRequest(transferRequestId);

  // 1. Validate all approvals received
  const pendingApprovals = transferRequest.approvals.filter(a => a.status === 'pending');
  if (pendingApprovals.length > 0) {
    throw new Error('Cannot execute transfer with pending approvals');
  }

  // 2. End current site assignment
  await updateSiteAssignment(transferRequest.from_site_id, transferRequest.company_equipment_id, {
    assignment_status: 'completed',
    actual_return_date: executionData.transfer_date,
    final_condition: executionData.condition_before_transfer,
    final_hours_used: executionData.final_hours_used
  });

  // 3. Create new site assignment
  const newAssignment = await createSiteAssignment({
    company_equipment_id: transferRequest.company_equipment_id,
    site_id: transferRequest.to_site_id,
    assignment_date: executionData.transfer_date,
    assignment_type: 'transferred',
    assignment_reason: `Transfer from ${transferRequest.from_site_id}`,
    transferred_from_site: transferRequest.from_site_id,
    assigned_by: executionData.executed_by
  });

  // 4. Update company equipment record
  await updateCompanyEquipment(transferRequest.company_equipment_id, {
    current_site_id: transferRequest.to_site_id,
    total_operating_hours: executionData.updated_total_hours,
    condition_status: executionData.condition_after_transfer,
    last_transfer_date: executionData.transfer_date
  });

  // 5. Create transfer history and lifecycle event
  await Promise.all([
    createTransferHistory(transferRequest, executionData),
    createEquipmentLifecycleEvent({
      company_equipment_id: transferRequest.company_equipment_id,
      event_type: 'transfer',
      event_date: executionData.transfer_date,
      description: `Equipment transferred from ${transferRequest.from_site_id} to ${transferRequest.to_site_id}`,
      location: transferRequest.to_site_id,
      performed_by: executionData.executed_by,
      cost: executionData.transport_cost
    })
  ]);

  return {
    transfer_completed: true,
    new_assignment_id: newAssignment.id,
    equipment_location: transferRequest.to_site_id
  };
};
```

## 7. Compliance Monitoring & Alerts

### 7.1 Automated Compliance Monitoring

```mermaid
flowchart TD
    A[Daily Compliance Scan] --> B[Scan All Site Equipment]
    B --> C[Check Insurance Expiry]
    C --> D[Check Certification Expiry]
    D --> E[Check Inspection Due Dates]
    E --> F[Check Rental Periods]
    F --> G[Generate Compliance Report]
    G --> H[Identify Critical Issues]
    H --> I[Send Alerts]
    I --> J[Update Equipment Status]
    J --> K[Block Non-Compliant Equipment]
```

#### Compliance Monitoring System:
```typescript
const runDailyComplianceMonitoring = async () => {
  const today = new Date();
  const complianceIssues = [];

  // 1. Get all active site equipment
  const allSiteEquipment = await getAllActiveSiteEquipment();

  for (const equipment of allSiteEquipment) {
    const issues = [];

    // Check insurance expiry
    if (equipment.insurance_expiry_date) {
      const daysUntilExpiry = Math.ceil(
        (equipment.insurance_expiry_date.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
      );

      if (daysUntilExpiry < 0) {
        issues.push({
          type: 'insurance_expired',
          severity: 'critical',
          message: 'Insurance coverage has expired',
          blocks_operation: true
        });
      } else if (daysUntilExpiry <= 7) {
        issues.push({
          type: 'insurance_expiring',
          severity: 'high',
          message: `Insurance expires in ${daysUntilExpiry} days`,
          blocks_operation: false
        });
      }
    }

    // Check safety certifications
    for (const cert of equipment.safety_certifications || []) {
      if (cert.expiry_date) {
        const daysUntilExpiry = Math.ceil(
          (cert.expiry_date.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
        );

        if (daysUntilExpiry < 0) {
          issues.push({
            type: 'certification_expired',
            severity: 'critical',
            message: `${cert.name} certification has expired`,
            blocks_operation: true
          });
        }
      }
    }

    // Check inspection due dates
    if (equipment.next_inspection_due && equipment.next_inspection_due < today) {
      const daysOverdue = Math.ceil(
        (today.getTime() - equipment.next_inspection_due.getTime()) / (1000 * 60 * 60 * 24)
      );

      issues.push({
        type: 'inspection_overdue',
        severity: daysOverdue > 30 ? 'critical' : 'high',
        message: `Inspection is ${daysOverdue} days overdue`,
        blocks_operation: daysOverdue > 30
      });
    }

    // Check rental period expiry
    if (equipment.ownership_type === 'rented' && equipment.rental_end_date) {
      const daysUntilExpiry = Math.ceil(
        (equipment.rental_end_date.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
      );

      if (daysUntilExpiry <= 0) {
        issues.push({
          type: 'rental_expired',
          severity: 'high',
          message: 'Rental period has expired',
          blocks_operation: true
        });
      } else if (daysUntilExpiry <= 3) {
        issues.push({
          type: 'rental_expiring',
          severity: 'medium',
          message: `Rental expires in ${daysUntilExpiry} days`,
          blocks_operation: false
        });
      }
    }

    if (issues.length > 0) {
      complianceIssues.push({
        equipment_id: equipment.id,
        equipment_name: equipment.equipment_name,
        site_id: equipment.site_id,
        ownership_type: equipment.ownership_type,
        issues: issues
      });

      // Update equipment compliance status
      const criticalIssues = issues.filter(i => i.blocks_operation);
      await updateEquipmentComplianceStatus(equipment.id, {
        compliance_status: criticalIssues.length > 0 ? 'non_compliant' : 'warning',
        last_compliance_check: today,
        blocking_issues: criticalIssues,
        is_approved_for_use: criticalIssues.length === 0
      });
    }
  }

  // Send alerts for compliance issues
  await sendComplianceAlerts(complianceIssues);

  return {
    total_equipment_checked: allSiteEquipment.length,
    equipment_with_issues: complianceIssues.length,
    critical_issues: complianceIssues.filter(e =>
      e.issues.some(i => i.blocks_operation)
    ).length
  };
};
```

## 8. Equipment Performance & Analytics

### 8.1 Equipment Utilization Tracking

```mermaid
flowchart TD
    A[Equipment Usage Data] --> B[Track Operating Hours]
    B --> C[Monitor Task Assignments]
    C --> D[Calculate Utilization Rates]
    D --> E[Analyze Performance Metrics]
    E --> F[Generate Utilization Reports]
    F --> G[Identify Optimization Opportunities]
    G --> H[Recommend Actions]
```

#### Performance Analytics:
```typescript
const generateEquipmentPerformanceAnalytics = async (siteId: string, period: DateRange) => {
  // 1. Get equipment utilization data
  const utilizationData = await getEquipmentUtilization(siteId, period);

  // 2. Calculate performance metrics
  const performanceMetrics = {
    total_equipment: utilizationData.length,
    average_utilization_rate: calculateAverageUtilization(utilizationData),
    high_performers: utilizationData.filter(e => e.utilization_rate > 80),
    underutilized: utilizationData.filter(e => e.utilization_rate < 40),
    maintenance_costs: await calculateMaintenanceCosts(siteId, period),
    compliance_rate: await calculateComplianceRate(siteId)
  };

  // 3. Generate recommendations
  const recommendations = generateOptimizationRecommendations(performanceMetrics);

  return {
    period: period,
    site_id: siteId,
    metrics: performanceMetrics,
    recommendations: recommendations,
    cost_analysis: await generateCostAnalysis(siteId, period)
  };
};
```

## Key Equipment Management KPIs

### Operational Metrics:
- **Equipment Availability Rate**: Percentage of equipment available for use
- **Utilization Rate**: Percentage of time equipment is actively used
- **Compliance Rate**: Percentage of equipment meeting all safety requirements
- **Transfer Efficiency**: Success rate and time for equipment transfers

### Safety Metrics:
- **Compliance Score**: Overall safety compliance rating
- **Incident Rate**: Number of equipment-related incidents
- **Certification Currency**: Percentage of certifications up to date
- **Inspection Compliance**: Percentage of inspections completed on time

### Financial Metrics:
- **Cost per Operating Hour**: Total cost divided by operating hours
- **Rental vs. Purchase Analysis**: Cost comparison for equipment decisions
- **Maintenance Cost Ratio**: Maintenance costs as percentage of equipment value
- **ROI on Equipment Investments**: Return on investment for company equipment

### Alert Categories:
- **Critical**: Expired insurance, critical safety violations, equipment failures
- **High**: Expiring certifications, overdue inspections, rental period ending
- **Medium**: Upcoming renewals, maintenance due, utilization concerns
- **Low**: Performance optimization opportunities, cost savings suggestions

This comprehensive equipment management workflow ensures safety compliance across all equipment types while optimizing utilization and maintaining proper documentation for audit and regulatory purposes.
```
