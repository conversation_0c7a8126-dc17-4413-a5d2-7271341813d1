# Site Creation & Management Workflow

## Overview
This document outlines the comprehensive workflow for creating and managing construction sites, from initial project setup through operational readiness and ongoing management.

## 1. Site Creation Initialization

### 1.1 Project Initiation Workflow

```mermaid
flowchart TD
    A[Project Manager Initiates Site Creation] --> B[Select Site Template]
    B --> C[Initialize Creation Session]
    C --> D[Gather Basic Information]
    D --> E[Define Project Stakeholders]
    E --> F[Set Technical Specifications]
    F --> G[Configure Regulatory Requirements]
    G --> H[Setup Organizational Structure]
    H --> I[Plan Project Phases]
    I --> J[Configure Safety Requirements]
    J --> K[Plan Resource Allocation]
    K --> L[Setup Technology Integration]
    L --> M[Review & Validate]
    M --> N[Create Site]
```

#### Site Creation Session Management:
```typescript
const initializeSiteCreation = async (initData: SiteCreationInit) => {
  // 1. Create creation session
  const session = await createCreationSession({
    tenant_id: initData.tenantId,
    created_by: initData.createdBy,
    template_id: initData.templateId,
    expires_at: addHours(new Date(), 24) // 24-hour session
  });
  
  // 2. Load template defaults if provided
  let templateDefaults = {};
  if (initData.templateId) {
    const template = await getSiteTemplate(initData.templateId);
    templateDefaults = template.defaultValues;
  }
  
  // 3. Initialize step data with template defaults
  await initializeStepData(session.id, templateDefaults);
  
  // 4. Get available templates for user selection
  const availableTemplates = await getAvailableTemplates(initData.tenantId);
  
  return {
    session_id: session.id,
    current_step: 'basic-info',
    template_defaults: templateDefaults,
    available_templates: availableTemplates,
    expires_at: session.expires_at
  };
};
```

### 1.2 Step-by-Step Data Collection

```mermaid
flowchart TD
    A[Current Step] --> B[Validate Input Data]
    B --> C{Validation Passed?}
    C -->|No| D[Show Validation Errors]
    D --> E[Allow Override or Fix]
    E --> F[User Fixes Issues]
    F --> B
    C -->|Yes| G[Save Step Data]
    G --> H[Update Session Progress]
    H --> I[Determine Next Step]
    I --> J{More Steps?}
    J -->|Yes| K[Navigate to Next Step]
    J -->|No| L[Ready for Site Creation]
```

#### Step Data Processing:
```typescript
const processStepData = async (sessionId: string, stepId: string, stepData: any) => {
  // 1. Validate step data
  const validation = await validateStepData(stepId, stepData);
  
  if (!validation.isValid && !validation.canProceedWithWarnings) {
    return {
      status: 'validation_failed',
      errors: validation.errors,
      warnings: validation.warnings
    };
  }
  
  // 2. Save step data
  await saveStepData(sessionId, stepId, stepData);
  
  // 3. Update session progress
  await updateSessionProgress(sessionId, stepId);
  
  // 4. Determine next step
  const nextStep = await determineNextStep(sessionId, stepId);
  
  // 5. Check if ready for creation
  const canFinalize = await canFinalizeCreation(sessionId);
  
  return {
    status: 'saved',
    next_step: nextStep,
    can_finalize: canFinalize,
    warnings: validation.warnings || []
  };
};
```

## 2. Site Configuration Workflows

### 2.1 Basic Information Setup

```mermaid
flowchart TD
    A[Enter Site Details] --> B[Validate Site Code Uniqueness]
    B --> C{Code Available?}
    C -->|No| D[Suggest Alternative Codes]
    D --> E[User Selects New Code]
    E --> B
    C -->|Yes| F[Set Location Coordinates]
    F --> G[Define Project Classification]
    G --> H[Set Timeline & Milestones]
    H --> I[Assign Priority Level]
    I --> J[Basic Info Complete]
```

### 2.2 Site Areas & Locations Definition

```mermaid
flowchart TD
    A[Define Site Boundaries] --> B[Create Main Areas]
    B --> C[Add Sub-Areas/Zones]
    C --> D[Set Area Classifications]
    D --> E[Define Access Requirements]
    E --> F[Add Safety Zones]
    F --> G[Configure Area Permissions]
    G --> H[Set Equipment Zones]
    H --> I[Areas Definition Complete]
```

#### Site Areas Configuration Process:
```typescript
const configureSiteAreas = async (siteId: string, areasData: SiteAreasConfiguration) => {
  // 1. Define site boundaries and main layout
  const siteBoundaries = await defineSiteBoundaries({
    site_id: siteId,
    total_area: areasData.totalArea,
    boundary_coordinates: areasData.boundaryCoordinates,
    site_layout_plan: areasData.layoutPlanUrl,
    elevation_data: areasData.elevationData
  });

  // 2. Create main construction areas
  const mainAreas = [];
  for (const area of areasData.mainAreas) {
    const createdArea = await createSiteArea({
      site_id: siteId,
      area_name: area.name,
      area_code: area.code,
      area_type: area.type, // 'construction', 'storage', 'office', 'parking', 'safety'
      description: area.description,
      coordinates: area.coordinates,
      area_size: area.size,
      classification: area.classification, // 'restricted', 'controlled', 'public'
      safety_level: area.safetyLevel, // 'high_risk', 'medium_risk', 'low_risk'
      access_requirements: area.accessRequirements,
      ppe_requirements: area.ppeRequirements,
      equipment_allowed: area.equipmentAllowed,
      max_occupancy: area.maxOccupancy,
      environmental_conditions: area.environmentalConditions
    });

    mainAreas.push(createdArea);
  }

  // 3. Create sub-areas and zones within main areas
  for (const area of mainAreas) {
    if (areasData.subAreas[area.area_code]) {
      const subAreas = areasData.subAreas[area.area_code];

      for (const subArea of subAreas) {
        await createSiteSubArea({
          parent_area_id: area.id,
          site_id: siteId,
          sub_area_name: subArea.name,
          sub_area_code: subArea.code,
          description: subArea.description,
          coordinates: subArea.coordinates,
          specific_hazards: subArea.specificHazards,
          control_measures: subArea.controlMeasures,
          work_activities: subArea.workActivities,
          equipment_restrictions: subArea.equipmentRestrictions
        });
      }
    }
  }

  // 4. Configure area-specific safety requirements
  await configureSafetyZones(siteId, areasData.safetyZones);

  // 5. Set up area access control
  await setupAreaAccessControl(siteId, areasData.accessControl);

  return {
    site_boundaries: siteBoundaries,
    main_areas: mainAreas.length,
    total_sub_areas: Object.values(areasData.subAreas).flat().length,
    safety_zones_configured: areasData.safetyZones.length
  };
};
```

#### Site Areas Data Structure:
```typescript
interface SiteAreasConfiguration {
  totalArea: number; // in square meters
  boundaryCoordinates: GeoCoordinate[];
  layoutPlanUrl?: string;
  elevationData?: ElevationPoint[];

  mainAreas: Array<{
    name: string;
    code: string; // e.g., 'MAIN-CONST', 'STORAGE-A', 'OFFICE-BLOCK'
    type: 'construction' | 'storage' | 'office' | 'parking' | 'safety' | 'utility';
    description: string;
    coordinates: GeoCoordinate[];
    size: number; // square meters
    classification: 'restricted' | 'controlled' | 'public';
    safetyLevel: 'high_risk' | 'medium_risk' | 'low_risk';
    accessRequirements: string[];
    ppeRequirements: string[];
    equipmentAllowed: string[];
    maxOccupancy?: number;
    environmentalConditions?: string[];
  }>;

  subAreas: Record<string, Array<{
    name: string;
    code: string;
    description: string;
    coordinates: GeoCoordinate[];
    specificHazards: string[];
    controlMeasures: string[];
    workActivities: string[];
    equipmentRestrictions: string[];
  }>>;

  safetyZones: Array<{
    name: string;
    type: 'exclusion' | 'caution' | 'emergency_assembly' | 'first_aid';
    coordinates: GeoCoordinate[];
    radius?: number; // for circular zones
    restrictions: string[];
    emergencyProcedures?: string[];
  }>;

  accessControl: Array<{
    area_code: string;
    required_roles: string[];
    required_training: string[];
    required_ppe: string[];
    time_restrictions?: TimeRestriction[];
    approval_required: boolean;
  }>;
}

interface GeoCoordinate {
  latitude: number;
  longitude: number;
  elevation?: number;
  osm_node_id?: string; // OpenStreetMap node reference
  accuracy?: number; // GPS accuracy in meters
}

interface ElevationPoint extends GeoCoordinate {
  elevation: number;
  description?: string;
  elevation_source?: 'gps' | 'survey' | 'osm' | 'manual';
}

interface TimeRestriction {
  days: string[]; // ['monday', 'tuesday', ...]
  start_time: string; // '08:00'
  end_time: string; // '17:00'
}

interface OSMIntegration {
  map_provider: 'openstreetmap' | 'mapbox' | 'google';
  tile_server: string;
  geocoding_service: string;
  reverse_geocoding_enabled: boolean;
  offline_tiles_enabled: boolean;
  coordinate_system: 'WGS84' | 'UTM';
}
```

#### OpenStreetMap Integration for Coordinate Management:

```typescript
// OpenStreetMap Integration Service
class OSMCoordinateService {
  private osmConfig: OSMIntegration;
  private tileCache: Map<string, any> = new Map();

  constructor(config: OSMIntegration) {
    this.osmConfig = config;
  }

  // Get coordinates using interactive map selection
  async getCoordinatesFromMap(siteId: string): Promise<GeoCoordinate[]> {
    return new Promise((resolve) => {
      const mapInterface = this.initializeOSMMap({
        center: this.getDefaultCenter(), // Default to site location or country center
        zoom: 18, // High zoom for precise coordinate selection
        drawing_tools: true,
        coordinate_display: true
      });

      mapInterface.onPolygonComplete((coordinates: GeoCoordinate[]) => {
        // Validate coordinates and add OSM node references
        const validatedCoordinates = this.validateAndEnrichCoordinates(coordinates);
        resolve(validatedCoordinates);
      });
    });
  }

  // Geocoding: Convert address to coordinates
  async geocodeAddress(address: string, country: string = 'Kenya'): Promise<GeoCoordinate[]> {
    const geocodingUrl = `https://nominatim.openstreetmap.org/search`;
    const params = new URLSearchParams({
      q: `${address}, ${country}`,
      format: 'json',
      limit: '5',
      addressdetails: '1',
      bounded: '1',
      countrycodes: country === 'Kenya' ? 'ke' : 'us' // Support multiple countries
    });

    try {
      const response = await fetch(`${geocodingUrl}?${params}`);
      const results = await response.json();

      return results.map((result: any) => ({
        latitude: parseFloat(result.lat),
        longitude: parseFloat(result.lon),
        osm_node_id: result.osm_id,
        accuracy: this.calculateAccuracyFromBoundingBox(result.boundingbox),
        address_components: {
          display_name: result.display_name,
          road: result.address?.road,
          suburb: result.address?.suburb,
          city: result.address?.city,
          county: result.address?.county,
          country: result.address?.country
        }
      }));
    } catch (error) {
      console.error('Geocoding failed:', error);
      throw new Error('Unable to geocode address. Please try manual coordinate entry.');
    }
  }

  // Reverse geocoding: Convert coordinates to address
  async reverseGeocode(coordinate: GeoCoordinate): Promise<string> {
    const reverseUrl = `https://nominatim.openstreetmap.org/reverse`;
    const params = new URLSearchParams({
      lat: coordinate.latitude.toString(),
      lon: coordinate.longitude.toString(),
      format: 'json',
      addressdetails: '1'
    });

    try {
      const response = await fetch(`${reverseUrl}?${params}`);
      const result = await response.json();
      return result.display_name || `${coordinate.latitude}, ${coordinate.longitude}`;
    } catch (error) {
      return `${coordinate.latitude}, ${coordinate.longitude}`;
    }
  }

  // Get elevation data from OpenStreetMap or external service
  async getElevationData(coordinates: GeoCoordinate[]): Promise<ElevationPoint[]> {
    const elevationPromises = coordinates.map(async (coord) => {
      try {
        // Try OpenElevation API (free alternative)
        const response = await fetch('https://api.open-elevation.com/api/v1/lookup', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            locations: [{ latitude: coord.latitude, longitude: coord.longitude }]
          })
        });

        const data = await response.json();
        const elevation = data.results?.[0]?.elevation || 0;

        return {
          ...coord,
          elevation: elevation,
          elevation_source: 'api' as const,
          description: await this.reverseGeocode(coord)
        };
      } catch (error) {
        // Fallback to manual entry or default elevation
        return {
          ...coord,
          elevation: 1000, // Default elevation for Kenya (approximate)
          elevation_source: 'manual' as const,
          description: 'Elevation data unavailable'
        };
      }
    });

    return Promise.all(elevationPromises);
  }

  // Validate coordinates are within reasonable bounds
  private validateAndEnrichCoordinates(coordinates: GeoCoordinate[]): GeoCoordinate[] {
    return coordinates.map(coord => {
      // Validate latitude/longitude bounds
      if (coord.latitude < -90 || coord.latitude > 90) {
        throw new Error(`Invalid latitude: ${coord.latitude}`);
      }
      if (coord.longitude < -180 || coord.longitude > 180) {
        throw new Error(`Invalid longitude: ${coord.longitude}`);
      }

      // Add accuracy estimate based on coordinate precision
      const accuracy = this.estimateCoordinateAccuracy(coord);

      return {
        ...coord,
        accuracy,
        osm_node_id: this.generateOSMReference(coord)
      };
    });
  }

  private estimateCoordinateAccuracy(coord: GeoCoordinate): number {
    // Estimate accuracy based on decimal places
    const latStr = coord.latitude.toString();
    const lonStr = coord.longitude.toString();
    const latDecimals = latStr.includes('.') ? latStr.split('.')[1].length : 0;
    const lonDecimals = lonStr.includes('.') ? lonStr.split('.')[1].length : 0;
    const avgDecimals = (latDecimals + lonDecimals) / 2;

    // Rough accuracy estimation (meters)
    // 4 decimal places ≈ 11m accuracy
    // 5 decimal places ≈ 1.1m accuracy
    // 6 decimal places ≈ 0.11m accuracy
    return Math.max(1, Math.pow(10, 2 - avgDecimals));
  }

  private generateOSMReference(coord: GeoCoordinate): string {
    // Generate a reference ID for tracking
    return `osm_${Date.now()}_${Math.round(coord.latitude * 1000000)}_${Math.round(coord.longitude * 1000000)}`;
  }

  private calculateAccuracyFromBoundingBox(boundingBox: string[]): number {
    if (!boundingBox || boundingBox.length !== 4) return 100; // Default 100m accuracy

    const [south, north, west, east] = boundingBox.map(parseFloat);
    const latDiff = north - south;
    const lonDiff = east - west;

    // Convert to approximate meters (rough calculation)
    const avgLatDiff = latDiff * 111000; // 1 degree lat ≈ 111km
    const avgLonDiff = lonDiff * 111000 * Math.cos((north + south) / 2 * Math.PI / 180);

    return Math.max(avgLatDiff, avgLonDiff) / 2; // Return half the bounding box size as accuracy
  }

  private getDefaultCenter(): { lat: number; lon: number } {
    // Default to Nairobi, Kenya coordinates
    return { lat: -1.2921, lon: 36.8219 };
  }

  // Initialize interactive OSM map for coordinate selection
  private initializeOSMMap(config: any) {
    // This would integrate with Leaflet.js or similar mapping library
    return {
      onPolygonComplete: (callback: (coords: GeoCoordinate[]) => void) => {
        // Map interaction logic would be implemented here
        // For now, return mock implementation
        callback([]);
      }
    };
  }
}
```

#### Coordinate Acquisition Methods:

```typescript
// Multiple methods for acquiring site coordinates
const acquireSiteCoordinates = async (siteId: string, method: CoordinateAcquisitionMethod) => {
  const osmService = new OSMCoordinateService({
    map_provider: 'openstreetmap',
    tile_server: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
    geocoding_service: 'https://nominatim.openstreetmap.org',
    reverse_geocoding_enabled: true,
    offline_tiles_enabled: true,
    coordinate_system: 'WGS84'
  });

  switch (method) {
    case 'interactive_map':
      // Method 1: Interactive map selection
      return await osmService.getCoordinatesFromMap(siteId);

    case 'address_geocoding':
      // Method 2: Address-based geocoding
      const siteAddress = await getSiteAddress(siteId);
      return await osmService.geocodeAddress(siteAddress);

    case 'gps_survey':
      // Method 3: GPS survey data import
      return await importGPSSurveyData(siteId);

    case 'manual_entry':
      // Method 4: Manual coordinate entry with validation
      return await manualCoordinateEntry(siteId);

    case 'cad_import':
      // Method 5: Import from CAD/GIS files
      return await importFromCADFile(siteId);

    default:
      throw new Error(`Unsupported coordinate acquisition method: ${method}`);
  }
};

type CoordinateAcquisitionMethod =
  | 'interactive_map'
  | 'address_geocoding'
  | 'gps_survey'
  | 'manual_entry'
  | 'cad_import';
```

#### OpenStreetMap Integration Features:

```typescript
// Enhanced site areas with OSM integration
const createSiteAreaWithOSM = async (siteId: string, areaData: SiteAreaInput) => {
  const osmService = new OSMCoordinateService(osmConfig);

  // 1. Validate and enrich coordinates with OSM data
  const enrichedCoordinates = await Promise.all(
    areaData.coordinates.map(async (coord) => {
      const address = await osmService.reverseGeocode(coord);
      const elevation = await osmService.getElevationData([coord]);

      return {
        ...coord,
        address_description: address,
        elevation: elevation[0]?.elevation,
        osm_validated: true,
        validation_timestamp: new Date()
      };
    })
  );

  // 2. Calculate area size using OSM geometry
  const calculatedArea = calculatePolygonArea(enrichedCoordinates);

  // 3. Get nearby OSM features for context
  const nearbyFeatures = await getNearbyOSMFeatures(enrichedCoordinates[0], 500); // 500m radius

  // 4. Create site area with OSM integration
  const siteArea = await createSiteArea({
    ...areaData,
    coordinates: enrichedCoordinates,
    calculated_area: calculatedArea,
    osm_features: nearbyFeatures,
    coordinate_source: 'osm_integrated',
    last_coordinate_update: new Date()
  });

  return siteArea;
};

// Get nearby OpenStreetMap features for context
const getNearbyOSMFeatures = async (center: GeoCoordinate, radiusMeters: number) => {
  const overpassQuery = `
    [out:json][timeout:25];
    (
      way["building"](around:${radiusMeters},${center.latitude},${center.longitude});
      way["highway"](around:${radiusMeters},${center.latitude},${center.longitude});
      way["amenity"](around:${radiusMeters},${center.latitude},${center.longitude});
      node["emergency"](around:${radiusMeters},${center.latitude},${center.longitude});
    );
    out geom;
  `;

  try {
    const response = await fetch('https://overpass-api.de/api/interpreter', {
      method: 'POST',
      body: overpassQuery
    });

    const data = await response.json();

    return data.elements.map((element: any) => ({
      osm_id: element.id,
      type: element.type,
      tags: element.tags,
      geometry: element.geometry || element.lat && element.lon ?
        [{ lat: element.lat, lon: element.lon }] : [],
      relevance: determineFeatureRelevance(element.tags)
    }));
  } catch (error) {
    console.error('Failed to fetch nearby OSM features:', error);
    return [];
  }
};

// Calculate polygon area using coordinates
const calculatePolygonArea = (coordinates: GeoCoordinate[]): number => {
  if (coordinates.length < 3) return 0;

  let area = 0;
  const n = coordinates.length;

  for (let i = 0; i < n; i++) {
    const j = (i + 1) % n;
    area += coordinates[i].longitude * coordinates[j].latitude;
    area -= coordinates[j].longitude * coordinates[i].latitude;
  }

  area = Math.abs(area) / 2;

  // Convert from degrees to square meters (approximate)
  const metersPerDegree = 111000; // Approximate meters per degree
  return area * metersPerDegree * metersPerDegree;
};
```

#### Example Site Areas Configuration:
```typescript
const exampleSiteAreas: SiteAreasConfiguration = {
  totalArea: 50000, // 5 hectares
  boundaryCoordinates: [
    {
      latitude: -1.2921,
      longitude: 36.8219,
      elevation: 1795,
      osm_node_id: "osm_1704067200_-1292100_36821900",
      accuracy: 2.5 // GPS accuracy in meters
    },
    {
      latitude: -1.2925,
      longitude: 36.8225,
      elevation: 1798,
      osm_node_id: "osm_1704067201_-1292500_36822500",
      accuracy: 2.5
    },
    {
      latitude: -1.2930,
      longitude: 36.8220,
      elevation: 1802,
      osm_node_id: "osm_1704067202_-1293000_36822000",
      accuracy: 2.5
    },
    {
      latitude: -1.2926,
      longitude: 36.8214,
      elevation: 1799,
      osm_node_id: "osm_1704067203_-1292600_36821400",
      accuracy: 2.5
    }
  ],
  layoutPlanUrl: "https://storage.example.com/site-layouts/site-001-layout.pdf",
  osmIntegration: {
    map_provider: 'openstreetmap',
    tile_server: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
    geocoding_service: 'https://nominatim.openstreetmap.org',
    reverse_geocoding_enabled: true,
    offline_tiles_enabled: true,
    coordinate_system: 'WGS84'
  },
  coordinateAcquisitionMethod: 'interactive_map',
  nearbyOSMFeatures: [
    {
      osm_id: 'way_123456789',
      type: 'highway',
      tags: { highway: 'primary', name: 'Thika Road' },
      distance_meters: 150,
      relevance: 'high' // Affects site access
    },
    {
      osm_id: 'node_987654321',
      type: 'amenity',
      tags: { amenity: 'hospital', name: 'Kenyatta National Hospital' },
      distance_meters: 2500,
      relevance: 'medium' // Emergency services
    }
  ],

  mainAreas: [
    {
      name: "Main Construction Area",
      code: "MAIN-CONST",
      type: "construction",
      description: "Primary building construction zone",
      coordinates: [
        { latitude: -1.2922, longitude: 36.8220 },
        { latitude: -1.2924, longitude: 36.8223 },
        { latitude: -1.2926, longitude: 36.8221 },
        { latitude: -1.2924, longitude: 36.8218 }
      ],
      size: 15000,
      classification: "restricted",
      safetyLevel: "high_risk",
      accessRequirements: ["safety_induction", "site_access_card"],
      ppeRequirements: ["hard_hat", "safety_boots", "high_vis_vest", "safety_glasses"],
      equipmentAllowed: ["cranes", "excavators", "concrete_pumps", "scaffolding"],
      maxOccupancy: 150,
      environmentalConditions: ["noise_controlled", "dust_monitored"]
    },
    {
      name: "Material Storage Area",
      code: "STORAGE-A",
      type: "storage",
      description: "Primary material and equipment storage",
      coordinates: [
        { latitude: -1.2927, longitude: 36.8216 },
        { latitude: -1.2929, longitude: 36.8218 },
        { latitude: -1.2928, longitude: 36.8220 },
        { latitude: -1.2926, longitude: 36.8218 }
      ],
      size: 8000,
      classification: "controlled",
      safetyLevel: "medium_risk",
      accessRequirements: ["site_access_card", "storage_authorization"],
      ppeRequirements: ["hard_hat", "safety_boots", "high_vis_vest"],
      equipmentAllowed: ["forklifts", "mobile_cranes", "delivery_trucks"],
      maxOccupancy: 50
    },
    {
      name: "Site Office Complex",
      code: "OFFICE-BLOCK",
      type: "office",
      description: "Administrative and management offices",
      coordinates: [
        { latitude: -1.2923, longitude: 36.8215 },
        { latitude: -1.2924, longitude: 36.8217 },
        { latitude: -1.2922, longitude: 36.8218 },
        { latitude: -1.2921, longitude: 36.8216 }
      ],
      size: 2000,
      classification: "controlled",
      safetyLevel: "low_risk",
      accessRequirements: ["visitor_registration"],
      ppeRequirements: ["visitor_badge"],
      equipmentAllowed: ["office_equipment"],
      maxOccupancy: 80
    }
  ],

  subAreas: {
    "MAIN-CONST": [
      {
        name: "Foundation Zone",
        code: "FOUND-01",
        description: "Building foundation excavation and concrete work",
        coordinates: [
          { latitude: -1.2922, longitude: 36.8220 },
          { latitude: -1.2923, longitude: 36.8221 },
          { latitude: -1.2924, longitude: 36.8220 },
          { latitude: -1.2923, longitude: 36.8219 }
        ],
        specificHazards: ["excavation_collapse", "concrete_burns", "heavy_machinery"],
        controlMeasures: ["shoring_systems", "ppe_enforcement", "exclusion_zones"],
        workActivities: ["excavation", "concrete_pouring", "rebar_installation"],
        equipmentRestrictions: ["no_smoking", "authorized_operators_only"]
      },
      {
        name: "Structural Steel Zone",
        code: "STEEL-01",
        description: "Steel frame erection area",
        coordinates: [
          { latitude: -1.2924, longitude: 36.8221 },
          { latitude: -1.2925, longitude: 36.8222 },
          { latitude: -1.2926, longitude: 36.8221 },
          { latitude: -1.2925, longitude: 36.8220 }
        ],
        specificHazards: ["falling_objects", "height_work", "crane_operations"],
        controlMeasures: ["hard_barricades", "fall_protection", "crane_exclusion_zones"],
        workActivities: ["steel_erection", "welding", "crane_operations"],
        equipmentRestrictions: ["certified_crane_operators", "fall_protection_required"]
      }
    ],
    "STORAGE-A": [
      {
        name: "Hazardous Materials Storage",
        code: "HAZ-STOR-01",
        description: "Storage for hazardous construction materials",
        coordinates: [
          { latitude: -1.2927, longitude: 36.8216 },
          { latitude: -1.2928, longitude: 36.8217 },
          { latitude: -1.2927, longitude: 36.8218 },
          { latitude: -1.2926, longitude: 36.8217 }
        ],
        specificHazards: ["chemical_exposure", "fire_risk", "environmental_contamination"],
        controlMeasures: ["containment_systems", "fire_suppression", "spill_kits"],
        workActivities: ["material_handling", "inventory_management", "waste_disposal"],
        equipmentRestrictions: ["no_ignition_sources", "specialized_handling_equipment"]
      }
    ]
  },

  safetyZones: [
    {
      name: "Emergency Assembly Point",
      type: "emergency_assembly",
      coordinates: [{ latitude: -1.2921, longitude: 36.8215 }],
      radius: 50,
      restrictions: ["keep_clear", "emergency_access_only"],
      emergencyProcedures: ["evacuation_route_1", "headcount_procedure"]
    },
    {
      name: "Crane Exclusion Zone",
      type: "exclusion",
      coordinates: [
        { latitude: -1.2923, longitude: 36.8220 },
        { latitude: -1.2925, longitude: 36.8222 },
        { latitude: -1.2926, longitude: 36.8220 },
        { latitude: -1.2924, longitude: 36.8218 }
      ],
      restrictions: ["no_personnel_during_lifts", "authorized_access_only"],
      emergencyProcedures: ["crane_emergency_stop", "evacuation_procedure"]
    },
    {
      name: "First Aid Station",
      type: "first_aid",
      coordinates: [{ latitude: -1.2922, longitude: 36.8216 }],
      radius: 10,
      restrictions: ["keep_access_clear", "emergency_vehicles_only"],
      emergencyProcedures: ["first_aid_response", "emergency_services_contact"]
    }
  ],

  accessControl: [
    {
      area_code: "MAIN-CONST",
      required_roles: ["construction_worker", "site_supervisor", "hse_officer"],
      required_training: ["site_safety_induction", "construction_safety"],
      required_ppe: ["hard_hat", "safety_boots", "high_vis_vest", "safety_glasses"],
      time_restrictions: [
        {
          days: ["monday", "tuesday", "wednesday", "thursday", "friday"],
          start_time: "07:00",
          end_time: "18:00"
        }
      ],
      approval_required: false
    },
    {
      area_code: "HAZ-STOR-01",
      required_roles: ["hazmat_handler", "store_keeper", "hse_officer"],
      required_training: ["hazmat_handling", "spill_response", "fire_safety"],
      required_ppe: ["chemical_resistant_gloves", "respirator", "chemical_suit"],
      approval_required: true
    }
  ]
};
```

#### Areas Integration with Other Systems:

```typescript
const integrateAreasWithSystems = async (siteId: string, areas: SiteArea[]) => {
  // 1. Integration with Task Management
  await setupTaskAreaMapping(siteId, areas);

  // 2. Integration with Permit System
  await configurePermitAreaRequirements(siteId, areas);

  // 3. Integration with Safety Management
  await setupSafetyZoneMonitoring(siteId, areas);

  // 4. Integration with Equipment Management
  await configureEquipmentAreaRestrictions(siteId, areas);

  // 5. Integration with Time & Attendance
  await setupAreaBasedAttendance(siteId, areas);

  // 6. Integration with Training System
  await configureAreaSpecificTraining(siteId, areas);
};

// Task Management Integration
const setupTaskAreaMapping = async (siteId: string, areas: SiteArea[]) => {
  for (const area of areas) {
    await createTaskAreaMapping({
      site_id: siteId,
      area_code: area.area_code,
      allowed_task_types: area.workActivities,
      required_permits: area.permitRequirements,
      safety_requirements: area.safetyRequirements,
      equipment_restrictions: area.equipmentRestrictions
    });
  }
};

// Permit System Integration
const configurePermitAreaRequirements = async (siteId: string, areas: SiteArea[]) => {
  for (const area of areas) {
    if (area.classification === 'restricted' || area.safetyLevel === 'high_risk') {
      await createPermitAreaRequirement({
        site_id: siteId,
        area_code: area.area_code,
        permit_types_required: determineRequiredPermits(area),
        additional_approvals: area.approvalRequired ? ['hse_manager'] : [],
        special_conditions: area.specialConditions || []
      });
    }
  }
};

// Safety Management Integration
const setupSafetyZoneMonitoring = async (siteId: string, areas: SiteArea[]) => {
  for (const area of areas) {
    await createSafetyZoneMonitoring({
      site_id: siteId,
      area_code: area.area_code,
      monitoring_level: area.safetyLevel,
      hazard_types: area.specificHazards,
      control_measures: area.controlMeasures,
      inspection_frequency: determineSafetyInspectionFrequency(area.safetyLevel),
      incident_escalation: area.incidentEscalationProcedure
    });
  }
};
```

#### Areas-Based Access Control:

```typescript
const enforceAreaAccessControl = async (workerId: string, areaCode: string, siteId: string) => {
  // 1. Get area access requirements
  const area = await getSiteArea(siteId, areaCode);
  const accessControl = await getAreaAccessControl(siteId, areaCode);

  // 2. Verify worker role
  const workerRole = await getWorkerRole(workerId, siteId);
  if (!accessControl.required_roles.includes(workerRole)) {
    return {
      access_granted: false,
      reason: 'insufficient_role_permissions',
      required_roles: accessControl.required_roles
    };
  }

  // 3. Check training requirements
  const workerTraining = await getWorkerTraining(workerId);
  const missingTraining = accessControl.required_training.filter(
    training => !workerTraining.some(wt => wt.training_type === training && wt.status === 'valid')
  );

  if (missingTraining.length > 0) {
    return {
      access_granted: false,
      reason: 'missing_required_training',
      missing_training: missingTraining
    };
  }

  // 4. Verify PPE requirements
  const workerPPE = await getWorkerActivePPE(workerId, siteId);
  const missingPPE = accessControl.required_ppe.filter(
    ppe => !workerPPE.some(wp => wp.ppe_type === ppe && wp.status === 'assigned')
  );

  if (missingPPE.length > 0) {
    return {
      access_granted: false,
      reason: 'missing_required_ppe',
      missing_ppe: missingPPE
    };
  }

  // 5. Check time restrictions
  if (accessControl.time_restrictions) {
    const currentTime = new Date();
    const isWithinAllowedTime = checkTimeRestrictions(currentTime, accessControl.time_restrictions);

    if (!isWithinAllowedTime) {
      return {
        access_granted: false,
        reason: 'outside_allowed_hours',
        allowed_times: accessControl.time_restrictions
      };
    }
  }

  // 6. Check if approval is required
  if (accessControl.approval_required) {
    const hasApproval = await checkAreaAccessApproval(workerId, areaCode, siteId);
    if (!hasApproval) {
      return {
        access_granted: false,
        reason: 'approval_required',
        approval_process: 'contact_site_supervisor'
      };
    }
  }

  // 7. Log access attempt
  await logAreaAccess({
    worker_id: workerId,
    area_code: areaCode,
    site_id: siteId,
    access_time: new Date(),
    access_granted: true,
    verification_method: 'system_check'
  });

  return {
    access_granted: true,
    area_name: area.area_name,
    safety_briefing_required: area.safetyLevel === 'high_risk',
    emergency_procedures: area.emergencyProcedures
  };
};
```

#### User Interface for Coordinate Acquisition:

```typescript
// React component for interactive coordinate selection
const SiteAreaCoordinateSelector: React.FC<{
  onCoordinatesSelected: (coordinates: GeoCoordinate[]) => void;
  initialCenter?: GeoCoordinate;
}> = ({ onCoordinatesSelected, initialCenter }) => {
  const [mapInstance, setMapInstance] = useState<any>(null);
  const [drawingMode, setDrawingMode] = useState<'polygon' | 'rectangle' | 'circle'>('polygon');
  const [coordinates, setCoordinates] = useState<GeoCoordinate[]>([]);
  const [addressSearch, setAddressSearch] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);

  // Initialize OpenStreetMap with Leaflet
  useEffect(() => {
    const map = L.map('coordinate-map').setView([
      initialCenter?.latitude || -1.2921,
      initialCenter?.longitude || 36.8219
    ], 18);

    // Add OpenStreetMap tiles
    L.tileLayer('https://tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© OpenStreetMap contributors',
      maxZoom: 20
    }).addTo(map);

    // Add drawing controls
    const drawControl = new L.Control.Draw({
      draw: {
        polygon: {
          allowIntersection: false,
          showArea: true,
          metric: true
        },
        rectangle: {
          showArea: true,
          metric: true
        },
        circle: {
          showRadius: true,
          metric: true
        },
        marker: true,
        polyline: false,
        circlemarker: false
      }
    });

    map.addControl(drawControl);

    // Handle drawing events
    map.on(L.Draw.Event.CREATED, (event: any) => {
      const layer = event.layer;
      const coordinates = extractCoordinatesFromLayer(layer);
      setCoordinates(coordinates);
      onCoordinatesSelected(coordinates);
    });

    setMapInstance(map);

    return () => {
      map.remove();
    };
  }, [initialCenter]);

  // Address search functionality
  const handleAddressSearch = async () => {
    if (!addressSearch.trim()) return;

    try {
      const osmService = new OSMCoordinateService(osmConfig);
      const results = await osmService.geocodeAddress(addressSearch, 'Kenya');
      setSearchResults(results);
    } catch (error) {
      console.error('Address search failed:', error);
    }
  };

  // Jump to search result
  const jumpToLocation = (result: any) => {
    if (mapInstance) {
      mapInstance.setView([result.latitude, result.longitude], 18);

      // Add marker for the location
      L.marker([result.latitude, result.longitude])
        .addTo(mapInstance)
        .bindPopup(result.address_components?.display_name || 'Selected Location')
        .openPopup();
    }
  };

  return (
    <div className="coordinate-selector">
      {/* Address Search */}
      <div className="search-section">
        <div className="search-input-group">
          <input
            type="text"
            value={addressSearch}
            onChange={(e) => setAddressSearch(e.target.value)}
            placeholder="Search for address or landmark..."
            className="address-search-input"
          />
          <button onClick={handleAddressSearch} className="search-button">
            Search
          </button>
        </div>

        {/* Search Results */}
        {searchResults.length > 0 && (
          <div className="search-results">
            {searchResults.map((result, index) => (
              <div
                key={index}
                className="search-result-item"
                onClick={() => jumpToLocation(result)}
              >
                <div className="result-name">{result.address_components?.display_name}</div>
                <div className="result-coords">
                  {result.latitude.toFixed(6)}, {result.longitude.toFixed(6)}
                </div>
                <div className="result-accuracy">
                  Accuracy: ±{result.accuracy?.toFixed(1)}m
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Drawing Tools */}
      <div className="drawing-tools">
        <div className="tool-group">
          <label>Drawing Mode:</label>
          <select
            value={drawingMode}
            onChange={(e) => setDrawingMode(e.target.value as any)}
          >
            <option value="polygon">Polygon (Custom Shape)</option>
            <option value="rectangle">Rectangle</option>
            <option value="circle">Circle</option>
          </select>
        </div>

        <div className="coordinate-display">
          <h4>Selected Coordinates:</h4>
          {coordinates.map((coord, index) => (
            <div key={index} className="coordinate-item">
              <span>Point {index + 1}: </span>
              <span>{coord.latitude.toFixed(6)}, {coord.longitude.toFixed(6)}</span>
              {coord.elevation && <span> (Elevation: {coord.elevation}m)</span>}
              {coord.accuracy && <span> (±{coord.accuracy}m)</span>}
            </div>
          ))}
        </div>
      </div>

      {/* Map Container */}
      <div id="coordinate-map" className="map-container" style={{ height: '500px', width: '100%' }}>
        {/* Leaflet map will be rendered here */}
      </div>

      {/* Coordinate Input Methods */}
      <div className="input-methods">
        <div className="method-tabs">
          <button className="method-tab active">Interactive Map</button>
          <button className="method-tab">Manual Entry</button>
          <button className="method-tab">GPS Import</button>
          <button className="method-tab">CAD Import</button>
        </div>

        <div className="method-content">
          {/* Manual coordinate entry form would go here */}
          <div className="manual-entry">
            <h4>Manual Coordinate Entry</h4>
            <div className="coordinate-inputs">
              <input type="number" placeholder="Latitude" step="0.000001" />
              <input type="number" placeholder="Longitude" step="0.000001" />
              <input type="number" placeholder="Elevation (optional)" />
              <button>Add Point</button>
            </div>
          </div>
        </div>
      </div>

      {/* Validation and Actions */}
      <div className="validation-section">
        <div className="validation-status">
          {coordinates.length > 0 && (
            <div className="validation-info">
              <div>Area: {calculatePolygonArea(coordinates).toFixed(2)} m²</div>
              <div>Perimeter: {calculatePerimeter(coordinates).toFixed(2)} m</div>
              <div>Points: {coordinates.length}</div>
            </div>
          )}
        </div>

        <div className="action-buttons">
          <button
            onClick={() => setCoordinates([])}
            className="clear-button"
          >
            Clear All
          </button>
          <button
            onClick={() => onCoordinatesSelected(coordinates)}
            disabled={coordinates.length < 3}
            className="confirm-button"
          >
            Confirm Area
          </button>
        </div>
      </div>
    </div>
  );
};

// Helper functions
const extractCoordinatesFromLayer = (layer: any): GeoCoordinate[] => {
  if (layer instanceof L.Polygon || layer instanceof L.Rectangle) {
    return layer.getLatLngs()[0].map((latlng: any) => ({
      latitude: latlng.lat,
      longitude: latlng.lng,
      accuracy: 2.5, // Assume GPS accuracy
      osm_node_id: `osm_${Date.now()}_${Math.round(latlng.lat * 1000000)}_${Math.round(latlng.lng * 1000000)}`
    }));
  } else if (layer instanceof L.Circle) {
    const center = layer.getLatLng();
    const radius = layer.getRadius();
    // Convert circle to polygon approximation
    return generateCirclePolygon(center, radius);
  }
  return [];
};

const generateCirclePolygon = (center: any, radius: number): GeoCoordinate[] => {
  const points: GeoCoordinate[] = [];
  const numPoints = 32; // Number of points to approximate circle

  for (let i = 0; i < numPoints; i++) {
    const angle = (i * 2 * Math.PI) / numPoints;
    const lat = center.lat + (radius / 111000) * Math.cos(angle); // Rough conversion
    const lng = center.lng + (radius / (111000 * Math.cos(center.lat * Math.PI / 180))) * Math.sin(angle);

    points.push({
      latitude: lat,
      longitude: lng,
      accuracy: 2.5,
      osm_node_id: `osm_circle_${Date.now()}_${i}`
    });
  }

  return points;
};

const calculatePerimeter = (coordinates: GeoCoordinate[]): number => {
  if (coordinates.length < 2) return 0;

  let perimeter = 0;
  for (let i = 0; i < coordinates.length; i++) {
    const current = coordinates[i];
    const next = coordinates[(i + 1) % coordinates.length];

    // Haversine formula for distance between two points
    const R = 6371000; // Earth's radius in meters
    const dLat = (next.latitude - current.latitude) * Math.PI / 180;
    const dLon = (next.longitude - current.longitude) * Math.PI / 180;
    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(current.latitude * Math.PI / 180) * Math.cos(next.latitude * Math.PI / 180) *
              Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    const distance = R * c;

    perimeter += distance;
  }

  return perimeter;
};
```

### 2.4 Stakeholder Management

```mermaid
flowchart TD
    A[Define Client Information] --> B[Assign Design Team]
    B --> C[Set Main Contractor]
    C --> D[Add Subcontractors]
    D --> E[Assign Project Management]
    E --> F[Validate Licenses & Certifications]
    F --> G{All Stakeholders Valid?}
    G -->|No| H[Fix Invalid Entries]
    H --> F
    G -->|Yes| I[Setup Communication Protocols]
    I --> J[Stakeholder Setup Complete]
```

### 2.5 Regulatory & Compliance Setup

```mermaid
flowchart TD
    A[Upload Building Permits] --> B[Validate Permit Numbers]
    B --> C[Check Permit Expiry Dates]
    C --> D[Add Additional Permits]
    D --> E[Define Building Codes]
    E --> F[Set Safety Standards]
    F --> G[Configure Environmental Requirements]
    G --> H[Setup Compliance Monitoring]
    H --> I[Regulatory Setup Complete]
```

#### Permit Validation Process:
```typescript
const validatePermits = async (permits: PermitData[]) => {
  const validationResults = [];
  
  for (const permit of permits) {
    try {
      // 1. Check permit number format
      const formatValid = validatePermitFormat(permit.number, permit.type);
      
      // 2. Verify with issuing authority (if API available)
      const authorityCheck = await verifyWithAuthority(permit);
      
      // 3. Check expiry date
      const isExpired = permit.expiryDate < new Date();
      
      // 4. Check for duplicates
      const isDuplicate = await checkPermitDuplicate(permit.number);
      
      validationResults.push({
        permit_number: permit.number,
        permit_type: permit.type,
        is_valid: formatValid && !isExpired && !isDuplicate,
        status: isExpired ? 'expired' : 'active',
        authority_verified: authorityCheck.verified,
        warnings: generatePermitWarnings(permit, authorityCheck)
      });
      
    } catch (error) {
      validationResults.push({
        permit_number: permit.number,
        is_valid: false,
        error: error.message
      });
    }
  }
  
  return validationResults;
};
```

## 3. Operational Configuration

### 3.1 Safety & Training Requirements Setup

```mermaid
flowchart TD
    A[Define Site-Specific Training Requirements] --> B[Configure PPE Requirements by Area]
    B --> C[Setup Safety Protocols]
    C --> D[Define Incident Response Procedures]
    D --> E[Configure Emergency Contacts]
    E --> F[Setup Evacuation Plans]
    F --> G[Configure Safety Monitoring]
    G --> H[Safety Configuration Complete]
```

#### Site Training Requirements Configuration:
```typescript
const configureSiteTrainingRequirements = async (siteId: string, safetyConfig: SafetyConfiguration) => {
  // 1. Get base training requirements from site type
  const baseRequirements = await getBaseTrainingRequirements(safetyConfig.siteType);
  
  // 2. Add site-specific requirements
  const siteRequirements = [...baseRequirements, ...safetyConfig.additionalTrainings];
  
  // 3. Create site training requirements
  for (const requirement of siteRequirements) {
    await createSiteTrainingRequirement({
      site_id: siteId,
      training_module_id: requirement.trainingId,
      is_mandatory: requirement.isMandatory,
      applicable_roles: requirement.applicableRoles,
      validity_override: requirement.validityOverride
    });
  }
  
  // 4. Setup PPE requirements by area
  for (const ppeReq of safetyConfig.ppeRequirements) {
    await createSitePPERequirement({
      site_id: siteId,
      area_name: ppeReq.area,
      required_ppe: ppeReq.requiredPPE,
      optional_ppe: ppeReq.optionalPPE,
      inspection_frequency: ppeReq.inspectionFrequency
    });
  }
  
  // 5. Configure incident response procedures
  await configureSiteIncidentResponse(siteId, safetyConfig.incidentResponse);
  
  return {
    training_requirements_count: siteRequirements.length,
    ppe_areas_configured: safetyConfig.ppeRequirements.length,
    incident_categories: safetyConfig.incidentResponse.categories.length
  };
};
```

### 3.2 Technology Integration Setup

```mermaid
flowchart TD
    A[Configure Hikvision Devices] --> B[Test Device Connectivity]
    B --> C{All Devices Connected?}
    C -->|No| D[Fix Connection Issues]
    D --> B
    C -->|Yes| E[Setup Face Recognition Settings]
    E --> F[Configure Attendance Rules]
    F --> G[Setup System Integrations]
    G --> H[Configure Reporting]
    H --> I[Technology Setup Complete]
```

#### Hikvision Device Setup:
```typescript
const setupHikvisionDevices = async (siteId: string, deviceConfigs: HikvisionDeviceConfig[]) => {
  const setupResults = [];
  
  for (const config of deviceConfigs) {
    try {
      // 1. Test device connectivity
      const connectivity = await testDeviceConnectivity(config.ipAddress);
      if (!connectivity.success) {
        throw new Error(`Device connectivity failed: ${connectivity.error}`);
      }
      
      // 2. Register device in system
      const device = await createHikvisionDevice({
        site_id: siteId,
        device_id: config.deviceId,
        device_name: config.deviceName,
        device_ip: config.ipAddress,
        device_model: config.model,
        location_description: config.location,
        access_credentials: encryptCredentials(config.credentials),
        status: 'active'
      });
      
      // 3. Configure device settings
      await configureDeviceSettings(device.id, {
        face_recognition_threshold: config.faceRecognitionThreshold || 0.85,
        max_users: config.maxUsers || 1000,
        operating_hours: config.operatingHours,
        attendance_rules: config.attendanceRules
      });
      
      setupResults.push({
        device_id: config.deviceId,
        device_name: config.deviceName,
        status: 'success',
        connectivity_test: true
      });
      
    } catch (error) {
      setupResults.push({
        device_id: config.deviceId,
        device_name: config.deviceName,
        status: 'failed',
        error: error.message,
        connectivity_test: false
      });
    }
  }
  
  return {
    setup_results: setupResults,
    successful_devices: setupResults.filter(r => r.status === 'success').length,
    failed_devices: setupResults.filter(r => r.status === 'failed').length
  };
};
```

## 4. Site Creation Finalization

### 4.1 Final Validation & Creation

```mermaid
flowchart TD
    A[Final Validation Check] --> B{All Steps Valid?}
    B -->|No| C[Show Validation Summary]
    C --> D[User Fixes Issues]
    D --> A
    B -->|Yes| E[Create Site Record]
    E --> F[Initialize Database Schema]
    F --> G[Setup User Permissions]
    G --> H[Configure Integrations]
    H --> I[Queue Setup Tasks]
    I --> J[Send Notifications]
    J --> K[Site Created Successfully]
```

#### Site Creation Process:
```typescript
const finalizeSiteCreation = async (sessionId: string, overrides: string[] = []) => {
  // 1. Final validation of all step data
  const finalValidation = await performFinalValidation(sessionId, overrides);
  if (!finalValidation.canProceed) {
    return {
      success: false,
      blocking_issues: finalValidation.blockingIssues
    };
  }
  
  // 2. Aggregate all step data
  const siteData = await aggregateStepData(sessionId);
  
  // 3. Create site record
  const site = await createSiteRecord(siteData);
  
  // 4. Initialize site-specific components
  const initializationTasks = [
    initializeSiteDatabase(site.id),
    setupSitePermissions(site.id, siteData.stakeholders),
    configureSiteIntegrations(site.id, siteData.integrations),
    setupSiteTrainingRequirements(site.id, siteData.safety),
    initializeHikvisionDevices(site.id, siteData.devices)
  ];
  
  const initResults = await Promise.allSettled(initializationTasks);
  
  // 5. Queue post-creation setup tasks
  const setupTasks = await queueSetupTasks(site.id, siteData);
  
  // 6. Send notifications to stakeholders
  await sendSiteCreationNotifications(site, siteData.stakeholders);
  
  // 7. Clean up creation session
  await cleanupCreationSession(sessionId);
  
  return {
    success: true,
    site_id: site.id,
    initialization_results: initResults,
    setup_tasks: setupTasks,
    estimated_ready_date: calculateReadyDate(setupTasks)
  };
};
```

## 5. Post-Creation Setup Tasks

### 5.1 Immediate Setup Tasks

```mermaid
flowchart TD
    A[Site Created] --> B[Initialize Database Schema]
    B --> C[Setup User Access Permissions]
    C --> D[Configure Hikvision Devices]
    D --> E[Generate Site Documentation]
    E --> F[Create Safety Checklists]
    F --> G[Setup Monitoring Dashboards]
    G --> H[Immediate Tasks Complete]
```

### 5.2 24-Hour Setup Tasks

```mermaid
flowchart TD
    A[Immediate Tasks Complete] --> B[Notify All Stakeholders]
    B --> C[Schedule Initial Site Meeting]
    C --> D[Order Required Equipment]
    D --> E[Setup Compliance Monitoring]
    E --> F[Initialize Document Structure]
    F --> G[Configure Reporting Systems]
    G --> H[24-Hour Tasks Complete]
```

### 5.3 Weekly Setup Tasks

```mermaid
flowchart TD
    A[24-Hour Tasks Complete] --> B[Conduct Site Safety Assessment]
    B --> C[Complete Equipment Installation]
    C --> D[Train Site Personnel]
    D --> E[Establish Communication Protocols]
    E --> F[Begin Worker Enrollment Process]
    F --> G[Setup Ongoing Monitoring]
    G --> H[Site Fully Operational]
```

## 6. Site Management Operations

### 6.1 Ongoing Site Monitoring

```mermaid
flowchart TD
    A[Daily Site Operations] --> B[Monitor Compliance Status]
    B --> C[Track Project Progress]
    C --> D[Monitor Safety Metrics]
    D --> E[Check Equipment Status]
    E --> F[Review Worker Performance]
    F --> G[Update Stakeholders]
    G --> H[Generate Daily Reports]
```

### 6.2 Site Modification Workflow

```mermaid
flowchart TD
    A[Modification Request] --> B[Assess Impact]
    B --> C[Check Regulatory Requirements]
    C --> D[Update Permits if Needed]
    D --> E[Modify Site Configuration]
    E --> F[Update Training Requirements]
    F --> G[Notify Affected Workers]
    G --> H[Update Documentation]
    H --> I[Modification Complete]
```

## 7. Site Templates Management

### 7.1 Template Creation Workflow

```mermaid
flowchart TD
    A[Create New Template] --> B[Define Template Category]
    B --> C[Set Default Values]
    C --> D[Configure Required Fields]
    D --> E[Setup Validation Rules]
    E --> F[Define Default Resources]
    F --> G[Test Template]
    G --> H{Template Valid?}
    H -->|No| I[Fix Template Issues]
    I --> G
    H -->|Yes| J[Save Template]
    J --> K[Make Available to Users]
```

#### Template Management:
```typescript
const createSiteTemplate = async (templateData: TemplateCreationData) => {
  // 1. Validate template configuration
  const validation = await validateTemplateConfiguration(templateData);
  if (!validation.isValid) {
    throw new Error(`Template validation failed: ${validation.errors.join(', ')}`);
  }
  
  // 2. Create template record
  const template = await createTemplate({
    name: templateData.name,
    category: templateData.category,
    description: templateData.description,
    version: '1.0.0',
    created_by: templateData.createdBy,
    is_public: templateData.isPublic,
    default_values: templateData.defaultValues,
    required_fields: templateData.requiredFields,
    custom_validations: templateData.customValidations
  });
  
  // 3. Setup default resources
  await setupTemplateResources(template.id, templateData.defaultResources);
  
  // 4. Test template by creating a test site
  const testResult = await testTemplate(template.id);
  if (!testResult.success) {
    await deleteTemplate(template.id);
    throw new Error(`Template test failed: ${testResult.errors.join(', ')}`);
  }
  
  return {
    template_id: template.id,
    status: 'created',
    test_results: testResult
  };
};
```

## Key Site Management KPIs

### Creation Metrics:
- **Site Creation Success Rate**: Percentage of successful site creations
- **Average Creation Time**: Time from initiation to operational readiness
- **Template Usage Rate**: Adoption rate of site templates
- **Validation Error Rate**: Frequency of validation issues during creation

### Operational Metrics:
- **Site Compliance Rate**: Percentage of sites meeting all regulatory requirements
- **Setup Task Completion Rate**: Percentage of post-creation tasks completed on time
- **Stakeholder Satisfaction**: Feedback scores from project stakeholders
- **System Integration Success**: Success rate of technology integrations

### Performance Metrics:
- **Site Readiness Time**: Time from creation to first worker assignment
- **Configuration Accuracy**: Accuracy of initial site configurations
- **Change Request Frequency**: Number of modifications needed post-creation
- **Documentation Completeness**: Percentage of required documentation completed

## Alert & Notification System

### Critical Alerts:
- Site creation failures requiring immediate attention
- Permit expiry warnings (30, 14, 7 days before expiry)
- Safety configuration issues blocking worker assignments
- Device connectivity failures affecting operations

### Warning Alerts:
- Incomplete setup tasks approaching deadlines
- Stakeholder information requiring updates
- Template validation warnings during creation
- Integration issues affecting non-critical functions

### Information Alerts:
- Site creation milestones achieved
- Setup task completions
- Template usage statistics
- Monthly site performance reports

This comprehensive site creation and management workflow ensures systematic establishment of construction sites with proper configuration, compliance, and operational readiness.
