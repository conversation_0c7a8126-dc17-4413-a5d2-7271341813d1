# Time & Attendance Management Workflow

## Overview
This document outlines the comprehensive time and attendance management system workflow, covering worker enrollment to Hikvision devices, daily attendance tracking, real-time monitoring, and reporting systems.

## 1. Device Setup & Worker Enrollment

### 1.1 Hikvision Device Configuration

```mermaid
flowchart TD
    A[New Site Created] --> B[Install Hikvision Devices]
    B --> C[Configure Device Network Settings]
    C --> D[Register Device in System]
    D --> E[Set Device Location & Access Rules]
    E --> F[Test Device Connectivity]
    F --> G[Configure Face Recognition Settings]
    G --> H[Set Attendance Rules]
    H --> I[Device Ready for Worker Enrollment]
```

#### Device Registration Process:
```typescript
const registerHikvisionDevice = async (deviceData: DeviceRegistration) => {
  // 1. Validate device connectivity
  const connectivity = await testDeviceConnectivity(deviceData.deviceIP);
  if (!connectivity.success) {
    throw new Error(`Device connectivity failed: ${connectivity.error}`);
  }
  
  // 2. Register device in system
  const device = await createHikvisionDevice({
    site_id: deviceData.siteId,
    device_id: deviceData.deviceId,
    device_name: deviceData.deviceName,
    device_ip: deviceData.deviceIP,
    device_model: deviceData.model,
    location_description: deviceData.location,
    access_credentials: encryptCredentials(deviceData.credentials),
    status: 'active'
  });
  
  // 3. Configure device settings
  await configureDeviceSettings(device.id, {
    face_recognition_threshold: 0.85,
    max_users: 1000,
    attendance_rules: deviceData.attendanceRules
  });
  
  return device;
};
```

### 1.2 Worker Enrollment to Site Devices

```mermaid
flowchart TD
    A[Worker Assigned to Site] --> B[Check Worker Compliance]
    B --> C{Worker Compliant?}
    C -->|No| D[Block Enrollment - Show Issues]
    C -->|Yes| E[Validate Worker Photo]
    E --> F{Photo Available?}
    F -->|No| G[Request Photo Upload]
    G --> H[Upload & Process Photo]
    F -->|Yes| I[Get Site Devices]
    H --> I
    I --> J[Enroll to Each Device]
    J --> K[Generate Face Template]
    K --> L[Upload to Device]
    L --> M{Enrollment Successful?}
    M -->|No| N[Log Error & Retry]
    M -->|Yes| O[Update Sync Status]
    O --> P[Worker Ready for Attendance]
    N --> Q[Manual Intervention Required]
```

#### Worker Device Enrollment:
```typescript
const enrollWorkerToSiteDevices = async (siteId: string, workerId: string) => {
  // 1. Verify worker compliance
  const compliance = await checkWorkerCompliance(workerId);
  if (!compliance.eligible) {
    throw new ComplianceError(`Worker not compliant: ${compliance.blockingIssues.join(', ')}`);
  }
  
  // 2. Get worker data with photo
  const worker = await getWorkerWithPhoto(workerId);
  if (!worker.photo_url) {
    throw new Error('Worker photo required for face recognition enrollment');
  }
  
  // 3. Get all site devices
  const devices = await getHikvisionDevicesForSite(siteId);
  const enrollmentResults = [];
  
  // 4. Enroll to each device
  for (const device of devices) {
    try {
      // Create user on device
      const deviceUser = await hikvisionAPI.createUser({
        deviceIP: device.device_ip,
        credentials: device.access_credentials,
        userData: {
          name: `${worker.first_name} ${worker.last_name}`,
          employeeNo: worker.employee_number,
          userType: 'normal',
          accessLevel: determineAccessLevel(worker.trades)
        }
      });
      
      // Upload face template
      await hikvisionAPI.uploadFaceTemplate({
        deviceIP: device.device_ip,
        userId: deviceUser.userId,
        faceImage: worker.photo_url
      });
      
      // Record successful sync
      await createDeviceUserSync({
        hikvision_device_id: device.id,
        worker_id: workerId,
        device_user_id: deviceUser.userId,
        face_template_uploaded: true,
        sync_status: 'synced',
        access_level: determineAccessLevel(worker.trades)
      });
      
      enrollmentResults.push({
        device_id: device.device_id,
        status: 'success',
        device_user_id: deviceUser.userId
      });
      
    } catch (error) {
      // Record failed sync
      await createDeviceUserSync({
        hikvision_device_id: device.id,
        worker_id: workerId,
        sync_status: 'failed',
        sync_error_message: error.message
      });
      
      enrollmentResults.push({
        device_id: device.device_id,
        status: 'failed',
        error: error.message
      });
    }
  }
  
  return { enrollment_results: enrollmentResults };
};
```

## 2. Daily Attendance Tracking

### 2.1 Real-Time Attendance Processing

```mermaid
flowchart TD
    A[Worker Approaches Device] --> B[Face Recognition Scan]
    B --> C{Face Recognized?}
    C -->|No| D[Access Denied - Log Unknown Face]
    C -->|Yes| E[Identify Worker]
    E --> F[Check Current Attendance Status]
    F --> G{Already Checked In Today?}
    G -->|No| H[Process Check-In]
    G -->|Yes| I[Process Check-Out]
    H --> J[Record Check-In Time]
    I --> K[Record Check-Out Time]
    J --> L[Calculate Work Hours]
    K --> L
    L --> M[Update Attendance Record]
    M --> N[Send Real-Time Notification]
    N --> O[Update Dashboard]
    D --> P[Security Alert]
```

#### Hikvision Event Processing:
```typescript
const processHikvisionEvent = async (eventData: HikvisionEventData) => {
  // 1. Find the device
  const device = await getHikvisionDeviceByDeviceId(eventData.deviceId);
  if (!device) {
    console.warn('Event from unknown device:', eventData.deviceId);
    return;
  }
  
  // 2. Log the raw event
  await logDeviceEvent({
    hikvision_device_id: device.id,
    event_type: eventData.eventType,
    event_timestamp: new Date(eventData.timestamp),
    device_event_id: eventData.eventId,
    face_recognition_confidence: eventData.confidence,
    raw_event_data: eventData,
    processed: false
  });
  
  // 3. Process based on event type
  switch (eventData.eventType) {
    case 'ACCESS_GRANTED':
      await processAccessGranted(device, eventData);
      break;
    case 'ACCESS_DENIED':
      await processAccessDenied(device, eventData);
      break;
    case 'UNKNOWN_FACE':
      await processUnknownFace(device, eventData);
      break;
  }
  
  // 4. Mark event as processed
  await markEventProcessed(eventData.eventId);
};

const processAccessGranted = async (device: HikvisionDevice, eventData: HikvisionEventData) => {
  // 1. Find worker by device user ID
  const userSync = await getDeviceUserSyncByDeviceUserId(device.id, eventData.userId);
  if (!userSync) {
    console.warn('Access granted for unknown user:', eventData.userId);
    return;
  }
  
  // 2. Determine if this is check-in or check-out
  const today = new Date().toISOString().split('T')[0];
  const existingAttendance = await getDailyAttendance(device.site_id, userSync.worker_id, today);
  
  const eventTime = new Date(eventData.timestamp).toTimeString().split(' ')[0];
  
  if (!existingAttendance || !existingAttendance.check_in_time) {
    // This is a check-in
    await processCheckIn(device, userSync, eventData, eventTime);
  } else if (!existingAttendance.check_out_time) {
    // This is a check-out
    await processCheckOut(device, userSync, existingAttendance, eventData, eventTime);
  }
};
```

### 2.2 Check-In Process

```mermaid
flowchart TD
    A[Check-In Event] --> B[Validate Worker Assignment]
    B --> C{Worker Assigned to Site?}
    C -->|No| D[Block Access - Not Assigned]
    C -->|Yes| E[Check Compliance Status]
    E --> F{Worker Compliant?}
    F -->|No| G[Block Access - Non-Compliant]
    F -->|Yes| H[Record Check-In]
    H --> I[Calculate Expected Check-Out]
    I --> J[Send Check-In Notification]
    J --> K[Update Real-Time Dashboard]
    K --> L[Log Successful Check-In]
```

#### Check-In Processing:
```typescript
const processCheckIn = async (
  device: HikvisionDevice, 
  userSync: DeviceUserSync, 
  eventData: HikvisionEventData, 
  eventTime: string
) => {
  // 1. Validate worker assignment
  const assignment = await getActiveSiteAssignment(device.site_id, userSync.worker_id);
  if (!assignment) {
    await logAccessViolation(userSync.worker_id, 'NOT_ASSIGNED_TO_SITE');
    return;
  }
  
  // 2. Check compliance status
  const compliance = await checkWorkerCompliance(userSync.worker_id);
  if (!compliance.eligible) {
    await logAccessViolation(userSync.worker_id, 'NON_COMPLIANT', compliance.blockingIssues);
    return;
  }
  
  // 3. Create attendance record
  const today = new Date().toISOString().split('T')[0];
  const attendance = await createAttendanceRecord({
    site_id: device.site_id,
    worker_id: userSync.worker_id,
    hikvision_device_id: device.device_id,
    attendance_date: today,
    check_in_time: eventTime,
    face_recognition_confidence: eventData.confidence,
    device_event_id: eventData.eventId,
    status: 'present',
    sync_status: 'synced'
  });
  
  // 4. Send real-time notification
  await broadcastAttendanceEvent(device.site_id, {
    type: 'check_in',
    worker_name: userSync.worker.name,
    employee_number: userSync.worker.employee_number,
    timestamp: eventData.timestamp,
    device_location: device.location_description,
    confidence: eventData.confidence
  });
  
  return attendance;
};
```

### 2.3 Check-Out Process

```mermaid
flowchart TD
    A[Check-Out Event] --> B[Find Existing Check-In]
    B --> C{Check-In Found?}
    C -->|No| D[Log Error - No Check-In]
    C -->|Yes| E[Calculate Total Hours]
    E --> F[Calculate Overtime]
    F --> G[Update Attendance Record]
    G --> H[Check for Anomalies]
    H --> I{Hours Anomaly?}
    I -->|Yes| J[Flag for Review]
    I -->|No| K[Send Check-Out Notification]
    J --> L[Notify Supervisor]
    K --> M[Update Dashboard]
    L --> M
    M --> N[Generate Daily Summary]
```

#### Check-Out Processing:
```typescript
const processCheckOut = async (
  device: HikvisionDevice,
  userSync: DeviceUserSync,
  existingAttendance: AttendanceRecord,
  eventData: HikvisionEventData,
  eventTime: string
) => {
  // 1. Calculate work hours
  const totalHours = calculateHours(existingAttendance.check_in_time, eventTime);
  const overtimeHours = Math.max(totalHours - 8, 0);
  
  // 2. Check for anomalies
  const anomalies = detectAttendanceAnomalies(totalHours, existingAttendance.check_in_time, eventTime);
  
  // 3. Update attendance record
  const updatedAttendance = await updateAttendanceRecord(existingAttendance.id, {
    check_out_time: eventTime,
    total_hours: totalHours,
    overtime_hours: overtimeHours,
    status: 'completed',
    anomalies: anomalies.length > 0 ? anomalies : null,
    requires_review: anomalies.length > 0
  });
  
  // 4. Send notifications
  await broadcastAttendanceEvent(device.site_id, {
    type: 'check_out',
    worker_name: userSync.worker.name,
    employee_number: userSync.worker.employee_number,
    timestamp: eventData.timestamp,
    device_location: device.location_description,
    total_hours: totalHours,
    overtime_hours: overtimeHours,
    anomalies: anomalies
  });
  
  // 5. If anomalies detected, notify supervisor
  if (anomalies.length > 0) {
    await notifySupervisorOfAnomalies(device.site_id, updatedAttendance, anomalies);
  }
  
  return updatedAttendance;
};
```

## 3. Manual Attendance Management

### 3.1 Manual Entry Process

```mermaid
flowchart TD
    A[Manual Entry Request] --> B[Validate Requester Authority]
    B --> C{Authorized?}
    C -->|No| D[Deny Request]
    C -->|Yes| E[Validate Worker Assignment]
    E --> F[Check for Existing Record]
    F --> G{Record Exists?}
    G -->|Yes| H[Prevent Duplicate Entry]
    G -->|No| I[Create Manual Entry]
    I --> J[Calculate Hours]
    J --> K[Flag for Approval]
    K --> L[Notify Supervisor]
    L --> M[Log Manual Entry]
```

#### Manual Entry Implementation:
```typescript
const createManualAttendanceEntry = async (manualEntryData: ManualAttendanceEntry) => {
  const { site_id, worker_id, attendance_date, check_in_time, check_out_time, reason, entered_by } = manualEntryData;
  
  // 1. Verify requester authority
  const requester = await getUserById(entered_by);
  if (!hasManualEntryPermission(requester, site_id)) {
    throw new Error('Insufficient permissions for manual entry');
  }
  
  // 2. Verify worker assignment
  const assignment = await getSiteWorkerAssignment(site_id, worker_id);
  if (!assignment) {
    throw new Error('Worker not assigned to this site');
  }
  
  // 3. Check for existing record
  const existingRecord = await getDailyAttendance(site_id, worker_id, attendance_date);
  if (existingRecord) {
    throw new Error('Attendance record already exists for this date');
  }
  
  // 4. Calculate hours
  const totalHours = check_out_time ? calculateHours(check_in_time, check_out_time) : null;
  const overtimeHours = totalHours ? Math.max(totalHours - 8, 0) : null;
  
  // 5. Create manual entry
  const attendance = await createAttendanceRecord({
    site_id,
    worker_id,
    attendance_date,
    check_in_time,
    check_out_time,
    total_hours: totalHours,
    overtime_hours: overtimeHours,
    status: check_out_time ? 'completed' : 'present',
    sync_status: 'manual_entry',
    notes: `Manual entry: ${reason}. Entered by: ${requester.name}`,
    requires_approval: true
  });
  
  // 6. Log manual entry
  await logManualAttendanceEntry({
    attendance_id: attendance.id,
    entered_by,
    reason,
    timestamp: new Date()
  });
  
  // 7. Notify supervisor for approval
  await notifySupervisorForApproval(site_id, attendance);
  
  return attendance;
};
```

## 4. Attendance Analytics & Reporting

### 4.1 Daily Attendance Dashboard

```mermaid
flowchart TD
    A[Dashboard Request] --> B[Get Site Workers]
    B --> C[Fetch Today's Attendance]
    C --> D[Calculate Summary Statistics]
    D --> E[Identify Anomalies]
    E --> F[Get Device Status]
    F --> G[Generate Real-Time Metrics]
    G --> H[Create Dashboard Data]
    H --> I[Send to Frontend]
```

#### Dashboard Data Generation:
```typescript
const getSiteDailyAttendance = async (siteId: string, date: string) => {
  // 1. Get all workers assigned to site
  const siteWorkers = await getActiveSiteWorkers(siteId);
  
  // 2. Get attendance records for the date
  const attendanceRecords = await getDailyAttendanceRecords(siteId, date);
  
  // 3. Calculate summary statistics
  const summary = {
    total_assigned: siteWorkers.length,
    present: attendanceRecords.filter(r => r.check_in_time).length,
    absent: siteWorkers.length - attendanceRecords.filter(r => r.check_in_time).length,
    late_arrivals: attendanceRecords.filter(r => 
      r.check_in_time && r.check_in_time > '08:00:00'
    ).length,
    early_departures: attendanceRecords.filter(r => 
      r.check_out_time && r.check_out_time < '17:00:00'
    ).length,
    total_hours: attendanceRecords.reduce((sum, r) => sum + (r.total_hours || 0), 0),
    overtime_hours: attendanceRecords.reduce((sum, r) => sum + (r.overtime_hours || 0), 0),
    anomalies: attendanceRecords.filter(r => r.requires_review).length
  };
  
  // 4. Get device status
  const devices = await getHikvisionDevicesForSite(siteId);
  const deviceStatus = await Promise.all(
    devices.map(async (device) => ({
      device_name: device.device_name,
      status: device.status,
      last_event: await getLastDeviceEvent(device.id),
      connectivity: await checkDeviceConnectivity(device.device_ip)
    }))
  );
  
  // 5. Identify workers needing attention
  const workersNeedingAttention = identifyWorkersNeedingAttention(siteWorkers, attendanceRecords);
  
  return {
    site: await getSite(siteId),
    date,
    summary,
    attendance_records: attendanceRecords,
    device_status: deviceStatus,
    workers_needing_attention: workersNeedingAttention
  };
};
```

### 4.2 Attendance Reporting

```mermaid
flowchart TD
    A[Report Request] --> B{Report Type?}
    B -->|Daily| C[Generate Daily Report]
    B -->|Weekly| D[Generate Weekly Summary]
    B -->|Monthly| E[Generate Monthly Analytics]
    B -->|Payroll| F[Generate Payroll Export]
    C --> G[Format Report Data]
    D --> G
    E --> G
    F --> G
    G --> H[Apply Filters & Grouping]
    H --> I[Generate Document]
    I --> J[Send to Recipients]
```

## 5. Real-Time Monitoring & Alerts

### 5.1 Live Attendance Monitoring

```mermaid
flowchart TD
    A[Attendance Event] --> B[Process Event]
    B --> C[Update Real-Time Cache]
    C --> D[Check Alert Conditions]
    D --> E{Alert Triggered?}
    E -->|Yes| F[Send Alert]
    E -->|No| G[Update Dashboard]
    F --> H[Log Alert]
    H --> G
    G --> I[Broadcast to WebSocket Clients]
```

#### Real-Time Event Broadcasting:
```typescript
const broadcastAttendanceEvent = async (siteId: string, eventData: AttendanceEvent) => {
  // 1. Update real-time cache
  await updateAttendanceCache(siteId, eventData);
  
  // 2. Check for alert conditions
  const alerts = await checkAttendanceAlerts(siteId, eventData);
  
  // 3. Send alerts if needed
  for (const alert of alerts) {
    await sendAttendanceAlert(alert);
  }
  
  // 4. Broadcast to WebSocket clients
  const websocketData = {
    type: 'attendance_event',
    site_id: siteId,
    data: eventData,
    alerts: alerts,
    timestamp: new Date()
  };
  
  await broadcastToSiteClients(siteId, websocketData);
  
  // 5. Update dashboard metrics
  await updateDashboardMetrics(siteId);
};
```

## Key Attendance Management KPIs

### Operational Metrics:
- **Daily Attendance Rate**: Percentage of assigned workers present
- **Punctuality Rate**: Percentage of on-time arrivals
- **Average Work Hours**: Average daily work hours per worker
- **Overtime Utilization**: Percentage of workers working overtime
- **Device Uptime**: Percentage of time devices are operational

### Quality Metrics:
- **Data Accuracy**: Percentage of attendance records without anomalies
- **Manual Entry Rate**: Percentage of manually entered records
- **Alert Response Time**: Average time to resolve attendance alerts
- **Compliance Rate**: Percentage of workers with valid site access

### Alert Configurations:
- **Critical**: Device offline, unauthorized access attempts
- **Warning**: High absenteeism, excessive overtime, attendance anomalies
- **Information**: Daily summaries, weekly reports, device maintenance due

This comprehensive time and attendance workflow ensures accurate tracking, real-time monitoring, and efficient management of workforce attendance across all sites.
