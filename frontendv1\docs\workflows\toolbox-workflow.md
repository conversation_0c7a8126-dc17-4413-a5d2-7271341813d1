# Toolbox Workflow Documentation

## Overview

The Toolbox system is a **communication tool** that briefs workers on the hazards and control measures for tasks being performed each day. It serves as the bridge between approved task risk assessments and daily work execution, ensuring that all safety information is effectively communicated to the workforce.

## Core Principles

1. **Communication Tool Only**: Toolbox does not create or modify hazards - it communicates pre-approved task information
2. **Task-Centric Data Source**: All hazard and control measure information comes from approved tasks
3. **Daily Snapshot**: Creates a point-in-time view of approved tasks for the day
4. **Worker Safety Briefing**: Primary purpose is to inform workers about risks and protective measures
5. **Attendance Integration**: Links safety briefings to worker attendance records

## Toolbox Workflow Overview

```mermaid
graph TD
    A[Get Approved Tasks] --> B[Morning Toolbox Creation]
    B --> C[Fill Toolbox Form]
    C --> D[Add Hazards & Control Measures per Task]
    D --> E[Attendance is Filed]
    E --> F[Workers Briefed on Safety]
    F --> G[Work Begins - Tasks Started]
    G --> H[Evening Toolbox Summary]
    H --> I[End of Day Closure]
    
    style A fill:#e8f5e8
    style B fill:#e3f2fd
    style F fill:#fff3e0
    style I fill:#f3e5f5
```

## Detailed Workflow Steps

### Phase 1: Pre-Work Preparation
**Time: Early Morning (Before Work Starts)**
**Actor: Site Supervisor/Safety Officer**

#### Step 1: Retrieve Approved Tasks
- System displays all tasks with status `approved` for the current day
- Tasks contain pre-approved hazards, control measures, and weather protocols
- No modification of task data is allowed at this stage

```mermaid
sequenceDiagram
    participant SS as Site Supervisor
    participant TS as Toolbox System
    participant DB as Task Database
    participant WS as Weather Service
    
    SS->>TS: Request today's approved tasks
    TS->>DB: Query approved tasks for date
    DB->>TS: Return task list with hazards/controls
    TS->>WS: Get current weather conditions
    WS->>TS: Return weather data
    TS->>SS: Display tasks with applicable weather protocols
```

#### Step 2: Morning Toolbox Creation
- Create new toolbox session for the day
- System automatically populates:
  - All approved tasks for the day
  - Associated hazards from each task
  - Pre-approved control measures
  - Weather-specific protocols based on current conditions
  - Required PPE and equipment

### Phase 2: Safety Briefing
**Time: Morning (Before Work Starts)**
**Actor: Site Supervisor + All Workers**

#### Step 3: Fill Toolbox Form
The toolbox form includes:

```typescript
interface ToolboxSession {
  id: string;
  date: Date;
  siteId: string;
  conductedBy: string;
  
  // Task Information (Read-Only from approved tasks)
  tasksForDay: {
    taskId: string;
    taskName: string;
    location: string;
    hazards: Hazard[];
    controlMeasures: ControlMeasure[];
    requiredPPE: string[];
    weatherProtocols: WeatherHazardProtocol[];
  }[];
  
  // Current Conditions
  currentWeatherCondition: string;
  applicableWeatherProtocols: WeatherHazardProtocol[];
  
  // Briefing Details
  keySafetyPoints: string[];
  emergencyProcedures: string[];
  specialInstructions: string[];
  
  // Attendance
  attendees: WorkerAttendance[];
  
  // Status
  status: 'in-progress' | 'completed';
  startTime: Date;
  endTime?: Date;
}
```

#### Step 4: Add Hazards & Control Measures per Task
For each approved task:
- **Display task hazards** (from approved task - read-only)
- **Show control measures** (from approved task - read-only)
- **Select applicable weather protocols** (from pre-approved options)
- **Confirm required PPE availability**
- **Verify equipment readiness**

**Important**: No new hazards can be added or existing ones modified. If new hazards are discovered, work must stop and task re-assessment is required.

### Phase 3: Worker Engagement
**Time: Morning Briefing**
**Actor: All Workers**

#### Step 5: Attendance is Filed
- Record all workers present for the briefing
- Link attendance to specific tasks they'll be working on
- Verify worker competencies match task requirements
- Confirm understanding of safety briefings

```mermaid
flowchart TD
    A[Worker Arrives] --> B[Sign Attendance]
    B --> C[Verify Competency for Assigned Tasks]
    C --> D{Competency Match?}
    D -->|Yes| E[Include in Safety Briefing]
    D -->|No| F[Reassign or Provide Training]
    F --> E
    E --> G[Acknowledge Understanding]
    G --> H[Ready for Work]
    
    style D fill:#fff3e0
    style F fill:#ffebee
    style H fill:#e8f5e8
```

#### Step 6: Workers Briefed on Safety
The safety briefing covers:
- **Tasks planned for the day** and their locations
- **Specific hazards** for each task (from approved risk assessments)
- **Control measures** that must be implemented
- **Current weather conditions** and applicable protocols
- **Required PPE** for each task
- **Emergency procedures** and contact information
- **Stop work authority** and escalation procedures

### Phase 4: Work Execution
**Time: During Work Hours**
**Actor: Workers + Supervisors**

#### Step 7: Work Begins - Tasks Started
- Workers begin executing approved tasks
- Supervisors monitor compliance with briefed control measures
- Any deviations from approved protocols require immediate attention
- If conditions change beyond pre-approved parameters, work stops

```mermaid
graph LR
    A[Work Starts] --> B[Monitor Conditions]
    B --> C{Conditions Within Approved Parameters?}
    C -->|Yes| D[Continue Work]
    C -->|No| E[Stop Work]
    E --> F[Assess New Conditions]
    F --> G{Can Use Pre-Approved Protocol?}
    G -->|Yes| H[Apply Alternative Protocol]
    G -->|No| I[Require Task Re-Assessment]
    H --> D
    I --> J[Update Task & Re-Approve]
    D --> B
    
    style E fill:#ffebee
    style I fill:#ffebee
    style J fill:#fff3e0
```

### Phase 5: End of Day Closure
**Time: End of Work Day**
**Actor: Site Supervisor**

#### Step 8: Evening Toolbox Summary
- Review work completed vs. planned
- Document any issues or deviations encountered
- Record any near-misses or safety observations
- Note any conditions that affected work execution
- Prepare feedback for task improvement

#### Step 9: End of Day Closure
- Close toolbox session
- Generate summary report
- Update task progress based on work completed
- Prepare information for next day's planning

## Data Flow Architecture

```mermaid
graph TD
    A[Approved Tasks DB] --> B[Toolbox System]
    C[Weather Service] --> B
    D[Worker Attendance] --> B
    B --> E[Daily Safety Briefing]
    E --> F[Work Execution Monitoring]
    F --> G[Progress Updates]
    G --> H[Task Status Updates]
    
    style A fill:#e8f5e8
    style B fill:#e3f2fd
    style E fill:#fff3e0
    style H fill:#f3e5f5
```

## Key Features

### 1. Read-Only Task Data
- Hazards and control measures are **read-only** from approved tasks
- No modification of risk assessment data allowed
- Maintains integrity of approved safety documentation

### 2. Weather Protocol Selection
- Current weather conditions automatically detected
- System presents applicable pre-approved weather protocols
- Supervisor selects appropriate protocol for current conditions
- If weather exceeds pre-approved parameters, work stops

### 3. Attendance Integration
- Links safety briefings to worker attendance
- Verifies worker competencies for assigned tasks
- Maintains record of who received which safety briefings
- Supports compliance and audit requirements

### 4. Real-Time Monitoring
- Tracks work progress against planned tasks
- Monitors compliance with briefed safety measures
- Provides alerts for condition changes
- Supports stop-work decisions

## Integration Points

### With Task Management System
- Consumes approved task data (hazards, controls, requirements)
- Updates task progress based on work completion
- Triggers task re-assessment when needed
- Maintains audit trail of safety communications

### With Permit System
- References permit requirements from tasks
- Confirms permit availability before work starts
- Links toolbox attendance to permit worker assignments
- Supports permit compliance verification

### With Attendance System
- Records daily worker attendance
- Links attendance to specific task assignments
- Verifies worker competencies and certifications
- Maintains training compliance records

## Compliance and Audit

### Documentation Requirements
- Complete record of daily safety briefings
- Worker attendance and acknowledgment records
- Weather conditions and protocol selections
- Any deviations or incidents during work
- Feedback for task improvement

### Audit Trail
- Who conducted the briefing and when
- Which workers attended and acknowledged
- What safety information was communicated
- How weather conditions were addressed
- Any stop-work decisions and reasons

## System Benefits

1. **Standardized Communication**: Ensures consistent safety messaging
2. **Regulatory Compliance**: Meets requirements for worker safety briefings
3. **Audit Readiness**: Complete documentation of safety communications
4. **Risk Mitigation**: Proper briefing reduces incident likelihood
5. **Continuous Improvement**: Feedback loop for task enhancement

## Conclusion

The Toolbox system serves as a critical communication bridge between approved task risk assessments and daily work execution. By maintaining the task-centric approach and treating toolbox as a communication tool rather than a risk management system, we ensure:

- **Data Integrity**: Single source of truth for safety information
- **Regulatory Compliance**: Proper worker briefing and documentation
- **Operational Efficiency**: Streamlined daily safety processes
- **Continuous Safety**: Real-time monitoring and response capabilities

The toolbox workflow supports safe work execution while maintaining the integrity of the approved risk assessment process.