# Training Management Workflow

## Overview
This document outlines the comprehensive training management system workflow, from training module creation to certification tracking, expiry management, and visualization systems for company and site-level training oversight.

## 1. Training Module Creation & Management

### 1.1 Training Module Setup

```mermaid
flowchart TD
    A[Training Coordinator Creates Module] --> B[Define Training Details]
    B --> C[Set Validity Period]
    C --> D[Assign to Trades/Roles]
    D --> E[Define Prerequisites]
    E --> F[Set Training Provider Requirements]
    F --> G[Configure Renewal Rules]
    G --> H[Activate Training Module]
    H --> I[Update Trade Requirements Matrix]
```

#### Training Module Configuration:
```typescript
const createTrainingModule = async (moduleData: TrainingModuleInput) => {
  // 1. Create base training module
  const module = await createModule({
    name: moduleData.name,
    code: moduleData.code,
    category: moduleData.category, // critical, operational, foundation, administrative
    validity_period_months: moduleData.validityMonths,
    requires_full_recertification: moduleData.requiresRecertification,
    description: moduleData.description,
    training_provider_requirements: moduleData.providerRequirements
  });
  
  // 2. Link to trades
  for (const tradeId of moduleData.applicableTrades) {
    await createTradeTrainingRequirement({
      trade_id: tradeId,
      training_module_id: module.id,
      is_mandatory: moduleData.isMandatory
    });
  }
  
  // 3. Set up renewal schedule
  await setupRenewalSchedule(module.id, moduleData.renewalRules);
  
  return module;
};
```

### 1.2 Training Requirements Matrix

```mermaid
flowchart TD
    A[Trade Defined] --> B[Identify Required Training Modules]
    B --> C[Set Mandatory vs Optional]
    C --> D[Define Skill Level Requirements]
    D --> E[Set Prerequisites Chain]
    E --> F[Configure Renewal Schedules]
    F --> G[Update Training Matrix]
    G --> H[Notify Affected Workers]
```

## 2. Training Delivery & Certification

### 2.1 Training Session Planning

```mermaid
flowchart TD
    A[Training Need Identified] --> B{Training Type?}
    B -->|Individual| C[Schedule Individual Training]
    B -->|Group| D[Plan Group Session]
    B -->|Company-wide| E[Organize Company Training]
    C --> F[Book Training Provider]
    D --> F
    E --> F
    F --> G[Schedule Participants]
    G --> H[Send Training Notifications]
    H --> I[Prepare Training Materials]
    I --> J[Conduct Training Session]
    J --> K[Assess Participants]
    K --> L[Issue Certificates]
```

#### Training Session Management:
```typescript
const planTrainingSession = async (sessionData: TrainingSessionInput) => {
  // 1. Create training session
  const session = await createTrainingSession({
    training_module_id: sessionData.moduleId,
    scheduled_date: sessionData.date,
    training_provider: sessionData.provider,
    location: sessionData.location,
    max_participants: sessionData.maxParticipants
  });
  
  // 2. Identify participants
  let participants = [];
  if (sessionData.type === 'RENEWAL') {
    participants = await getWorkersWithExpiringCertifications(
      sessionData.moduleId, 
      sessionData.expiryThreshold
    );
  } else if (sessionData.type === 'NEW_REQUIREMENT') {
    participants = await getWorkersNeedingTraining(sessionData.moduleId);
  } else {
    participants = sessionData.selectedParticipants;
  }
  
  // 3. Register participants
  for (const participant of participants) {
    await registerParticipant(session.id, participant.workerId);
  }
  
  // 4. Send notifications
  await sendTrainingNotifications(session.id, participants);
  
  return { session, participants };
};
```

### 2.2 Certificate Management

```mermaid
flowchart TD
    A[Training Completed] --> B[Validate Training Completion]
    B --> C[Generate Certificate]
    C --> D[Calculate Expiry Date]
    D --> E[Upload Certificate Document]
    E --> F[Update Worker Training Record]
    F --> G[Recalculate Worker Compliance]
    G --> H[Update Site Eligibility]
    H --> I[Schedule Renewal Reminder]
    I --> J[Notify Stakeholders]
```

#### Certificate Processing:
```typescript
const processCertificateCompletion = async (completionData: CertificateCompletion) => {
  // 1. Validate completion
  const validation = await validateTrainingCompletion(completionData);
  if (!validation.valid) {
    throw new Error(`Invalid completion: ${validation.errors.join(', ')}`);
  }
  
  // 2. Calculate expiry date
  const trainingModule = await getTrainingModule(completionData.moduleId);
  const expiryDate = trainingModule.validity_period_months 
    ? addMonths(completionData.completionDate, trainingModule.validity_period_months)
    : null;
  
  // 3. Create training record
  const record = await createTrainingRecord({
    worker_id: completionData.workerId,
    training_module_id: completionData.moduleId,
    completion_date: completionData.completionDate,
    expiry_date: expiryDate,
    certificate_number: generateCertificateNumber(),
    certificate_file_url: completionData.certificateUrl,
    training_provider: completionData.provider,
    score: completionData.score,
    status: 'valid'
  });
  
  // 4. Update compliance status
  await updateWorkerComplianceStatus(completionData.workerId);
  
  // 5. Schedule renewal reminder
  if (expiryDate) {
    await scheduleRenewalReminders(record.id, expiryDate);
  }
  
  return record;
};
```

## 3. Bulk Training Management

### 3.1 Company-Wide Training Updates

```mermaid
flowchart TD
    A[Company Training Event] --> B[Upload Participant List]
    B --> C[Validate Participant Data]
    C --> D[Batch Process Certificates]
    D --> E[Generate Certificate Numbers]
    E --> F[Calculate Expiry Dates]
    F --> G[Update Training Records]
    G --> H[Recalculate All Compliance Statuses]
    H --> I[Update Site Eligibilities]
    I --> J[Generate Completion Report]
    J --> K[Send Notifications]
```

#### Bulk Training Processing:
```typescript
const processBulkTrainingUpdate = async (bulkData: BulkTrainingUpdate) => {
  const results = [];
  const errors = [];
  
  // 1. Validate all participants
  for (const participant of bulkData.participants) {
    try {
      await validateParticipant(participant);
    } catch (error) {
      errors.push({ participant: participant.workerId, error: error.message });
    }
  }
  
  if (errors.length > 0) {
    throw new BulkValidationError(errors);
  }
  
  // 2. Process in batches
  const batchSize = 50;
  for (let i = 0; i < bulkData.participants.length; i += batchSize) {
    const batch = bulkData.participants.slice(i, i + batchSize);
    
    const batchResults = await Promise.allSettled(
      batch.map(participant => processCertificateCompletion({
        workerId: participant.workerId,
        moduleId: bulkData.trainingModuleId,
        completionDate: bulkData.completionDate,
        certificateUrl: participant.certificateUrl,
        provider: bulkData.provider,
        score: participant.score
      }))
    );
    
    results.push(...batchResults);
  }
  
  // 3. Generate summary report
  const summary = generateBulkUpdateSummary(results);
  
  // 4. Notify stakeholders
  await notifyBulkUpdateCompletion(summary);
  
  return summary;
};
```

## 4. Training Compliance Monitoring

### 4.1 Expiry Tracking & Alerts

```mermaid
flowchart TD
    A[Daily Compliance Check] --> B[Scan All Training Records]
    B --> C[Identify Expiring Certificates]
    C --> D{Days Until Expiry?}
    D -->|30 days| E[Send Early Warning]
    D -->|14 days| F[Send Urgent Warning]
    D -->|7 days| G[Send Critical Alert]
    D -->|Expired| H[Mark as Non-Compliant]
    E --> I[Update Dashboard]
    F --> I
    G --> I
    H --> J[Remove Site Eligibility]
    J --> I
    I --> K[Generate Alert Reports]
    K --> L[Notify Stakeholders]
```

#### Automated Compliance Monitoring:
```typescript
const runDailyComplianceCheck = async () => {
  const today = new Date();
  
  // 1. Get all active training records
  const activeRecords = await getActiveTrainingRecords();
  
  // 2. Categorize by expiry status
  const expiryCategories = {
    expiring_30_days: [],
    expiring_14_days: [],
    expiring_7_days: [],
    expired: []
  };
  
  for (const record of activeRecords) {
    if (!record.expiry_date) continue;
    
    const daysUntilExpiry = Math.ceil(
      (record.expiry_date.getTime() - today.getTime()) / (1000 * 60 * 60 * 24)
    );
    
    if (daysUntilExpiry < 0) {
      expiryCategories.expired.push(record);
    } else if (daysUntilExpiry <= 7) {
      expiryCategories.expiring_7_days.push(record);
    } else if (daysUntilExpiry <= 14) {
      expiryCategories.expiring_14_days.push(record);
    } else if (daysUntilExpiry <= 30) {
      expiryCategories.expiring_30_days.push(record);
    }
  }
  
  // 3. Process each category
  await processExpiryAlerts(expiryCategories);
  
  // 4. Update compliance statuses
  await updateComplianceStatuses(expiryCategories.expired);
  
  // 5. Generate daily report
  return generateComplianceReport(expiryCategories);
};
```

### 4.2 Renewal Scheduling

```mermaid
flowchart TD
    A[Certificate Expiring Soon] --> B[Check Renewal Requirements]
    B --> C{Requires Full Recertification?}
    C -->|Yes| D[Schedule Full Training]
    C -->|No| E[Schedule Refresher Training]
    D --> F[Find Available Training Sessions]
    E --> F
    F --> G[Auto-enroll Worker]
    G --> H[Send Training Notification]
    H --> I[Update Training Calendar]
    I --> J[Set Follow-up Reminders]
```

## 5. Training Analytics & Visualization

### 5.1 Company-Level Training Dashboard

```mermaid
flowchart TD
    A[Training Dashboard Request] --> B[Aggregate Training Data]
    B --> C[Calculate Compliance Metrics]
    C --> D[Generate Trend Analysis]
    D --> E[Create Visualization Data]
    E --> F[Render Dashboard Components]
    F --> G{User Role?}
    G -->|Executive| H[Show High-Level KPIs]
    G -->|Training Manager| I[Show Detailed Analytics]
    G -->|Site Manager| J[Show Site-Specific Data]
```

#### Dashboard Data Generation:
```typescript
const generateTrainingDashboard = async (userId: string, filters: DashboardFilters) => {
  const userRole = await getUserRole(userId);
  
  // 1. Get base training data
  const trainingData = await getTrainingDataForUser(userId, filters);
  
  // 2. Calculate key metrics
  const metrics = {
    overall_compliance_rate: calculateComplianceRate(trainingData),
    expiring_certificates: getExpiringCertificates(trainingData, 30),
    overdue_trainings: getOverdueTrainings(trainingData),
    training_completion_rate: calculateCompletionRate(trainingData),
    top_expiring_modules: getTopExpiringModules(trainingData),
    compliance_by_trade: getComplianceByTrade(trainingData),
    training_costs: calculateTrainingCosts(trainingData, filters.period)
  };
  
  // 3. Generate visualizations based on role
  const visualizations = await generateRoleBasedVisualizations(userRole, metrics);
  
  // 4. Create alerts
  const alerts = generateTrainingAlerts(metrics);
  
  return {
    metrics,
    visualizations,
    alerts,
    last_updated: new Date()
  };
};
```

### 5.2 Site-Level Training Monitoring

```mermaid
flowchart TD
    A[Site Training Request] --> B[Get Site Workers]
    B --> C[Aggregate Worker Training Data]
    C --> D[Calculate Site Compliance]
    D --> E[Identify Training Gaps]
    E --> F[Generate Site Training Plan]
    F --> G[Create Site Dashboard]
    G --> H[Set Site-Specific Alerts]
```

## 6. Training Reporting System

### 6.1 Automated Report Generation

```mermaid
flowchart TD
    A[Scheduled Report Time] --> B[Determine Report Type]
    B --> C{Report Frequency?}
    C -->|Daily| D[Generate Daily Compliance Report]
    C -->|Weekly| E[Generate Weekly Training Summary]
    C -->|Monthly| F[Generate Monthly Analytics]
    C -->|Quarterly| G[Generate Quarterly Review]
    D --> H[Format Report Data]
    E --> H
    F --> H
    G --> H
    H --> I[Generate PDF/Excel]
    I --> J[Email to Stakeholders]
    J --> K[Archive Report]
```

#### Report Generation:
```typescript
const generateTrainingReport = async (reportType: ReportType, period: DateRange) => {
  let reportData;
  
  switch (reportType) {
    case 'DAILY_COMPLIANCE':
      reportData = await generateDailyComplianceData(period);
      break;
    case 'WEEKLY_SUMMARY':
      reportData = await generateWeeklyTrainingSummary(period);
      break;
    case 'MONTHLY_ANALYTICS':
      reportData = await generateMonthlyAnalytics(period);
      break;
    case 'QUARTERLY_REVIEW':
      reportData = await generateQuarterlyReview(period);
      break;
  }
  
  // Generate report document
  const report = await createReportDocument(reportType, reportData);
  
  // Send to stakeholders
  const recipients = await getReportRecipients(reportType);
  await emailReport(report, recipients);
  
  // Archive
  await archiveReport(report);
  
  return report;
};
```

## 7. Training Cost Management

### 7.1 Training Budget Tracking

```mermaid
flowchart TD
    A[Training Cost Incurred] --> B[Categorize Cost Type]
    B --> C[Assign to Cost Center]
    C --> D[Update Budget Tracking]
    D --> E[Check Budget Limits]
    E --> F{Over Budget?}
    F -->|Yes| G[Send Budget Alert]
    F -->|No| H[Continue Normal Process]
    G --> I[Request Budget Approval]
    I --> J[Update Budget Allocation]
    J --> H
    H --> K[Generate Cost Report]
```

## Key Training Management KPIs

### Compliance Metrics:
- **Overall Compliance Rate**: Percentage of workers with all required valid certifications
- **Training Completion Rate**: Percentage of scheduled trainings completed on time
- **Certificate Expiry Rate**: Number of certificates expiring per month
- **Renewal Success Rate**: Percentage of renewals completed before expiry

### Operational Metrics:
- **Training Cost per Worker**: Average training investment per worker
- **Training ROI**: Return on investment from training programs
- **Time to Compliance**: Average time for new workers to achieve full compliance
- **Training Utilization**: Percentage of available training slots filled

### Quality Metrics:
- **Training Effectiveness Score**: Based on post-training assessments
- **Incident Reduction Rate**: Correlation between training and incident reduction
- **Skill Improvement Rate**: Measured improvement in worker capabilities

## Alert System Configuration

### Critical Alerts (Immediate Action Required):
- Certificates expired (worker non-compliant)
- Critical safety training overdue
- Site assignment blocked due to training issues

### Warning Alerts (Action Required Soon):
- Certificates expiring within 7 days
- Training sessions under-enrolled
- Budget limits approaching

### Information Alerts (Monitoring):
- Certificates expiring within 30 days
- Training completion milestones
- Monthly compliance reports available

This comprehensive training management workflow ensures systematic tracking, compliance monitoring, and continuous improvement of workforce training programs.
