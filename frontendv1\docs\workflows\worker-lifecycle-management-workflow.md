# Worker Lifecycle Management Workflow

## Overview
This document outlines the complete lifecycle of a worker in the workforce management system, from initial creation through various management activities to eventual removal from the system.

## 1. Worker Creation Workflow

### 1.1 Initial Worker Registration

```mermaid
flowchart TD
    A[HR Manager Initiates Worker Creation] --> B[Enter Basic Information]
    B --> C[Assign Trades & Skills]
    C --> D[Upload Documents & Photos]
    D --> E[System Generates Employee Number]
    E --> F[Determine Required Trainings]
    F --> G{Has Existing Certificates?}
    G -->|Yes| H[Upload Existing Certificates]
    G -->|No| I[Schedule Required Trainings]
    H --> J[Validate Certificates]
    I --> K[Calculate Compliance Status]
    J --> K
    K --> L[Worker Created - Pending Compliance]
```

#### Process Steps:
1. **Basic Information Entry**
   - Personal details (name, ID, contact info)
   - Employment information (hire date, company)
   - Emergency contacts

2. **Trade & Skill Assignment**
   - Select primary trade
   - Add secondary trades if applicable
   - Assign skill levels (beginner, intermediate, advanced)

3. **Document Management**
   - Upload profile photo (required for face recognition)
   - Upload signature
   - Upload identification documents
   - Medical certificates (if available)
   - Permanent certificates and qualifications

4. **Permanent Certificates Management**
   - Upload lifetime/permanent certificates (degrees, professional licenses)
   - Validate certificate authenticity and expiry (if applicable)
   - Store in separate permanent certificates table
   - Link to worker profile for compliance checking

5. **Training Requirements Calculation**
   ```typescript
   const calculateRequiredTrainings = async (workerTrades: Trade[]) => {
     const requiredTrainings = [];
     for (const trade of workerTrades) {
       const tradeRequirements = await getTradeTrainingRequirements(trade.id);
       requiredTrainings.push(...tradeRequirements);
     }
     return removeDuplicates(requiredTrainings);
   };
   ```

### 1.2 Permanent Certificates Processing (Manual Entry)

```mermaid
flowchart TD
    A[Worker Created] --> B[Upload Certificate Files]
    B --> C[Generate Standardized File Names]
    C --> D[Manual Data Entry Form]
    D --> E[Enter Certificate Details]
    E --> F[Enter Issue Date]
    F --> G[Enter Expiry Date or Mark as Lifetime]
    G --> H[Add Certificate Number if Available]
    H --> I[Store with Standardized Naming]
    I --> J[Link to Worker Profile]
    J --> K[Update Worker Qualifications]
    K --> L[Mark for Manual Verification]
    L --> M[Proceed to Compliance Assessment]
```

#### Permanent Certificate Processing (Manual Entry):
```typescript
const processPermanentCertificates = async (workerId: string, certificates: PermanentCertificateInput[]) => {
  const processedCertificates = [];

  for (const cert of certificates) {
    // 1. Generate standardized document name
    const standardizedFileName = generateStandardizedFileName(workerId, cert);

    // 2. Upload certificate file with standardized name
    const uploadResult = await uploadCertificateFile(cert.file, standardizedFileName);

    // 3. Create permanent certificate record with manual data entry
    const permanentCert = await createPermanentCertificate({
      worker_id: workerId,
      certificate_name: cert.name,
      certificate_type: cert.type, // 'degree', 'professional_license', 'trade_certification'
      issuing_authority: cert.issuingAuthority,
      certificate_number: cert.number || null, // manually entered
      issue_date: cert.issueDate, // manually entered
      expiry_date: cert.expiryDate, // manually entered (null for lifetime certificates)
      certificate_file_url: uploadResult.fileUrl,
      standardized_file_name: standardizedFileName,
      verification_status: 'pending_manual_review', // always pending for manual entry
      is_lifetime: cert.isLifetime, // manually specified
      status: 'active',
      manual_entry_notes: cert.notes || null
    });

    // 4. Update worker qualifications
    await updateWorkerQualifications(workerId, cert.qualifications);

    processedCertificates.push(permanentCert);
  }

  return processedCertificates;
};

// Document naming standardization
const generateStandardizedFileName = (workerId: string, certificate: PermanentCertificateInput) => {
  const worker = await getWorkerById(workerId);
  const timestamp = new Date().toISOString().split('T')[0]; // YYYY-MM-DD

  // Format: {EmployeeNumber}_{LastName}_{FirstName}_{CertificateType}_{IssuingAuthority}_{Date}.{extension}
  const standardizedName = [
    worker.employee_number,
    sanitizeFileName(worker.last_name),
    sanitizeFileName(worker.first_name),
    sanitizeFileName(certificate.type.toUpperCase()),
    sanitizeFileName(certificate.issuingAuthority),
    timestamp
  ].join('_');

  const fileExtension = certificate.file.name.split('.').pop();
  return `${standardizedName}.${fileExtension}`;
};

const sanitizeFileName = (input: string) => {
  return input.replace(/[^a-zA-Z0-9]/g, '').toUpperCase();
};
```

### 1.3 Compliance Assessment

```mermaid
flowchart TD
    A[Permanent Certificates Processed] --> B[Get Required Trainings for Trades]
    B --> C[Check Existing Training Certificates]
    C --> D[Check Permanent Certificate Coverage]
    D --> E{All Requirements Met?}
    E -->|Yes| F[Mark as Compliant]
    E -->|No| G[Identify Missing Requirements]
    G --> H[Schedule Required Trainings]
    H --> I[Mark as Pending Training]
    F --> J[Eligible for Site Assignment]
    I --> K[Not Eligible for Site Assignment]
```

## 2. Worker Assignment Workflow

### 2.1 Site Assignment Process

```mermaid
flowchart TD
    A[Site Manager Requests Worker Assignment] --> B[Select Workers from Company Pool]
    B --> C[Check Worker Compliance Status]
    C --> D{Worker Compliant?}
    D -->|No| E[Display Blocking Issues]
    E --> F[Resolve Compliance Issues]
    F --> C
    D -->|Yes| G[Check Site-Specific Requirements]
    G --> H{Meets Site Requirements?}
    H -->|No| I[Additional Training Required]
    I --> J[Schedule Site-Specific Training]
    H -->|Yes| K[Create Site Assignment]
    K --> L[Enroll Worker to Site Devices]
    L --> M[Notify Worker of Assignment]
    M --> N[Assignment Complete]
```

#### Assignment Validation Rules:
```typescript
const validateSiteAssignment = async (siteId: string, workerId: string) => {
  // 1. Check basic compliance (including permanent certificates)
  const compliance = await checkWorkerCompliance(workerId);
  if (!compliance.eligible) {
    throw new Error(`Worker not compliant: ${compliance.blockingIssues.join(', ')}`);
  }

  // 2. Check site-specific requirements
  const siteRequirements = await getSiteSpecificRequirements(siteId);
  const workerQualifications = await getWorkerQualifications(workerId);

  // 3. Include permanent certificates in qualification check
  const permanentCerts = await getWorkerPermanentCertificates(workerId);
  const allQualifications = [...workerQualifications, ...permanentCerts];

  const missingRequirements = siteRequirements.filter(req =>
    !allQualifications.some(qual => qual.id === req.id || qual.covers?.includes(req.id))
  );

  if (missingRequirements.length > 0) {
    throw new Error(`Missing site requirements: ${missingRequirements.map(r => r.name).join(', ')}`);
  }

  return { eligible: true };
};
```

### 2.2 Device Enrollment Process

```mermaid
flowchart TD
    A[Worker Assigned to Site] --> B[Get Site Hikvision Devices]
    B --> C[Validate Worker Photo Available]
    C --> D{Photo Available?}
    D -->|No| E[Request Photo Upload]
    E --> F[Upload Photo]
    F --> G[Process Face Template]
    D -->|Yes| G
    G --> H[Enroll to Each Device]
    H --> I{Enrollment Successful?}
    I -->|No| J[Log Error & Retry]
    I -->|Yes| K[Update Device Sync Status]
    K --> L[Worker Ready for Attendance]
```

## 3. Document Naming Standards & Storage

### 3.1 Standardized File Naming Convention

All uploaded documents follow a standardized naming convention for uniform storage and easy retrieval:

#### Naming Pattern:
```
{EmployeeNumber}_{LastName}_{FirstName}_{DocumentType}_{IssuingAuthority}_{Date}.{extension}
```

#### Examples:
```typescript
// Permanent Certificates
"EMP001_SMITH_JOHN_PROFESSIONALLICENSE_ENGINEERSBOARD_2024-02-20.pdf"
"EMP001_SMITH_JOHN_EDUCATIONALDEGREE_UNIVERSITYOFTORONTO_2024-02-20.pdf"
"EMP001_SMITH_JOHN_TRADECERTIFICATION_ELECTRICALBOARD_2024-02-20.pdf"

// Training Certificates
"EMP001_SMITH_JOHN_TRAINING_SITESAFETY_SAFETYINSTITUTE_2024-02-20.pdf"
"EMP001_SMITH_JOHN_TRAINING_FIRSTAID_REDCROSS_2024-02-20.pdf"

// Other Documents
"EMP001_SMITH_JOHN_MEDICAL_PHYSICALEXAM_OCCUPATIONALHEALTH_2024-02-20.pdf"
"EMP001_SMITH_JOHN_IDENTIFICATION_DRIVERSLICENSE_GOVERNMENT_2024-02-20.pdf"
```

#### File Naming Implementation:
```typescript
interface DocumentNamingStandard {
  permanent_certificate: {
    pattern: "{employeeNumber}_{lastName}_{firstName}_{certificateType}_{issuingAuthority}_{uploadDate}";
    certificateTypes: [
      'PROFESSIONALLICENSE',
      'EDUCATIONALDEGREE',
      'TRADECERTIFICATION',
      'SAFETYCERTIFICATION',
      'SPECIALIZEDQUALIFICATION'
    ];
  };

  training_certificate: {
    pattern: "{employeeNumber}_{lastName}_{firstName}_TRAINING_{trainingModule}_{provider}_{uploadDate}";
  };

  medical_document: {
    pattern: "{employeeNumber}_{lastName}_{firstName}_MEDICAL_{documentType}_{provider}_{uploadDate}";
  };

  identification: {
    pattern: "{employeeNumber}_{lastName}_{firstName}_IDENTIFICATION_{idType}_{issuingAuthority}_{uploadDate}";
  };
}

const generateDocumentFileName = (worker: Worker, documentType: string, metadata: DocumentMetadata) => {
  const timestamp = new Date().toISOString().split('T')[0];
  const sanitizedLastName = sanitizeFileName(worker.last_name);
  const sanitizedFirstName = sanitizeFileName(worker.first_name);
  const sanitizedAuthority = sanitizeFileName(metadata.issuingAuthority || metadata.provider);

  let fileName: string;

  switch (documentType) {
    case 'permanent_certificate':
      fileName = `${worker.employee_number}_${sanitizedLastName}_${sanitizedFirstName}_${metadata.certificateType.toUpperCase()}_${sanitizedAuthority}_${timestamp}`;
      break;
    case 'training_certificate':
      fileName = `${worker.employee_number}_${sanitizedLastName}_${sanitizedFirstName}_TRAINING_${sanitizeFileName(metadata.trainingModule)}_${sanitizedAuthority}_${timestamp}`;
      break;
    case 'medical_document':
      fileName = `${worker.employee_number}_${sanitizedLastName}_${sanitizedFirstName}_MEDICAL_${sanitizeFileName(metadata.documentType)}_${sanitizedAuthority}_${timestamp}`;
      break;
    case 'identification':
      fileName = `${worker.employee_number}_${sanitizedLastName}_${sanitizedFirstName}_IDENTIFICATION_${sanitizeFileName(metadata.idType)}_${sanitizedAuthority}_${timestamp}`;
      break;
    default:
      fileName = `${worker.employee_number}_${sanitizedLastName}_${sanitizedFirstName}_${documentType.toUpperCase()}_${timestamp}`;
  }

  return `${fileName}.${metadata.fileExtension}`;
};
```

### 3.2 Document Storage Structure

```
/documents/
├── workers/
│   ├── {employeeNumber}/
│   │   ├── permanent_certificates/
│   │   │   ├── professional_licenses/
│   │   │   ├── educational_degrees/
│   │   │   ├── trade_certifications/
│   │   │   └── safety_certifications/
│   │   ├── training_certificates/
│   │   │   ├── safety_training/
│   │   │   ├── technical_training/
│   │   │   └── compliance_training/
│   │   ├── medical_documents/
│   │   ├── identification/
│   │   └── other/
```

### 3.3 Manual Certificate Data Entry Form

Since OCR is not implemented, users must manually enter certificate details after uploading the file:

```mermaid
flowchart TD
    A[Select Certificate File] --> B[Upload File]
    B --> C[Generate Preview with Standardized Name]
    C --> D[Manual Data Entry Form]
    D --> E[Certificate Name/Title]
    E --> F[Certificate Type Selection]
    F --> G[Issuing Authority]
    G --> H[Certificate Number Optional]
    H --> I[Issue Date]
    I --> J{Has Expiry Date?}
    J -->|Yes| K[Enter Expiry Date]
    J -->|No| L[Mark as Lifetime Certificate]
    K --> M[Additional Notes Optional]
    L --> M
    M --> N[Validate Form Data]
    N --> O{Validation Passed?}
    O -->|No| P[Show Validation Errors]
    P --> D
    O -->|Yes| Q[Save Certificate Record]
    Q --> R[Store with Standardized Name]
```

#### Manual Entry Form Interface:
```typescript
interface CertificateManualEntryForm {
  // File Upload
  certificateFile: File;

  // Basic Information
  certificateName: string; // e.g., "Professional Engineer License"
  certificateType: 'professional_license' | 'educational_degree' | 'trade_certification' | 'safety_certification' | 'specialized_qualification';

  // Issuing Details
  issuingAuthority: string; // e.g., "Professional Engineers Board"
  certificateNumber?: string; // Optional, some certificates don't have numbers

  // Dates
  issueDate: Date;
  hasExpiryDate: boolean;
  expiryDate?: Date; // Required if hasExpiryDate is true
  isLifetime: boolean; // Calculated from hasExpiryDate

  // Additional Information
  notes?: string; // Optional additional notes
  qualificationsCovered: string[]; // What qualifications this certificate provides
}

const validateCertificateForm = (formData: CertificateManualEntryForm): ValidationResult => {
  const errors: string[] = [];

  // Required field validation
  if (!formData.certificateName?.trim()) {
    errors.push('Certificate name is required');
  }

  if (!formData.certificateType) {
    errors.push('Certificate type must be selected');
  }

  if (!formData.issuingAuthority?.trim()) {
    errors.push('Issuing authority is required');
  }

  if (!formData.issueDate) {
    errors.push('Issue date is required');
  }

  // Date validation
  if (formData.issueDate && formData.issueDate > new Date()) {
    errors.push('Issue date cannot be in the future');
  }

  if (formData.hasExpiryDate && !formData.expiryDate) {
    errors.push('Expiry date is required when certificate expires');
  }

  if (formData.expiryDate && formData.issueDate && formData.expiryDate <= formData.issueDate) {
    errors.push('Expiry date must be after issue date');
  }

  // File validation
  if (!formData.certificateFile) {
    errors.push('Certificate file is required');
  }

  const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'];
  if (formData.certificateFile && !allowedTypes.includes(formData.certificateFile.type)) {
    errors.push('Only PDF, JPEG, and PNG files are allowed');
  }

  const maxSize = 10 * 1024 * 1024; // 10MB
  if (formData.certificateFile && formData.certificateFile.size > maxSize) {
    errors.push('File size must be less than 10MB');
  }

  return {
    isValid: errors.length === 0,
    errors: errors,
    warnings: generateFormWarnings(formData)
  };
};

const generateFormWarnings = (formData: CertificateManualEntryForm): string[] => {
  const warnings: string[] = [];

  // Check for certificates expiring soon
  if (formData.expiryDate) {
    const daysUntilExpiry = Math.ceil(
      (formData.expiryDate.getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
    );

    if (daysUntilExpiry < 30) {
      warnings.push('This certificate expires within 30 days');
    } else if (daysUntilExpiry < 90) {
      warnings.push('This certificate expires within 90 days');
    }
  }

  // Check for missing certificate number
  if (!formData.certificateNumber && formData.certificateType === 'professional_license') {
    warnings.push('Professional licenses typically have certificate numbers');
  }

  return warnings;
};
```

## 4. Permanent Certificates Management

### 3.1 Permanent Certificate Types & Handling

```mermaid
flowchart TD
    A[Permanent Certificate Upload] --> B{Certificate Type?}
    B -->|Professional License| C[Validate License Number]
    B -->|Educational Degree| D[Verify Institution]
    B -->|Trade Certification| E[Check Issuing Body]
    B -->|Safety Certification| F[Validate Authority]
    C --> G[Check Expiry Status]
    D --> H[Lifetime Certificate]
    E --> G
    F --> G
    G --> I[Store in Permanent Certificates Table]
    H --> I
    I --> J[Update Worker Qualifications]
    J --> K[Recalculate Site Eligibility]
```

#### Permanent Certificate Categories:
```typescript
interface PermanentCertificateTypes {
  PROFESSIONAL_LICENSE: {
    examples: ['Professional Engineer', 'Architect License', 'Contractor License'];
    hasExpiry: true;
    requiresRenewal: true;
    verificationRequired: true;
  };

  EDUCATIONAL_DEGREE: {
    examples: ['Bachelor of Engineering', 'Master of Architecture', 'Trade Diploma'];
    hasExpiry: false;
    requiresRenewal: false;
    verificationRequired: true;
  };

  TRADE_CERTIFICATION: {
    examples: ['Journeyman Electrician', 'Master Plumber', 'Certified Welder'];
    hasExpiry: true;
    requiresRenewal: true;
    verificationRequired: true;
  };

  SAFETY_CERTIFICATION: {
    examples: ['First Aid Instructor', 'Safety Officer Certification', 'Crane Operator License'];
    hasExpiry: true;
    requiresRenewal: true;
    verificationRequired: true;
  };

  SPECIALIZED_QUALIFICATION: {
    examples: ['Hazmat Handler', 'Confined Space Entry', 'High Voltage Work'];
    hasExpiry: true;
    requiresRenewal: true;
    verificationRequired: true;
  };
}
```

### 3.2 Manual Certificate Verification Process

```mermaid
flowchart TD
    A[Certificate Uploaded with Manual Data] --> B[Generate Standardized File Name]
    B --> C[Store Certificate File]
    C --> D[Create Certificate Record]
    D --> E[Queue for Manual Verification]
    E --> F[Assign to Verification Team]
    F --> G[Verifier Reviews Certificate]
    G --> H[Verifier Checks Issuing Authority]
    H --> I[Verifier Validates Details]
    I --> J{Verification Result?}
    J -->|Valid| K[Mark as Verified]
    J -->|Invalid| L[Mark as Invalid]
    J -->|Needs Clarification| M[Request Additional Information]
    K --> N[Update Worker Profile]
    L --> O[Notify HR for Investigation]
    M --> P[Contact Worker/HR]
    P --> Q[Await Additional Documentation]
    Q --> G
    N --> R[Certificate Status: Active]
    O --> S[Certificate Status: Invalid]
```

#### Manual Certificate Verification Implementation:
```typescript
const processManualCertificateVerification = async (certificateId: string, verificationData: ManualVerificationData) => {
  // 1. Get certificate record
  const certificate = await getPermanentCertificate(certificateId);

  // 2. Queue for manual verification with standardized file name
  const verificationTask = await queueForManualVerification(certificateId, {
    priority: determinePriority(certificate.certificate_type),
    assigned_to: await getNextAvailableVerifier(),
    verification_deadline: calculateVerificationDeadline(certificate.certificate_type),
    file_location: certificate.certificate_file_url,
    standardized_name: certificate.standardized_file_name,
    manual_data: {
      certificate_name: certificate.certificate_name,
      issuing_authority: certificate.issuing_authority,
      certificate_number: certificate.certificate_number,
      issue_date: certificate.issue_date,
      expiry_date: certificate.expiry_date,
      is_lifetime: certificate.is_lifetime
    }
  });

  return {
    verification_task_id: verificationTask.id,
    status: 'queued_for_manual_verification',
    estimated_completion: verificationTask.deadline,
    assigned_verifier: verificationTask.assigned_to
  };
};

const completeManualVerification = async (verificationTaskId: string, verificationResult: VerificationResult) => {
  // 1. Update certificate verification status
  await updateCertificateVerificationStatus(verificationResult.certificateId, {
    verification_status: verificationResult.isValid ? 'verified' : 'invalid',
    verification_date: new Date(),
    verification_method: 'manual',
    verified_by: verificationResult.verifiedBy,
    verification_notes: verificationResult.notes,
    verification_details: verificationResult.details
  });

  // 2. Update worker compliance if certificate is now verified
  if (verificationResult.isValid) {
    await updateWorkerComplianceStatus(verificationResult.workerId);
  }

  // 3. Complete verification task
  await completeVerificationTask(verificationTaskId, verificationResult);

  return {
    certificate_id: verificationResult.certificateId,
    verification_status: verificationResult.isValid ? 'verified' : 'invalid',
    worker_compliance_updated: verificationResult.isValid
  };
};
```

### 3.3 Permanent Certificate Expiry Management

```mermaid
flowchart TD
    A[Daily Certificate Check] --> B[Scan Permanent Certificates]
    B --> C[Identify Expiring Certificates]
    C --> D{Days Until Expiry?}
    D -->|90 days| E[Send Early Renewal Notice]
    D -->|30 days| F[Send Urgent Renewal Notice]
    D -->|7 days| G[Send Critical Alert]
    D -->|Expired| H[Mark as Expired]
    E --> I[Update Dashboard]
    F --> I
    G --> I
    H --> J[Remove from Active Qualifications]
    J --> K[Recalculate Worker Compliance]
    K --> L[Update Site Eligibility]
    L --> I
    I --> M[Notify Stakeholders]
```

## 4. Worker Management Activities

### 3.1 Certificate & Training Management

```mermaid
flowchart TD
    A[Certificate/Training Update Required] --> B{Update Type?}
    B -->|Permanent Certificate| C[Upload New Permanent Certificate]
    B -->|Training Renewal| D[Schedule Renewal Training]
    B -->|New Training Requirement| E[Add New Training Requirement]
    B -->|Training Upgrade| F[Schedule Advanced Training]
    C --> G[Validate Permanent Certificate]
    D --> H[Complete Training]
    E --> H
    F --> H
    G --> I[Update Permanent Certificate Records]
    H --> J[Upload Training Certificate]
    I --> K[Recalculate Compliance Status]
    J --> L[Update Training Records]
    L --> K
    K --> M[Update Site Eligibility]
    M --> N[Notify Relevant Parties]
```

#### Certificate & Training Update Process:
```typescript
const updateWorkerCertificatesAndTraining = async (workerId: string, updateData: CertificateTrainingUpdate) => {
  const results = { permanent_certificates: [], training_records: [] };

  // 1. Process permanent certificates if provided
  if (updateData.permanentCertificates?.length > 0) {
    for (const permCert of updateData.permanentCertificates) {
      const validation = await validatePermanentCertificate(permCert);
      if (!validation.valid) {
        throw new Error(`Invalid permanent certificate: ${validation.errors.join(', ')}`);
      }

      const permanentCertRecord = await createPermanentCertificate({
        worker_id: workerId,
        certificate_name: permCert.name,
        certificate_type: permCert.type,
        issuing_authority: permCert.issuingAuthority,
        certificate_number: permCert.number,
        issue_date: permCert.issueDate,
        expiry_date: permCert.expiryDate,
        certificate_file_url: permCert.fileUrl,
        is_lifetime: permCert.isLifetime,
        status: 'active'
      });

      results.permanent_certificates.push(permanentCertRecord);
    }
  }

  // 2. Process training certificates if provided
  if (updateData.trainingCertificates?.length > 0) {
    for (const trainingCert of updateData.trainingCertificates) {
      const validation = await validateTrainingCertificate(trainingCert);
      if (!validation.valid) {
        throw new Error(`Invalid training certificate: ${validation.errors.join(', ')}`);
      }

      // Generate standardized file name for training certificate
      const worker = await getWorkerById(workerId);
      const trainingModule = await getTrainingModule(trainingCert.trainingModuleId);
      const standardizedFileName = generateDocumentFileName(worker, 'training_certificate', {
        trainingModule: trainingModule.name,
        provider: trainingCert.provider,
        fileExtension: trainingCert.file.name.split('.').pop()
      });

      // Upload with standardized name
      const uploadResult = await uploadCertificateFile(trainingCert.file, standardizedFileName);

      const trainingRecord = await createTrainingRecord({
        worker_id: workerId,
        training_module_id: trainingCert.trainingModuleId,
        completion_date: trainingCert.completionDate,
        expiry_date: calculateExpiryDate(trainingCert),
        certificate_file_url: uploadResult.fileUrl,
        standardized_file_name: standardizedFileName,
        training_provider: trainingCert.provider,
        status: 'valid'
      });

      results.training_records.push(trainingRecord);
    }
  }

  // 3. Update compliance status (considering both permanent and training certificates)
  await updateWorkerComplianceStatus(workerId);

  // 4. Check impact on site assignments
  await checkSiteAssignmentEligibility(workerId);

  return results;
};
```

### 3.2 Worker Performance Management

```mermaid
flowchart TD
    A[Performance Review Triggered] --> B{Review Type?}
    B -->|Time-based| C[Analyze Attendance Data]
    B -->|Incident-based| D[Review Incident Reports]
    B -->|Skill Assessment| E[Conduct Skill Evaluation]
    C --> F[Calculate Performance Metrics]
    D --> F
    E --> F
    F --> G[Update Worker Rating]
    G --> H{Performance Acceptable?}
    H -->|Yes| I[Continue Current Assignments]
    H -->|No| J[Performance Improvement Plan]
    J --> K[Additional Training Required?]
    K -->|Yes| L[Schedule Remedial Training]
    K -->|No| M[Reassign to Different Role]
    L --> N[Monitor Progress]
    M --> N
    N --> O[Re-evaluate Performance]
```

#### Performance Metrics Calculation:
```typescript
const calculateWorkerPerformance = async (workerId: string, period: DateRange) => {
  // 1. Attendance metrics
  const attendanceData = await getWorkerAttendance(workerId, period);
  const attendanceScore = calculateAttendanceScore(attendanceData);
  
  // 2. Incident metrics
  const incidents = await getWorkerIncidents(workerId, period);
  const safetyScore = calculateSafetyScore(incidents);
  
  // 3. Productivity metrics
  const productivity = await getWorkerProductivity(workerId, period);
  const productivityScore = calculateProductivityScore(productivity);
  
  // 4. Overall rating
  const overallRating = (attendanceScore * 0.3 + safetyScore * 0.4 + productivityScore * 0.3);
  
  return {
    attendance_score: attendanceScore,
    safety_score: safetyScore,
    productivity_score: productivityScore,
    overall_rating: overallRating,
    grade: getPerformanceGrade(overallRating)
  };
};
```

### 3.3 Worker Reassignment

```mermaid
flowchart TD
    A[Reassignment Request] --> B{Reassignment Type?}
    B -->|Site Change| C[Check New Site Eligibility]
    B -->|Trade Change| D[Assess New Trade Requirements]
    B -->|Role Change| E[Validate Role Requirements]
    C --> F{Eligible for New Assignment?}
    D --> F
    E --> F
    F -->|No| G[Identify Required Actions]
    G --> H[Complete Required Training/Updates]
    H --> I[Re-check Eligibility]
    I --> F
    F -->|Yes| J[Remove from Current Assignment]
    J --> K[Update Worker Records]
    K --> L[Create New Assignment]
    L --> M[Update Device Access]
    M --> N[Notify All Parties]
```

## 4. Incident Management

### 4.1 Incident Reporting & Impact

```mermaid
flowchart TD
    A[Incident Reported] --> B[Record Incident Details]
    B --> C[Assess Severity Level]
    C --> D{Severity Level?}
    D -->|Minor| E[Document Only]
    D -->|Major| F[Investigate Thoroughly]
    D -->|Critical| G[Immediate Action Required]
    E --> H[Update Worker Record]
    F --> I[Determine Root Cause]
    G --> J[Suspend Worker if Necessary]
    I --> K[Assign Corrective Actions]
    J --> L[Conduct Investigation]
    K --> M[Schedule Follow-up Training]
    L --> M
    M --> N[Update Safety Rating]
    N --> O[Review Site Assignment Status]
    H --> P[Continue Normal Operations]
    O --> P
```

#### Incident Impact Assessment:
```typescript
const processWorkerIncident = async (incidentData: IncidentReport) => {
  // 1. Create incident record
  const incident = await createIncident(incidentData);
  
  // 2. Assess impact on worker
  const impactAssessment = assessIncidentImpact(incident);
  
  // 3. Update worker safety score
  await updateWorkerSafetyScore(incident.workerId, impactAssessment);
  
  // 4. Determine required actions
  const requiredActions = determineRequiredActions(impactAssessment);
  
  // 5. Execute actions
  for (const action of requiredActions) {
    switch (action.type) {
      case 'ADDITIONAL_TRAINING':
        await scheduleRemedialTraining(incident.workerId, action.trainingModules);
        break;
      case 'SITE_REASSIGNMENT':
        await reassignWorkerToSaferSite(incident.workerId);
        break;
      case 'TEMPORARY_SUSPENSION':
        await suspendWorker(incident.workerId, action.duration);
        break;
    }
  }
  
  return { incident, actions: requiredActions };
};
```

## 5. Payment & Payroll Integration

### 5.1 Payroll Data Preparation

```mermaid
flowchart TD
    A[Payroll Period End] --> B[Collect Attendance Data]
    B --> C[Calculate Regular Hours]
    C --> D[Calculate Overtime Hours]
    D --> E[Apply Site-specific Rates]
    E --> F[Factor in Performance Bonuses]
    F --> G[Deduct Incident Penalties]
    G --> H[Generate Payroll Export]
    H --> I[Send to Payroll System]
    I --> J[Update Payment Records]
```

#### Payroll Calculation:
```typescript
const calculateWorkerPayroll = async (workerId: string, payPeriod: PayPeriod) => {
  // 1. Get attendance data
  const attendance = await getWorkerAttendanceForPeriod(workerId, payPeriod);
  
  // 2. Calculate hours
  const regularHours = attendance.filter(a => a.total_hours <= 8)
    .reduce((sum, a) => sum + a.total_hours, 0);
  const overtimeHours = attendance.filter(a => a.total_hours > 8)
    .reduce((sum, a) => sum + (a.total_hours - 8), 0);
  
  // 3. Get worker rates
  const worker = await getWorkerWithRates(workerId);
  
  // 4. Calculate base pay
  const regularPay = regularHours * worker.hourlyRate;
  const overtimePay = overtimeHours * worker.hourlyRate * 1.5;
  
  // 5. Apply bonuses and deductions
  const performanceBonus = await calculatePerformanceBonus(workerId, payPeriod);
  const incidentDeductions = await calculateIncidentDeductions(workerId, payPeriod);
  
  const totalPay = regularPay + overtimePay + performanceBonus - incidentDeductions;
  
  return {
    worker_id: workerId,
    pay_period: payPeriod,
    regular_hours: regularHours,
    overtime_hours: overtimeHours,
    regular_pay: regularPay,
    overtime_pay: overtimePay,
    performance_bonus: performanceBonus,
    deductions: incidentDeductions,
    total_pay: totalPay
  };
};
```

## 6. Worker Removal Workflow

### 6.1 Worker Termination Process

```mermaid
flowchart TD
    A[Termination Initiated] --> B{Termination Type?}
    B -->|Voluntary| C[Process Resignation]
    B -->|Involuntary| D[Document Termination Reasons]
    B -->|End of Contract| E[Complete Contract Terms]
    C --> F[Set End Date]
    D --> F
    E --> F
    F --> G[Remove from Active Site Assignments]
    G --> H[Remove Device Access]
    H --> I[Calculate Final Pay]
    I --> J[Archive Worker Records]
    J --> K[Update System Status to 'Terminated']
    K --> L[Generate Termination Report]
```

#### Termination Process:
```typescript
const terminateWorker = async (workerId: string, terminationData: TerminationData) => {
  // 1. Validate termination request
  await validateTerminationRequest(workerId, terminationData);
  
  // 2. Remove from active assignments
  const activeAssignments = await getActiveAssignments(workerId);
  for (const assignment of activeAssignments) {
    await endSiteAssignment(assignment.id, terminationData.endDate);
  }
  
  // 3. Remove device access
  await removeWorkerFromAllDevices(workerId);
  
  // 4. Calculate final pay
  const finalPay = await calculateFinalPay(workerId, terminationData.endDate);
  
  // 5. Archive records
  await archiveWorkerRecords(workerId);
  
  // 6. Update status
  await updateWorkerStatus(workerId, 'terminated', terminationData);
  
  // 7. Generate reports
  const terminationReport = await generateTerminationReport(workerId, terminationData);
  
  return { finalPay, terminationReport };
};
```

## Key Performance Indicators (KPIs)

### Worker Management KPIs:
- **Overall Compliance Rate**: Percentage of workers with all valid certifications (permanent + training)
- **Permanent Certificate Verification Rate**: Percentage of permanent certificates verified
- **Assignment Efficiency**: Time from worker creation to first site assignment
- **Training Completion Rate**: Percentage of required trainings completed on time
- **Certificate Expiry Management**: Percentage of certificates renewed before expiry
- **Performance Score Distribution**: Distribution of worker performance ratings
- **Incident Rate**: Number of incidents per worker per time period
- **Retention Rate**: Percentage of workers retained over time periods

### Monitoring & Alerts:
- **Compliance Alerts**: Workers with expiring certifications (permanent + training)
- **Permanent Certificate Alerts**: Unverified or expiring permanent certificates
- **Performance Alerts**: Workers with declining performance scores
- **Assignment Alerts**: Sites with insufficient qualified workers
- **Training Alerts**: Overdue training requirements
- **Verification Alerts**: Certificates requiring manual verification
- **Incident Alerts**: Workers with multiple recent incidents

This comprehensive workflow ensures proper management of workers throughout their entire lifecycle while maintaining compliance, performance standards, and operational efficiency.
