import React, { useEffect, useRef } from 'react';

interface GraphPaperLinesProps {
  className?: string;
}

interface GridLine {
  x: number;
  y: number;
  type: 'horizontal' | 'vertical';
  opacity: number;
  createdAt: number;
  length: number;
  colorPhase: number; // 0-1 for color transition
  interacting: boolean; // whether line is near a background element
}

const GraphPaperLines: React.FC<GraphPaperLinesProps> = ({ className = '' }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const mouseRef = useRef({ x: 0, y: 0 });
  const gridLinesRef = useRef<GridLine[]>([]);
  const lastMouseMoveRef = useRef(0);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const GRID_SIZE = 20;
    const FADE_DURATION = 2000; // 2 seconds
    const LINE_SPAWN_DELAY = 100; // milliseconds

    // Background element detection areas (approximate positions)
    const backgroundElements = [
      { x: 40, y: 80, width: 24, height: 24 }, // top-left area
      { x: window.innerWidth - 80, y: 160, width: 32, height: 32 }, // top-right area
      { x: window.innerWidth / 4, y: 240, width: 24, height: 24 }, // top-quarter area
      { x: window.innerWidth - 40, y: window.innerHeight / 3, width: 40, height: 40 }, // middle-right
      { x: 64, y: window.innerHeight / 2, width: 32, height: 32 }, // middle-left
      { x: (window.innerWidth * 3) / 4, y: (window.innerHeight * 2) / 3, width: 40, height: 40 }, // bottom-right quarter
      { x: 80, y: window.innerHeight - 160, width: 32, height: 32 }, // bottom-left
      { x: window.innerWidth - 64, y: window.innerHeight - 240, width: 24, height: 24 }, // bottom-right
    ];

    // Function to check if a line intersects with background elements
    const checkLineInteraction = (line: GridLine) => {
      for (const element of backgroundElements) {
        if (line.type === 'horizontal') {
          // Check if horizontal line passes through element
          if (line.y >= element.y && line.y <= element.y + element.height &&
              line.x <= element.x + element.width && line.x + line.length >= element.x) {
            return true;
          }
        } else {
          // Check if vertical line passes through element
          if (line.x >= element.x && line.x <= element.x + element.width &&
              line.y <= element.y + element.height && line.y + line.length >= element.y) {
            return true;
          }
        }
      }
      return false;
    };

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Mouse move handler
    const handleMouseMove = (e: MouseEvent) => {
      const now = Date.now();
      mouseRef.current.x = e.clientX;
      mouseRef.current.y = e.clientY;

      // Throttle line creation
      if (now - lastMouseMoveRef.current > LINE_SPAWN_DELAY) {
        lastMouseMoveRef.current = now;

        // Create grid lines around mouse position
        const mouseGridX = Math.round(mouseRef.current.x / GRID_SIZE) * GRID_SIZE;
        const mouseGridY = Math.round(mouseRef.current.y / GRID_SIZE) * GRID_SIZE;

        // Add horizontal lines
        for (let i = -2; i <= 2; i++) {
          const y = mouseGridY + (i * GRID_SIZE);
          if (y >= 0 && y <= canvas.height) {
            gridLinesRef.current.push({
              x: mouseGridX - GRID_SIZE * 3,
              y: y,
              type: 'horizontal',
              opacity: 0.6,
              createdAt: now,
              length: GRID_SIZE * 6,
              colorPhase: Math.random(),
              interacting: false
            });
          }
        }

        // Add vertical lines
        for (let i = -2; i <= 2; i++) {
          const x = mouseGridX + (i * GRID_SIZE);
          if (x >= 0 && x <= canvas.width) {
            gridLinesRef.current.push({
              x: x,
              y: mouseGridY - GRID_SIZE * 3,
              type: 'vertical',
              opacity: 0.6,
              createdAt: now,
              length: GRID_SIZE * 6,
              colorPhase: Math.random(),
              interacting: false
            });
          }
        }

        // Limit number of lines to prevent memory issues
        if (gridLinesRef.current.length > 200) {
          gridLinesRef.current = gridLinesRef.current.slice(-100);
        }
      }
    };

    // Animation loop
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      const now = Date.now();

      // Update and draw grid lines
      gridLinesRef.current = gridLinesRef.current.filter(line => {
        const age = now - line.createdAt;

        // Calculate opacity based on age
        if (age > FADE_DURATION) {
          return false; // Remove old lines
        }

        const fadeProgress = age / FADE_DURATION;
        line.opacity = 0.6 * (1 - fadeProgress);

        // Check for interaction with background elements
        line.interacting = checkLineInteraction(line);

        // Color transition based on fade progress and color phase
        let r, g, b;
        if (fadeProgress < 0.3) {
          // Start with orange
          r = 249; g = 115; b = 22;
        } else if (fadeProgress < 0.7) {
          // Transition to purple/pink/green based on colorPhase
          const transitionProgress = (fadeProgress - 0.3) / 0.4;
          if (line.colorPhase < 0.33) {
            // Purple transition
            r = Math.floor(249 + (147 - 249) * transitionProgress); // 249 -> 147 (purple)
            g = Math.floor(115 + (51 - 115) * transitionProgress);  // 115 -> 51
            b = Math.floor(22 + (234 - 22) * transitionProgress);   // 22 -> 234
          } else if (line.colorPhase < 0.66) {
            // Pink transition
            r = Math.floor(249 + (236 - 249) * transitionProgress); // 249 -> 236 (pink)
            g = Math.floor(115 + (72 - 115) * transitionProgress);  // 115 -> 72
            b = Math.floor(22 + (153 - 22) * transitionProgress);   // 22 -> 153
          } else {
            // Green transition
            r = Math.floor(249 + (34 - 249) * transitionProgress);  // 249 -> 34 (green)
            g = Math.floor(115 + (197 - 115) * transitionProgress); // 115 -> 197
            b = Math.floor(22 + (94 - 22) * transitionProgress);    // 22 -> 94
          }
        } else {
          // Final color based on colorPhase
          if (line.colorPhase < 0.33) {
            r = 147; g = 51; b = 234; // purple
          } else if (line.colorPhase < 0.66) {
            r = 236; g = 72; b = 153; // pink
          } else {
            r = 34; g = 197; b = 94;  // green
          }
        }

        // Create gradient
        const gradient = line.type === 'horizontal'
          ? ctx.createLinearGradient(line.x, line.y, line.x + line.length, line.y)
          : ctx.createLinearGradient(line.x, line.y, line.x, line.y + line.length);

        gradient.addColorStop(0, `rgba(${r}, ${g}, ${b}, 0)`); // transparent
        gradient.addColorStop(0.5, `rgba(${r}, ${g}, ${b}, ${line.opacity})`); // colored
        gradient.addColorStop(1, `rgba(${r}, ${g}, ${b}, 0)`); // transparent

        // Draw line with interaction effects
        ctx.beginPath();
        ctx.strokeStyle = gradient;
        ctx.lineWidth = line.interacting ? 2 : 1; // Thicker lines when interacting

        // Add glow effect for interacting lines
        if (line.interacting) {
          ctx.shadowColor = `rgba(${r}, ${g}, ${b}, ${line.opacity * 0.5})`;
          ctx.shadowBlur = 8;
        } else {
          ctx.shadowBlur = 0;
        }

        if (line.type === 'horizontal') {
          ctx.moveTo(line.x, line.y);
          ctx.lineTo(line.x + line.length, line.y);
        } else {
          ctx.moveTo(line.x, line.y);
          ctx.lineTo(line.x, line.y + line.length);
        }

        ctx.stroke();

        // Reset shadow for next drawing
        ctx.shadowBlur = 0;

        return true;
      });

      requestAnimationFrame(animate);
    };

    // Start animation
    document.addEventListener('mousemove', handleMouseMove);
    animate();

    // Cleanup
    return () => {
      window.removeEventListener('resize', resizeCanvas);
      document.removeEventListener('mousemove', handleMouseMove);
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className={`fixed inset-0 pointer-events-none z-10 ${className}`}
      style={{ mixBlendMode: 'multiply' }}
    />
  );
};

export default GraphPaperLines;
