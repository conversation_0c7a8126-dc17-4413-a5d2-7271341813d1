import React, { useState, useEffect } from 'react';
import {
  Search,
  CheckCircle,
  XCircle,
  Clock,
  Calendar,
  MapPin,
  Shield,
  // Alert<PERSON>riangle,
  ClipboardList
} from 'lucide-react';
import { Task } from '../../types/tasks';

interface TaskApprovalListProps {
  siteId: string;
}

interface TaskApproval {
  task: Task;
  submittedBy: string;
  submittedAt: Date;
}

const TaskApprovalList: React.FC<TaskApprovalListProps> = ({ siteId }) => {
  const [pendingTasks, setPendingTasks] = useState<TaskApproval[]>([]);
  const [filteredTasks, setFilteredTasks] = useState<TaskApproval[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [disapprovalReason, setDisapprovalReason] = useState('');
  const [taskToDisapprove, setTaskToDisapprove] = useState<string | null>(null);
  const [showDisapprovalModal, setShowDisapprovalModal] = useState(false);

  useEffect(() => {
    fetchPendingTasks();
  }, [siteId]);

  useEffect(() => {
    // Apply search filter
    if (searchTerm) {
      setFilteredTasks(pendingTasks.filter(item =>
        item.task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.task.taskNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.task.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.submittedBy.toLowerCase().includes(searchTerm.toLowerCase())
      ));
    } else {
      setFilteredTasks(pendingTasks);
    }
  }, [pendingTasks, searchTerm]);
  const fetchPendingTasks = async () => {
    // In a real app, this would be an API call
    // For now, using mock data
    const mockPendingTasks: TaskApproval[] = [
      {
        task: {
          id: "task-approval-1",
          taskNumber: "TSK-2024-101",
          title: "Morning Safety Briefing",
          description: "Conduct safety briefing for all workers on site",
          category: "safety",
          location: "Main Office - Site A",
          siteId: "site-1",
          plannedStartDate: new Date(new Date().setDate(new Date().getDate() + 1)),
          plannedEndDate: new Date(new Date().setDate(new Date().getDate() + 1)),
          estimatedDuration: 1,
          // status: "pending-approval",
          priority: "high",
          progressPercentage: 0,
          createdBy: "supervisor-1",
          createdByName: "John Smith",
          assignedSupervisor: "supervisor-1",
          assignedSupervisorName: "John Smith",
          // assignedWorkers: ["worker-1", "worker-6", "worker-8"],
          dependencies: [],
          requiresPermit: false,
          riskLevel: "low",
          safetyRequirements: [],
          requiredPPE: ["hard-hat", "safety-vest"],
          requiredTrainings: ["safety-orientation"],
          requiredCertifications: [],
          ramsDocuments: [],
          attachments: [],
          qualityChecks: [],
          complianceRequirements: [],
          history: [],
          tags: ["safety", "daily"],
          customFields: {},
          createdAt: new Date(),
          updatedAt: new Date(),
          status: 'completed',
          assignedWorkers: []
        },
        submittedBy: "John Smith",
        submittedAt: new Date(new Date().setHours(new Date().getHours() - 2))
      },
      {
        task: {
          id: "task-approval-2",
          taskNumber: "TSK-2024-102",
          title: "Electrical Wiring - Floor 3",
          description: "Install electrical wiring for Floor 3 office spaces",
          category: "electrical",
          location: "Zone B - Floor 3",
          siteId: "site-1",
          plannedStartDate: new Date(new Date().setDate(new Date().getDate() + 1)),
          plannedEndDate: new Date(new Date().setDate(new Date().getDate() + 1)),
          estimatedDuration: 8,
          // status: "pending-approval",
          priority: "medium",
          progressPercentage: 0,
          createdBy: "supervisor-2",
          createdByName: "Sarah Johnson",
          assignedSupervisor: "supervisor-2",
          assignedSupervisorName: "Sarah Johnson",
          // assignedWorkers: ["worker-2"],
          dependencies: [],
          requiresPermit: true,
          permitTypes: ["electrical-work"],
          riskLevel: "medium",
          safetyRequirements: ["Lock-out/Tag-out procedure"],
          requiredPPE: ["hard-hat", "safety-vest", "insulated-gloves"],
          requiredTrainings: ["electrical-safety"],
          requiredCertifications: ["electrician-license"],
          ramsDocuments: [],
          attachments: [],
          qualityChecks: [],
          complianceRequirements: [],
          history: [],
          tags: ["electrical", "installation"],
          customFields: {},
          createdAt: new Date(),
          updatedAt: new Date(),
          status: 'completed',
          assignedWorkers: []
        },
        submittedBy: "Sarah Johnson",
        submittedAt: new Date(new Date().setHours(new Date().getHours() - 4))
      },
      {
        task: {
          id: "task-approval-3",
          taskNumber: "TSK-2024-103",
          title: "Concrete Pouring - Foundation",
          description: "Pour concrete for foundation at Building C",
          category: "construction",
          location: "Building C - Foundation",
          siteId: "site-1",
          plannedStartDate: new Date(new Date().setDate(new Date().getDate() + 1)),
          plannedEndDate: new Date(new Date().setDate(new Date().getDate() + 1)),
          estimatedDuration: 6,
          // status: "pending-approval",
          priority: "high",
          progressPercentage: 0,
          createdBy: "supervisor-1",
          createdByName: "John Smith",
          assignedSupervisor: "supervisor-1",
          assignedSupervisorName: "John Smith",
          // assignedWorkers: ["worker-1", "worker-5", "worker-8"],
          dependencies: [],
          requiresPermit: false,
          riskLevel: "medium",
          safetyRequirements: [],
          requiredPPE: ["hard-hat", "safety-vest", "safety-boots", "gloves"],
          requiredTrainings: ["safety-orientation", "heavy-machinery"],
          requiredCertifications: [],
          ramsDocuments: [],
          attachments: [],
          qualityChecks: [],
          complianceRequirements: [],
          history: [],
          tags: ["construction", "concrete"],
          customFields: {},
          createdAt: new Date(),
          updatedAt: new Date(),
          status: 'completed',
          assignedWorkers: []
        },
        submittedBy: "John Smith",
        submittedAt: new Date(new Date().setHours(new Date().getHours() - 1))
      }
    ];

    setPendingTasks(mockPendingTasks);
    setFilteredTasks(mockPendingTasks);
    setLoading(false);
  };

  const handleApprove = (taskId: string) => {
    // In a real app, this would be an API call
    console.log(`Approving task ${taskId}`);

    // Remove from pending list
    setPendingTasks(prev => prev.filter(item => item.task.id !== taskId));
  };

  const openDisapprovalModal = (taskId: string) => {
    setTaskToDisapprove(taskId);
    setDisapprovalReason('');
    setShowDisapprovalModal(true);
  };

  const handleDisapprove = () => {
    if (!taskToDisapprove || !disapprovalReason.trim()) {
      return;
    }

    // In a real app, this would be an API call
    console.log(`Disapproving task ${taskToDisapprove} with reason: ${disapprovalReason}`);

    // Remove from pending list
    setPendingTasks(prev => prev.filter(item => item.task.id !== taskToDisapprove));

    // Close modal and reset
    setShowDisapprovalModal(false);
    setTaskToDisapprove(null);
    setDisapprovalReason('');
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Tasks Pending Approval</h2>
      </div>

      {/* Search Filter */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search tasks..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>
      {/* Tasks List */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        {loading ? (
          <div className="text-center py-10">Loading tasks...</div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredTasks.length === 0 ? (
              <div className="p-12 text-center">
                <ClipboardList className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No tasks pending approval</h3>
                <p className="text-gray-500">
                  {searchTerm ? 'Try adjusting your search to see more results.' : 'All tasks have been reviewed.'}
                </p>
              </div>
            ) : (
              filteredTasks.map((item) => (
                <div key={item.task.id} className="p-6 hover:bg-gray-50">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-medium text-gray-900">{item.task.title}</h3>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${item.task.priority === 'high' ? 'bg-red-100 text-red-800' :
                            item.task.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                              'bg-green-100 text-green-800'
                          }`}>
                          {item.task.priority.toUpperCase()}
                        </span>
                        <span className="px-2 py-1 text-xs font-medium rounded-full bg-amber-100 text-amber-800">
                          PENDING APPROVAL
                        </span>
                      </div>

                      <p className="text-sm text-gray-600 mb-3">
                        {item.task.description}
                      </p>

                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm text-gray-500">
                        <div className="flex items-center">
                          <MapPin className="h-4 w-4 mr-1" />
                          {item.task.location}
                        </div>
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-1" />
                          {item.task.estimatedDuration}h estimated
                        </div>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          Planned: {formatDate(item.task.plannedStartDate)}
                        </div>
                        <div className="flex items-center">
                          <Shield className="h-4 w-4 mr-1" />
                          {item.task.riskLevel} risk
                        </div>
                      </div>
                      {item.task.requiresPermit && item.task.permitTypes && (
                        <div className="mt-3 text-xs">
                          <span className="text-gray-500">Required permits: </span>
                          {item.task.permitTypes.map(permit => (
                            <span key={permit} className="bg-purple-100 text-purple-800 px-2 py-1 rounded mr-1">
                              {permit}
                            </span>
                          ))}
                        </div>
                      )}

                      {item.task.requiredPPE && item.task.requiredPPE.length > 0 && (
                        <div className="mt-2 text-xs">
                          <span className="text-gray-500">Required PPE: </span>
                          {item.task.requiredPPE.map(ppe => (
                            <span key={ppe} className="bg-blue-100 text-blue-800 px-2 py-1 rounded mr-1">
                              {ppe}
                            </span>
                          ))}
                        </div>
                      )}

                      <div className="mt-4 flex justify-between text-xs text-gray-500">
                        <div>
                          Submitted by: <span className="font-medium">{item.submittedBy}</span>
                        </div>
                        <div>
                          {formatDate(item.submittedAt)} at {formatTime(item.submittedAt)}
                        </div>
                      </div>

                      <div className="mt-4 flex space-x-4">
                        <button
                          onClick={() => handleApprove(item.task.id)}
                          className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700"
                        >
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Approve
                        </button>
                        <button
                          onClick={() => openDisapprovalModal(item.task.id)}
                          className="flex items-center px-4 py-2 bg-red-600 text-white rounded-md text-sm font-medium hover:bg-red-700"
                        >
                          <XCircle className="h-4 w-4 mr-2" />
                          Disapprove
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </div>
      {/* Disapproval Modal */}
      {showDisapprovalModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Provide Reason for Disapproval</h3>

            <div className="mb-4">
              <label htmlFor="disapprovalReason" className="block text-sm font-medium text-gray-700 mb-1">
                Reason <span className="text-red-500">*</span>
              </label>
              <textarea
                id="disapprovalReason"
                rows={4}
                value={disapprovalReason}
                onChange={(e) => setDisapprovalReason(e.target.value)}
                placeholder="Please provide a detailed reason for disapproval..."
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              />
              {disapprovalReason.trim() === '' && (
                <p className="mt-1 text-sm text-red-600">A reason is required for disapproval</p>
              )}
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => setShowDisapprovalModal(false)}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleDisapprove}
                disabled={disapprovalReason.trim() === ''}
                className={`px-4 py-2 rounded-md text-sm font-medium text-white ${disapprovalReason.trim() === ''
                    ? 'bg-red-300 cursor-not-allowed'
                    : 'bg-red-600 hover:bg-red-700'
                  }`}
              >
                Confirm Disapproval
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TaskApprovalList;   
