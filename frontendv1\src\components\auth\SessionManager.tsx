import React, { useState, useEffect } from 'react';
import { 
  Monitor, 
  Smartphone, 
  Tablet, 
  Globe, 
  X, 
  AlertCircle, 
  Clock,
  MapPin,
  LogOut
} from 'lucide-react';
import { useAuth } from '../../hooks/useAuthContext';
import { UserSession } from '../../types/auth';

const SessionManager: React.FC = () => {
  const [sessions, setSessions] = useState<UserSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [terminatingSession, setTerminatingSession] = useState<string | null>(null);

  const { user, getActiveSessions, endSession, logoutAllSessions, session: currentSession } = useAuth();

  useEffect(() => {
    loadSessions();
  }, []);

  const loadSessions = async () => {
    if (!user) return;

    try {
      setLoading(true);
      const activeSessions = await getActiveSessions();
      setSessions(activeSessions);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load sessions');
    } finally {
      setLoading(false);
    }
  };

  const handleEndSession = async (sessionId: string) => {
    try {
      setTerminatingSession(sessionId);
      await endSession(sessionId);
      await loadSessions();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to end session');
    } finally {
      setTerminatingSession(null);
    }
  };

  const handleLogoutAllSessions = async () => {
    if (!window.confirm('Are you sure you want to log out from all devices? This will end all active sessions.')) {
      return;
    }

    try {
      await logoutAllSessions();
      // This will redirect to login page
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to logout all sessions');
    }
  };

  const getDeviceIcon = (userAgent: string) => {
    const ua = userAgent.toLowerCase();
    
    if (ua.includes('mobile') || ua.includes('android') || ua.includes('iphone')) {
      return <Smartphone className="h-5 w-5 text-blue-500" />;
    } else if (ua.includes('tablet') || ua.includes('ipad')) {
      return <Tablet className="h-5 w-5 text-green-500" />;
    } else {
      return <Monitor className="h-5 w-5 text-gray-500" />;
    }
  };

  const getDeviceInfo = (userAgent: string) => {
    // Simple user agent parsing - in production, you might want to use a library
    const ua = userAgent.toLowerCase();
    
    if (ua.includes('chrome')) {
      return 'Chrome Browser';
    } else if (ua.includes('firefox')) {
      return 'Firefox Browser';
    } else if (ua.includes('safari')) {
      return 'Safari Browser';
    } else if (ua.includes('edge')) {
      return 'Edge Browser';
    } else {
      return 'Unknown Browser';
    }
  };

  const formatLastActivity = (lastActivity: string) => {
    const date = new Date(lastActivity);
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));

    if (diffInMinutes < 1) {
      return 'Just now';
    } else if (diffInMinutes < 60) {
      return `${diffInMinutes} minute${diffInMinutes > 1 ? 's' : ''} ago`;
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else {
      const days = Math.floor(diffInMinutes / 1440);
      return `${days} day${days > 1 ? 's' : ''} ago`;
    }
  };

  const isCurrentSession = (sessionId: string) => {
    return currentSession?.id === sessionId;
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow p-6">
        <div className="animate-pulse">
          <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            <div className="h-4 bg-gray-200 rounded"></div>
            <div className="h-4 bg-gray-200 rounded w-5/6"></div>
            <div className="h-4 bg-gray-200 rounded w-4/6"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium text-gray-900 flex items-center">
            <Globe className="h-5 w-5 mr-2 text-green-600" />
            Active Sessions
          </h3>
          {sessions.length > 1 && (
            <button
              onClick={handleLogoutAllSessions}
              className="inline-flex items-center px-3 py-1 border border-red-300 rounded-md text-sm font-medium text-red-700 bg-red-50 hover:bg-red-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              <LogOut className="h-4 w-4 mr-1" />
              Logout All
            </button>
          )}
        </div>
        <p className="mt-1 text-sm text-gray-600">
          Manage your active sessions across different devices
        </p>
      </div>

      {error && (
        <div className="px-6 py-4 bg-red-50 border-b border-red-200">
          <div className="flex">
            <AlertCircle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      <div className="divide-y divide-gray-200">
        {sessions.length === 0 ? (
          <div className="px-6 py-8 text-center">
            <Globe className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No active sessions found</p>
          </div>
        ) : (
          sessions.map((session) => (
            <div key={session.id} className="px-6 py-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    {getDeviceIcon(session.userAgent)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <p className="text-sm font-medium text-gray-900">
                        {getDeviceInfo(session.userAgent)}
                      </p>
                      {isCurrentSession(session.id) && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Current
                        </span>
                      )}
                    </div>
                    <div className="flex items-center space-x-4 mt-1 text-sm text-gray-500">
                      <div className="flex items-center">
                        <MapPin className="h-4 w-4 mr-1" />
                        {session.ipAddress}
                      </div>
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-1" />
                        {formatLastActivity(session.lastActivity)}
                      </div>
                    </div>
                    <p className="text-xs text-gray-400 mt-1 truncate">
                      {session.userAgent}
                    </p>
                  </div>
                </div>
                <div className="flex-shrink-0">
                  {!isCurrentSession(session.id) && (
                    <button
                      onClick={() => handleEndSession(session.id)}
                      disabled={terminatingSession === session.id}
                      className="inline-flex items-center p-2 border border-transparent rounded-md text-red-400 hover:text-red-600 hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                      title="End Session"
                    >
                      {terminatingSession === session.id ? (
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-500"></div>
                      ) : (
                        <X className="h-4 w-4" />
                      )}
                    </button>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      <div className="px-6 py-4 bg-gray-50 border-t border-gray-200">
        <div className="flex items-center text-sm text-gray-600">
          <AlertCircle className="h-4 w-4 mr-2" />
          <span>
            Sessions automatically expire after 24 hours of inactivity. 
            You can manually end sessions from other devices for security.
          </span>
        </div>
      </div>
    </div>
  );
};

export default SessionManager;
