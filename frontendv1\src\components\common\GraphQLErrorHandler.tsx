import React from 'react';
import { ApolloError } from '@apollo/client';
import { AlertTriangle, Wifi, Server, Shield, RefreshCw } from 'lucide-react';

interface GraphQLErrorHandlerProps {
  error: ApolloError;
  onRetry?: () => void;
  showDetails?: boolean;
}

export const GraphQLErrorHandler: React.FC<GraphQLErrorHandlerProps> = ({
  error,
  onRetry,
  showDetails = false,
}) => {
  const getErrorType = () => {
    if (error.networkError) {
      return 'network';
    }
    if (error.graphQLErrors?.length > 0) {
      const firstError = error.graphQLErrors[0];
      if (firstError.extensions?.code === 'UNAUTHENTICATED') {
        return 'auth';
      }
      if (firstError.extensions?.code === 'FORBIDDEN') {
        return 'permission';
      }
      return 'graphql';
    }
    return 'unknown';
  };

  const getErrorConfig = () => {
    const errorType = getErrorType();
    
    switch (errorType) {
      case 'network':
        return {
          icon: <Wifi className="h-8 w-8 text-red-500" />,
          title: 'Connection Error',
          message: 'Unable to connect to the server. Please check your internet connection.',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          textColor: 'text-red-800',
        };
      case 'auth':
        return {
          icon: <Shield className="h-8 w-8 text-yellow-500" />,
          title: 'Authentication Required',
          message: 'Your session has expired. Please log in again.',
          bgColor: 'bg-yellow-50',
          borderColor: 'border-yellow-200',
          textColor: 'text-yellow-800',
        };
      case 'permission':
        return {
          icon: <Shield className="h-8 w-8 text-orange-500" />,
          title: 'Access Denied',
          message: 'You do not have permission to perform this action.',
          bgColor: 'bg-orange-50',
          borderColor: 'border-orange-200',
          textColor: 'text-orange-800',
        };
      case 'graphql':
        return {
          icon: <Server className="h-8 w-8 text-red-500" />,
          title: 'Server Error',
          message: 'The server encountered an error processing your request.',
          bgColor: 'bg-red-50',
          borderColor: 'border-red-200',
          textColor: 'text-red-800',
        };
      default:
        return {
          icon: <AlertTriangle className="h-8 w-8 text-gray-500" />,
          title: 'Unknown Error',
          message: 'An unexpected error occurred.',
          bgColor: 'bg-gray-50',
          borderColor: 'border-gray-200',
          textColor: 'text-gray-800',
        };
    }
  };

  const config = getErrorConfig();

  const handleAuthError = () => {
    // Redirect to login or refresh token
    window.location.href = '/login';
  };

  const getActionButton = () => {
    const errorType = getErrorType();
    
    if (errorType === 'auth') {
      return (
        <button
          onClick={handleAuthError}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
        >
          <Shield className="h-4 w-4 mr-2" />
          Log In Again
        </button>
      );
    }

    if (onRetry && (errorType === 'network' || errorType === 'graphql')) {
      return (
        <button
          onClick={onRetry}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </button>
      );
    }

    return null;
  };

  return (
    <div className={`rounded-lg border p-6 ${config.bgColor} ${config.borderColor}`}>
      <div className="flex items-start">
        <div className="flex-shrink-0">
          {config.icon}
        </div>
        <div className="ml-3 flex-1">
          <h3 className={`text-lg font-medium ${config.textColor}`}>
            {config.title}
          </h3>
          <p className={`mt-2 text-sm ${config.textColor}`}>
            {config.message}
          </p>

          {showDetails && (
            <div className="mt-4">
              <details className="cursor-pointer">
                <summary className={`text-sm font-medium ${config.textColor} hover:underline`}>
                  Technical Details
                </summary>
                <div className="mt-2 p-3 bg-white rounded border">
                  {error.networkError && (
                    <div className="mb-2">
                      <strong>Network Error:</strong>
                      <pre className="text-xs mt-1 text-gray-600">
                        {error.networkError.message}
                      </pre>
                    </div>
                  )}
                  {error.graphQLErrors?.map((gqlError, index) => (
                    <div key={index} className="mb-2">
                      <strong>GraphQL Error {index + 1}:</strong>
                      <pre className="text-xs mt-1 text-gray-600">
                        {gqlError.message}
                      </pre>
                      {gqlError.locations && (
                        <pre className="text-xs mt-1 text-gray-500">
                          Location: Line {gqlError.locations[0]?.line}, Column {gqlError.locations[0]?.column}
                        </pre>
                      )}
                      {gqlError.path && (
                        <pre className="text-xs mt-1 text-gray-500">
                          Path: {gqlError.path.join(' → ')}
                        </pre>
                      )}
                    </div>
                  ))}
                </div>
              </details>
            </div>
          )}

          <div className="mt-4">
            {getActionButton()}
          </div>
        </div>
      </div>
    </div>
  );
};

// Hook for consistent error handling across components
export const useGraphQLErrorHandler = () => {
  const handleError = (error: ApolloError, context?: string) => {
    console.error(`GraphQL Error${context ? ` in ${context}` : ''}:`, error);
    
    // You can add additional error reporting here
    // e.g., send to error tracking service like Sentry
    
    return error;
  };

  const isRetryableError = (error: ApolloError) => {
    if (error.networkError) return true;
    if (error.graphQLErrors?.some(e => e.extensions?.code === 'INTERNAL_ERROR')) return true;
    return false;
  };

  const shouldShowRetry = (error: ApolloError) => {
    return isRetryableError(error);
  };

  return {
    handleError,
    isRetryableError,
    shouldShowRetry,
  };
};
