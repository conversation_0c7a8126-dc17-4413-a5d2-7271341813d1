# Universal Filter Modal Component

A comprehensive, reusable filter modal component designed for enterprise applications with support for multiple filter types, accessibility features, and responsive design.

## Features

- **Multiple Filter Types**: Dropdown, multiselect, checkbox, date range, text, and number inputs
- **Accessibility**: Full WCAG 2.1 AA compliance with keyboard navigation and screen reader support
- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile devices
- **Brand Consistency**: Uses the application's green color scheme and design system
- **Focus Management**: Proper focus handling and keyboard navigation
- **Active Filter Indicators**: Shows count of active filters and visual indicators
- **Flexible Configuration**: Easy to configure for different pages and use cases

## Basic Usage

```typescript
import React, { useState } from 'react';
import UniversalFilterModal, { FilterValues } from './UniversalFilterModal';
import { workerDirectoryFilters } from './filterConfigs';

const MyComponent = () => {
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [filterValues, setFilterValues] = useState<FilterValues>({});

  const handleApplyFilters = (values: FilterValues) => {
    setFilterValues(values);
    // Apply filters to your data
    console.log('Applied filters:', values);
  };

  const handleClearFilters = () => {
    setFilterValues({});
    // Clear filters from your data
  };

  return (
    <div>
      <button onClick={() => setIsFilterOpen(true)}>
        Open Filters
      </button>

      <UniversalFilterModal
        isOpen={isFilterOpen}
        onClose={() => setIsFilterOpen(false)}
        title="Filter Workers"
        subtitle="Refine your search to find specific workers"
        filters={workerDirectoryFilters}
        initialValues={filterValues}
        onApplyFilters={handleApplyFilters}
        onClearFilters={handleClearFilters}
        size="md"
      />
    </div>
  );
};
```

## Filter Configuration

### Filter Types

#### 1. Dropdown
Single selection from a list of options.

```typescript
{
  id: 'trade',
  label: 'Trade',
  type: 'dropdown',
  placeholder: 'Select trade',
  options: [
    { value: 'carpenter', label: 'Carpenter', count: 12 },
    { value: 'electrician', label: 'Electrician', count: 8 }
  ]
}
```

#### 2. Multiselect
Multiple selections from a list with checkboxes.

```typescript
{
  id: 'status',
  label: 'Worker Status',
  type: 'multiselect',
  options: [
    { value: 'active', label: 'Active', count: 42 },
    { value: 'on_leave', label: 'On Leave', count: 3 }
  ]
}
```

#### 3. Checkbox
Simple checkbox options for boolean-like filters.

```typescript
{
  id: 'onSite',
  label: 'Currently On Site',
  type: 'checkbox',
  options: [
    { value: 'true', label: 'Show only workers currently on site', count: 28 }
  ]
}
```

#### 4. Date Range
Start and end date selection.

```typescript
{
  id: 'hireDate',
  label: 'Hire Date Range',
  type: 'daterange'
}
```

#### 5. Text Input
Free text search input.

```typescript
{
  id: 'search',
  label: 'Search',
  type: 'text',
  placeholder: 'Search by name or ID'
}
```

#### 6. Number Input
Numeric input with optional min/max constraints.

```typescript
{
  id: 'experienceYears',
  label: 'Years of Experience',
  type: 'number',
  placeholder: 'Minimum years',
  min: 0,
  max: 50
}
```

## Integration Examples

### Workers Page Integration

```typescript
// WorkerDirectory.tsx
import React, { useState, useMemo } from 'react';
import UniversalFilterModal, { FilterValues } from '../components/common/UniversalFilterModal';
import { workerDirectoryFilters } from '../components/common/filterConfigs';

const WorkerDirectory = () => {
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [activeFilters, setActiveFilters] = useState<FilterValues>({});
  const [workers, setWorkers] = useState([]); // Your worker data

  // Filter workers based on active filters
  const filteredWorkers = useMemo(() => {
    return workers.filter(worker => {
      // Trade filter
      if (activeFilters.trade && worker.primaryTrade !== activeFilters.trade) {
        return false;
      }

      // Compliance status filter
      if (activeFilters.complianceStatus && worker.complianceStatus !== activeFilters.complianceStatus) {
        return false;
      }

      // Multi-select status filter
      if (activeFilters.status && activeFilters.status.length > 0) {
        if (!activeFilters.status.includes(worker.status)) {
          return false;
        }
      }

      // On-site checkbox filter
      if (activeFilters.onSite && activeFilters.onSite.includes('true')) {
        if (!worker.isOnSite) {
          return false;
        }
      }

      // Experience years filter
      if (activeFilters.experienceYears && worker.experienceYears < parseInt(activeFilters.experienceYears)) {
        return false;
      }

      // Date range filter
      if (activeFilters.hireDate && activeFilters.hireDate.start) {
        const hireDate = new Date(worker.hireDate);
        const startDate = new Date(activeFilters.hireDate.start);
        const endDate = activeFilters.hireDate.end ? new Date(activeFilters.hireDate.end) : new Date();
        
        if (hireDate < startDate || hireDate > endDate) {
          return false;
        }
      }

      return true;
    });
  }, [workers, activeFilters]);

  const handleApplyFilters = (values: FilterValues) => {
    setActiveFilters(values);
  };

  const handleClearFilters = () => {
    setActiveFilters({});
  };

  // Count active filters for display
  const activeFilterCount = Object.values(activeFilters).filter(value => {
    if (Array.isArray(value)) return value.length > 0;
    if (typeof value === 'object' && value !== null) {
      return Object.values(value).some(v => v !== '' && v !== null);
    }
    return value !== '' && value !== null && value !== false;
  }).length;

  return (
    <div>
      {/* Filter Button */}
      <button
        onClick={() => setIsFilterOpen(true)}
        className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
      >
        <Filter className="h-4 w-4 mr-2" />
        Filters
        {activeFilterCount > 0 && (
          <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            {activeFilterCount}
          </span>
        )}
      </button>

      {/* Active Filters Display */}
      {activeFilterCount > 0 && (
        <div className="mt-4 flex flex-wrap gap-2">
          {Object.entries(activeFilters).map(([key, value]) => {
            if (!value || (Array.isArray(value) && value.length === 0)) return null;
            
            const filter = workerDirectoryFilters.find(f => f.id === key);
            if (!filter) return null;

            let displayValue = '';
            if (Array.isArray(value)) {
              displayValue = value.join(', ');
            } else if (typeof value === 'object' && value.start) {
              displayValue = `${value.start} - ${value.end || 'Present'}`;
            } else {
              displayValue = value.toString();
            }

            return (
              <span
                key={key}
                className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-green-100 text-green-800"
              >
                {filter.label}: {displayValue}
                <button
                  onClick={() => {
                    const newFilters = { ...activeFilters };
                    delete newFilters[key];
                    setActiveFilters(newFilters);
                  }}
                  className="ml-2 text-green-600 hover:text-green-800"
                >
                  ×
                </button>
              </span>
            );
          })}
        </div>
      )}

      {/* Workers List */}
      <div className="mt-6">
        {filteredWorkers.map(worker => (
          <div key={worker.id}>
            {/* Worker card content */}
          </div>
        ))}
      </div>

      {/* Filter Modal */}
      <UniversalFilterModal
        isOpen={isFilterOpen}
        onClose={() => setIsFilterOpen(false)}
        title="Filter Workers"
        subtitle="Refine your search to find specific workers"
        filters={workerDirectoryFilters}
        initialValues={activeFilters}
        onApplyFilters={handleApplyFilters}
        onClearFilters={handleClearFilters}
        size="lg"
      />
    </div>
  );
};
```

## Accessibility Features

- **Keyboard Navigation**: Full keyboard support with Tab, Enter, Escape, and arrow keys
- **Focus Management**: Proper focus trapping within the modal
- **Screen Reader Support**: ARIA labels, roles, and descriptions
- **Color Contrast**: Meets WCAG 2.1 AA standards
- **Touch Targets**: Minimum 44px touch targets for mobile devices

## Responsive Design

The modal automatically adapts to different screen sizes:

- **Desktop**: Full-width modal with optimal spacing
- **Tablet**: Responsive grid layouts and touch-friendly controls
- **Mobile**: Stack filters vertically and optimize for thumb navigation

## Customization

### Size Options
- `sm`: Small modal (max-width: 28rem)
- `md`: Medium modal (max-width: 32rem) - Default
- `lg`: Large modal (max-width: 42rem)
- `xl`: Extra large modal (max-width: 56rem)

### Styling
The component uses Tailwind CSS classes and follows the application's design system. All colors use the green theme defined in the application's color palette.

## API Integration

For dynamic filter options, you can use the `getDynamicFilterOptions` helper function:

```typescript
import { getDynamicFilterOptions } from './filterConfigs';

// Fetch dynamic options
const tradeOptions = await getDynamicFilterOptions('trade', searchTerm);
```

## Best Practices

1. **Keep filters relevant**: Only include filters that are meaningful for your data
2. **Show counts**: Include option counts when possible to guide user decisions
3. **Provide defaults**: Set sensible default values for common use cases
4. **Clear feedback**: Always show active filter indicators
5. **Performance**: Debounce filter applications for large datasets
6. **Mobile-first**: Design filters with mobile users in mind

## Implementation Summary

The Universal Filter Modal has been successfully implemented with the following features:

### ✅ **Completed Features**
- **Modal Design**: Centered modal overlay with backdrop
- **Multiple Filter Types**: Dropdown, multiselect, checkbox, date range, text, number
- **Brand Consistency**: Uses green color scheme (#22c55e, #16a34a, #15803d)
- **Responsive Design**: Works on desktop, tablet, and mobile
- **Accessibility**: WCAG 2.1 AA compliant with keyboard navigation
- **Focus Management**: Proper focus trapping and restoration
- **Active Filter Indicators**: Shows count and visual indicators
- **Flexible Configuration**: Easy to configure for different pages

### 🎯 **Integration Status**
- **Workers Page**: ✅ Fully integrated and functional
- **Training Dashboard**: ✅ Example implementation created
- **Filter Configurations**: ✅ Pre-built configs for common use cases
- **Documentation**: ✅ Comprehensive guides and examples

### 📁 **File Structure**
```
frontendv1/src/components/common/
├── UniversalFilterModal.tsx          # Main modal component
├── filterConfigs.ts                  # Pre-built filter configurations
├── examples/
│   └── TrainingDashboardExample.tsx  # Complete example implementation
└── README.md                         # This documentation
```

### 🚀 **Quick Start**
1. Import the component: `import UniversalFilterModal from '../components/common/UniversalFilterModal'`
2. Define your filter configuration using the provided types
3. Add state management for modal visibility and filter values
4. Implement filter logic in your data processing
5. Add the modal component to your JSX

### 🔧 **Customization Options**
- **Size**: `sm`, `md`, `lg`, `xl`
- **Filter Types**: All major input types supported
- **Styling**: Follows application design system
- **Behavior**: Configurable validation and requirements

The component is now ready for use across the application and provides a consistent, accessible, and user-friendly filtering experience.
