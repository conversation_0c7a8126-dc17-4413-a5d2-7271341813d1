import React, { useState, useEffect, useRef } from 'react';
import { X, Filter, RotateCcw } from 'lucide-react';

// Filter type definitions
export type FilterType = 'dropdown' | 'multiselect' | 'checkbox' | 'daterange' | 'text' | 'number';

export interface FilterOption {
  value: string | number;
  label: string;
  count?: number; // Optional count for each option
}

export interface FilterConfig {
  id: string;
  label: string;
  type: FilterType;
  options?: FilterOption[];
  placeholder?: string;
  required?: boolean;
  defaultValue?: any;
  min?: number;
  max?: number;
}

export interface FilterValues {
  [key: string]: any;
}

export interface UniversalFilterModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  subtitle?: string;
  filters: FilterConfig[];
  initialValues?: FilterValues;
  onApplyFilters: (values: FilterValues) => void;
  onClearFilters?: () => void;
  showActiveCount?: boolean;
  size?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';
}

const UniversalFilterModal: React.FC<UniversalFilterModalProps> = ({
  isOpen,
  onClose,
  title = 'Filter Options',
  subtitle,
  filters,
  initialValues = {},
  onApplyFilters,
  onClearFilters,
  showActiveCount = true,
  size = 'xl'
}) => {
  const [filterValues, setFilterValues] = useState<FilterValues>(initialValues);
  const [, setHasChanges] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);
  const firstInputRef = useRef<HTMLInputElement>(null);

  // Size classes
  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl',
    '2xl': 'max-w-6xl'
  };

  // Initialize filter values
  useEffect(() => {
    if (isOpen) {
      const defaultValues = { ...initialValues };
      filters.forEach(filter => {
        if (filter.defaultValue !== undefined && !(filter.id in defaultValues)) {
          defaultValues[filter.id] = filter.defaultValue;
        }
      });
      setFilterValues(defaultValues);
      setHasChanges(false);
    }
  }, [isOpen, initialValues, filters]);

  // Focus management
  useEffect(() => {
    if (isOpen && firstInputRef.current) {
      setTimeout(() => {
        firstInputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  // Keyboard event handling
  useEffect(() => {
    if (!isOpen) return;

    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        handleClose();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen]);

  // Prevent body scroll when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const handleFilterChange = (filterId: string, value: any) => {
    setFilterValues(prev => ({
      ...prev,
      [filterId]: value
    }));
    setHasChanges(true);
  };

  const handleClose = () => {
    onClose();
  };

  const handleBackdropClick = (event: React.MouseEvent) => {
    if (event.target === event.currentTarget) {
      handleClose();
    }
  };

  const handleApply = () => {
    // Validate filter combinations
    // Validate filter combinations - commented out as not currently used
    // const hasValidFilters = Object.values(filterValues).some(value => {
    //   if (Array.isArray(value)) return value.length > 0;
    //   if (typeof value === 'object' && value !== null) {
    //     return Object.values(value).some(v => v !== '' && v !== null);
    //   }
    //   return value !== '' && value !== null && value !== false;
    // });

    // Apply filters even if no filters are selected (to show all results)
    onApplyFilters(filterValues);
    handleClose();
  };

  const handleClearAll = () => {
    const clearedValues: FilterValues = {};
    filters.forEach(filter => {
      switch (filter.type) {
        case 'multiselect':
          clearedValues[filter.id] = [];
          break;
        case 'checkbox':
          clearedValues[filter.id] = false;
          break;
        case 'daterange':
          clearedValues[filter.id] = { start: '', end: '' };
          break;
        default:
          clearedValues[filter.id] = '';
      }
    });
    setFilterValues(clearedValues);
    setHasChanges(true);
    if (onClearFilters) {
      onClearFilters();
    }
  };

  // Count active filters
  const activeFilterCount = Object.values(filterValues).filter(value => {
    if (Array.isArray(value)) return value.length > 0;
    if (typeof value === 'object' && value !== null) {
      return Object.values(value).some(v => v !== '' && v !== null);
    }
    return value !== '' && value !== null && value !== false;
  }).length;

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50"
      onClick={handleBackdropClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby="filter-modal-title"
    >
      <div
        ref={modalRef}
        className={`bg-white rounded-xl shadow-xl ${sizeClasses[size]} w-full max-h-[90vh] flex flex-col`}
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200 bg-white flex-shrink-0">
          <div className="flex items-center space-x-3">
            <Filter className="h-5 w-5 text-green-600" />
            <div>
              <h2 id="filter-modal-title" className="text-lg font-semibold text-gray-900">
                {title}
                {showActiveCount && activeFilterCount > 0 && (
                  <span className="ml-2 inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    {activeFilterCount}
                  </span>
                )}
              </h2>
              {subtitle && (
                <p className="text-sm text-gray-600 mt-1">{subtitle}</p>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            {/* Action buttons in header for always visible */}
            <button
              onClick={handleClearAll}
              disabled={activeFilterCount === 0}
              className="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <RotateCcw className="h-3 w-3 mr-1" />
              Clear
            </button>
            <button
              onClick={handleClose}
              className="px-3 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleApply}
              className="px-3 py-1.5 text-xs font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-colors"
            >
              Apply
            </button>
            <button
              onClick={handleClose}
              className="text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-md hover:bg-gray-100 ml-2"
              aria-label="Close filter modal"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>

        {/* Content - Scrollable */}
        <div className="flex-1 overflow-y-auto p-6">
          <div className="space-y-8">
            {filters.map((filter, index) => (
              <FilterInput
                key={filter.id}
                filter={filter}
                value={filterValues[filter.id]}
                onChange={(value) => handleFilterChange(filter.id, value)}
                ref={index === 0 ? firstInputRef : undefined}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// Individual filter input component
interface FilterInputProps {
  filter: FilterConfig;
  value: any;
  onChange: (value: any) => void;
}

const FilterInput = React.forwardRef<HTMLInputElement, FilterInputProps>(
  ({ filter, value, onChange }, ref) => {
    const getSelectedCount = () => {
      if (filter.type === 'multiselect' || filter.type === 'checkbox') {
        return Array.isArray(value) ? value.length : 0;
      }
      return value ? 1 : 0;
    };

    const handleSelectAll = () => {
      if (filter.options) {
        const allValues = filter.options.map(opt => opt.value);
        onChange(allValues);
      }
    };

    const handleClearAll = () => {
      if (filter.type === 'multiselect' || filter.type === 'checkbox') {
        onChange([]);
      } else {
        onChange('');
      }
    };

    const renderInput = () => {
      switch (filter.type) {
        case 'dropdown':
          // Convert dropdown to radio button pills for single selection
          return (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Select one option:</span>
                {value && (
                  <button
                    onClick={handleClearAll}
                    className="text-xs text-green-600 hover:text-green-800 font-medium"
                  >
                    Clear
                  </button>
                )}
              </div>
              <div className="flex flex-wrap gap-2">
                {filter.options?.map((option) => (
                  <label key={option.value} className="cursor-pointer">
                    <input
                      type="radio"
                      name={filter.id}
                      value={option.value}
                      checked={value === option.value}
                      onChange={(e) => onChange(e.target.value)}
                      className="sr-only"
                    />
                    <div className={`inline-flex items-center px-3 py-2 rounded-full text-sm font-medium border transition-all duration-200 ${
                      value === option.value
                        ? 'bg-green-100 text-green-800 border-green-300 ring-2 ring-green-500 ring-opacity-20'
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400'
                    }`}>
                      {option.label}
                      {option.count !== undefined && (
                        <span className="ml-1 text-xs opacity-75">({option.count})</span>
                      )}
                    </div>
                  </label>
                ))}
              </div>
            </div>
          );

        case 'text':
          return (
            <input
              ref={ref}
              type="text"
              value={value || ''}
              onChange={(e) => onChange(e.target.value)}
              placeholder={filter.placeholder}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
              required={filter.required}
            />
          );

        case 'multiselect':
          const selectedCount = getSelectedCount();
          return (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">
                  Select multiple options {selectedCount > 0 && `(${selectedCount} selected)`}
                </span>
                <div className="flex space-x-2">
                  {filter.options && filter.options.length > 0 && (
                    <button
                      onClick={handleSelectAll}
                      className="text-xs text-green-600 hover:text-green-800 font-medium"
                    >
                      Select All
                    </button>
                  )}
                  {selectedCount > 0 && (
                    <button
                      onClick={handleClearAll}
                      className="text-xs text-green-600 hover:text-green-800 font-medium"
                    >
                      Clear All
                    </button>
                  )}
                </div>
              </div>
              <div className="flex flex-wrap gap-2">
                {filter.options?.map((option) => (
                  <label key={option.value} className="cursor-pointer">
                    <input
                      type="checkbox"
                      checked={Array.isArray(value) ? value.includes(option.value) : false}
                      onChange={(e) => {
                        const currentValues = Array.isArray(value) ? value : [];
                        if (e.target.checked) {
                          onChange([...currentValues, option.value]);
                        } else {
                          onChange(currentValues.filter((v: any) => v !== option.value));
                        }
                      }}
                      className="sr-only"
                    />
                    <div className={`inline-flex items-center px-3 py-2 rounded-full text-sm font-medium border transition-all duration-200 ${
                      Array.isArray(value) && value.includes(option.value)
                        ? 'bg-green-100 text-green-800 border-green-300 ring-2 ring-green-500 ring-opacity-20'
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400'
                    }`}>
                      {option.label}
                      {option.count !== undefined && (
                        <span className="ml-1 text-xs opacity-75">({option.count})</span>
                      )}
                    </div>
                  </label>
                ))}
              </div>
            </div>
          );

        case 'checkbox':
          const checkboxSelectedCount = getSelectedCount();
          return (
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">
                  Options {checkboxSelectedCount > 0 && `(${checkboxSelectedCount} selected)`}
                </span>
                {checkboxSelectedCount > 0 && (
                  <button
                    onClick={handleClearAll}
                    className="text-xs text-green-600 hover:text-green-800 font-medium"
                  >
                    Clear
                  </button>
                )}
              </div>
              <div className="flex flex-wrap gap-2">
                {filter.options?.map((option) => (
                  <label key={option.value} className="cursor-pointer">
                    <input
                      type="checkbox"
                      checked={Array.isArray(value) ? value.includes(option.value) : false}
                      onChange={(e) => {
                        const currentValues = Array.isArray(value) ? value : [];
                        if (e.target.checked) {
                          onChange([...currentValues, option.value]);
                        } else {
                          onChange(currentValues.filter((v: any) => v !== option.value));
                        }
                      }}
                      className="sr-only"
                    />
                    <div className={`inline-flex items-center px-3 py-2 rounded-full text-sm font-medium border transition-all duration-200 ${
                      Array.isArray(value) && value.includes(option.value)
                        ? 'bg-green-100 text-green-800 border-green-300 ring-2 ring-green-500 ring-opacity-20'
                        : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50 hover:border-gray-400'
                    }`}>
                      {option.label}
                      {option.count !== undefined && (
                        <span className="ml-1 text-xs opacity-75">({option.count})</span>
                      )}
                    </div>
                  </label>
                ))}
              </div>
            </div>
          );

        case 'number':
          return (
            <input
              ref={ref}
              type="number"
              value={value || ''}
              onChange={(e) => onChange(e.target.value)}
              placeholder={filter.placeholder}
              min={filter.min}
              max={filter.max}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
              required={filter.required}
            />
          );

        // case 'text':
        //   return (
        //     <input
        //       ref={ref}
        //       type="text"
        //       value={value || ''}
        //       onChange={(e) => onChange(e.target.value)}
        //       placeholder={filter.placeholder}
        //       className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
        //       required={filter.required}
        //     />
        //   );

        case 'daterange':
          const dateValue = value || { start: '', end: '' };
          return (
            <div className="space-y-3">
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-xs text-gray-500 mb-1">From</label>
                  <input
                    type="date"
                    value={dateValue.start || ''}
                    onChange={(e) => onChange({ ...dateValue, start: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  />
                </div>
                <div>
                  <label className="block text-xs text-gray-500 mb-1">To</label>
                  <input
                    type="date"
                    value={dateValue.end || ''}
                    onChange={(e) => onChange({ ...dateValue, end: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  />
                </div>
              </div>
              {(dateValue.start || dateValue.end) && (
                <button
                  onClick={handleClearAll}
                  className="text-xs text-green-600 hover:text-green-800"
                >
                  Clear dates
                </button>
              )}
            </div>
          );

        default:
          return (
            <div className="text-sm text-gray-500">
              Filter type "{filter.type}" not implemented yet
            </div>
          );
      }
    };

    return (
      <div className="space-y-4">
        <label className="block text-base font-semibold text-gray-900">
          {filter.label}
          {filter.required && <span className="text-red-500 ml-1">*</span>}
        </label>
        {renderInput()}
      </div>
    );
  }
);

FilterInput.displayName = 'FilterInput';

export default UniversalFilterModal;
