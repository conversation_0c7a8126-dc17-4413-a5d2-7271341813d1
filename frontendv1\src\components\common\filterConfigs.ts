import { FilterConfig } from './UniversalFilterModal';

// Worker Directory Filter Configuration
export const workerDirectoryFilters: FilterConfig[] = [
  {
    id: 'trade',
    label: 'Trade',
    type: 'dropdown',
    placeholder: 'Select trade',
    options: [
      { value: 'carpenter', label: 'Carpenter', count: 12 },
      { value: 'electrician', label: 'Electrician', count: 8 },
      { value: 'plumber', label: 'Plumber', count: 6 },
      { value: 'mason', label: '<PERSON>', count: 15 },
      { value: 'welder', label: 'Welder', count: 4 },
      { value: 'painter', label: 'Painter', count: 7 },
    ]
  },
  {
    id: 'complianceStatus',
    label: 'Compliance Status',
    type: 'dropdown',
    placeholder: 'Select compliance status',
    options: [
      { value: 'compliant', label: 'Compliant', count: 35 },
      { value: 'pending_training', label: 'Pending Training', count: 8 },
      { value: 'non_compliant', label: 'Non-Compliant', count: 3 },
      { value: 'expired', label: 'Expired', count: 2 },
    ]
  },
  {
    id: 'status',
    label: 'Worker Status',
    type: 'multiselect',
    options: [
      { value: 'active', label: 'Active', count: 42 },
      { value: 'on_leave', label: 'On Leave', count: 3 },
      { value: 'suspended', label: 'Suspended', count: 1 },
      { value: 'terminated', label: 'Terminated', count: 2 },
    ]
  },
  {
    id: 'onSite',
    label: 'Currently On Site',
    type: 'checkbox',
    options: [
      { value: 'true', label: 'Show only workers currently on site', count: 28 }
    ]
  },
  {
    id: 'experienceYears',
    label: 'Years of Experience',
    type: 'number',
    placeholder: 'Minimum years',
    min: 0,
    max: 50
  },
  {
    id: 'hireDate',
    label: 'Hire Date Range',
    type: 'daterange'
  }
];

// Training Dashboard Filter Configuration
export const trainingDashboardFilters: FilterConfig[] = [
  {
    id: 'trainingType',
    label: 'Training Type',
    type: 'multiselect',
    options: [
      { value: 'safety', label: 'Safety Training', count: 25 },
      { value: 'technical', label: 'Technical Skills', count: 18 },
      { value: 'compliance', label: 'Compliance', count: 12 },
      { value: 'certification', label: 'Certification', count: 8 },
    ]
  },
  {
    id: 'status',
    label: 'Training Status',
    type: 'dropdown',
    placeholder: 'Select status',
    options: [
      { value: 'completed', label: 'Completed', count: 156 },
      { value: 'in_progress', label: 'In Progress', count: 23 },
      { value: 'scheduled', label: 'Scheduled', count: 45 },
      { value: 'overdue', label: 'Overdue', count: 7 },
    ]
  },
  {
    id: 'completionDate',
    label: 'Completion Date',
    type: 'daterange'
  },
  {
    id: 'expiryDate',
    label: 'Expiry Date',
    type: 'daterange'
  }
];

// Safety Dashboard Filter Configuration
export const safetyDashboardFilters: FilterConfig[] = [
  {
    id: 'incidentType',
    label: 'Incident Type',
    type: 'multiselect',
    options: [
      { value: 'near_miss', label: 'Near Miss', count: 15 },
      { value: 'injury', label: 'Injury', count: 3 },
      { value: 'property_damage', label: 'Property Damage', count: 2 },
      { value: 'environmental', label: 'Environmental', count: 1 },
    ]
  },
  {
    id: 'severity',
    label: 'Severity Level',
    type: 'dropdown',
    placeholder: 'Select severity',
    options: [
      { value: 'low', label: 'Low', count: 12 },
      { value: 'medium', label: 'Medium', count: 7 },
      { value: 'high', label: 'High', count: 2 },
      { value: 'critical', label: 'Critical', count: 0 },
    ]
  },
  {
    id: 'reportedBy',
    label: 'Reported By',
    type: 'dropdown',
    placeholder: 'Select reporter role',
    options: [
      { value: 'worker', label: 'Worker', count: 18 },
      { value: 'supervisor', label: 'Supervisor', count: 3 },
      { value: 'safety_officer', label: 'Safety Officer', count: 0 },
    ]
  },
  {
    id: 'dateRange',
    label: 'Incident Date',
    type: 'daterange'
  }
];

// Equipment Management Filter Configuration
export const equipmentFilters: FilterConfig[] = [
  {
    id: 'category',
    label: 'Equipment Category',
    type: 'dropdown',
    placeholder: 'Select category',
    options: [
      { value: 'heavy_machinery', label: 'Heavy Machinery', count: 8 },
      { value: 'tools', label: 'Tools', count: 45 },
      { value: 'safety_equipment', label: 'Safety Equipment', count: 67 },
      { value: 'vehicles', label: 'Vehicles', count: 12 },
    ]
  },
  {
    id: 'status',
    label: 'Equipment Status',
    type: 'multiselect',
    options: [
      { value: 'available', label: 'Available', count: 89 },
      { value: 'in_use', label: 'In Use', count: 34 },
      { value: 'maintenance', label: 'Under Maintenance', count: 7 },
      { value: 'out_of_service', label: 'Out of Service', count: 2 },
    ]
  },
  {
    id: 'location',
    label: 'Location',
    type: 'dropdown',
    placeholder: 'Select location',
    options: [
      { value: 'warehouse', label: 'Warehouse', count: 56 },
      { value: 'site_a', label: 'Construction Site A', count: 34 },
      { value: 'site_b', label: 'Construction Site B', count: 28 },
      { value: 'office', label: 'Office', count: 14 },
    ]
  },
  {
    id: 'lastMaintenance',
    label: 'Last Maintenance Date',
    type: 'daterange'
  }
];

// Project Management Filter Configuration
export const projectFilters: FilterConfig[] = [
  {
    id: 'status',
    label: 'Project Status',
    type: 'multiselect',
    options: [
      { value: 'planning', label: 'Planning', count: 5 },
      { value: 'active', label: 'Active', count: 12 },
      { value: 'on_hold', label: 'On Hold', count: 2 },
      { value: 'completed', label: 'Completed', count: 8 },
      { value: 'cancelled', label: 'Cancelled', count: 1 },
    ]
  },
  {
    id: 'priority',
    label: 'Priority Level',
    type: 'dropdown',
    placeholder: 'Select priority',
    options: [
      { value: 'low', label: 'Low', count: 6 },
      { value: 'medium', label: 'Medium', count: 15 },
      { value: 'high', label: 'High', count: 6 },
      { value: 'urgent', label: 'Urgent', count: 1 },
    ]
  },
  {
    id: 'budget',
    label: 'Budget Range (KES)',
    type: 'number',
    placeholder: 'Minimum budget',
    min: 0
  },
  {
    id: 'startDate',
    label: 'Project Start Date',
    type: 'daterange'
  },
  {
    id: 'client',
    label: 'Client',
    type: 'text',
    placeholder: 'Search by client name'
  }
];

// Helper function to get filter configuration by page type
export const getFilterConfig = (pageType: string): FilterConfig[] => {
  switch (pageType) {
    case 'workers':
      return workerDirectoryFilters;
    case 'training':
      return trainingDashboardFilters;
    case 'safety':
      return safetyDashboardFilters;
    case 'equipment':
      return equipmentFilters;
    case 'projects':
      return projectFilters;
    default:
      return [];
  }
};

// Helper function to get dynamic filter options (for API integration)
export const getDynamicFilterOptions = async (filterId: string, searchTerm?: string): Promise<any[]> => {
  // This would typically make an API call
  // For now, return mock data based on filter ID
  
  switch (filterId) {
    case 'trade':
      // Simulate API call for trades
      return [
        { value: 'carpenter', label: 'Carpenter', count: 12 },
        { value: 'electrician', label: 'Electrician', count: 8 },
        { value: 'plumber', label: 'Plumber', count: 6 },
      ].filter(option => 
        !searchTerm || option.label.toLowerCase().includes(searchTerm.toLowerCase())
      );
    
    case 'client':
      // Simulate API call for clients
      return [
        { value: 'client1', label: 'ABC Construction Ltd', count: 5 },
        { value: 'client2', label: 'XYZ Developers', count: 3 },
        { value: 'client3', label: 'Modern Homes Inc', count: 2 },
      ].filter(option => 
        !searchTerm || option.label.toLowerCase().includes(searchTerm.toLowerCase())
      );
    
    default:
      return [];
  }
};
