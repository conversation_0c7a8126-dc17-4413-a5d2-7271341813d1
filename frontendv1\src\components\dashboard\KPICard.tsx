import { ReactNode } from 'react';
import { useNavigate } from 'react-router-dom';

interface KPICardProps {
	title: string;
	value: string | number;
	change?: number;
	icon?: ReactNode;
	navigateTo?: string;
	className?: string;
	contextualMessage?: string; // New prop for intelligent contextual messaging
	priority?: 'high' | 'medium' | 'low'; // Priority level for styling
}

const KPICard = ({
	title,
	value,
	change,
	icon,
	navigateTo,
	className = '',
	contextualMessage,
	priority = 'low'
}: KPICardProps) => {
	const navigate = useNavigate();

	const handleClick = () => {
		if (navigateTo) {
			navigate(navigateTo);
		}
	};

	// Priority-based styling for contextual messages
	const getContextualMessageStyle = () => {
		switch (priority) {
			case 'high':
				return 'text-red-600 font-medium';
			case 'medium':
				return 'text-orange-600 font-medium';
			default:
				return 'text-gray-600';
		}
	};

	return (
		<div
			className={`bg-white p-4 rounded-lg border border-gray-200 shadow-sm ${navigateTo ? 'cursor-pointer hover:shadow-md transition-shadow' : ''} ${className}`}
			onClick={handleClick}
		>
			<div className="flex justify-between items-start">
				<div className="flex-1">
					{/* KPI text/title - first, greyed out to match Workers page */}
					<h3 className="text-sm font-medium text-gray-500">{title}</h3>

					{/* Count/number value - second, reduced font size to match Workers page */}
					<p className="text-2xl font-semibold mt-1">{value}</p>

					{/* Single contextual message - third (if any) */}
					{contextualMessage && (
						<p className={`text-xs mt-1 ${getContextualMessageStyle()}`}>
							{contextualMessage}
						</p>
					)}
				</div>
				{icon && <div className="p-2 rounded-full bg-gray-50 ml-3">{icon}</div>}
			</div>
		</div>
	);
};

export default KPICard;
