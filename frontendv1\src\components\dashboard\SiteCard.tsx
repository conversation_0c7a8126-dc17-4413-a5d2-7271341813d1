import { Link } from "react-router-dom";
import { SiteInfo } from "../../types";
import {
	AlertTriangle,
	Calendar,
	ChevronRight,
	FileCheck,
	MapPin,
	Users,
	MessageCircle,
	MoreHorizontal,
	ArrowRight,
} from "lucide-react";
import { useState, useRef, useEffect } from "react";

interface SiteCardProps {
	site: SiteInfo;
}

const SiteCard = ({ site }: SiteCardProps) => {
	const [showMenu, setShowMenu] = useState(false);
	const menuRef = useRef<HTMLDivElement>(null);

	const statusColorMap = {
		active: "border-gray-400 text-gray-600",
		draft: "border-gray-400 text-gray-600",
		paused: "border-gray-400 text-gray-600",
		closed: "border-gray-400 text-gray-600",
	};

	// Mock notification count - replace with actual data
	const notificationCount = 3;

	// Close menu when clicking outside
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
				setShowMenu(false);
			}
		};

		document.addEventListener('mousedown', handleClickOutside);
		return () => {
			document.removeEventListener('mousedown', handleClickOutside);
		};
	}, []);

	return (
		<div className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200">
			{/* Card Header */}
			<div className="p-4 border-b border-gray-100">
				<div className="flex items-center justify-between">
					{/* Status Badge */}
					<div className={`px-2 py-1 text-xs font-medium border rounded ${statusColorMap[site.status]} bg-transparent`} style={{ borderRadius: '5px' }}>
						{site.status.toUpperCase()}
					</div>

					{/* Right side icons */}
					<div className="flex items-center space-x-2">
						{/* Message Icon with notification count */}
						<div className="relative">
							<MessageCircle className="h-4 w-4 text-gray-500" />
							{notificationCount > 0 && (
								<span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center" style={{ fontSize: '10px' }}>
									{notificationCount}
								</span>
							)}
						</div>

						{/* Three-dot menu */}
						<div className="relative" ref={menuRef}>
							<button
								onClick={() => setShowMenu(!showMenu)}
								className="p-1 hover:bg-gray-100 rounded"
							>
								<MoreHorizontal className="h-4 w-4 text-gray-500" />
							</button>

							{/* Dropdown menu */}
							{showMenu && (
								<div className="absolute right-0 top-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg z-10 min-w-[120px]">
									<button
										className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
										onClick={() => {
											setShowMenu(false);
											console.log('Edit site:', site.id);
										}}
									>
										Edit
									</button>
									<button
										className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
										onClick={() => {
											setShowMenu(false);
											console.log('Delete site:', site.id);
										}}
									>
										Delete
									</button>
									<button
										className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
										onClick={() => {
											setShowMenu(false);
											console.log('Pause site:', site.id);
										}}
									>
										Pause
									</button>
								</div>
							)}
						</div>
					</div>
				</div>
			</div>

			{/* Card Body */}
			<div className="p-4">
				{/* Site/Project title - must not be truncated */}
				<h3 className="font-semibold text-gray-900 text-lg mb-2 whitespace-normal break-words">
					{site.name}
				</h3>

				{/* Location */}
				<div className="flex items-center text-sm text-gray-600 mb-4">
					<MapPin className="h-4 w-4 mr-1" />
					<span>{site.location}</span>
				</div>

				{/* Quick insights section */}
				<div className="grid grid-cols-3 gap-3">
					{/* Workers insight square */}
					<div className="bg-gray-50 p-3 rounded-md text-center">
						<Users className="h-5 w-5 text-gray-500 mx-auto mb-1" />
						<div className="text-lg font-semibold text-gray-900">{site.workersOnSite}</div>
						<div className="text-xs text-gray-500">Workers</div>
					</div>

					{/* Permits insight square */}
					<div className="bg-gray-50 p-3 rounded-md text-center">
						<FileCheck className="h-5 w-5 text-gray-500 mx-auto mb-1" />
						<div className="text-lg font-semibold text-gray-900">{site.activePermits}</div>
						<div className="text-xs text-gray-500">Permits</div>
					</div>

					{/* Incidents insight square */}
					<div className="bg-gray-50 p-3 rounded-md text-center">
						<AlertTriangle className="h-5 w-5 text-gray-500 mx-auto mb-1" />
						<div className="text-lg font-semibold text-gray-900">{site.openIncidents}</div>
						<div className="text-xs text-gray-500">Incidents</div>
					</div>
				</div>
			</div>

			{/* Card Footer */}
			<Link
				to={`/sites/${site.id}/dashboard`}
				className="block px-4 py-3 border-t border-gray-100 bg-gray-50 hover:bg-gray-100 transition-colors"
			>
				<div className="flex items-center justify-between">
					<span className="text-sm font-medium text-gray-700">VIEW</span>
					<div
						className="bg-black text-white p-2 rounded hover:bg-gray-800 transition-colors"
						style={{ borderRadius: '3px' }}
					>
						<ArrowRight className="h-4 w-4" />
					</div>
				</div>
			</Link>
		</div>
	);
};

export default SiteCard;
