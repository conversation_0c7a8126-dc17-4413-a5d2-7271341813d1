import { useState } from "react";
import {
	<PERSON>raduation<PERSON><PERSON>,
	HardHat,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>board<PERSON>ist,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
	<PERSON>,
	Clock,
} from "lucide-react";
import StatCard from "./shared/StatCard";
import QuickActionCard from "./shared/QuickActionCard";
import { MasterDataStats, RecentActivity } from "../../types/masterData";

interface DataDashboardProps {
	onNavigateToTab: (tabId: string) => void;
}

const DataDashboard = ({ onNavigateToTab }: DataDashboardProps) => {
	const [stats, _setStats] = useState<MasterDataStats>({
		trainingPrograms: 45,
		ppeItems: 128,
		permitTypes: 12,
		formTemplates: 23,
		incidentTypes: 8,
		tradesSkills: 34,
	});

	const [recentActivity, _setRecentActivity] = useState<RecentActivity[]>([
		{
			id: "1",
			type: "created",
			entityType: "training-program",
			entityName: "Fall Protection Training",
			userName: "<PERSON>",
			timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
		},
		{
			id: "2",
			type: "updated",
			entityType: "ppe-item",
			entityName: "Safety Helmet - Model XYZ",
			userName: "<PERSON>",
			timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
		},
		{
			id: "3",
			type: "created",
			entityType: "form-template",
			entityName: "Daily Safety Inspection",
			userName: "Mike Wilson",
			timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 hours ago
		},
	]);

	const [searchTerm, setSearchTerm] = useState("");

	const formatTimeAgo = (timestamp: Date) => {
		const now = new Date();
		const diffInHours = Math.floor(
			(now.getTime() - timestamp.getTime()) / (1000 * 60 * 60),
		);

		if (diffInHours < 1) return "Less than an hour ago";
		if (diffInHours === 1) return "1 hour ago";
		if (diffInHours < 24) return `${diffInHours} hours ago`;

		const diffInDays = Math.floor(diffInHours / 24);
		if (diffInDays === 1) return "1 day ago";
		return `${diffInDays} days ago`;
	};

	const getActivityIcon = (type: string) => {
		switch (type) {
			case "created":
				return <div className="w-2 h-2 bg-green-500 rounded-full" />;
			case "updated":
				return <div className="w-2 h-2 bg-blue-500 rounded-full" />;
			case "deleted":
				return <div className="w-2 h-2 bg-red-500 rounded-full" />;
			default:
				return <div className="w-2 h-2 bg-gray-500 rounded-full" />;
		}
	};

	return (
		<div className="space-y-6">
			{/* Statistics Grid */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
				<StatCard
					title="Training Programs"
					count={stats.trainingPrograms}
					icon={<GraduationCap className="h-6 w-6" />}
					onClick={() => onNavigateToTab("training-programs")}
				/>
				<StatCard
					title="PPE Items"
					count={stats.ppeItems}
					icon={<HardHat className="h-6 w-6" />}
					onClick={() => onNavigateToTab("ppe-catalog")}
				/>
				<StatCard
					title="Permit Types"
					count={stats.permitTypes}
					icon={<FileCheck className="h-6 w-6" />}
					onClick={() => onNavigateToTab("permit-types")}
				/>
				<StatCard
					title="Form Templates"
					count={stats.formTemplates}
					icon={<ClipboardList className="h-6 w-6" />}
					onClick={() => onNavigateToTab("form-templates")}
				/>
				<StatCard
					title="Incident Types"
					count={stats.incidentTypes}
					icon={<AlertTriangle className="h-6 w-6" />}
					onClick={() => onNavigateToTab("incident-types")}
				/>
				<StatCard
					title="Trades & Skills"
					count={stats.tradesSkills}
					icon={<Wrench className="h-6 w-6" />}
					onClick={() => onNavigateToTab("trades-skills")}
				/>
			</div>

			{/* Quick Actions */}
			<div className="bg-white rounded-lg p-6">
				<h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
				<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
					<QuickActionCard
						title="New Training Program"
						description="Create a new training program"
						icon={<GraduationCap className="h-5 w-5" />}
						onClick={() => onNavigateToTab("training-programs")}
					/>
					<QuickActionCard
						title="Add PPE Item"
						description="Add new PPE to catalog"
						icon={<HardHat className="h-5 w-5" />}
						onClick={() => onNavigateToTab("ppe-catalog")}
					/>
					<QuickActionCard
						title="Create Form Template"
						description="Build a new form template"
						icon={<ClipboardList className="h-5 w-5" />}
						onClick={() => onNavigateToTab("form-templates")}
					/>
					<QuickActionCard
						title="Add Permit Type"
						description="Define new permit type"
						icon={<FileCheck className="h-5 w-5" />}
						onClick={() => onNavigateToTab("permit-types")}
					/>
				</div>
			</div>

			{/* Global Search */}
			<div className="bg-white rounded-lg p-6">
				<h3 className="text-lg font-semibold mb-4">Search Master Data</h3>
				<div className="relative">
					<div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
						<Search className="h-4 w-4 text-gray-500" />
					</div>
					<input
						type="text"
						className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
						placeholder="Search across all master data..."
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
					/>
				</div>
			</div>

			{/* Recent Activity */}
			<div className="bg-white rounded-lg p-6">
				<div className="flex items-center justify-between mb-4">
					<h3 className="text-lg font-semibold">Recent Activity</h3>
					<Clock className="h-5 w-5 text-gray-400" />
				</div>
				<div className="space-y-3">
					{recentActivity.map((activity) => (
						<div
							key={activity.id}
							className="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-md"
						>
							<div className="flex-shrink-0 mt-1">
								{getActivityIcon(activity.type)}
							</div>
							<div className="flex-1 min-w-0">
								<p className="text-sm text-gray-900">
									<span className="font-medium">{activity.userName}</span>{" "}
									<span className="text-gray-600">
										{activity.type} {activity.entityType.replace("-", " ")}
									</span>{" "}
									<span className="font-medium">{activity.entityName}</span>
								</p>
								<p className="text-xs text-gray-500">
									{formatTimeAgo(activity.timestamp)}
								</p>
							</div>
						</div>
					))}
				</div>
			</div>
		</div>
	);
};

export default DataDashboard;
