import { useState } from "react";
import {
	Plus,
	Search,
	Filter,
	Edit,
	Trash2,
	Grid,
	List,
	Settings,
} from "lucide-react";
import { PPEItem, PPECategory } from "../../types/masterData";

const PPECatalog = () => {
	const [items, setItems] = useState<PPEItem[]>([
		{
			id: "1",
			name: "Safety Helmet - Model XYZ",
			sku: "PPE-HELM-001",
			category: {
				id: "helmets",
				name: "Helmets",
				description: "Head protection",
			},
			description: "High-impact resistant safety helmet",
			specifications: {
				Material: "ABS Plastic",
				Weight: "350g",
				Color: "White",
			},
			supplier: {
				id: "sup1",
				name: "SafetyFirst Inc.",
				contactInfo: {
					email: "<EMAIL>",
					phone: "555-0123",
					address: "123 Safety St",
				},
			},
			unitCost: 25.99,
			reorderLevel: 50,
			safetyStandards: ["ANSI Z89.1", "CSA Z94.1"],
			images: [],
			status: "active",
			createdAt: new Date("2024-01-15"),
			updatedAt: new Date("2024-01-15"),
		},
		{
			id: "2",
			name: "Safety Goggles - Clear",
			sku: "PPE-GOGG-002",
			category: {
				id: "eyewear",
				name: "Eye Protection",
				description: "Eye and face protection",
			},
			description: "Anti-fog safety goggles with clear lens",
			specifications: {
				Material: "Polycarbonate",
				"UV Protection": "Yes",
				"Anti-fog": "Yes",
			},
			supplier: {
				id: "sup2",
				name: "VisionSafe Corp.",
				contactInfo: {
					email: "<EMAIL>",
					phone: "555-0456",
					address: "456 Vision Ave",
				},
			},
			unitCost: 12.5,
			reorderLevel: 100,
			safetyStandards: ["ANSI Z87.1"],
			images: [],
			status: "active",
			createdAt: new Date("2024-01-10"),
			updatedAt: new Date("2024-01-20"),
		},
	]);

	const [categories] = useState<PPECategory[]>([
		{ id: "helmets", name: "Helmets", description: "Head protection" },
		{
			id: "eyewear",
			name: "Eye Protection",
			description: "Eye and face protection",
		},
		{ id: "gloves", name: "Gloves", description: "Hand protection" },
		{ id: "footwear", name: "Safety Footwear", description: "Foot protection" },
	]);

	const [searchTerm, setSearchTerm] = useState("");
	const [selectedCategory, setSelectedCategory] = useState<string>("all");
	const [view, setView] = useState<"grid" | "table">("table");
	const [selectedItem, setSelectedItem] = useState<PPEItem | null>(null);
	const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

	const filteredItems = items.filter((item) => {
		const matchesSearch =
			item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			item.sku.toLowerCase().includes(searchTerm.toLowerCase());
		const matchesCategory =
			selectedCategory === "all" || item.category.id === selectedCategory;
		return matchesSearch && matchesCategory;
	});

	const handleEdit = (item: PPEItem) => {
		setSelectedItem(item);
	};

	const handleDelete = (itemId: string) => {
		if (confirm("Are you sure you want to delete this PPE item?")) {
			setItems(items.filter((item) => item.id !== itemId));
		}
	};

	const getStatusBadge = (status: string) => {
		const baseClasses = "px-2 py-1 text-xs font-medium rounded-full";
		switch (status) {
			case "active":
				return `${baseClasses} bg-green-100 text-green-800`;
			case "discontinued":
				return `${baseClasses} bg-red-100 text-red-800`;
			default:
				return `${baseClasses} bg-gray-100 text-gray-800`;
		}
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<h2 className="text-2xl font-bold">PPE Catalog</h2>
				<div className="flex gap-2">
					<button className="flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
						<Settings className="h-4 w-4 mr-2" />
						Manage Categories
					</button>
					<button
						onClick={() => setIsCreateModalOpen(true)}
						className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700"
					>
						<Plus className="h-4 w-4 mr-2" />
						Add PPE Item
					</button>
				</div>
			</div>

			{/* Filters */}
			<div className="bg-white rounded-lg border border-gray-200 p-4">
				<div className="flex flex-col sm:flex-row gap-4 items-center">
					<div className="flex-1 relative">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
						<input
							type="text"
							placeholder="Search PPE items by name or SKU..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
						/>
					</div>
					<div className="flex items-center gap-2">
						<Filter className="h-4 w-4 text-gray-400" />
						<select
							value={selectedCategory}
							onChange={(e) => setSelectedCategory(e.target.value)}
							className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
						>
							<option value="all">All Categories</option>
							{categories.map((category) => (
								<option key={category.id} value={category.id}>
									{category.name}
								</option>
							))}
						</select>
					</div>
					<div className="flex border border-gray-300 rounded-md">
						<button
							onClick={() => setView("table")}
							className={`p-2 ${view === "table" ? "bg-green-100 text-green-600" : "text-gray-400 hover:text-gray-600"}`}
						>
							<List className="h-4 w-4" />
						</button>
						<button
							onClick={() => setView("grid")}
							className={`p-2 ${view === "grid" ? "bg-green-100 text-green-600" : "text-gray-400 hover:text-gray-600"}`}
						>
							<Grid className="h-4 w-4" />
						</button>
					</div>
				</div>
			</div>

			{/* Content */}
			{view === "table" ? (
				<div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
					<div className="overflow-x-auto">
						<table className="min-w-full divide-y divide-gray-200">
							<thead className="bg-gray-50">
								<tr>
									<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
										Item Details
									</th>
									<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
										Category
									</th>
									<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
										Unit Cost
									</th>
									<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
										Status
									</th>
									<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
										Supplier
									</th>
									<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
										Actions
									</th>
								</tr>
							</thead>
							<tbody className="bg-white divide-y divide-gray-200">
								{filteredItems.map((item) => (
									<tr key={item.id} className="hover:bg-gray-50">
										<td className="px-6 py-4 whitespace-nowrap">
											<div>
												<div className="text-sm font-medium text-gray-900">
													{item.name}
												</div>
												<div className="text-sm text-gray-500">
													SKU: {item.sku}
												</div>
											</div>
										</td>
										<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
											{item.category.name}
										</td>
										<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
											${item.unitCost.toFixed(2)}
										</td>
										<td className="px-6 py-4 whitespace-nowrap">
											<span className={getStatusBadge(item.status)}>
												{item.status}
											</span>
										</td>
										<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
											{item.supplier.name}
										</td>
										<td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
											<div className="flex space-x-2">
												<button
													onClick={() => handleEdit(item)}
													className="text-green-600 hover:text-green-900"
												>
													<Edit className="h-4 w-4" />
												</button>
												<button
													onClick={() => handleDelete(item.id)}
													className="text-red-600 hover:text-red-900"
												>
													<Trash2 className="h-4 w-4" />
												</button>
											</div>
										</td>
									</tr>
								))}
							</tbody>
						</table>
					</div>
				</div>
			) : (
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
					{filteredItems.map((item) => (
						<div
							key={item.id}
							className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow"
						>
							<div className="flex justify-between items-start mb-4">
								<h3 className="text-lg font-medium text-gray-900">
									{item.name}
								</h3>
								<span className={getStatusBadge(item.status)}>
									{item.status}
								</span>
							</div>
							<p className="text-sm text-gray-600 mb-2">SKU: {item.sku}</p>
							<p className="text-sm text-gray-600 mb-2">
								Category: {item.category.name}
							</p>
							<p className="text-lg font-semibold text-green-600 mb-4">
								${item.unitCost.toFixed(2)}
							</p>
							<div className="flex justify-end space-x-2">
								<button
									onClick={() => handleEdit(item)}
									className="text-green-600 hover:text-green-900"
								>
									<Edit className="h-4 w-4" />
								</button>
								<button
									onClick={() => handleDelete(item.id)}
									className="text-red-600 hover:text-red-900"
								>
									<Trash2 className="h-4 w-4" />
								</button>
							</div>
						</div>
					))}
				</div>
			)}

			{/* TODO: Add Create/Edit Modal */}
			{(isCreateModalOpen || selectedItem) && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
					<div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto">
						<h3 className="text-lg font-semibold mb-4">
							{selectedItem ? "Edit PPE Item" : "Create New PPE Item"}
						</h3>
						<p className="text-gray-600 mb-4">
							PPE item form will be implemented here.
						</p>
						<div className="flex justify-end space-x-2">
							<button
								onClick={() => {
									setIsCreateModalOpen(false);
									setSelectedItem(null);
								}}
								className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
							>
								Cancel
							</button>
							<button className="px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700">
								{selectedItem ? "Update" : "Create"} Item
							</button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default PPECatalog;
