import React, { useMemo, useState } from "react";
import { RefreshCw, Wifi, WifiOff, AlertTriangle, MapPin } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { siteDevices, HikDevice } from "../../data/timeMock";

// Flatten all devices across company
const useAllDevices = () => {
  return useMemo(() => {
    const list: HikDevice[] = [];
    for (const siteId of Object.keys(siteDevices)) {
      for (const d of siteDevices[siteId]) list.push(d);
    }
    return list;
  }, []);
};

const statusIcon = (status: HikDevice["status"]) => {
  switch (status) {
    case "online":
      return <Wifi className="h-4 w-4 text-green-600" />;
    case "offline":
      return <WifiOff className="h-4 w-4 text-gray-400" />;
    case "error":
      return <AlertTriangle className="h-4 w-4 text-red-600" />;
    default:
      return <WifiOff className="h-4 w-4 text-gray-400" />;
  }
};

const TimeCompany: React.FC = () => {
  const devices = useAllDevices();
  const [refreshing, setRefreshing] = useState(false);
  const navigate = useNavigate();

  const handleRefresh = () => {
    setRefreshing(true);
    // In a real app, call API; here, just simulate
    setTimeout(() => setRefreshing(false), 600);
  };

  // View navigation handled via button in table

  const companyStats = useMemo(() => {
    const total = devices.length;
    const online = devices.filter((d) => d.status === "online").length;
    const offline = devices.filter((d) => d.status === "offline").length;
    const error = devices.filter((d) => d.status === "error").length;
    return { total, online, offline, error };
  }, [devices]);

  return (
    <div className="space-y-6">
      {/* Header and actions */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Attendance Devices</h2>
          <p className="text-sm text-gray-600">Manage company-wide attendance devices and site assignments.</p>
        </div>
        <button
          onClick={handleRefresh}
          className="inline-flex items-center px-3 py-2 text-sm rounded-md border border-gray-300 bg-white hover:bg-gray-50 disabled:opacity-50"
          disabled={refreshing}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? "animate-spin" : ""}`} />
          {refreshing ? "Refreshing..." : "Refresh"}
        </button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="text-sm text-gray-500">Total Devices</div>
          <div className="text-2xl font-semibold">{companyStats.total}</div>
        </div>
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="text-sm text-gray-500">Online</div>
          <div className="text-2xl font-semibold text-green-700">{companyStats.online}</div>
        </div>
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="text-sm text-gray-500">Offline</div>
          <div className="text-2xl font-semibold text-gray-600">{companyStats.offline}</div>
        </div>
        <div className="bg-white border border-gray-200 rounded-lg p-4">
          <div className="text-sm text-gray-500">Errors</div>
          <div className="text-2xl font-semibold text-red-600">{companyStats.error}</div>
        </div>
      </div>

      {/* Devices Table */}
      <div className="bg-white rounded-lg border-2 border-gray-200 shadow-sm overflow-hidden" style={{ borderRadius: '5px' }}>
        <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Attendance Devices</h3>
          </div>
        </div>
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Device Details</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Capabilities</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Site Assignment</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {devices.map((device) => (
              <tr key={device.id} className="hover:bg-gray-50 transition-colors">
                {/* Device Details */}
                <td className="px-6 py-4">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-1">{device.name}</h4>
                    <p className="text-xs text-gray-600 mb-1">{device.model} • v{device.firmwareVersion}</p>
                    <div className="flex items-center text-xs text-gray-500">
                      <span>SN: {device.serialNumber}</span>
                    </div>
                  </div>
                </td>

                {/* Capabilities */}
                <td className="px-6 py-4 align-top">
                  <div className="text-xs space-y-1">
                    <div className="text-gray-900">
                      Users: {device.usage.users}/{device.capacity.maxUsers}
                    </div>
                    <div className="text-gray-900">
                      Faces: {device.usage.faces}/{device.capacity.maxFaces}
                    </div>
                    <div className="text-gray-900">
                      Storage: {device.usage.storageUsedGB}GB/{device.capacity.storageGB}GB
                    </div>
                  </div>
                </td>

                {/* Status */}
                <td className="px-6 py-4 align-top">
                  <div className="space-y-1">
                    <div className="inline-flex items-center space-x-2">
                      {statusIcon(device.status)}
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        device.status === 'online' ? 'bg-green-100 text-green-800' :
                        device.status === 'offline' ? 'bg-gray-100 text-gray-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {device.status}
                      </span>
                    </div>
                    <div className="text-[11px] text-gray-500">
                      Last Seen: {new Date(device.lastSeen).toLocaleDateString()}
                    </div>
                  </div>
                </td>

                {/* Site Assignment */}
                <td className="px-6 py-4 align-top">
                  <div className="text-xs">
                    <div className="text-gray-900 mb-1 flex items-center">
                      <MapPin className="h-3 w-3 mr-1 text-gray-500" />
                      {device.siteId}
                    </div>
                    <div className="text-gray-600">
                      {device.location || 'No location specified'}
                    </div>
                  </div>
                </td>

                {/* Actions */}
                <td className="px-6 py-4 align-top">
                  <button
                    onClick={() => navigate(`/devices/${device.id}`)}
                    className="px-3 py-1 text-xs border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors font-medium"
                    style={{ borderRadius: '5px' }}
                  >
                    View
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default TimeCompany;
