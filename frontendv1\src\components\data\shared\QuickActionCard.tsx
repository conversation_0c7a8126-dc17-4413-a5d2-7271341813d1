import { ReactNode } from "react";
import { Plus } from "lucide-react";

interface QuickActionCardProps {
	title: string;
	description?: string;
	icon?: ReactNode;
	onClick: () => void;
}

const QuickActionCard = ({
	title,
	description,
	icon,
	onClick,
}: QuickActionCardProps) => {
	return (
		<button
			onClick={onClick}
			className="w-full p-5 border border-[#24c45c] rounded-lg hover:border-green-500 hover:bg-green-50 transition-colors text-left group min-w-0 min-h-28"
		>
			<div className="flex items-start space-x-3">
				<div className="flex-shrink-0 text-blue-600 group-hover:text-blue-700">
					{icon || <Plus className="h-5 w-5" />}
				</div>
				<div className="flex-1 min-w-0">
					<h4 className="font-medium text-gray-900 group-hover:text-green-700 truncate">
						{title}
					</h4>
					{description && (
						<p className="text-sm text-gray-500 group-hover:text-green-600 mt-2 leading-relaxed line-clamp-2">
							{description}
						</p>
					)}
				</div>
			</div>
		</button>
	);
};

export default QuickActionCard;
