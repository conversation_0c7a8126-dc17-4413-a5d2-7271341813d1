import { ReactNode } from "react";

interface StatCardProps {
	title: string;
	count: number;
	icon: ReactNode;
	onClick?: () => void;
	subtitle?: string;
	alertCount?: number;
}

const StatCard = ({
	title,
	count,
	icon,
	onClick,
	subtitle,
	alertCount,
}: StatCardProps) => {
	const baseClasses =
		"bg-white p-6 rounded-lg border border-gray-200 shadow-sm";
	const interactiveClasses = onClick
		? "cursor-pointer hover:shadow-md transition-shadow"
		: "";

	return (
		<div className={`${baseClasses} ${interactiveClasses}`} onClick={onClick}>
			<div className="flex justify-between items-start">
				<div className="flex-1">
					<p className="text-sm font-medium text-gray-500">{title}</p>
					<p className="mt-2 text-2xl font-semibold text-gray-900">{count}</p>
					{subtitle && <p className="mt-1 text-sm text-gray-600">{subtitle}</p>}
				</div>
				<div className="flex items-center space-x-2">
					<div className="text-gray-400">{icon}</div>
					{alertCount !== undefined && alertCount > 0 && (
						<div className="bg-red-100 text-red-800 text-xs font-medium px-2 py-1 rounded-full">
							{alertCount}
						</div>
					)}
				</div>
			</div>
		</div>
	);
};

export default StatCard;
