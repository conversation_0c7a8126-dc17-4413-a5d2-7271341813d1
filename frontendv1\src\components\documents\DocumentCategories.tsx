import React from "react";
import { FolderOpen, Plus } from "lucide-react";

const DocumentCategories: React.FC = () => {
	return (
		<div className="space-y-6">
			<div className="flex justify-between items-center">
				<h2 className="text-xl font-semibold">Document Categories</h2>
				<button className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2">
					<Plus className="h-4 w-4" />
					<span>Add Category</span>
				</button>
			</div>

			<div className="text-center py-12">
				<FolderOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
				<h3 className="text-lg font-medium text-gray-900 mb-2">
					Document Categories Management
				</h3>
				<p className="text-gray-600">
					Configure document types and categories for your organization.
				</p>
				<p className="text-sm text-gray-500 mt-2">
					This feature will be implemented in the next phase.
				</p>
			</div>
		</div>
	);
};

export default DocumentCategories;
