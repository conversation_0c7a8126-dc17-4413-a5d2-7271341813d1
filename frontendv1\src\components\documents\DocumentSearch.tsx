import React, { useState } from "react";
import { Search, Filter, X } from "lucide-react";
import {
	DocumentFilter,
	DocumentCategory,
	DocumentStatus,
	ComplianceStatus,
} from "../../types/documents";

interface DocumentSearchProps {
	onFiltersChange: (filters: Partial<DocumentFilter>) => void;
	initialFilters?: DocumentFilter;
}

const DocumentSearch: React.FC<DocumentSearchProps> = ({
	onFiltersChange,
	initialFilters = {},
}) => {
	const [searchTerm, setSearchTerm] = useState(initialFilters.search || "");
	const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
	const [filters, setFilters] = useState<DocumentFilter>(initialFilters);

	const handleSearchChange = (value: string) => {
		setSearchTerm(value);
		const newFilters = { ...filters, search: value || undefined };
		setFilters(newFilters);
		onFiltersChange(newFilters);
	};

	const handleFilterChange = (key: keyof DocumentFilter, value: any) => {
		const newFilters = {
			...filters,
			[key]: value === "" ? undefined : value,
		};
		setFilters(newFilters);
		onFiltersChange(newFilters);
	};

	const clearFilters = () => {
		const clearedFilters: DocumentFilter = {
			entityType: filters.entityType,
			entityId: filters.entityId,
			category: initialFilters.category, // Keep initial category if set
		};
		setFilters(clearedFilters);
		setSearchTerm("");
		onFiltersChange(clearedFilters);
	};

	const hasActiveFilters = () => {
		return Object.keys(filters).some((key) => {
			if (key === "entityType" || key === "entityId") return false;
			if (key === "category" && initialFilters.category) return false;
			return filters[key as keyof DocumentFilter] !== undefined;
		});
	};

	return (
		<div className="space-y-4">
			{/* Search Bar */}
			<div className="flex items-center space-x-3">
				<div className="flex-1 relative">
					<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
					<input
						type="text"
						value={searchTerm}
						onChange={(e) => handleSearchChange(e.target.value)}
						placeholder="Search documents by name, description, or tags..."
						className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
					/>
				</div>

				<button
					onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
					className={`flex items-center space-x-2 px-3 py-2 border rounded-md transition-colors ${
						showAdvancedFilters || hasActiveFilters()
							? "border-blue-500 text-blue-600 bg-blue-50"
							: "border-gray-300 text-gray-600 hover:bg-gray-50"
					}`}
				>
					<Filter className="h-4 w-4" />
					<span>Filters</span>
					{hasActiveFilters() && (
						<span className="bg-blue-600 text-white text-xs rounded-full px-2 py-0.5">
							{
								Object.keys(filters).filter((key) => {
									if (key === "entityType" || key === "entityId") return false;
									if (key === "category" && initialFilters.category)
										return false;
									return filters[key as keyof DocumentFilter] !== undefined;
								}).length
							}
						</span>
					)}
				</button>

				{hasActiveFilters() && (
					<button
						onClick={clearFilters}
						className="flex items-center space-x-1 px-3 py-2 text-gray-600 hover:text-gray-800"
					>
						<X className="h-4 w-4" />
						<span>Clear</span>
					</button>
				)}
			</div>

			{/* Advanced Filters */}
			{showAdvancedFilters && (
				<div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
						{/* Category Filter */}
						{!initialFilters.category && (
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-1">
									Category
								</label>
								<select
									value={filters.category || ""}
									onChange={(e) =>
										handleFilterChange(
											"category",
											e.target.value as DocumentCategory,
										)
									}
									className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
								>
									<option value="">All Categories</option>
									{Object.values(DocumentCategory).map((category) => (
										<option key={category} value={category}>
											{category.charAt(0).toUpperCase() + category.slice(1)}
										</option>
									))}
								</select>
							</div>
						)}

						{/* Status Filter */}
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Status
							</label>
							<select
								value={filters.status || ""}
								onChange={(e) =>
									handleFilterChange("status", e.target.value as DocumentStatus)
								}
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							>
								<option value="">All Statuses</option>
								{Object.values(DocumentStatus).map((status) => (
									<option key={status} value={status}>
										{status.charAt(0).toUpperCase() + status.slice(1)}
									</option>
								))}
							</select>
						</div>

						{/* Compliance Status Filter */}
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Compliance
							</label>
							<select
								value={filters.complianceStatus || ""}
								onChange={(e) =>
									handleFilterChange(
										"complianceStatus",
										e.target.value as ComplianceStatus,
									)
								}
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							>
								<option value="">All Compliance</option>
								{Object.values(ComplianceStatus).map((status) => (
									<option key={status} value={status}>
										{status.charAt(0).toUpperCase() + status.slice(1)}
									</option>
								))}
							</select>
						</div>

						{/* File Type Filter */}
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								File Type
							</label>
							<select
								value={filters.fileType || ""}
								onChange={(e) => handleFilterChange("fileType", e.target.value)}
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							>
								<option value="">All Types</option>
								<option value="pdf">PDF</option>
								<option value="image">Images</option>
								<option value="document">Documents</option>
								<option value="video">Videos</option>
							</select>
						</div>

						{/* Date Range Filters */}
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Created From
							</label>
							<input
								type="date"
								value={filters.dateFrom || ""}
								onChange={(e) => handleFilterChange("dateFrom", e.target.value)}
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Created To
							</label>
							<input
								type="date"
								value={filters.dateTo || ""}
								onChange={(e) => handleFilterChange("dateTo", e.target.value)}
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Expires From
							</label>
							<input
								type="date"
								value={filters.expiryFrom || ""}
								onChange={(e) =>
									handleFilterChange("expiryFrom", e.target.value)
								}
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Expires To
							</label>
							<input
								type="date"
								value={filters.expiryTo || ""}
								onChange={(e) => handleFilterChange("expiryTo", e.target.value)}
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							/>
						</div>
					</div>

					{/* Active Filters Display */}
					{hasActiveFilters() && (
						<div className="mt-4 pt-4 border-t border-gray-200">
							<div className="flex items-center space-x-2 text-sm">
								<span className="text-gray-600">Active filters:</span>
								<div className="flex flex-wrap gap-2">
									{Object.entries(filters).map(([key, value]) => {
										if (!value || key === "entityType" || key === "entityId")
											return null;
										if (key === "category" && initialFilters.category)
											return null;

										return (
											<span
												key={key}
												className="inline-flex items-center space-x-1 px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs"
											>
												<span>
													{key}: {value}
												</span>
												<button
													onClick={() =>
														handleFilterChange(
															key as keyof DocumentFilter,
															undefined,
														)
													}
													className="hover:text-blue-600"
												>
													<X className="h-3 w-3" />
												</button>
											</span>
										);
									})}
								</div>
							</div>
						</div>
					)}
				</div>
			)}
		</div>
	);
};

export default DocumentSearch;
