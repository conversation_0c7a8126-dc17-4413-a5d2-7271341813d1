import React, { useState, useRef } from "react";
import {
	Upload,
	X,
	AlertCircle,
	CheckCircle,
} from "lucide-react";
import {
	DocumentUploadProps,
	DocumentCategory,
	DocumentUploadRequest,
	DOCUMENT_TYPES,
	Document,
	DocumentStatus,
	ComplianceStatus,
} from "../../types/documents";

const DocumentUpload: React.FC<DocumentUploadProps> = ({
	entityType,
	entityId,
	category,
	acceptedTypes,
	maxFileSize = 50 * 1024 * 1024, // 50MB default
	onClose,
	onUploaded,
}) => {
	const [formData, setFormData] = useState<Partial<DocumentUploadRequest>>({
		entityType,
		entityId,
		category: category || DocumentCategory.ADMINISTRATIVE,
		tags: [],
		renewalRequired: false,
	});
	const [selectedFile, setSelectedFile] = useState<File | null>(null);
	const [dragActive, setDragActive] = useState(false);
	const [uploading, setUploading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const fileInputRef = useRef<HTMLInputElement>(null);

	const availableCategories = Object.values(DocumentCategory);
	const availableTypes = formData.category
		? DOCUMENT_TYPES[formData.category] || []
		: [];

	const handleDrag = (e: React.DragEvent) => {
		e.preventDefault();
		e.stopPropagation();
		if (e.type === "dragenter" || e.type === "dragover") {
			setDragActive(true);
		} else if (e.type === "dragleave") {
			setDragActive(false);
		}
	};

	const handleDrop = (e: React.DragEvent) => {
		e.preventDefault();
		e.stopPropagation();
		setDragActive(false);

		if (e.dataTransfer.files && e.dataTransfer.files[0]) {
			handleFileSelect(e.dataTransfer.files[0]);
		}
	};

	const handleFileSelect = (file: File) => {
		setError(null);

		// Validate file size
		if (file.size > maxFileSize) {
			setError(
				`File size exceeds ${Math.round(maxFileSize / (1024 * 1024))}MB limit`,
			);
			return;
		}

		// Validate file type if specified
		if (
			acceptedTypes &&
			!acceptedTypes.some(
				(type) =>
					type === file.type ||
					(type.endsWith("/*") && file.type.startsWith(type.slice(0, -1))),
			)
		) {
			setError("File type not supported");
			return;
		}

		setSelectedFile(file);

		// Auto-fill name if not set
		if (!formData.name) {
			setFormData((prev) => ({
				...prev,
				name: file.name.replace(/\.[^/.]+$/, ""), // Remove extension
			}));
		}
	};

	const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		if (e.target.files && e.target.files[0]) {
			handleFileSelect(e.target.files[0]);
		}
	};

	const handleInputChange = (field: string, value: any) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const handleTagsChange = (tagsString: string) => {
		const tags = tagsString
			.split(",")
			.map((tag) => tag.trim())
			.filter((tag) => tag.length > 0);
		handleInputChange("tags", tags);
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!selectedFile || !formData.name || !formData.type) {
			setError("Please fill in all required fields and select a file");
			return;
		}

		setUploading(true);
		setError(null);

		try {
			const uploadRequest: DocumentUploadRequest = {
				...(formData as DocumentUploadRequest),
				file: selectedFile,
			};

			// Mock API call - replace with actual implementation
			const mockDocument: Document = {
				id: `doc-${Date.now()}`,
				name: uploadRequest.name,
				description: uploadRequest.description,
				category: uploadRequest.category,
				type: uploadRequest.type,
				version: "1.0",
				// status: "active" as const,
				fileName: selectedFile.name,
				fileSize: selectedFile.size,
				mimeType: selectedFile.type,
				fileUrl: URL.createObjectURL(selectedFile),
				tags: uploadRequest.tags || [],
				customFields: uploadRequest.customFields || {},
				expiryDate: uploadRequest.expiryDate,
				renewalRequired: uploadRequest.renewalRequired || false,
				// complianceStatus: "valid" as const,
				entityType: uploadRequest.entityType,
				entityId: uploadRequest.entityId,
				createdAt: new Date().toISOString(),
				createdBy: "current-user-id",
				createdByName: "Current User",
				updatedAt: new Date().toISOString(),
				updatedBy: "current-user-id",
				updatedByName: "Current User",
				status: DocumentStatus.ACTIVE,
				complianceStatus: ComplianceStatus.VALID,
				createElement: function (_arg0: string): unknown {
					throw new Error("Function not implemented.");
				},
				body: undefined
			};

			// Simulate upload delay
			await new Promise((resolve) => setTimeout(resolve, 1500));

			onUploaded(mockDocument);
		} catch (err) {
			setError("Failed to upload document. Please try again.");
			console.error("Upload error:", err);
		} finally {
			setUploading(false);
		}
	};

	return (
		<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
			<div className="bg-white rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
				<div className="flex items-center justify-between p-6 border-b">
					<h2 className="text-lg font-semibold">Upload Document</h2>
					<button
						onClick={onClose}
						className="text-gray-400 hover:text-gray-600"
					>
						<X className="h-5 w-5" />
					</button>
				</div>

				<form onSubmit={handleSubmit} className="p-6 space-y-6">
					{/* File Upload Area */}
					<div
						className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
							dragActive
								? "border-blue-400 bg-blue-50"
								: "border-gray-300 hover:border-gray-400"
						}`}
						onDragEnter={handleDrag}
						onDragLeave={handleDrag}
						onDragOver={handleDrag}
						onDrop={handleDrop}
					>
						{selectedFile ? (
							<div className="space-y-2">
								<CheckCircle className="h-8 w-8 text-green-500 mx-auto" />
								<p className="text-sm font-medium">{selectedFile.name}</p>
								<p className="text-xs text-gray-500">
									{(selectedFile.size / (1024 * 1024)).toFixed(2)} MB
								</p>
								<button
									type="button"
									onClick={() => setSelectedFile(null)}
									className="text-sm text-blue-600 hover:text-blue-800"
								>
									Choose different file
								</button>
							</div>
						) : (
							<div className="space-y-2">
								<Upload className="h-8 w-8 text-gray-400 mx-auto" />
								<p className="text-sm text-gray-600">
									Drag and drop your file here, or{" "}
									<button
										type="button"
										onClick={() => fileInputRef.current?.click()}
										className="text-blue-600 hover:text-blue-800 font-medium"
									>
										browse
									</button>
								</p>
								<p className="text-xs text-gray-500">
									Maximum file size: {Math.round(maxFileSize / (1024 * 1024))}MB
								</p>
							</div>
						)}

						<input
							ref={fileInputRef}
							type="file"
							onChange={handleFileInputChange}
							accept={acceptedTypes?.join(",")}
							className="hidden"
						/>
					</div>

					{/* Error Message */}
					{error && (
						<div className="flex items-center space-x-2 text-red-600 bg-red-50 p-3 rounded-md">
							<AlertCircle className="h-4 w-4" />
							<span className="text-sm">{error}</span>
						</div>
					)}

					{/* Form Fields */}
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div className="md:col-span-2">
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Document Name *
							</label>
							<input
								type="text"
								value={formData.name || ""}
								onChange={(e) => handleInputChange("name", e.target.value)}
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
								placeholder="Enter document name"
								required
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Category *
							</label>
							<select
								value={formData.category || ""}
								onChange={(e) =>
									handleInputChange(
										"category",
										e.target.value as DocumentCategory,
									)
								}
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
								required
							>
								{availableCategories.map((cat) => (
									<option key={cat} value={cat}>
										{cat.charAt(0).toUpperCase() + cat.slice(1)}
									</option>
								))}
							</select>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Document Type *
							</label>
							<select
								value={formData.type || ""}
								onChange={(e) => handleInputChange("type", e.target.value)}
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
								required
							>
								<option value="">Select type</option>
								{availableTypes.map((type) => (
									<option key={type.id} value={type.id}>
										{type.name}
									</option>
								))}
							</select>
						</div>

						<div className="md:col-span-2">
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Description
							</label>
							<textarea
								value={formData.description || ""}
								onChange={(e) =>
									handleInputChange("description", e.target.value)
								}
								rows={3}
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
								placeholder="Enter document description"
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Expiry Date
							</label>
							<input
								type="date"
								value={formData.expiryDate || ""}
								onChange={(e) =>
									handleInputChange("expiryDate", e.target.value)
								}
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
							/>
						</div>

						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Tags
							</label>
							<input
								type="text"
								value={formData.tags?.join(", ") || ""}
								onChange={(e) => handleTagsChange(e.target.value)}
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
								placeholder="Enter tags separated by commas"
							/>
						</div>

						<div className="md:col-span-2">
							<label className="flex items-center space-x-2">
								<input
									type="checkbox"
									checked={formData.renewalRequired || false}
									onChange={(e) =>
										handleInputChange("renewalRequired", e.target.checked)
									}
									className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
								/>
								<span className="text-sm text-gray-700">
									Renewal required when expired
								</span>
							</label>
						</div>
					</div>

					{/* Actions */}
					<div className="flex justify-end space-x-3 pt-4 border-t">
						<button
							type="button"
							onClick={onClose}
							className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
							disabled={uploading}
						>
							Cancel
						</button>
						<button
							type="submit"
							disabled={
								!selectedFile || !formData.name || !formData.type || uploading
							}
							className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
						>
							{uploading ? (
								<>
									<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
									<span>Uploading...</span>
								</>
							) : (
								<>
									<Upload className="h-4 w-4" />
									<span>Upload Document</span>
								</>
							)}
						</button>
					</div>
				</form>
			</div>
		</div>
	);
};

export default DocumentUpload;
