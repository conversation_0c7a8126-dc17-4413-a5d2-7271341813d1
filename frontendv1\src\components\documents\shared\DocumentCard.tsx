import React from "react";
import {
	FileText,
	Download,
	Eye,
	Edit,
	Trash2,
	Calendar,
	User,
	AlertTriangle,
	CheckCircle,
	Clock,
	MoreVertical,
} from "lucide-react";
import {
	DocumentCardProps,
	ComplianceStatus,
} from "../../../types/documents";

const DocumentCard: React.FC<DocumentCardProps> = ({
	document,
	viewMode,
	onView,
	onEdit,
	onDelete,
	onDownload,
	showActions = true,
}) => {
	const formatFileSize = (bytes: number): string => {
		if (bytes === 0) return "0 Bytes";
		const k = 1024;
		const sizes = ["Bytes", "KB", "MB", "GB"];
		const i = Math.floor(Math.log(bytes) / Math.log(k));
		return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
	};

	const formatDate = (dateString: string): string => {
		return new Date(dateString).toLocaleDateString("en-US", {
			year: "numeric",
			month: "short",
			day: "numeric",
		});
	};

	const getComplianceIcon = (status: ComplianceStatus) => {
		switch (status) {
			case ComplianceStatus.VALID:
				return <CheckCircle className="h-4 w-4 text-green-500" />;
			case ComplianceStatus.EXPIRING:
				return <Clock className="h-4 w-4 text-yellow-500" />;
			case ComplianceStatus.EXPIRED:
				return <AlertTriangle className="h-4 w-4 text-red-500" />;
			default:
				return null;
		}
	};

	const getComplianceColor = (status: ComplianceStatus) => {
		switch (status) {
			case ComplianceStatus.VALID:
				return "text-green-600 bg-green-50 border-green-200";
			case ComplianceStatus.EXPIRING:
				return "text-yellow-600 bg-yellow-50 border-yellow-200";
			case ComplianceStatus.EXPIRED:
				return "text-red-600 bg-red-50 border-red-200";
			default:
				return "text-gray-600 bg-gray-50 border-gray-200";
		}
	};

	const getFileIcon = (mimeType: string) => {
		if (mimeType.startsWith("image/")) {
			return document.thumbnailUrl ? (
				<img
					src={document.thumbnailUrl}
					alt={document.name}
					className="w-8 h-8 object-cover rounded"
				/>
			) : (
				<FileText className="h-8 w-8 text-blue-500" />
			);
		}
		return <FileText className="h-8 w-8 text-blue-500" />;
	};

	const handleAction = (action: string, e: React.MouseEvent) => {
		e.stopPropagation();
		switch (action) {
			case "view":
				onView?.(document);
				break;
			case "edit":
				onEdit?.(document);
				break;
			case "delete":
				onDelete?.(document.id);
				break;
			case "download":
				onDownload?.(document);
				break;
		}
	};

	if (viewMode === "list") {
		return (
			<div
				className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
				onClick={() => onView?.(document)}
			>
				<div className="flex items-center justify-between">
					<div className="flex items-center space-x-3 flex-1 min-w-0">
						{getFileIcon(document.mimeType)}
						<div className="flex-1 min-w-0">
							<h3 className="text-sm font-medium text-gray-900 truncate">
								{document.name}
							</h3>
							<div className="flex items-center space-x-4 text-xs text-gray-500 mt-1">
								<span>{document.category}</span>
								<span>{formatFileSize(document.fileSize)}</span>
								<span>{formatDate(document.createdAt)}</span>
								<span className="flex items-center space-x-1">
									<User className="h-3 w-3" />
									<span>{document.createdByName}</span>
								</span>
							</div>
						</div>
					</div>

					<div className="flex items-center space-x-2">
						{/* Compliance Status */}
						{document.expiryDate && (
							<div
								className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs border ${getComplianceColor(document.complianceStatus)}`}
							>
								{getComplianceIcon(document.complianceStatus)}
								<span className="capitalize">{document.complianceStatus}</span>
							</div>
						)}

						{/* Actions */}
						{showActions && (
							<div className="flex items-center space-x-1">
								<button
									onClick={(e) => handleAction("view", e)}
									className="p-1 text-gray-400 hover:text-gray-600 rounded"
									title="View"
								>
									<Eye className="h-4 w-4" />
								</button>
								<button
									onClick={(e) => handleAction("download", e)}
									className="p-1 text-gray-400 hover:text-gray-600 rounded"
									title="Download"
								>
									<Download className="h-4 w-4" />
								</button>
								{onEdit && (
									<button
										onClick={(e) => handleAction("edit", e)}
										className="p-1 text-gray-400 hover:text-gray-600 rounded"
										title="Edit"
									>
										<Edit className="h-4 w-4" />
									</button>
								)}
								{onDelete && (
									<button
										onClick={(e) => handleAction("delete", e)}
										className="p-1 text-gray-400 hover:text-red-600 rounded"
										title="Delete"
									>
										<Trash2 className="h-4 w-4" />
									</button>
								)}
							</div>
						)}
					</div>
				</div>
			</div>
		);
	}

	// Grid view
	return (
		<div
			className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
			onClick={() => onView?.(document)}
		>
			<div className="flex items-start justify-between mb-3">
				{getFileIcon(document.mimeType)}
				{showActions && (
					<div className="relative">
						<button className="p-1 text-gray-400 hover:text-gray-600 rounded">
							<MoreVertical className="h-4 w-4" />
						</button>
						{/* Dropdown menu would go here */}
					</div>
				)}
			</div>

			<div className="space-y-2">
				<h3 className="text-sm font-medium text-gray-900 line-clamp-2">
					{document.name}
				</h3>

				<div className="text-xs text-gray-500 space-y-1">
					<div className="flex items-center justify-between">
						<span className="capitalize">{document.category}</span>
						<span>{formatFileSize(document.fileSize)}</span>
					</div>

					<div className="flex items-center space-x-1">
						<Calendar className="h-3 w-3" />
						<span>{formatDate(document.createdAt)}</span>
					</div>

					<div className="flex items-center space-x-1">
						<User className="h-3 w-3" />
						<span className="truncate">{document.createdByName}</span>
					</div>
				</div>

				{/* Compliance Status */}
				{document.expiryDate && (
					<div
						className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs border ${getComplianceColor(document.complianceStatus)}`}
					>
						{getComplianceIcon(document.complianceStatus)}
						<span className="capitalize">{document.complianceStatus}</span>
					</div>
				)}

				{/* Tags */}
				{document.tags && document.tags.length > 0 && (
					<div className="flex flex-wrap gap-1">
						{document.tags.slice(0, 2).map((tag, index) => (
							<span
								key={index}
								className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded"
							>
								{tag}
							</span>
						))}
						{document.tags.length > 2 && (
							<span className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-600 rounded">
								+{document.tags.length - 2}
							</span>
						)}
					</div>
				)}
			</div>
		</div>
	);
};

export default DocumentCard;
