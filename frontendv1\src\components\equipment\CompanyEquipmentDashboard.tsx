import React from "react";
import { 
  Package, 
  CheckCircle, 
  Clock, 
  AlertTriangle, 
  TrendingUp, 
  Plus, 
  Upload, 
  UserCheck, 
  Search,
  Building,
  Truck,
  Users
} from "lucide-react";
import { mockCompanyEquipment, getEquipmentKPIs } from "../../data/equipmentMockData";

interface CompanyEquipmentDashboardProps {
  onNavigateToTab: (tabId: string) => void;
}

interface KPICardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon: React.ReactNode;
  color: "blue" | "green" | "yellow" | "red" | "purple" | "orange";
  trend?: {
    value: number;
    isPositive: boolean;
  };
  onClick?: () => void;
}

function KPICard({ title, value, subtitle, icon, color, trend, onClick }: KPICardProps) {
  const colorClasses = {
    blue: "bg-blue-50 text-blue-600 border-blue-200",
    green: "bg-green-50 text-green-600 border-green-200",
    yellow: "bg-yellow-50 text-yellow-600 border-yellow-200",
    red: "bg-red-50 text-red-600 border-red-200",
    purple: "bg-purple-50 text-purple-600 border-purple-200",
    orange: "bg-orange-50 text-orange-600 border-orange-200"
  };

  return (
    <div 
      className={`bg-white rounded-lg border p-6 hover:shadow-md transition-shadow ${onClick ? 'cursor-pointer' : ''}`}
      onClick={onClick}
    >
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-bold text-gray-900 mt-1">{value}</p>
          {subtitle && <p className="text-sm text-gray-500 mt-1">{subtitle}</p>}
        </div>
        <div className={`p-3 rounded-lg ${colorClasses[color]}`}>
          {icon}
        </div>
      </div>
      {trend && (
        <div className="mt-4 flex items-center">
          <TrendingUp className={`w-4 h-4 mr-1 ${trend.isPositive ? 'text-green-500' : 'text-red-500 rotate-180'}`} />
          <span className={`text-sm ${trend.isPositive ? 'text-green-600' : 'text-red-600'}`}>
            {trend.isPositive ? '+' : ''}{trend.value}%
          </span>
          <span className="text-sm text-gray-500 ml-1">vs last month</span>
        </div>
      )}
    </div>
  );
}

interface QuickActionCardProps {
  title: string;
  icon: React.ReactNode;
  onClick: () => void;
}

function QuickActionCard({ title, icon, onClick }: QuickActionCardProps) {
  return (
    <button
      onClick={onClick}
      className="flex flex-col items-center p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-gray-300 transition-colors"
    >
      <div className="p-2 bg-blue-50 text-blue-600 rounded-lg mb-2">
        {icon}
      </div>
      <span className="text-sm font-medium text-gray-900">{title}</span>
    </button>
  );
}

export default function CompanyEquipmentDashboard({ onNavigateToTab }: CompanyEquipmentDashboardProps) {
  const kpis = getEquipmentKPIs(mockCompanyEquipment);

  return (
    <div className="space-y-6">
      {/* KPI Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <KPICard
          title="Total Equipment"
          value={kpis.totalEquipment}
          icon={<Package className="h-6 w-6" />}
          color="blue"
          onClick={() => onNavigateToTab("equipment")}
        />
        <KPICard
          title="Available"
          value={kpis.availableEquipment}
          subtitle={`${Math.round((kpis.availableEquipment / kpis.totalEquipment) * 100)}% of total`}
          icon={<CheckCircle className="h-6 w-6" />}
          color="green"
          onClick={() => onNavigateToTab("equipment")}
        />
        <KPICard
          title="In Use"
          value={kpis.assignedEquipment}
          subtitle={`${kpis.utilizationRate}% utilization`}
          icon={<UserCheck className="h-6 w-6" />}
          color="purple"
          onClick={() => onNavigateToTab("assignments")}
        />
        <KPICard
          title="Maintenance Required"
          value={kpis.maintenanceEquipment}
          icon={<Clock className="h-6 w-6" />}
          color="yellow"
          onClick={() => onNavigateToTab("maintenance")}
        />
      </div>

      {/* Ownership Type Breakdown */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <KPICard
          title="Company Owned"
          value={kpis.companyOwned}
          subtitle={`$${(kpis.totalValue / 1000).toFixed(0)}K total value`}
          icon={<Building className="h-6 w-6" />}
          color="blue"
        />
        <KPICard
          title="Rented Equipment"
          value={kpis.rented}
          subtitle={`$${(kpis.monthlyRentalCost / 1000).toFixed(1)}K/month`}
          icon={<Truck className="h-6 w-6" />}
          color="purple"
        />
        <KPICard
          title="Contracted"
          value={kpis.contracted}
          icon={<Users className="h-6 w-6" />}
          color="orange"
        />
      </div>

      {/* Compliance and Issues */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <KPICard
          title="Overdue Inspections"
          value={kpis.overdueInspections}
          icon={<AlertTriangle className="h-6 w-6" />}
          color="red"
          onClick={() => onNavigateToTab("maintenance")}
        />
        <KPICard
          title="Compliance Issues"
          value={kpis.complianceIssues}
          icon={<AlertTriangle className="h-6 w-6" />}
          color="yellow"
          onClick={() => onNavigateToTab("templates")}
        />
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <QuickActionCard
            title="Add Equipment"
            icon={<Plus className="h-5 w-5" />}
            onClick={() => onNavigateToTab("add-import")}
          />
          <QuickActionCard
            title="Import Equipment"
            icon={<Upload className="h-5 w-5" />}
            onClick={() => onNavigateToTab("add-import")}
          />
          <QuickActionCard
            title="Assign to Site"
            icon={<UserCheck className="h-5 w-5" />}
            onClick={() => onNavigateToTab("assignments")}
          />
          <QuickActionCard
            title="Search Equipment"
            icon={<Search className="h-5 w-5" />}
            onClick={() => onNavigateToTab("equipment")}
          />
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold mb-4">Recent Activity</h3>
        <div className="space-y-3">
          <div className="flex items-center justify-between py-2 border-b border-gray-100">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-3"></div>
              <span className="text-sm">CAT 320 Excavator assigned to Downtown Construction</span>
            </div>
            <span className="text-xs text-gray-500">2 hours ago</span>
          </div>
          <div className="flex items-center justify-between py-2 border-b border-gray-100">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
              <span className="text-sm">Liebherr Crane inspection completed</span>
            </div>
            <span className="text-xs text-gray-500">4 hours ago</span>
          </div>
          <div className="flex items-center justify-between py-2 border-b border-gray-100">
            <div className="flex items-center">
              <div className="w-2 h-2 bg-yellow-500 rounded-full mr-3"></div>
              <span className="text-sm">Komatsu Loader maintenance scheduled</span>
            </div>
            <span className="text-xs text-gray-500">1 day ago</span>
          </div>
        </div>
      </div>
    </div>
  );
}
