import React, { useEffect, useMemo, useState } from 'react';
import { equipmentCategories, equipmentSites, equipmentStatuses, complianceStatuses, ownershipTypes, CompanyEquipment, normalizeSiteId } from '../../data/equipmentMockData';

export type BasicInfo = {
  name: string;
  equipmentNumber: string;
  category?: string;
};

type Props = {
  submitSignal: number;
  onSuccess: (equipment: CompanyEquipment) => void;
  onCancel: () => void;
  onBasicInfoChange?: (info: BasicInfo) => void;
  onPrev?: () => void;
  onNext?: () => void;
  siteId?: string;
};

const CreateEquipmentForm: React.FC<Props> = ({ submitSignal, onSuccess, onCancel, onBasicInfoChange, onPrev, onNext, siteId }) => {
  const [form, setForm] = useState({
    name: '',
    equipmentNumber: '',
    category: equipmentCategories[0] || '',
    manufacturer: '',
    model: '',
    serialNumber: '',
    yearOfManufacture: new Date().getFullYear(),
    purchaseDate: new Date().toISOString().slice(0, 10),
    purchasePrice: 0,
    currentBookValue: 0,
    ownershipType: ownershipTypes[0]?.id || 'company',
    overallStatus: (equipmentStatuses[0]?.id as CompanyEquipment['overallStatus']) || 'active',
    isAvailableForAssignment: true,
    currentSiteId: siteId ? normalizeSiteId(siteId) : '',
    currentSiteName: siteId ? (equipmentSites.find(s => s.id === normalizeSiteId(siteId))?.name || '') : '',
    lastMaintenanceDate: '',
    nextMaintenanceDate: '',
    nextInspectionDate: '',
    complianceStatus: (complianceStatuses[0]?.id as CompanyEquipment['complianceStatus']) || 'compliant',
    specifications: {} as Record<string, string>,
    rentalInfo: {
      rentalCompany: '', rentalStartDate: '', rentalEndDate: '', monthlyRate: 0, contactPerson: '', contactPhone: ''
    },
    contractorInfo: {
      contractorCompany: '', contractStartDate: '', contractEndDate: '', contactPerson: '', contactPhone: '', serviceType: ''
    }
  });

  const [specKey, setSpecKey] = useState('');
  const [specVal, setSpecVal] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    onBasicInfoChange?.({ name: form.name, equipmentNumber: form.equipmentNumber, category: form.category });
  }, [form.name, form.equipmentNumber, form.category]);

  const siteOptions = useMemo(() => equipmentSites.map(s => ({ value: s.id, label: s.name })), []);

  const validate = (): boolean => {
    const e: Record<string, string> = {};
    if (!form.name.trim()) e.name = 'Name is required';
    if (!form.equipmentNumber.trim()) e.equipmentNumber = 'Equipment Number is required';
    if (!form.category) e.category = 'Category is required';
    if (!form.manufacturer.trim()) e.manufacturer = 'Manufacturer is required';
    if (!form.model.trim()) e.model = 'Model is required';
    if (!form.yearOfManufacture || form.yearOfManufacture < 1950) e.yearOfManufacture = 'Enter a valid year';
    if (!form.ownershipType) e.ownershipType = 'Ownership type is required';

    if (form.ownershipType === 'rented') {
      if (!form.rentalInfo.rentalCompany) e.rentalCompany = 'Rental company is required';
      if (!form.rentalInfo.rentalStartDate) e.rentalStartDate = 'Rental start date is required';
    }
    if (form.ownershipType === 'contracted') {
      if (!form.contractorInfo.contractorCompany) e.contractorCompany = 'Contractor company is required';
      if (!form.contractorInfo.contractStartDate) e.contractStartDate = 'Contract start date is required';
    }

    setErrors(e);
    return Object.keys(e).length === 0;
  };

  const handleSubmit = () => {
    if (!validate()) return;
    const now = new Date().toISOString();
    const newEquipment: CompanyEquipment = {
      id: `eq-${Math.random().toString(36).slice(2, 9)}`,
      equipmentNumber: form.equipmentNumber,
      name: form.name,
      category: form.category!,
      manufacturer: form.manufacturer,
      model: form.model,
      serialNumber: form.serialNumber || undefined,
      yearOfManufacture: form.yearOfManufacture,
      purchaseDate: form.purchaseDate,
      purchasePrice: Number(form.purchasePrice) || 0,
      currentBookValue: Number(form.currentBookValue) || 0,
      ownershipType: form.ownershipType as CompanyEquipment['ownershipType'],
      overallStatus: form.overallStatus,
      isAvailableForAssignment: !!form.isAvailableForAssignment,
      currentSiteId: form.currentSiteId || undefined,
      currentSiteName: form.currentSiteName || undefined,
      assignedOperator: undefined,
      totalHours: 0,
      lastMaintenanceDate: form.lastMaintenanceDate || undefined,
      nextMaintenanceDate: form.nextMaintenanceDate || undefined,
      nextInspectionDate: form.nextInspectionDate || undefined,
      complianceStatus: form.complianceStatus,
      specifications: form.specifications,
      rentalInfo: form.ownershipType === 'rented' ? form.rentalInfo : undefined,
      contractorInfo: form.ownershipType === 'contracted' ? form.contractorInfo : undefined,
      createdAt: now,
      updatedAt: now,
    };
    onSuccess(newEquipment);
  };

  useEffect(() => {
    // listen for Save from parent
    if (submitSignal > 0) {
      handleSubmit();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [submitSignal]);

  const addSpec = () => {
    if (!specKey.trim()) return;
    setForm(prev => ({ ...prev, specifications: { ...prev.specifications, [specKey.trim()]: specVal.trim() } }));
    setSpecKey('');
    setSpecVal('');
  };
  const removeSpec = (k: string) => {
    setForm(prev => {
      const copy = { ...prev.specifications };
      delete copy[k];
      return { ...prev, specifications: copy };
    });
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-xs font-medium text-gray-500 uppercase">Name</label>
          <input value={form.name} onChange={e => setForm({ ...form, name: e.target.value })} className={`mt-1 w-full rounded-md border ${errors.name ? 'border-red-300' : 'border-gray-300'} px-3 py-2 text-sm`} placeholder="e.g., CAT 320 Excavator" />
          {errors.name && <p className="text-xs text-red-600 mt-1">{errors.name}</p>}
        </div>
        <div>
          <label className="block text-xs font-medium text-gray-500 uppercase">Equipment Number</label>
          <input value={form.equipmentNumber} onChange={e => setForm({ ...form, equipmentNumber: e.target.value })} className={`mt-1 w-full rounded-md border ${errors.equipmentNumber ? 'border-red-300' : 'border-gray-300'} px-3 py-2 text-sm`} placeholder="e.g., EQ-COMP-2024-010" />
          {errors.equipmentNumber && <p className="text-xs text-red-600 mt-1">{errors.equipmentNumber}</p>}
        </div>
        <div>
          <label className="block text-xs font-medium text-gray-500 uppercase">Category</label>
          <select value={form.category} onChange={e => setForm({ ...form, category: e.target.value })} className={`mt-1 w-full rounded-md border ${errors.category ? 'border-red-300' : 'border-gray-300'} px-3 py-2 text-sm`}>
            {equipmentCategories.map(cat => (<option key={cat} value={cat}>{cat}</option>))}
          </select>
          {errors.category && <p className="text-xs text-red-600 mt-1">{errors.category}</p>}
        </div>
        <div>
          <label className="block text-xs font-medium text-gray-500 uppercase">Manufacturer</label>
          <input value={form.manufacturer} onChange={e => setForm({ ...form, manufacturer: e.target.value })} className={`mt-1 w-full rounded-md border ${errors.manufacturer ? 'border-red-300' : 'border-gray-300'} px-3 py-2 text-sm`} />
          {errors.manufacturer && <p className="text-xs text-red-600 mt-1">{errors.manufacturer}</p>}
        </div>
        <div>
          <label className="block text-xs font-medium text-gray-500 uppercase">Model</label>
          <input value={form.model} onChange={e => setForm({ ...form, model: e.target.value })} className={`mt-1 w-full rounded-md border ${errors.model ? 'border-red-300' : 'border-gray-300'} px-3 py-2 text-sm`} />
          {errors.model && <p className="text-xs text-red-600 mt-1">{errors.model}</p>}
        </div>
        <div>
          <label className="block text-xs font-medium text-gray-500 uppercase">Serial Number</label>
          <input value={form.serialNumber} onChange={e => setForm({ ...form, serialNumber: e.target.value })} className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 text-sm" />
        </div>
        <div>
          <label className="block text-xs font-medium text-gray-500 uppercase">Year of Manufacture</label>
          <input type="number" value={form.yearOfManufacture} onChange={e => setForm({ ...form, yearOfManufacture: Number(e.target.value) })} className={`mt-1 w-full rounded-md border ${errors.yearOfManufacture ? 'border-red-300' : 'border-gray-300'} px-3 py-2 text-sm`} />
          {errors.yearOfManufacture && <p className="text-xs text-red-600 mt-1">{errors.yearOfManufacture}</p>}
        </div>
        <div>
          <label className="block text-xs font-medium text-gray-500 uppercase">Ownership Type</label>
          <select value={form.ownershipType} onChange={e => setForm({ ...form, ownershipType: e.target.value })} className={`mt-1 w-full rounded-md border ${errors.ownershipType ? 'border-red-300' : 'border-gray-300'} px-3 py-2 text-sm`}>
            {ownershipTypes.map(o => (<option key={o.id} value={o.id}>{o.name}</option>))}
          </select>
          {errors.ownershipType && <p className="text-xs text-red-600 mt-1">{errors.ownershipType}</p>}
        </div>
      </div>

      {/* Financial/assignment/status */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-xs font-medium text-gray-500 uppercase">Purchase Date</label>
          <input type="date" value={form.purchaseDate} onChange={e => setForm({ ...form, purchaseDate: e.target.value })} className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 text-sm" />
        </div>
        <div>
          <label className="block text-xs font-medium text-gray-500 uppercase">Purchase Price</label>
          <input type="number" value={form.purchasePrice} onChange={e => setForm({ ...form, purchasePrice: Number(e.target.value) })} className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 text-sm" />
        </div>
        <div>
          <label className="block text-xs font-medium text-gray-500 uppercase">Current Book Value</label>
          <input type="number" value={form.currentBookValue} onChange={e => setForm({ ...form, currentBookValue: Number(e.target.value) })} className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 text-sm" />
        </div>
        <div>
          <label className="block text-xs font-medium text-gray-500 uppercase">Overall Status</label>
          <select value={form.overallStatus} onChange={e => setForm({ ...form, overallStatus: e.target.value as any })} className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 text-sm">
            {equipmentStatuses.map(s => (<option key={s.id} value={s.id}>{s.name}</option>))}
          </select>
        </div>
        <div>
          <label className="block text-xs font-medium text-gray-500 uppercase">Compliance Status</label>
          <select value={form.complianceStatus} onChange={e => setForm({ ...form, complianceStatus: e.target.value as any })} className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 text-sm">
            {complianceStatuses.map(s => (<option key={s.id} value={s.id}>{s.name}</option>))}
          </select>
        </div>
        <div className="flex items-center gap-2 mt-6">
          <input id="avail" type="checkbox" checked={form.isAvailableForAssignment} onChange={e => setForm({ ...form, isAvailableForAssignment: e.target.checked })} />
          <label htmlFor="avail" className="text-sm text-gray-700">Available for assignment</label>
        </div>
      </div>

      {/* Site assignment */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-xs font-medium text-gray-500 uppercase">Assign to Site</label>
          <select value={form.currentSiteId} onChange={e => {
            const val = e.target.value;
            const siteName = equipmentSites.find(s => s.id === val)?.name || '';
            setForm({ ...form, currentSiteId: val, currentSiteName: siteName });
          }} className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 text-sm">
            <option value="">Unassigned</option>
            {siteOptions.map(s => (<option key={s.value} value={s.value}>{s.label}</option>))}
          </select>
        </div>
        <div>
          <label className="block text-xs font-medium text-gray-500 uppercase">Next Inspection</label>
          <input type="date" value={form.nextInspectionDate} onChange={e => setForm({ ...form, nextInspectionDate: e.target.value })} className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 text-sm" />
        </div>
      </div>

      {/* Maintenance dates */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <label className="block text-xs font-medium text-gray-500 uppercase">Last Maintenance</label>
          <input type="date" value={form.lastMaintenanceDate} onChange={e => setForm({ ...form, lastMaintenanceDate: e.target.value })} className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 text-sm" />
        </div>
        <div>
          <label className="block text-xs font-medium text-gray-500 uppercase">Next Maintenance</label>
          <input type="date" value={form.nextMaintenanceDate} onChange={e => setForm({ ...form, nextMaintenanceDate: e.target.value })} className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 text-sm" />
        </div>
        <div>
          <label className="block text-xs font-medium text-gray-500 uppercase">Compliance Status</label>
          <select value={form.complianceStatus} onChange={e => setForm({ ...form, complianceStatus: e.target.value as any })} className="mt-1 w-full rounded-md border border-gray-300 px-3 py-2 text-sm">
            {complianceStatuses.map(s => (<option key={s.id} value={s.id}>{s.name}</option>))}
          </select>
        </div>
      </div>

      {/* Specifications */}
      <div>
        <div className="flex items-center justify-between">
          <label className="block text-sm font-semibold text-gray-900">Specifications</label>
          <div className="flex items-center gap-2">
            <input value={specKey} onChange={e => setSpecKey(e.target.value)} placeholder="Key (e.g., Engine Power)" className="w-56 rounded-md border border-gray-300 px-3 py-2 text-sm" />
            <input value={specVal} onChange={e => setSpecVal(e.target.value)} placeholder="Value (e.g., 120 kW)" className="w-56 rounded-md border border-gray-300 px-3 py-2 text-sm" />
            <button type="button" onClick={addSpec} className="px-3 py-2 rounded-md text-sm text-green-700 border border-green-600 bg-white hover:bg-green-50">Add</button>
          </div>
        </div>
        <div className="mt-3 grid grid-cols-1 md:grid-cols-2 gap-2">
          {Object.entries(form.specifications).map(([k, v]) => (
            <div key={k} className="flex items-center justify-between bg-gray-50 border border-gray-200 rounded-md px-3 py-2">
              <div>
                <div className="text-xs text-gray-500">{k}</div>
                <div className="text-sm text-gray-900">{v}</div>
              </div>
              <button type="button" onClick={() => removeSpec(k)} className="text-xs text-red-600 hover:text-red-700">Remove</button>
            </div>
          ))}
        </div>
      </div>

      <div className="flex items-center justify-between">
        <div className="space-x-2">
          {onPrev && (
            <button onClick={onPrev} className="inline-flex items-center px-4 h-9 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">Back</button>
          )}
        </div>
        <div className="space-x-2">
          <button onClick={onCancel} className="inline-flex items-center px-4 h-9 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">Cancel</button>
          <button onClick={() => handleSubmit()} className="inline-flex items-center px-5 h-9 rounded-md text-sm font-medium text-white bg-green-600 hover:bg-green-700">Save</button>
          {onNext && (
            <button onClick={onNext} className="inline-flex items-center px-4 h-9 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">Next</button>
          )}
        </div>
      </div>
    </div>
  );
};

export default CreateEquipmentForm;
