import { <PERSON>older, FileTex<PERSON>, ClipboardList, Alert<PERSON>riangle, Shield } from "lucide-react";
import { ExplorerItem } from "../shared/VSCodeInterface";
import { CompanyEquipment } from "../../data/equipmentMockData";

const createEquipmentExplorerItems = (equipment: CompanyEquipment): ExplorerItem[] => {
  return [
    {
      id: 'equipment-about',
      name: 'About',
      type: 'file',
      icon: <FileText className="h-4 w-4" />,
      data: { type: 'details', equipment }
    },
    {
      id: 'equipment-documents',
      name: 'Documents',
      type: 'file',
      icon: <ClipboardList className="h-4 w-4" />,
      data: { type: 'document', equipment }
    },
    {
      id: 'equipment-incidents',
      name: 'Incidents',
      type: 'file',
      icon: <AlertTriangle className="h-4 w-4" />,
      data: { type: 'incidents', equipment }
    },
    {
      id: 'equipment-inspections',
      name: 'Inspections',
      type: 'file',
      icon: <Shield className="h-4 w-4" />,
      data: { type: 'inspections', equipment }
    }
  ];
};

export default createEquipmentExplorerItems;
