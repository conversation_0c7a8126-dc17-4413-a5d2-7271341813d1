import { useState, useEffect } from "react";
import {
	Plus,
	Search,
	Filter,
	ClipboardCheck,
	Calendar,
	AlertTriangle,
	CheckCircle,
	Clock,
	Camera,
	MapPin,
	User,
	Eye,
} from "lucide-react";
import { EquipmentInspection } from "../../types/equipment";

interface EquipmentInspectionsProps {
	siteId: string;
}

const EquipmentInspections = ({ siteId }: EquipmentInspectionsProps) => {
	const [inspections, setInspections] = useState<EquipmentInspection[]>([]);
	const [filteredInspections, setFilteredInspections] = useState<
		EquipmentInspection[]
	>([]);
	const [searchTerm, setSearchTerm] = useState("");
	const [statusFilter, setStatusFilter] = useState("");
	const [isLoading, setIsLoading] = useState(true);
	const [selectedInspection, setSelectedInspection] =
		useState<EquipmentInspection | null>(null);
	const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

	// Mock data
	useEffect(() => {
		const mockInspections: EquipmentInspection[] = [
			{
				id: "insp-001",
				siteEquipment: {
					id: "eq-001",
					equipmentMaster: {
						id: "em-001",
						name: "Caterpillar Excavator 320D",
						sku: "CAT-EX-320D",
						category: {
							id: "heavy",
							name: "Heavy Machinery",
							description: "Heavy construction equipment",
							parentCategoryId: undefined,
							safetyRequirements: {},
							isPPECategory: false,
						},
						description: "Heavy-duty excavator for construction work",
						defaultCost: 150000,
						expectedLifespanDays: 3650,
						defaultInspectionIntervalDays: 30,
						defaultMaintenanceIntervalDays: 90,
						isPPE: false,
						requiresCertification: true,
						requiredTrainingIds: ["heavy-machinery-cert"],
						safetyStandards: ["ISO 6165"],
						specifications: {
							"Engine Power": "122 kW",
							"Operating Weight": "20,300 kg",
						},
						images: [],
						status: "active",
						createdAt: new Date(),
						updatedAt: new Date(),
					},
					siteId,
					serialNumber: "CAT320D2024001",
					status: {
						id: "in-use",
						name: "In Use",
						isAvailableForAssignment: false,
						color: "blue",
					},
					purchaseDate: new Date("2024-01-15"),
					totalUsageHours: 1250,
					acquisitionCost: 150000,
					currentValue: 135000,
					condition: "good",
				},
				templateId: "tmpl-001",
				templateName: "Heavy Machinery Daily Inspection",
				conductedByWorkerId: "worker-001",
				conductedByWorkerName: "John Mwangi",
				completionDate: new Date("2024-12-15"),
				overallStatus: "pass",
				gpsLocation: { lat: -1.2921, lng: 36.8219 },
				weatherConditions: "Clear, 25°C",
				offlineSyncStatus: "synced",
				photos: ["photo1.jpg", "photo2.jpg"],
				notes:
					"All systems operational. Minor hydraulic fluid leak noted but within acceptable limits.",
			},
			{
				id: "insp-002",
				templateId: "tmpl-002",
				templateName: "Power Tools Weekly Inspection",
				conductedByWorkerId: "worker-002",
				conductedByWorkerName: "Mary Wanjiku",
				completionDate: new Date("2024-12-14"),
				overallStatus: "pass-with-issues",
				offlineSyncStatus: "synced",
				photos: ["photo3.jpg"],
				notes:
					"Circular saw blade needs replacement. Safety guard functioning properly.",
			},
			{
				id: "insp-003",
				siteEquipment: {
					id: "eq-003",
					equipmentMaster: {
						id: "em-003",
						name: "Tower Crane TC-5013",
						sku: "TC-5013",
						category: {
							id: "lifting",
							name: "Lifting Equipment",
							description: "Lifting and hoisting equipment",
							parentCategoryId: undefined,
							safetyRequirements: {},
							isPPECategory: false,
						},
						description: "Tower crane for high-rise construction",
						defaultCost: 500000,
						expectedLifespanDays: 7300,
						defaultInspectionIntervalDays: 7,
						defaultMaintenanceIntervalDays: 30,
						isPPE: false,
						requiresCertification: true,
						requiredTrainingIds: ["crane-operator-cert"],
						safetyStandards: ["EN 14439"],
						specifications: { "Max Load": "5 tons", "Jib Length": "50m" },
						images: [],
						status: "active",
						createdAt: new Date(),
						updatedAt: new Date(),
					},
					siteId,
					serialNumber: "TC5013-2024-001",
					status: {
						id: "maintenance",
						name: "Under Maintenance",
						isAvailableForAssignment: false,
						color: "yellow",
					},
					purchaseDate: new Date("2024-02-01"),
					totalUsageHours: 890,
					acquisitionCost: 500000,
					currentValue: 480000,
					condition: "fair",
				},
				templateId: "tmpl-003",
				templateName: "Tower Crane Weekly Inspection",
				conductedByWorkerId: "worker-003",
				conductedByWorkerName: "Peter Kimani",
				completionDate: new Date("2024-12-13"),
				overallStatus: "fail",
				gpsLocation: { lat: -1.2921, lng: 36.8219 },
				weatherConditions: "Windy, 22°C",
				offlineSyncStatus: "synced",
				photos: ["photo4.jpg", "photo5.jpg", "photo6.jpg"],
				notes:
					"Critical issue found with wire rope. Equipment taken out of service immediately. Maintenance required before next use.",
			},
		];

		setInspections(mockInspections);
		setFilteredInspections(mockInspections);
		setIsLoading(false);
	}, [siteId]);

	// Filter and search logic
	useEffect(() => {
		let filtered = inspections.filter((inspection) => {
			const matchesSearch =
				inspection.siteEquipment?.equipmentMaster.name
					.toLowerCase()
					.includes(searchTerm.toLowerCase()) ||
				inspection.templateName
					.toLowerCase()
					.includes(searchTerm.toLowerCase()) ||
				inspection.conductedByWorkerName
					.toLowerCase()
					.includes(searchTerm.toLowerCase());

			const matchesStatus =
				!statusFilter || inspection.overallStatus === statusFilter;

			return matchesSearch && matchesStatus;
		});

		setFilteredInspections(filtered);
	}, [inspections, searchTerm, statusFilter]);

	const getStatusBadge = (status: string) => {
		const statusConfig = {
			pass: { color: "bg-green-100 text-green-800", icon: CheckCircle },
			"pass-with-issues": {
				color: "bg-yellow-100 text-yellow-800",
				icon: AlertTriangle,
			},
			fail: { color: "bg-red-100 text-red-800", icon: AlertTriangle },
		};

		const config = statusConfig[status as keyof typeof statusConfig];
		const IconComponent = config?.icon || CheckCircle;

		return (
			<span
				className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${config?.color || "bg-gray-100 text-gray-800"}`}
			>
				<IconComponent className="h-3 w-3 mr-1" />
				{status.replace("-", " ").toUpperCase()}
			</span>
		);
	};

	const getSyncStatusIcon = (status: string) => {
		switch (status) {
			case "synced":
				return <CheckCircle className="h-4 w-4 text-green-500" />;
			case "pending":
				return <Clock className="h-4 w-4 text-yellow-500" />;
			case "failed":
				return <AlertTriangle className="h-4 w-4 text-red-500" />;
			default:
				return <Clock className="h-4 w-4 text-gray-500" />;
		}
	};

	const handleViewDetails = (inspection: EquipmentInspection) => {
		setSelectedInspection(inspection);
		setIsDetailModalOpen(true);
	};

	if (isLoading) {
		return (
			<div className="space-y-6">
				<div className="animate-pulse">
					<div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
					<div className="h-32 bg-gray-200 rounded mb-4"></div>
					<div className="space-y-3">
						{[...Array(5)].map((_, i) => (
							<div key={i} className="h-16 bg-gray-200 rounded"></div>
						))}
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<h2 className="text-2xl font-bold">Equipment Inspections</h2>
				<div className="flex gap-2">
					<button className="flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
						<Calendar className="h-4 w-4 mr-2" />
						Schedule Inspection
					</button>
					<button className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700">
						<Plus className="h-4 w-4 mr-2" />
						New Inspection
					</button>
				</div>
			</div>

			{/* Summary Cards */}
			<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Total Inspections</p>
							<p className="text-2xl font-bold">{inspections.length}</p>
						</div>
						<ClipboardCheck className="h-8 w-8 text-blue-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Passed</p>
							<p className="text-2xl font-bold text-green-600">
								{inspections.filter((i) => i.overallStatus === "pass").length}
							</p>
						</div>
						<CheckCircle className="h-8 w-8 text-green-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">With Issues</p>
							<p className="text-2xl font-bold text-yellow-600">
								{
									inspections.filter(
										(i) => i.overallStatus === "pass-with-issues",
									).length
								}
							</p>
						</div>
						<AlertTriangle className="h-8 w-8 text-yellow-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Failed</p>
							<p className="text-2xl font-bold text-red-600">
								{inspections.filter((i) => i.overallStatus === "fail").length}
							</p>
						</div>
						<AlertTriangle className="h-8 w-8 text-red-500" />
					</div>
				</div>
			</div>

			{/* Filters */}
			<div className="bg-white rounded-lg border border-gray-200 p-4">
				<div className="flex flex-col sm:flex-row gap-4 items-center">
					<div className="flex-1 relative">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
						<input
							type="text"
							placeholder="Search inspections by equipment, template, or inspector..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
						/>
					</div>
					<div className="flex items-center gap-2">
						<Filter className="h-4 w-4 text-gray-400" />
						<select
							value={statusFilter}
							onChange={(e) => setStatusFilter(e.target.value)}
							className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
						>
							<option value="">All Status</option>
							<option value="pass">Passed</option>
							<option value="pass-with-issues">Pass with Issues</option>
							<option value="fail">Failed</option>
						</select>
					</div>
				</div>
			</div>

			{/* Inspections List */}
			<div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
				<div className="overflow-x-auto">
					<table className="min-w-full divide-y divide-gray-200">
						<thead className="bg-gray-50">
							<tr>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Equipment & Template
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Inspector
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Date & Status
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Location & Photos
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Sync Status
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Actions
								</th>
							</tr>
						</thead>
						<tbody className="bg-white divide-y divide-gray-200">
							{filteredInspections.map((inspection) => (
								<tr key={inspection.id} className="hover:bg-gray-50">
									<td className="px-6 py-4 whitespace-nowrap">
										<div>
											<div className="text-sm font-medium text-gray-900">
												{inspection.siteEquipment?.equipmentMaster.name ||
													"General Site Inspection"}
											</div>
											<div className="text-sm text-gray-500">
												{inspection.templateName}
											</div>
											{inspection.siteEquipment?.serialNumber && (
												<div className="text-sm text-gray-500">
													Serial: {inspection.siteEquipment.serialNumber}
												</div>
											)}
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<div className="flex items-center">
											<User className="h-4 w-4 text-gray-400 mr-2" />
											<span className="text-sm text-gray-900">
												{inspection.conductedByWorkerName}
											</span>
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<div>
											<div className="text-sm text-gray-900">
												{inspection.completionDate.toLocaleDateString()}
											</div>
											<div className="mt-1">
												{getStatusBadge(inspection.overallStatus)}
											</div>
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<div>
											{inspection.gpsLocation && (
												<div className="flex items-center text-sm text-gray-900 mb-1">
													<MapPin className="h-4 w-4 text-gray-400 mr-1" />
													GPS Recorded
												</div>
											)}
											<div className="flex items-center text-sm text-gray-500">
												<Camera className="h-4 w-4 text-gray-400 mr-1" />
												{inspection.photos.length} photos
											</div>
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<div className="flex items-center">
											{getSyncStatusIcon(inspection.offlineSyncStatus)}
											<span className="ml-2 text-sm text-gray-900 capitalize">
												{inspection.offlineSyncStatus}
											</span>
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
										<button
											onClick={() => handleViewDetails(inspection)}
											className="text-green-600 hover:text-green-900"
											title="View Details"
										>
											<Eye className="h-4 w-4" />
										</button>
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>
			</div>

			{/* Inspection Detail Modal */}
			{isDetailModalOpen && selectedInspection && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
					<div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
						<div className="flex justify-between items-start mb-4">
							<h3 className="text-lg font-semibold">Inspection Details</h3>
							<button
								onClick={() => {
									setIsDetailModalOpen(false);
									setSelectedInspection(null);
								}}
								className="text-gray-400 hover:text-gray-600"
							>
								×
							</button>
						</div>

						<div className="space-y-4">
							<div>
								<h4 className="font-medium text-gray-900">Equipment</h4>
								<p className="text-sm text-gray-600">
									{selectedInspection.siteEquipment?.equipmentMaster.name ||
										"General Site Inspection"}
								</p>
							</div>

							<div>
								<h4 className="font-medium text-gray-900">Template</h4>
								<p className="text-sm text-gray-600">
									{selectedInspection.templateName}
								</p>
							</div>

							<div>
								<h4 className="font-medium text-gray-900">Inspector</h4>
								<p className="text-sm text-gray-600">
									{selectedInspection.conductedByWorkerName}
								</p>
							</div>

							<div>
								<h4 className="font-medium text-gray-900">Status</h4>
								{getStatusBadge(selectedInspection.overallStatus)}
							</div>

							{selectedInspection.notes && (
								<div>
									<h4 className="font-medium text-gray-900">Notes</h4>
									<p className="text-sm text-gray-600">
										{selectedInspection.notes}
									</p>
								</div>
							)}

							{selectedInspection.weatherConditions && (
								<div>
									<h4 className="font-medium text-gray-900">
										Weather Conditions
									</h4>
									<p className="text-sm text-gray-600">
										{selectedInspection.weatherConditions}
									</p>
								</div>
							)}

							<div>
								<h4 className="font-medium text-gray-900">Photos</h4>
								<p className="text-sm text-gray-600">
									{selectedInspection.photos.length} photos captured
								</p>
							</div>
						</div>

						<div className="flex justify-end mt-6">
							<button
								onClick={() => {
									setIsDetailModalOpen(false);
									setSelectedInspection(null);
								}}
								className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
							>
								Close
							</button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default EquipmentInspections;
