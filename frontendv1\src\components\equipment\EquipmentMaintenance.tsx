import { useState, useEffect } from "react";
import {
	Plus,
	Search,
	Filter,
	Wrench,
	Calendar,
	AlertTriangle,
	CheckCircle,
	Clock,
	DollarSign,
	User,
	Eye,
	Edit,
} from "lucide-react";
import { MaintenanceWorkOrder } from "../../types/equipment";

interface EquipmentMaintenanceProps {
	siteId: string;
}

const EquipmentMaintenance = ({ siteId }: EquipmentMaintenanceProps) => {
	const [workOrders, setWorkOrders] = useState<MaintenanceWorkOrder[]>([]);
	const [filteredWorkOrders, setFilteredWorkOrders] = useState<
		MaintenanceWorkOrder[]
	>([]);
	const [searchTerm, setSearchTerm] = useState("");
	const [statusFilter, setStatusFilter] = useState("");
	const [priorityFilter, setPriorityFilter] = useState("");
	const [isLoading, setIsLoading] = useState(true);
	const [selectedWorkOrder, setSelectedWorkOrder] =
		useState<MaintenanceWorkOrder | null>(null);
	const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

	// Mock data
	useEffect(() => {
		const mockWorkOrders: MaintenanceWorkOrder[] = [
			{
				id: "wo-001",
				siteEquipment: {
					id: "eq-001",
					equipmentMaster: {
						id: "em-001",
						name: "Caterpillar Excavator 320D",
						sku: "CAT-EX-320D",
						category: {
							id: "heavy",
							name: "Heavy Machinery",
							description: "Heavy construction equipment",
							parentCategoryId: undefined,
							safetyRequirements: {},
							isPPECategory: false,
						},
						description: "Heavy-duty excavator for construction work",
						defaultCost: 150000,
						expectedLifespanDays: 3650,
						defaultInspectionIntervalDays: 30,
						defaultMaintenanceIntervalDays: 90,
						isPPE: false,
						requiresCertification: true,
						requiredTrainingIds: ["heavy-machinery-cert"],
						safetyStandards: ["ISO 6165"],
						specifications: {
							"Engine Power": "122 kW",
							"Operating Weight": "20,300 kg",
						},
						images: [],
						status: "active",
						createdAt: new Date(),
						updatedAt: new Date(),
					},
					siteId,
					serialNumber: "CAT320D2024001",
					status: {
						id: "maintenance",
						name: "Under Maintenance",
						isAvailableForAssignment: false,
						color: "yellow",
					},
					purchaseDate: new Date("2024-01-15"),
					totalUsageHours: 1250,
					acquisitionCost: 150000,
					currentValue: 135000,
					condition: "good",
				},
				workOrderType: "scheduled",
				priority: "medium",
				description:
					"Scheduled 500-hour maintenance service including oil change, filter replacement, and hydraulic system check",
				scheduledDate: new Date("2024-12-20"),
				actualStartDate: new Date("2024-12-18"),
				assignedToWorkerId: "mech-001",
				assignedToWorkerName: "James Ochieng",
				status: "in-progress",
				laborCost: 800,
				partsCost: 1200,
				totalCost: 2000,
				downtimeHours: 8,
				notes:
					"Hydraulic fluid leak detected during inspection. Additional seals required.",
				partsUsed: [
					{
						id: "part-001",
						name: "Engine Oil Filter",
						partNumber: "CAT-1R-0750",
						quantity: 2,
						unitCost: 45,
						supplier: "Caterpillar",
					},
					{
						id: "part-002",
						name: "Hydraulic Seal Kit",
						partNumber: "CAT-9T-5716",
						quantity: 1,
						unitCost: 180,
						supplier: "Caterpillar",
					},
				],
			},
			{
				id: "wo-002",
				siteEquipment: {
					id: "eq-002",
					equipmentMaster: {
						id: "em-002",
						name: "Tower Crane TC-5013",
						sku: "TC-5013",
						category: {
							id: "lifting",
							name: "Lifting Equipment",
							description: "Lifting and hoisting equipment",
							parentCategoryId: undefined,
							safetyRequirements: {},
							isPPECategory: false,
						},
						description: "Tower crane for high-rise construction",
						defaultCost: 500000,
						expectedLifespanDays: 7300,
						defaultInspectionIntervalDays: 7,
						defaultMaintenanceIntervalDays: 30,
						isPPE: false,
						requiresCertification: true,
						requiredTrainingIds: ["crane-operator-cert"],
						safetyStandards: ["EN 14439"],
						specifications: { "Max Load": "5 tons", "Jib Length": "50m" },
						images: [],
						status: "active",
						createdAt: new Date(),
						updatedAt: new Date(),
					},
					siteId,
					serialNumber: "TC5013-2024-001",
					status: {
						id: "out-of-service",
						name: "Out of Service",
						isAvailableForAssignment: false,
						color: "red",
					},
					purchaseDate: new Date("2024-02-01"),
					totalUsageHours: 890,
					acquisitionCost: 500000,
					currentValue: 480000,
					condition: "poor",
				},
				workOrderType: "emergency",
				priority: "critical",
				description:
					"Emergency repair - Wire rope replacement due to inspection failure",
				scheduledDate: new Date("2024-12-16"),
				assignedToWorkerId: "mech-002",
				assignedToWorkerName: "David Mutua",
				status: "pending",
				laborCost: 1500,
				partsCost: 3500,
				totalCost: 5000,
				downtimeHours: 24,
				notes:
					"Critical safety issue. Equipment must remain out of service until repair is completed and re-certified.",
				partsUsed: [
					{
						id: "part-003",
						name: "Wire Rope 16mm",
						partNumber: "WR-16-6x19",
						quantity: 100,
						unitCost: 25,
						supplier: "Lifting Solutions Ltd",
					},
					{
						id: "part-004",
						name: "Wire Rope Clips",
						partNumber: "WRC-16",
						quantity: 20,
						unitCost: 15,
						supplier: "Lifting Solutions Ltd",
					},
				],
			},
			{
				id: "wo-003",
				siteEquipment: {
					id: "eq-003",
					equipmentMaster: {
						id: "em-003",
						name: "Concrete Mixer CM-350",
						sku: "CM-350",
						category: {
							id: "concrete",
							name: "Concrete Equipment",
							description: "Concrete mixing and pumping equipment",
							parentCategoryId: undefined,
							safetyRequirements: {},
							isPPECategory: false,
						},
						description: "Portable concrete mixer",
						defaultCost: 5000,
						expectedLifespanDays: 2190,
						defaultInspectionIntervalDays: 14,
						defaultMaintenanceIntervalDays: 60,
						isPPE: false,
						requiresCertification: false,
						requiredTrainingIds: [],
						safetyStandards: ["EN 12151"],
						specifications: { Capacity: "350L", Engine: "Honda GX160" },
						images: [],
						status: "active",
						createdAt: new Date(),
						updatedAt: new Date(),
					},
					siteId,
					serialNumber: "CM350-2024-003",
					status: {
						id: "available",
						name: "Available",
						isAvailableForAssignment: true,
						color: "green",
					},
					purchaseDate: new Date("2024-03-15"),
					totalUsageHours: 245,
					acquisitionCost: 5000,
					currentValue: 4200,
					condition: "excellent",
				},
				workOrderType: "scheduled",
				priority: "low",
				description: "Routine maintenance - Engine service and drum cleaning",
				scheduledDate: new Date("2024-12-25"),
				status: "completed",
				actualStartDate: new Date("2024-12-14"),
				actualCompletionDate: new Date("2024-12-14"),
				assignedToWorkerId: "mech-003",
				assignedToWorkerName: "Samuel Kiprotich",
				laborCost: 150,
				partsCost: 85,
				totalCost: 235,
				downtimeHours: 2,
				notes:
					"Routine maintenance completed successfully. All systems functioning normally.",
				partsUsed: [
					{
						id: "part-005",
						name: "Engine Oil SAE 30",
						partNumber: "EO-SAE30-1L",
						quantity: 1,
						unitCost: 25,
						supplier: "Local Supplier",
					},
					{
						id: "part-006",
						name: "Air Filter",
						partNumber: "AF-GX160",
						quantity: 1,
						unitCost: 35,
						supplier: "Honda Parts",
					},
				],
			},
		];

		setWorkOrders(mockWorkOrders);
		setFilteredWorkOrders(mockWorkOrders);
		setIsLoading(false);
	}, [siteId]);

	// Filter and search logic
	useEffect(() => {
		let filtered = workOrders.filter((workOrder) => {
			const matchesSearch =
				workOrder.siteEquipment.equipmentMaster.name
					.toLowerCase()
					.includes(searchTerm.toLowerCase()) ||
				workOrder.description
					.toLowerCase()
					.includes(searchTerm.toLowerCase()) ||
				workOrder.assignedToWorkerName
					?.toLowerCase()
					.includes(searchTerm.toLowerCase());

			const matchesStatus = !statusFilter || workOrder.status === statusFilter;
			const matchesPriority =
				!priorityFilter || workOrder.priority === priorityFilter;

			return matchesSearch && matchesStatus && matchesPriority;
		});

		setFilteredWorkOrders(filtered);
	}, [workOrders, searchTerm, statusFilter, priorityFilter]);

	const getStatusBadge = (status: string) => {
		const statusConfig = {
			pending: { color: "bg-yellow-100 text-yellow-800", icon: Clock },
			"in-progress": { color: "bg-blue-100 text-blue-800", icon: Wrench },
			completed: { color: "bg-green-100 text-green-800", icon: CheckCircle },
			cancelled: { color: "bg-gray-100 text-gray-800", icon: AlertTriangle },
		};

		const config = statusConfig[status as keyof typeof statusConfig];
		const IconComponent = config?.icon || Clock;

		return (
			<span
				className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${config?.color || "bg-gray-100 text-gray-800"}`}
			>
				<IconComponent className="h-3 w-3 mr-1" />
				{status.replace("-", " ").toUpperCase()}
			</span>
		);
	};

	const getPriorityBadge = (priority: string) => {
		const priorityConfig = {
			low: { color: "bg-gray-100 text-gray-800" },
			medium: { color: "bg-yellow-100 text-yellow-800" },
			high: { color: "bg-orange-100 text-orange-800" },
			critical: { color: "bg-red-100 text-red-800" },
		};

		const config = priorityConfig[priority as keyof typeof priorityConfig];

		return (
			<span
				className={`px-2 py-1 text-xs font-medium rounded-full ${config?.color || "bg-gray-100 text-gray-800"}`}
			>
				{priority.toUpperCase()}
			</span>
		);
	};

	const handleViewDetails = (workOrder: MaintenanceWorkOrder) => {
		setSelectedWorkOrder(workOrder);
		setIsDetailModalOpen(true);
	};

	if (isLoading) {
		return (
			<div className="space-y-6">
				<div className="animate-pulse">
					<div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
					<div className="h-32 bg-gray-200 rounded mb-4"></div>
					<div className="space-y-3">
						{[...Array(5)].map((_, i) => (
							<div key={i} className="h-16 bg-gray-200 rounded"></div>
						))}
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<h2 className="text-2xl font-bold">Equipment Maintenance</h2>
				<div className="flex gap-2">
					<button className="flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
						<Calendar className="h-4 w-4 mr-2" />
						Schedule Maintenance
					</button>
					<button className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700">
						<Plus className="h-4 w-4 mr-2" />
						New Work Order
					</button>
				</div>
			</div>

			{/* Summary Cards */}
			<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Total Work Orders</p>
							<p className="text-2xl font-bold">{workOrders.length}</p>
						</div>
						<Wrench className="h-8 w-8 text-blue-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">In Progress</p>
							<p className="text-2xl font-bold text-blue-600">
								{workOrders.filter((wo) => wo.status === "in-progress").length}
							</p>
						</div>
						<Clock className="h-8 w-8 text-blue-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Critical Priority</p>
							<p className="text-2xl font-bold text-red-600">
								{workOrders.filter((wo) => wo.priority === "critical").length}
							</p>
						</div>
						<AlertTriangle className="h-8 w-8 text-red-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Total Cost</p>
							<p className="text-2xl font-bold text-green-600">
								$
								{workOrders
									.reduce((sum, wo) => sum + wo.totalCost, 0)
									.toLocaleString()}
							</p>
						</div>
						<DollarSign className="h-8 w-8 text-green-500" />
					</div>
				</div>
			</div>

			{/* Filters */}
			<div className="bg-white rounded-lg border border-gray-200 p-4">
				<div className="flex flex-col sm:flex-row gap-4 items-center">
					<div className="flex-1 relative">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
						<input
							type="text"
							placeholder="Search work orders by equipment, description, or technician..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
						/>
					</div>
					<div className="flex items-center gap-2">
						<Filter className="h-4 w-4 text-gray-400" />
						<select
							value={statusFilter}
							onChange={(e) => setStatusFilter(e.target.value)}
							className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
						>
							<option value="">All Status</option>
							<option value="pending">Pending</option>
							<option value="in-progress">In Progress</option>
							<option value="completed">Completed</option>
							<option value="cancelled">Cancelled</option>
						</select>
						<select
							value={priorityFilter}
							onChange={(e) => setPriorityFilter(e.target.value)}
							className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
						>
							<option value="">All Priorities</option>
							<option value="low">Low</option>
							<option value="medium">Medium</option>
							<option value="high">High</option>
							<option value="critical">Critical</option>
						</select>
					</div>
				</div>
			</div>

			{/* Work Orders List */}
			<div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
				<div className="overflow-x-auto">
					<table className="min-w-full divide-y divide-gray-200">
						<thead className="bg-gray-50">
							<tr>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Equipment & Description
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Priority & Status
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Technician
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Schedule
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Cost & Downtime
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Actions
								</th>
							</tr>
						</thead>
						<tbody className="bg-white divide-y divide-gray-200">
							{filteredWorkOrders.map((workOrder) => (
								<tr key={workOrder.id} className="hover:bg-gray-50">
									<td className="px-6 py-4 whitespace-nowrap">
										<div>
											<div className="text-sm font-medium text-gray-900">
												{workOrder.siteEquipment.equipmentMaster.name}
											</div>
											<div className="text-sm text-gray-500">
												Serial: {workOrder.siteEquipment.serialNumber}
											</div>
											<div className="text-sm text-gray-500 mt-1">
												{workOrder.description.length > 50
													? `${workOrder.description.substring(0, 50)}...`
													: workOrder.description}
											</div>
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<div className="space-y-1">
											{getPriorityBadge(workOrder.priority)}
											{getStatusBadge(workOrder.status)}
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										{workOrder.assignedToWorkerName ? (
											<div className="flex items-center">
												<User className="h-4 w-4 text-gray-400 mr-2" />
												<span className="text-sm text-gray-900">
													{workOrder.assignedToWorkerName}
												</span>
											</div>
										) : (
											<span className="text-sm text-gray-500">Unassigned</span>
										)}
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<div className="text-sm text-gray-900">
											<div>
												Scheduled:{" "}
												{workOrder.scheduledDate.toLocaleDateString()}
											</div>
											{workOrder.actualStartDate && (
												<div className="text-gray-500">
													Started:{" "}
													{workOrder.actualStartDate.toLocaleDateString()}
												</div>
											)}
											{workOrder.actualCompletionDate && (
												<div className="text-green-600">
													Completed:{" "}
													{workOrder.actualCompletionDate.toLocaleDateString()}
												</div>
											)}
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<div className="text-sm text-gray-900">
											<div className="flex items-center">
												<DollarSign className="h-4 w-4 text-gray-400 mr-1" />$
												{workOrder.totalCost.toLocaleString()}
											</div>
											<div className="text-gray-500">
												{workOrder.downtimeHours}h downtime
											</div>
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
										<div className="flex space-x-2">
											<button
												onClick={() => handleViewDetails(workOrder)}
												className="text-green-600 hover:text-green-900"
												title="View Details"
											>
												<Eye className="h-4 w-4" />
											</button>
											<button
												className="text-blue-600 hover:text-blue-900"
												title="Edit Work Order"
											>
												<Edit className="h-4 w-4" />
											</button>
										</div>
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>
			</div>

			{/* Work Order Detail Modal */}
			{isDetailModalOpen && selectedWorkOrder && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
					<div className="bg-white rounded-lg p-6 w-full max-w-3xl max-h-[90vh] overflow-y-auto">
						<div className="flex justify-between items-start mb-4">
							<h3 className="text-lg font-semibold">
								Work Order Details - {selectedWorkOrder.id}
							</h3>
							<button
								onClick={() => {
									setIsDetailModalOpen(false);
									setSelectedWorkOrder(null);
								}}
								className="text-gray-400 hover:text-gray-600"
							>
								×
							</button>
						</div>

						<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
							<div className="space-y-4">
								<div>
									<h4 className="font-medium text-gray-900">Equipment</h4>
									<p className="text-sm text-gray-600">
										{selectedWorkOrder.siteEquipment.equipmentMaster.name}
									</p>
									<p className="text-sm text-gray-500">
										Serial: {selectedWorkOrder.siteEquipment.serialNumber}
									</p>
								</div>

								<div>
									<h4 className="font-medium text-gray-900">Description</h4>
									<p className="text-sm text-gray-600">
										{selectedWorkOrder.description}
									</p>
								</div>

								<div>
									<h4 className="font-medium text-gray-900">
										Priority & Status
									</h4>
									<div className="flex space-x-2 mt-1">
										{getPriorityBadge(selectedWorkOrder.priority)}
										{getStatusBadge(selectedWorkOrder.status)}
									</div>
								</div>

								<div>
									<h4 className="font-medium text-gray-900">Technician</h4>
									<p className="text-sm text-gray-600">
										{selectedWorkOrder.assignedToWorkerName || "Unassigned"}
									</p>
								</div>
							</div>

							<div className="space-y-4">
								<div>
									<h4 className="font-medium text-gray-900">Cost Breakdown</h4>
									<div className="text-sm text-gray-600 space-y-1">
										<div>
											Labor: ${selectedWorkOrder.laborCost.toLocaleString()}
										</div>
										<div>
											Parts: ${selectedWorkOrder.partsCost.toLocaleString()}
										</div>
										<div className="font-medium">
											Total: ${selectedWorkOrder.totalCost.toLocaleString()}
										</div>
									</div>
								</div>

								<div>
									<h4 className="font-medium text-gray-900">Timeline</h4>
									<div className="text-sm text-gray-600 space-y-1">
										<div>
											Scheduled:{" "}
											{selectedWorkOrder.scheduledDate.toLocaleDateString()}
										</div>
										{selectedWorkOrder.actualStartDate && (
											<div>
												Started:{" "}
												{selectedWorkOrder.actualStartDate.toLocaleDateString()}
											</div>
										)}
										{selectedWorkOrder.actualCompletionDate && (
											<div>
												Completed:{" "}
												{selectedWorkOrder.actualCompletionDate.toLocaleDateString()}
											</div>
										)}
										<div>Downtime: {selectedWorkOrder.downtimeHours} hours</div>
									</div>
								</div>

								{selectedWorkOrder.partsUsed.length > 0 && (
									<div>
										<h4 className="font-medium text-gray-900">Parts Used</h4>
										<div className="text-sm text-gray-600 space-y-1">
											{selectedWorkOrder.partsUsed.map((part) => (
												<div key={part.id}>
													{part.name} (Qty: {part.quantity}) - $
													{(part.quantity * part.unitCost).toFixed(2)}
												</div>
											))}
										</div>
									</div>
								)}
							</div>
						</div>

						{selectedWorkOrder.notes && (
							<div className="mt-6">
								<h4 className="font-medium text-gray-900">Notes</h4>
								<p className="text-sm text-gray-600 mt-1">
									{selectedWorkOrder.notes}
								</p>
							</div>
						)}

						<div className="flex justify-end mt-6">
							<button
								onClick={() => {
									setIsDetailModalOpen(false);
									setSelectedWorkOrder(null);
								}}
								className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
							>
								Close
							</button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default EquipmentMaintenance;
