import { Link, useNavigate } from "react-router-dom";
import { useState } from "react";
import { CompanyEquipment } from "../../data/equipmentMockData";
import { FilePlus2 } from "lucide-react";
import EquipmentHeader from "./EquipmentHeader";
import { VSCodeTab } from "../shared/VSCodeInterface";
import TabContainer from "../data/shared/TabContainer";

type Props = { 
  equipment: CompanyEquipment; 
  siteId?: string;
};

const EquipmentTabs = ({ equipment, siteId }: Props) => {
  const navigate = useNavigate();
  const [aboutActiveTab, setAboutActiveTab] = useState<string>("overview");
  const renderTabContent = (tab: VSCodeTab) => {
    // Be tolerant of tab id/type differences by matching id suffixes and type
    if (tab.id === 'equipment-about' || tab.type === 'details' || tab.id?.endsWith('-about')) {
      return (
          <div className="p-6 space-y-8">
            {/* Header with actions */}
            <EquipmentHeader
              equipment={equipment}
              onEdit={() => navigate(`/sites/${siteId}/equipment/${equipment.id}/edit`)}
              onDelete={() => { if (confirm('Remove this equipment?')) { console.log('remove equipment placeholder'); } }}
            />

            {/* Quick metadata */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-white p-4 rounded-lg border border-gray-200">
                <div className="text-xs text-gray-500">Equipment Number</div>
                <div className="text-sm font-medium text-gray-900">{equipment.equipmentNumber}</div>
              </div>
              <div className="bg-white p-4 rounded-lg border border-gray-200">
                <div className="text-xs text-gray-500">Manufacturer</div>
                <div className="text-sm font-medium text-gray-900">{equipment.manufacturer || '—'}</div>
              </div>
              <div className="bg-white p-4 rounded-lg border border-gray-200">
                <div className="text-xs text-gray-500">Model</div>
                <div className="text-sm font-medium text-gray-900">{equipment.model || '—'}</div>
              </div>
              <div className="bg-white p-4 rounded-lg border border-gray-200">
                <div className="text-xs text-gray-500">Current Site</div>
                <div className="text-sm font-medium text-gray-900">{equipment.currentSiteName || 'Unassigned'}</div>
              </div>
            </div>

            {/* Embedded navigation tabs */}
            <div className="pt-4">
              <TabContainer
                tabs={[
                  {
                    id: 'overview',
                    label: 'Overview',
                    content: (
                      <div className="bg-white border border-gray-200 rounded-lg p-6">
                        <h3 className="text-sm font-semibold text-gray-700 mb-4">General Information</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                          <div>
                            <div className="text-gray-500">Serial Number</div>
                            <div className="text-gray-900">{equipment.serialNumber || '—'}</div>
                          </div>
                          <div>
                            <div className="text-gray-500">Year of Manufacture</div>
                            <div className="text-gray-900">{equipment.yearOfManufacture || '—'}</div>
                          </div>
                          <div>
                            <div className="text-gray-500">Ownership</div>
                            <div className="text-gray-900">{equipment.ownershipType || '—'}</div>
                          </div>
                          <div>
                            <div className="text-gray-500">Total Hours</div>
                            <div className="text-gray-900">{equipment.totalHours ?? '—'}</div>
                          </div>
                        </div>
                      </div>
                    )
                  },
                  {
                    id: 'documents',
                    label: 'Documents',
                    content: (
                      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                        <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                          <h3 className="text-sm font-semibold text-gray-900">Documents</h3>
                          <button className="inline-flex items-center px-3 py-1.5 border border-green-600 rounded-md text-sm font-medium text-green-600 bg-white hover:bg-green-50">Add Document</button>
                        </div>
                        <div className="overflow-x-auto">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Issue Date</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expiry Date</th>
                                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              <tr>
                                <td colSpan={5} className="px-6 py-8 text-center text-sm text-gray-500">No documents found</td>
                              </tr>
                            </tbody>
                          </table>
                        </div>
                      </div>
                    )
                  },
                  {
                    id: 'incidents',
                    label: 'Incidents',
                    content: (
                      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                        <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                          <h3 className="text-sm font-semibold text-gray-900">Incidents</h3>
                          <button className="inline-flex items-center px-3 py-1.5 border border-green-600 rounded-md text-sm font-medium text-green-600 bg-white hover:bg-green-50">Report Incident</button>
                        </div>
                        <div className="overflow-x-auto">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Severity</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              <tr>
                                <td colSpan={5} className="px-6 py-8 text-center text-sm text-gray-500">No incidents found</td>
                              </tr>
                            </tbody>
                          </table>
                        </div>
                      </div>
                    )
                  },
                  {
                    id: 'inspections',
                    label: 'Inspections',
                    content: (
                      <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                        <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                          <h3 className="text-sm font-semibold text-gray-900">Inspections</h3>
                          <Link to={`/sites/${siteId}/inspections/form`} className="inline-flex items-center px-3 py-1.5 border border-green-600 rounded-md text-sm font-medium text-green-600 bg-white hover:bg-green-50">Add Inspection</Link>
                        </div>
                        <div className="overflow-x-auto">
                          <table className="min-w-full divide-y divide-gray-200">
                            <thead className="bg-gray-50">
                              <tr>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Inspector</th>
                                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                              </tr>
                            </thead>
                            <tbody className="bg-white divide-y divide-gray-200">
                              <tr>
                                <td colSpan={4} className="px-6 py-8 text-center text-sm text-gray-500">No inspections found</td>
                              </tr>
                            </tbody>
                          </table>
                        </div>
                      </div>
                    )
                  }
                ]}
                activeTab={aboutActiveTab}
                onTabChange={setAboutActiveTab}
              />
            </div>
          </div>
        );

    }

    if (tab.id === 'equipment-documents' || tab.id?.endsWith('-documents')) {
      return (
          <div className="p-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-sm font-semibold text-gray-700 mb-2">Documents</h3>
              <p className="text-sm text-gray-600">Attach and manage equipment documents here. (Placeholder)</p>
            </div>
          </div>
        );

    }

    if (tab.id === 'equipment-incidents' || tab.id?.endsWith('-incidents')) {
      return (
          <div className="p-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-sm font-semibold text-gray-700 mb-2">Incidents</h3>
              <p className="text-sm text-gray-600">View incident history related to this equipment. (Placeholder)</p>
            </div>
          </div>
        );

    }

    if (tab.id === 'equipment-inspections' || tab.id?.endsWith('-inspections')) {
      return (
          <div className="p-6">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-sm font-semibold text-gray-700">Inspections</h3>
                <Link
                  to={`/sites/${siteId}/inspections/form`}
                  className="inline-flex items-center px-3 py-1.5 border border-green-600 rounded-md text-sm font-medium text-green-600 bg-white hover:bg-green-50"
                >
                  <FilePlus2 className="h-4 w-4 mr-2" />
                  Add Inspection
                </Link>
              </div>
              <p className="text-sm text-gray-600">View the inspection history for this equipment. (Placeholder)</p>
            </div>
          </div>
        );

    }

    // Fallback
    return (
          <div className="p-6">
            <div className="text-center py-12">
              <p className="text-gray-500">Content not found</p>
            </div>
          </div>
    );
  };

  return { renderTabContent };
};

export default EquipmentTabs;
