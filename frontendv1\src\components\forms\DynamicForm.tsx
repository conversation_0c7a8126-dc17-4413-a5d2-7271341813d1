import React, { useState, useEffect } from "react";
import {
	FormTemplate,
	FormSubmissionData,
	FormValidationError,
	FormState,
} from "../../types/forms";
import FormField from "./FormField";
import {
	ChevronLeft,
	ChevronRight,
	Save,
	Send,
	AlertCircle,
	Clock,
} from "lucide-react";

interface DynamicFormProps {
	template: FormTemplate;
	initialData?: FormSubmissionData;
	onSave?: (data: FormSubmissionData, isDraft: boolean) => void;
	onSubmit?: (data: FormSubmissionData) => void;
	readonly?: boolean;
	showProgress?: boolean;
}

const DynamicForm: React.FC<DynamicFormProps> = ({
	template,
	initialData = {},
	onSave,
	onSubmit,
	readonly = false,
	showProgress = true,
}) => {
	const [formState, setFormState] = useState<FormState>({
		data: initialData,
		errors: [],
		isValid: false,
		isDirty: false,
		isSubmitting: false,
		currentSection: 0,
		completedSections: new Set(),
	});

	const [startTime] = useState(Date.now());

	// Validate form data
	const validateForm = (): FormValidationError[] => {
		const errors: FormValidationError[] = [];

		template.sections.forEach((section) => {
			section.fields.forEach((field) => {
				const value = formState.data[field.id];

				// Check if field should be visible based on conditional logic
				if (field.conditional) {
					const dependentValue = formState.data[field.conditional.dependsOn];
					const shouldShow = evaluateCondition(
						dependentValue,
						field.conditional.value,
						field.conditional.operator,
					);
					if (!shouldShow) return; // Skip validation if field is hidden
				}

				// Required field validation
				if (
					field.required &&
					(!value ||
						value === "" ||
						(Array.isArray(value) && value.length === 0))
				) {
					errors.push({
						fieldId: field.id,
						message: `${field.label} is required`,
					});
				}

				// Type-specific validation
				if (value && field.validation) {
					const validation = field.validation;

					// Number validation
					if (field.type === "number" && typeof value === "number") {
						if (validation.min !== undefined && value < validation.min) {
							errors.push({
								fieldId: field.id,
								message: `${field.label} must be at least ${validation.min}`,
							});
						}
						if (validation.max !== undefined && value > validation.max) {
							errors.push({
								fieldId: field.id,
								message: `${field.label} must be at most ${validation.max}`,
							});
						}
					}

					// String length validation
					if (typeof value === "string") {
						if (validation.minLength && value.length < validation.minLength) {
							errors.push({
								fieldId: field.id,
								message: `${field.label} must be at least ${validation.minLength} characters`,
							});
						}
						if (validation.maxLength && value.length > validation.maxLength) {
							errors.push({
								fieldId: field.id,
								message: `${field.label} must be at most ${validation.maxLength} characters`,
							});
						}
					}

					// Email validation
					if (field.type === "email" && typeof value === "string") {
						const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
						if (!emailRegex.test(value)) {
							errors.push({
								fieldId: field.id,
								message: "Please enter a valid email address",
							});
						}
					}

					// Pattern validation
					if (validation.pattern && typeof value === "string") {
						const regex = new RegExp(validation.pattern);
						if (!regex.test(value)) {
							errors.push({
								fieldId: field.id,
								message:
									validation.message || `${field.label} format is invalid`,
							});
						}
					}
				}
			});
		});

		return errors;
	};

	// Evaluate conditional logic
	const evaluateCondition = (
		actualValue: any,
		expectedValue: any,
		operator: string,
	): boolean => {
		switch (operator) {
			case "equals":
				return actualValue === expectedValue;
			case "not_equals":
				return actualValue !== expectedValue;
			case "contains":
				return Array.isArray(actualValue)
					? actualValue.includes(expectedValue)
					: typeof actualValue === "string"
						? actualValue.includes(expectedValue)
						: false;
			case "greater_than":
				return Number(actualValue) > Number(expectedValue);
			case "less_than":
				return Number(actualValue) < Number(expectedValue);
			default:
				return true;
		}
	};

	// Update form state when data changes
	useEffect(() => {
		const errors = validateForm();
		setFormState((prev) => ({
			...prev,
			errors,
			isValid: errors.length === 0,
		}));
	}, [formState.data]);

	// Handle field value changes
	const handleFieldChange = (fieldId: string, value: any) => {
		setFormState((prev) => ({
			...prev,
			data: {
				...prev.data,
				[fieldId]: value,
			},
			isDirty: true,
		}));
	};

	// Handle section navigation
	const goToSection = (sectionIndex: number) => {
		if (sectionIndex >= 0 && sectionIndex < template.sections.length) {
			setFormState((prev) => ({
				...prev,
				currentSection: sectionIndex,
			}));
		}
	};

	// Handle save as draft
	const handleSaveDraft = async () => {
		if (onSave) {
			setFormState((prev) => ({ ...prev, isSubmitting: true }));
			try {
				await onSave(formState.data, true);
				setFormState((prev) => ({ ...prev, isDirty: false }));
			} catch (error) {
				console.error("Error saving draft:", error);
			} finally {
				setFormState((prev) => ({ ...prev, isSubmitting: false }));
			}
		}
	};

	// Handle form submission
	const handleSubmit = async () => {
		const errors = validateForm();
		if (errors.length > 0) {
			setFormState((prev) => ({ ...prev, errors }));
			return;
		}

		if (onSubmit) {
			setFormState((prev) => ({ ...prev, isSubmitting: true }));
			try {
				await onSubmit(formState.data);
			} catch (error) {
				console.error("Error submitting form:", error);
			} finally {
				setFormState((prev) => ({ ...prev, isSubmitting: false }));
			}
		}
	};

	const currentSection = template.sections[formState.currentSection];
	const isLastSection =
		formState.currentSection === template.sections.length - 1;
	const isFirstSection = formState.currentSection === 0;

	// Calculate completion time
	const getCompletionTime = () => {
		const minutes = Math.floor((Date.now() - startTime) / 60000);
		return minutes;
	};

	// Filter visible fields based on conditional logic
	const getVisibleFields = (section: typeof currentSection) => {
		return section.fields.filter((field) => {
			if (!field.conditional) return true;

			const dependentValue = formState.data[field.conditional.dependsOn];
			return evaluateCondition(
				dependentValue,
				field.conditional.value,
				field.conditional.operator,
			);
		});
	};

	return (
		<div className="max-w-4xl mx-auto">
			{/* Form Header */}
			<div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
				<div className="flex items-center justify-between mb-4">
					<div>
						<h1 className="text-2xl font-bold text-gray-900">
							{template.name}
						</h1>
						<p className="text-gray-600">{template.description}</p>
					</div>
					<div className="flex items-center space-x-4 text-sm text-gray-500">
						{template.estimatedDuration && (
							<div className="flex items-center space-x-1">
								<Clock className="h-4 w-4" />
								<span>~{template.estimatedDuration} min</span>
							</div>
						)}
						<div className="flex items-center space-x-1">
							<span>Time: {getCompletionTime()} min</span>
						</div>
					</div>
				</div>

				{/* Progress Bar */}
				{showProgress && template.sections.length > 1 && (
					<div className="mb-4">
						<div className="flex items-center justify-between text-sm text-gray-600 mb-2">
							<span>Progress</span>
							<span>
								{formState.currentSection + 1} of {template.sections.length}
							</span>
						</div>
						<div className="w-full bg-gray-200 rounded-full h-2">
							<div
								className="bg-green-600 h-2 rounded-full transition-all duration-300"
								style={{
									width: `${((formState.currentSection + 1) / template.sections.length) * 100}%`,
								}}
							/>
						</div>
					</div>
				)}

				{/* Instructions */}
				{template.instructions && (
					<div className="bg-blue-50 border border-blue-200 rounded-md p-4">
						<div className="flex items-start space-x-2">
							<AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
							<p className="text-blue-800 text-sm">{template.instructions}</p>
						</div>
					</div>
				)}
			</div>

			{/* Current Section */}
			<div className="bg-white rounded-lg border border-gray-200 p-6 mb-6">
				<div className="mb-6">
					<h2 className="text-xl font-semibold text-gray-900 mb-2">
						{currentSection.title}
					</h2>
					{currentSection.description && (
						<p className="text-gray-600">{currentSection.description}</p>
					)}
				</div>

				{/* Form Fields */}
				<div className="space-y-6">
					{getVisibleFields(currentSection)
						.sort((a, b) => a.order - b.order)
						.map((field) => (
							<FormField
								key={field.id}
								field={field}
								value={formState.data[field.id]}
								onChange={handleFieldChange}
								error={formState.errors.find((e) => e.fieldId === field.id)}
								disabled={readonly || formState.isSubmitting}
							/>
						))}
				</div>
			</div>

			{/* Navigation and Actions */}
			<div className="bg-white rounded-lg border border-gray-200 p-6">
				<div className="flex items-center justify-between">
					{/* Section Navigation */}
					<div className="flex items-center space-x-4">
						<button
							onClick={() => goToSection(formState.currentSection - 1)}
							disabled={isFirstSection || readonly}
							className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
						>
							<ChevronLeft className="h-4 w-4" />
							<span>Previous</span>
						</button>

						{!isLastSection && (
							<button
								onClick={() => goToSection(formState.currentSection + 1)}
								disabled={readonly}
								className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
							>
								<span>Next</span>
								<ChevronRight className="h-4 w-4" />
							</button>
						)}
					</div>

					{/* Action Buttons */}
					{!readonly && (
						<div className="flex items-center space-x-4">
							{/* Save Draft */}
							{onSave && (
								<button
									onClick={handleSaveDraft}
									disabled={formState.isSubmitting || !formState.isDirty}
									className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
								>
									<Save className="h-4 w-4" />
									<span>Save Draft</span>
								</button>
							)}

							{/* Submit */}
							{onSubmit && isLastSection && (
								<button
									onClick={handleSubmit}
									disabled={formState.isSubmitting || !formState.isValid}
									className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
								>
									{formState.isSubmitting ? (
										<>
											<div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
											<span>Submitting...</span>
										</>
									) : (
										<>
											<Send className="h-4 w-4" />
											<span>Submit Form</span>
										</>
									)}
								</button>
							)}
						</div>
					)}
				</div>

				{/* Validation Errors Summary */}
				{formState.errors.length > 0 && (
					<div className="mt-4 bg-red-50 border border-red-200 rounded-md p-4">
						<div className="flex items-start space-x-2">
							<AlertCircle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
							<div>
								<h3 className="text-sm font-medium text-red-800">
									Please fix the following errors:
								</h3>
								<ul className="mt-2 text-sm text-red-700 list-disc list-inside">
									{formState.errors.map((error, index) => (
										<li key={index}>{error.message}</li>
									))}
								</ul>
							</div>
						</div>
					</div>
				)}
			</div>
		</div>
	);
};

export default DynamicForm;
