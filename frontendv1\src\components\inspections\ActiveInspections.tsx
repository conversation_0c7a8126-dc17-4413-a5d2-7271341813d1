import React, { } from "react";
import {
	<PERSON>,
	Pause,
	<PERSON><PERSON><PERSON><PERSON>,
	Clock,
	User,
	<PERSON><PERSON><PERSON>riangle,
} from "lucide-react";

interface ActiveInspectionsProps {
	siteId: string;
}

interface ActiveInspection {
	id: string;
	templateName: string;
	target: string;
	inspector: string;
	startedAt: string;
	progress: number;
	totalItems: number;
	completedItems: number;
	criticalIssues: number;
	estimatedCompletion: string;
}

const ActiveInspections: React.FC<ActiveInspectionsProps> = ({ siteId: _siteId }) => {
	// Mock data - replace with actual API call
	const activeInspections: ActiveInspection[] = [
		{
			id: "1",
			templateName: "Daily Site Safety Inspection",
			target: "Main Construction Area",
			inspector: "<PERSON>",
			startedAt: "2024-01-16 09:15",
			progress: 65,
			totalItems: 20,
			completedItems: 13,
			criticalIssues: 1,
			estimatedCompletion: "10:30",
		},
		{
			id: "2",
			templateName: "Scaffold Safety Check",
			target: "Building A - Level 3",
			inspector: "<PERSON>",
			startedAt: "2024-01-16 11:00",
			progress: 30,
			totalItems: 15,
			completedItems: 5,
			criticalIssues: 0,
			estimatedCompletion: "12:15",
		},
		{
			id: "3",
			templateName: "Equipment Pre-Use Check",
			target: "Crane CR-002",
			inspector: "Mary Wanjiku",
			startedAt: "2024-01-16 08:45",
			progress: 85,
			totalItems: 12,
			completedItems: 10,
			criticalIssues: 0,
			estimatedCompletion: "09:45",
		},
	];

	const getProgressColor = (progress: number) => {
		if (progress >= 80) return "bg-green-500";
		if (progress >= 50) return "bg-yellow-500";
		return "bg-blue-500";
	};

	const formatTime = (timeString: string) => {
		const time = new Date(`2024-01-01 ${timeString}`);
		return time.toLocaleTimeString("en-US", {
			hour: "2-digit",
			minute: "2-digit",
			hour12: false,
		});
	};

	// const formatStartTime = (dateTimeString: string) => {
	// 	const dateTime = new Date(dateTimeString);
	// 	return dateTime.toLocaleTimeString("en-US", {
	// 		hour: "2-digit",
	// 		minute: "2-digit",
	// 		hour12: false,
	// 	});
	// };

	const calculateDuration = (startedAt: string) => {
		const start = new Date(startedAt);
		const now = new Date();
		const diffMs = now.getTime() - start.getTime();
		const diffMins = Math.floor(diffMs / (1000 * 60));

		if (diffMins < 60) {
			return `${diffMins}m`;
		} else {
			const hours = Math.floor(diffMins / 60);
			const mins = diffMins % 60;
			return `${hours}h ${mins}m`;
		}
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex items-center justify-between">
				<div>
					<h2 className="text-xl font-semibold text-gray-900">
						Active Inspections
					</h2>
					<p className="text-sm text-gray-600 mt-1">
						Inspections currently in progress ({activeInspections.length}{" "}
						active)
					</p>
				</div>
			</div>

			{/* Active Inspections Grid */}
			{activeInspections.length > 0 ? (
				<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
					{activeInspections.map((inspection) => (
						<div
							key={inspection.id}
							className="bg-white rounded-lg border border-gray-200 shadow-sm"
						>
							{/* Header */}
							<div className="p-6 border-b border-gray-200">
								<div className="flex items-start justify-between">
									<div className="flex-1 min-w-0">
										<h3 className="text-lg font-semibold text-gray-900 truncate">
											{inspection.templateName}
										</h3>
										<p className="text-sm text-gray-600 mt-1">
											{inspection.target}
										</p>
									</div>
									<div className="flex items-center space-x-2 ml-4">
										{inspection.criticalIssues > 0 && (
											<div className="flex items-center text-red-600">
												<AlertTriangle className="h-4 w-4 mr-1" />
												<span className="text-xs font-medium">
													{inspection.criticalIssues}
												</span>
											</div>
										)}
										<div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
									</div>
								</div>
							</div>

							{/* Progress Section */}
							<div className="p-6">
								<div className="space-y-4">
									{/* Progress Bar */}
									<div>
										<div className="flex items-center justify-between mb-2">
											<span className="text-sm font-medium text-gray-700">
												Progress
											</span>
											<span className="text-sm text-gray-600">
												{inspection.completedItems}/{inspection.totalItems}{" "}
												items
											</span>
										</div>
										<div className="w-full bg-gray-200 rounded-full h-2">
											<div
												className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(inspection.progress)}`}
												style={{ width: `${inspection.progress}%` }}
											></div>
										</div>
										<div className="flex justify-between text-xs text-gray-500 mt-1">
											<span>{inspection.progress}% complete</span>
											<span>
												Est. completion:{" "}
												{formatTime(inspection.estimatedCompletion)}
											</span>
										</div>
									</div>

									{/* Inspector and Time Info */}
									<div className="grid grid-cols-2 gap-4 text-sm">
										<div className="flex items-center">
											<User className="h-4 w-4 text-gray-400 mr-2" />
											<div>
												<div className="text-gray-900">
													{inspection.inspector}
												</div>
												<div className="text-gray-500 text-xs">Inspector</div>
											</div>
										</div>
										<div className="flex items-center">
											<Clock className="h-4 w-4 text-gray-400 mr-2" />
											<div>
												<div className="text-gray-900">
													{calculateDuration(inspection.startedAt)}
												</div>
												<div className="text-gray-500 text-xs">Duration</div>
											</div>
										</div>
									</div>

									{/* Critical Issues Alert */}
									{inspection.criticalIssues > 0 && (
										<div className="bg-red-50 border border-red-200 rounded-md p-3">
											<div className="flex items-center">
												<AlertTriangle className="h-4 w-4 text-red-500 mr-2" />
												<span className="text-sm text-red-700">
													{inspection.criticalIssues} critical issue
													{inspection.criticalIssues > 1 ? "s" : ""} found
												</span>
											</div>
										</div>
									)}

									{/* Action Buttons */}
									<div className="flex space-x-3 pt-2">
										<button className="flex-1 inline-flex items-center justify-center px-3 py-2 bg-green-600 text-white text-sm font-medium rounded-md hover:bg-green-700 transition-colors">
											<Play className="h-4 w-4 mr-2" />
											Continue
										</button>
										<button className="inline-flex items-center justify-center px-3 py-2 border border-gray-300 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-50 transition-colors">
											<Pause className="h-4 w-4 mr-2" />
											Pause
										</button>
									</div>
								</div>
							</div>
						</div>
					))}
				</div>
			) : (
				<div className="text-center py-12">
					<CheckCircle className="mx-auto h-12 w-12 text-gray-400" />
					<h3 className="mt-2 text-sm font-medium text-gray-900">
						No active inspections
					</h3>
					<p className="mt-1 text-sm text-gray-500">
						All scheduled inspections have been completed or are pending.
					</p>
					<div className="mt-6">
						<button className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
							<Play className="h-4 w-4 mr-2" />
							Start New Inspection
						</button>
					</div>
				</div>
			)}

			{/* Summary Stats */}
			{activeInspections.length > 0 && (
				<div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
					<h3 className="text-lg font-semibold text-gray-900 mb-4">
						Active Inspections Summary
					</h3>
					<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
						<div className="text-center">
							<div className="text-2xl font-bold text-blue-600">
								{activeInspections.length}
							</div>
							<div className="text-sm text-gray-600">In Progress</div>
						</div>
						<div className="text-center">
							<div className="text-2xl font-bold text-green-600">
								{Math.round(
									activeInspections.reduce(
										(acc, curr) => acc + curr.progress,
										0,
									) / activeInspections.length,
								)}
								%
							</div>
							<div className="text-sm text-gray-600">Avg Progress</div>
						</div>
						<div className="text-center">
							<div className="text-2xl font-bold text-yellow-600">
								{activeInspections.reduce(
									(acc, curr) => acc + curr.completedItems,
									0,
								)}
							</div>
							<div className="text-sm text-gray-600">Items Completed</div>
						</div>
						<div className="text-center">
							<div className="text-2xl font-bold text-red-600">
								{activeInspections.reduce(
									(acc, curr) => acc + curr.criticalIssues,
									0,
								)}
							</div>
							<div className="text-sm text-gray-600">Critical Issues</div>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default ActiveInspections;
