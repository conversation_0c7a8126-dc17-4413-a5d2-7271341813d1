import React, { useState, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { FileText, ArrowRight } from "lucide-react";
import { inspectionFormTypes } from "../../data/inspectionFormTemplate";
import UniversalFilter, { TagOption } from "../shared/UniversalFilter";

interface InspectionFormsProps {
	siteId: string;
}

interface InspectionForm {
	id: string;
	name: string;
	description: string;
	category: string;
	itemCount: number;
}

const InspectionForms: React.FC<InspectionFormsProps> = ({ siteId }) => {
	const navigate = useNavigate();
	const [searchQuery, setSearchQuery] = useState("");
	const [selectedCategory, setSelectedCategory] = useState("all");

	// Convert inspection form types to display format with categorization
	const inspectionForms: InspectionForm[] = inspectionFormTypes.map((form) => {
		// Categorize forms based on their names with simplified category names
		let category = "Equipment";
		const name = form.name.toLowerCase();

		if (name.includes("fire") || name.includes("safety")) {
			category = "Safety";
		} else if (name.includes("concrete") || name.includes("mixer") || name.includes("pump")) {
			category = "Concrete";
		} else if (name.includes("excavator") || name.includes("crane") || name.includes("grader") || name.includes("loader")) {
			category = "Machinery";
		} else if (name.includes("cutting") || name.includes("welding") || name.includes("drilling") || name.includes("grinding")) {
			category = "Tools";
		} else if (name.includes("truck") || name.includes("ready mix")) {
			category = "Vehicles";
		}

		return {
			id: form.id,
			name: form.name,
			description: `Inspection checklist for ${form.name.toLowerCase()} with ${form.information.length} inspection points`,
			category,
			itemCount: form.information.length,
		};
	});

	// Category filter options
	const categoryFilters: TagOption[] = [
		{ id: "tools", name: "Tools" },
		{ id: "machinery", name: "Machinery" },
		{ id: "concrete", name: "Concrete" },
		{ id: "safety", name: "Safety" },
		{ id: "vehicles", name: "Vehicles" },
		{ id: "equipment", name: "Equipment" },
	];

	// Filter and search logic
	const filteredForms = useMemo(() => {
		return inspectionForms.filter(form => {
			// Search filter
			const matchesSearch = searchQuery === "" ||
				form.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
				form.description.toLowerCase().includes(searchQuery.toLowerCase());

			// Category filter
			const matchesCategory = selectedCategory === "all" ||
				form.category.toLowerCase() === selectedCategory.toLowerCase();

			return matchesSearch && matchesCategory;
		});
	}, [searchQuery, selectedCategory, inspectionForms]);

	const handleViewForm = (formId: string) => {
		// Navigate to form view page
		navigate(`/sites/${siteId}/inspections/form/${formId}`);
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex items-center justify-between">
				<div>
					<h2 className="text-xl font-semibold text-gray-900">Inspection Forms</h2>
					<p className="text-sm text-gray-600">Inspection checklists and forms</p>
				</div>
				<div className="text-sm text-gray-600">
					{filteredForms.length} forms
				</div>
			</div>

			{/* Search and Filter */}
			<UniversalFilter
				variant="search-tags"
				searchQuery={searchQuery}
				onSearchChange={setSearchQuery}
				searchPlaceholder="Search by form name or description..."
				tags={[{ id: "all", name: "All" }, ...categoryFilters]}
				selectedTagId={selectedCategory}
				onTagChange={setSelectedCategory}
			/>

			{/* Forms Grid */}
			<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
				{filteredForms.map((form) => (
					<div
						key={form.id}
						className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200"
					>
						{/* Card Header */}
						<div className="p-4 border-b border-gray-100">
							<div className="flex items-center justify-end">
								{/* Category badge - matching SiteCard status badge design */}
								<div
									className="px-2 py-1 text-xs font-medium border border-gray-400 text-gray-600 bg-transparent"
									style={{ borderRadius: '5px' }}
								>
									{form.category.toUpperCase()}
								</div>
							</div>
						</div>

						{/* Card Body */}
						<div className="p-4">
							{/* Form title */}
							<h3 className="font-semibold text-gray-900 text-lg mb-2 whitespace-normal break-words">
								{form.name}
							</h3>

							{/* Description */}
							<p className="text-sm text-gray-600 mb-4 leading-relaxed">
								{form.description}
							</p>

							{/* Metadata */}
							<div className="text-sm text-gray-700 font-medium">
								{form.itemCount} inspection points
							</div>
						</div>

						{/* Card Footer - matching SiteCard footer design */}
						<div
							className="px-4 py-3 border-t border-gray-100 bg-gray-50 hover:bg-gray-100 transition-colors cursor-pointer"
							onClick={() => handleViewForm(form.id)}
						>
							<div className="flex items-center justify-between">
								<span className="text-sm font-medium text-gray-700">VIEW</span>
								<div
									className="bg-black text-white p-2 rounded hover:bg-gray-800 transition-colors"
									style={{ borderRadius: '3px' }}
								>
									<ArrowRight className="h-4 w-4" />
								</div>
							</div>
						</div>
					</div>
				))}
			</div>

			{/* Empty state */}
			{filteredForms.length === 0 && (
				<div className="text-center py-12">
					<div className="text-gray-400 mb-4">
						<FileText className="h-12 w-12 mx-auto" />
					</div>
					<h3 className="text-lg font-medium text-gray-900 mb-2">No inspection forms found</h3>
					<p className="text-gray-600">
						{searchQuery || selectedCategory !== "all"
							? "Try adjusting your search or filter criteria."
							: "No inspection forms are available."}
					</p>
				</div>
			)}
		</div>
	);
};

export default InspectionForms;
