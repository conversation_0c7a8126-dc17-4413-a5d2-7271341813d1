import React from "react";
import { BarChart3, Download, Calendar, TrendingUp, Alert<PERSON>riangle, CheckCircle } from "lucide-react";

interface InspectionReportsProps {
	siteId: string;
}

const InspectionReports: React.FC<InspectionReportsProps> = ({ siteId: _siteId }) => {
	// Mock data for reports
	const reportMetrics = {
		totalInspections: 156,
		passRate: 87,
		criticalIssues: 12,
		averageCompletionTime: "32 minutes",
	};

	const recentReports = [
		{
			id: "1",
			title: "Weekly Safety Inspection Summary",
			period: "Jan 8-14, 2024",
			type: "Safety",
			generatedAt: "2024-01-15 09:00",
		},
		{
			id: "2",
			title: "Equipment Inspection Report",
			period: "January 2024",
			type: "Equipment",
			generatedAt: "2024-01-14 16:30",
		},
		{
			id: "3",
			title: "Compliance Audit Report",
			period: "Q4 2023",
			type: "Compliance",
			generatedAt: "2024-01-10 14:15",
		},
	];

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<div>
					<h2 className="text-xl font-semibold text-gray-900">Inspection Reports</h2>
					<p className="text-sm text-gray-600">Analytics and reporting for inspection activities</p>
				</div>
				<button className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
					<Download className="h-4 w-4 mr-2" />
					Generate Report
				</button>
			</div>

			{/* Metrics Cards */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
				<div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
					<div className="flex items-center">
						<BarChart3 className="h-8 w-8 text-blue-500" />
						<div className="ml-4">
							<p className="text-sm font-medium text-gray-600">Total Inspections</p>
							<p className="text-2xl font-bold text-gray-900">{reportMetrics.totalInspections}</p>
						</div>
					</div>
				</div>
				
				<div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
					<div className="flex items-center">
						<CheckCircle className="h-8 w-8 text-green-500" />
						<div className="ml-4">
							<p className="text-sm font-medium text-gray-600">Pass Rate</p>
							<p className="text-2xl font-bold text-gray-900">{reportMetrics.passRate}%</p>
						</div>
					</div>
				</div>
				
				<div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
					<div className="flex items-center">
						<AlertTriangle className="h-8 w-8 text-red-500" />
						<div className="ml-4">
							<p className="text-sm font-medium text-gray-600">Critical Issues</p>
							<p className="text-2xl font-bold text-gray-900">{reportMetrics.criticalIssues}</p>
						</div>
					</div>
				</div>
				
				<div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
					<div className="flex items-center">
						<TrendingUp className="h-8 w-8 text-purple-500" />
						<div className="ml-4">
							<p className="text-sm font-medium text-gray-600">Avg. Time</p>
							<p className="text-2xl font-bold text-gray-900">{reportMetrics.averageCompletionTime}</p>
						</div>
					</div>
				</div>
			</div>

			{/* Recent Reports */}
			<div className="bg-white rounded-lg border border-gray-200 shadow-sm">
				<div className="px-6 py-4 border-b border-gray-200">
					<h3 className="text-lg font-semibold text-gray-900">Recent Reports</h3>
				</div>
				<div className="divide-y divide-gray-200">
					{recentReports.map((report) => (
						<div key={report.id} className="px-6 py-4 hover:bg-gray-50">
							<div className="flex items-center justify-between">
								<div className="flex items-center space-x-4">
									<Calendar className="h-5 w-5 text-gray-400" />
									<div>
										<h4 className="text-sm font-medium text-gray-900">{report.title}</h4>
										<p className="text-sm text-gray-600">{report.period}</p>
									</div>
								</div>
								<div className="flex items-center space-x-4">
									<span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
										{report.type}
									</span>
									<span className="text-sm text-gray-500">{report.generatedAt}</span>
									<button className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
										<Download className="h-4 w-4 mr-1" />
										Download
									</button>
								</div>
							</div>
						</div>
					))}
				</div>
			</div>
		</div>
	);
};

export default InspectionReports;
