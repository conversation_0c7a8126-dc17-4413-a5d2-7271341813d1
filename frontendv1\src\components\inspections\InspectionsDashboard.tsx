import React from "react";
import {
	<PERSON>lip<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	Clock,
	FileText,
	BarChart3,
} from "lucide-react";
import { useNavigate } from "react-router-dom";
import QuickActions, { QuickActionItem } from "../shared/QuickActions";
import KPICard from "../permits/shared/KPICard";

interface InspectionsDashboardProps {
	siteId: string;
	onNavigateToTab: (tabId: string) => void;
}

const InspectionsDashboard: React.FC<InspectionsDashboardProps> = ({
	siteId,
	onNavigateToTab,
}) => {
	const navigate = useNavigate();

	// Mock data - replace with actual API calls
	const metrics = {
		totalScheduled: 24,
		overdue: 3,
		completed: 18,
		inProgress: 2,
		complianceRate: 87,
	};

	// Mock recent inspections data
	const mockRecentInspections = [
		{
			id: "1",
			templateName: "Daily Site Safety Inspection",
			target: "Main Construction Area",
			inspector: "<PERSON>",
			completedAt: "2024-01-15 14:30",
			status: "passed" as const,
		},
		{
			id: "2",
			templateName: "Equipment Safety Check",
			target: "Excavator CAT-001",
			inspector: "<PERSON>",
			completedAt: "2024-01-15 11:15",
			status: "failed" as const,
			criticalIssues: 2,
		},
		{
			id: "3",
			templateName: "Scaffold Safety Inspection",
			target: "Building A - Level 3",
			inspector: "Peter Kiprotich",
			completedAt: "2024-01-15 09:45",
			status: "passed" as const,
		},
		{
			id: "4",
			templateName: "PPE Compliance Check",
			target: "Site Entrance",
			inspector: "Sarah Njeri",
			completedAt: "2024-01-14 16:20",
			status: "passed" as const,
		},
	];

	const handleStartInspection = () => {
		// Navigate to inspection form demo page
		navigate("/sites/:siteId/inspections/demo");
	};

	const handleViewOverdue = () => {
		// Navigate to pending tab with overdue filter
		onNavigateToTab("pending");
	};

	const handleInspectionClick = (inspection: any) => {
		// Navigate to inspection details page
		navigate(`/sites/${siteId}/inspections/view/${inspection.id}`);
	};

	return (
		<div className="space-y-8">
			{/* KPI Cards */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
				<KPICard
					title="Total Inspections"
					value={metrics.totalScheduled}
					change={8}
					icon={<ClipboardCheck className="h-6 w-6 text-blue-500" />}
					onClick={() => onNavigateToTab('history')}
				/>
				<KPICard
					title="Pending Inspections"
					value={metrics.overdue}
					icon={<Clock className="h-6 w-6 text-orange-500" />}
					onClick={handleViewOverdue}
				/>
				<KPICard
					title="Passed Inspections"
					value={metrics.completed}
					icon={<CheckCircle className="h-6 w-6 text-green-500" />}
				/>
				<KPICard
					title="Failed Inspections"
					value={metrics.inProgress}
					icon={<AlertTriangle className="h-6 w-6 text-red-500" />}
				/>
			</div>

			{/* Quick Actions */}
			<div className="mb-2">
				<h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
				<QuickActions
					actions={[
						{
							title: "Pending Inspections",
							description: "View and manage pending inspections",
							icon: <Clock className="h-6 w-6 text-indigo-600" />,
							onClick: () => onNavigateToTab('pending'),
						},
						{
							title: "Inspection History",
							description: "Browse completed inspections",
							icon: <FileText className="h-6 w-6 text-orange-600" />,
							onClick: () => onNavigateToTab('history'),
						},
						{
							title: "View Templates",
							description: "Manage inspection templates",
							icon: <ClipboardCheck className="h-6 w-6 text-blue-600" />,
							onClick: () => onNavigateToTab('forms'),
						},
						{
							title: "Inspection Reports",
							description: "Generate and view reports",
							icon: <BarChart3 className="h-6 w-6 text-green-600" />,
							onClick: () => onNavigateToTab('reports'),
						},
					] as QuickActionItem[]}
					className=""
				/>
			</div>

			{/* Recent Inspections Table */}
			<div className="bg-white shadow overflow-hidden border border-gray-200 rounded-lg">
				<div className="px-6 py-4 border-b border-gray-200">
					<h3 className="text-lg font-semibold text-gray-900">Recent Inspections</h3>
				</div>
				<div className="overflow-x-auto">
					<table className="min-w-full divide-y divide-gray-200">
						<thead className="bg-gray-50">
							<tr>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Inspection Details
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Inspector
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Status
								</th>
								<th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
									Actions
								</th>
							</tr>
						</thead>
						<tbody className="bg-white divide-y divide-gray-200">
							{mockRecentInspections.map((inspection) => (
								<tr key={inspection.id} className="hover:bg-gray-50">
									<td className="px-6 py-4">
										<div>
											<div className="text-sm font-medium text-gray-900">
												{inspection.templateName}
											</div>
											<div className="text-sm text-gray-500">
												{inspection.completedAt}
											</div>
										</div>
									</td>
									<td className="px-6 py-4">
										<div>
											<div className="text-sm font-medium text-gray-900">
												{inspection.inspector}
											</div>
											<div className="text-sm text-gray-500">Inspector</div>
										</div>
									</td>
									<td className="px-6 py-4">
										<div>
											<span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
												inspection.status === 'passed' ? 'bg-green-100 text-green-800' :
												inspection.status === 'failed' ? 'bg-red-100 text-red-800' :
												'bg-yellow-100 text-yellow-800'
											}`}>
												{inspection.status === 'passed' ? 'Passed' :
												 inspection.status === 'failed' ? 'Failed' : 'In Progress'}
											</span>
											{inspection.status === 'passed' && (
												<div className="text-xs text-gray-500 mt-1">
													All items passed
												</div>
											)}
											{inspection.status === 'failed' && inspection.criticalIssues && (
												<div className="text-xs text-red-600 mt-1">
													{inspection.criticalIssues} critical issues
												</div>
											)}
										</div>
									</td>
									<td className="px-6 py-4 text-right">
										<button
											onClick={() => handleInspectionClick(inspection)}
											className="px-3 py-1 text-xs border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors"
											style={{ borderRadius: '5px' }}
										>
											View
										</button>
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>
			</div>
		</div>
	);
};

export default InspectionsDashboard;
