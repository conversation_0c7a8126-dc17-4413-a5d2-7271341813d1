import React, { useState, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { Calendar, ArrowRight, Plus } from "lucide-react";
import { inspectionFormTypes } from "../../data/inspectionFormTemplate";
import UniversalFilter, { TagOption } from "../shared/UniversalFilter";

interface ScheduledInspectionsProps {
	siteId: string;
}

interface ScheduledInspection {
	id: string;
	formId: string;
	templateName: string;
	target: string;
	assignedTo: string;
	dueDate: string;
	dueTime: string;
	category: string;
	itemCount: number;
	estimatedDuration: string;
}

const ScheduledInspections: React.FC<ScheduledInspectionsProps> = ({ siteId }) => {
	const navigate = useNavigate();
	const [searchQuery, setSearchQuery] = useState("");
	const [selectedCategory, setSelectedCategory] = useState("all");
	const [showScheduleModal, setShowScheduleModal] = useState(false);

	// Generate realistic scheduled inspections from inspection form templates
	const scheduledInspections: ScheduledInspection[] = [
		{
			id: "sched-1",
			formId: "13",
			templateName: "EXCAVATOR",
			target: "Excavator CAT-320D",
			assignedTo: "John Mwangi",
			dueDate: "2024-01-16",
			dueTime: "07:00",
			category: "Machinery",
			itemCount: 24,
			estimatedDuration: "45 min",
		},
		{
			id: "sched-2",
			formId: "8",
			templateName: "CONCRETE MIXER",
			target: "Mixer Unit CM-001",
			assignedTo: "Mary Wanjiku",
			dueDate: "2024-01-16",
			dueTime: "06:30",
			category: "Concrete",
			itemCount: 16,
			estimatedDuration: "30 min",
		},
		{
			id: "sched-3",
			formId: "14",
			templateName: "FIRE EXTINGUISHERS",
			target: "Site Office Building",
			assignedTo: "Peter Kiprotich",
			dueDate: "2024-01-16",
			dueTime: "16:00",
			category: "Safety",
			itemCount: 12,
			estimatedDuration: "20 min",
		},
		{
			id: "sched-4",
			formId: "21",
			templateName: "WELDING MACHINE",
			target: "Welding Station WS-02",
			assignedTo: "Sarah Njeri",
			dueDate: "2024-01-16",
			dueTime: "08:00",
			category: "Tools",
			itemCount: 16,
			estimatedDuration: "35 min",
		},
		{
			id: "sched-5",
			formId: "20",
			templateName: "TIPPER TRUCK",
			target: "Truck TT-105",
			assignedTo: "David Ochieng",
			dueDate: "2024-01-16",
			dueTime: "09:30",
			category: "Vehicles",
			itemCount: 17,
			estimatedDuration: "25 min",
		},
		{
			id: "sched-6",
			formId: "15",
			templateName: "GRADER",
			target: "Motor Grader MG-140",
			assignedTo: "Grace Wanjiru",
			dueDate: "2024-01-16",
			dueTime: "10:00",
			category: "Machinery",
			itemCount: 30,
			estimatedDuration: "50 min",
		},
		{
			id: "sched-7",
			formId: "9",
			templateName: "CONCRETE PUMP",
			target: "Pump Unit CP-003",
			assignedTo: "James Kimani",
			dueDate: "2024-01-16",
			dueTime: "11:30",
			category: "Concrete",
			itemCount: 22,
			estimatedDuration: "40 min",
		},
		{
			id: "sched-8",
			formId: "2",
			templateName: "ANGLE GRINDING MACHINE",
			target: "Grinder AG-007",
			assignedTo: "Lucy Muthoni",
			dueDate: "2024-01-17",
			dueTime: "07:30",
			category: "Tools",
			itemCount: 14,
			estimatedDuration: "25 min",
		},
	];

	// Category filter options (matching InspectionForms)
	const categoryFilters: TagOption[] = [
		{ id: "tools", name: "Tools" },
		{ id: "machinery", name: "Machinery" },
		{ id: "concrete", name: "Concrete" },
		{ id: "safety", name: "Safety" },
		{ id: "vehicles", name: "Vehicles" },
		{ id: "equipment", name: "Equipment" },
	];

	// Filter and search logic
	const filteredInspections = useMemo(() => {
		return scheduledInspections.filter(inspection => {
			// Search filter
			const matchesSearch = searchQuery === "" ||
				inspection.templateName.toLowerCase().includes(searchQuery.toLowerCase()) ||
				inspection.target.toLowerCase().includes(searchQuery.toLowerCase()) ||
				inspection.assignedTo.toLowerCase().includes(searchQuery.toLowerCase());

			// Category filter
			const matchesCategory = selectedCategory === "all" ||
				inspection.category.toLowerCase() === selectedCategory.toLowerCase();

			return matchesSearch && matchesCategory;
		});
	}, [searchQuery, selectedCategory, scheduledInspections]);

	const handleViewInspection = (inspection: ScheduledInspection) => {
		// Navigate to inspection execution page
		navigate(`/sites/${siteId}/inspections/form/${inspection.formId}`);
	};

	const formatDate = (dateString: string) => {
		const date = new Date(dateString);
		return date.toLocaleDateString("en-US", {
			weekday: "short",
			month: "short",
			day: "numeric",
		});
	};

	const formatTime = (timeString: string) => {
		return timeString;
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex items-center justify-between">
				<div>
					<h2 className="text-xl font-semibold text-gray-900">Pending Inspections</h2>
					<p className="text-sm text-gray-600">Daily inspections scheduled for completion</p>
				</div>
				<div className="flex items-center gap-4">
					<div className="text-sm text-gray-600">
						{filteredInspections.length} inspections
					</div>
					<button
						onClick={() => setShowScheduleModal(true)}
						className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
					>
						<Plus className="h-4 w-4 mr-2" />
						Schedule
					</button>
				</div>
			</div>

			{/* Search and Filter */}
			<UniversalFilter
				variant="search-tags"
				searchQuery={searchQuery}
				onSearchChange={setSearchQuery}
				searchPlaceholder="Search by inspection name, target, or inspector..."
				tags={[{ id: "all", name: "All" }, ...categoryFilters]}
				selectedTagId={selectedCategory}
				onTagChange={setSelectedCategory}
			/>

			{/* Inspections Grid */}
			<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
				{filteredInspections.map((inspection) => (
					<div
						key={inspection.id}
						className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow duration-200"
					>
						{/* Card Header */}
						<div className="p-4 border-b border-gray-100">
							<div className="flex items-center justify-end">
								{/* Category badge */}
								<div
									className="px-2 py-1 text-xs font-medium border border-gray-400 text-gray-600 bg-transparent"
									style={{ borderRadius: '5px' }}
								>
									{inspection.category.toUpperCase()}
								</div>
							</div>
						</div>

						{/* Card Body */}
						<div className="p-4">
							{/* Inspection title */}
							<h3 className="font-semibold text-gray-900 text-lg mb-2 whitespace-normal break-words">
								{inspection.templateName}
							</h3>

							{/* Target and inspector */}
							<div className="space-y-2 mb-4">
								<div className="text-sm text-gray-600">
									<span className="font-medium">Target:</span> {inspection.target}
								</div>
								<div className="text-sm text-gray-600">
									<span className="font-medium">Inspector:</span> {inspection.assignedTo}
								</div>
							</div>

							{/* Schedule info */}
							<div className="space-y-1 mb-4">
								<div className="text-sm text-gray-700 font-medium">
									{formatDate(inspection.dueDate)} at {formatTime(inspection.dueTime)}
								</div>
								<div className="text-sm text-gray-600">
									{inspection.itemCount} points • {inspection.estimatedDuration}
								</div>
							</div>
						</div>

						{/* Card Footer - changed to START */}
						<div
							className="px-4 py-3 border-t border-gray-100 bg-gray-50 hover:bg-gray-100 transition-colors cursor-pointer"
							onClick={() => handleViewInspection(inspection)}
						>
							<div className="flex items-center justify-between">
								<span className="text-sm font-medium text-gray-700">START</span>
								<div
									className="bg-black text-white p-2 rounded hover:bg-gray-800 transition-colors"
									style={{ borderRadius: '3px' }}
								>
									<ArrowRight className="h-4 w-4" />
								</div>
							</div>
						</div>
					</div>
				))}
			</div>

			{/* Empty state */}
			{filteredInspections.length === 0 && (
				<div className="text-center py-12">
					<div className="text-gray-400 mb-4">
						<Calendar className="h-12 w-12 mx-auto" />
					</div>
					<h3 className="text-lg font-medium text-gray-900 mb-2">No pending inspections found</h3>
					<p className="text-gray-600">
						{searchQuery || selectedCategory !== "all"
							? "Try adjusting your search or filter criteria."
							: "No inspections are currently scheduled."}
					</p>
				</div>
			)}

			{/* Schedule Inspection Modal */}
			{showScheduleModal && (
				<ScheduleInspectionModal
					isOpen={showScheduleModal}
					onClose={() => setShowScheduleModal(false)}
					siteId={siteId}
				/>
			)}
		</div>
	);
};

// Schedule Inspection Modal Component
interface ScheduleInspectionModalProps {
	isOpen: boolean;
	onClose: () => void;
	siteId: string;
}

const ScheduleInspectionModal: React.FC<ScheduleInspectionModalProps> = ({
	isOpen,
	onClose,
	siteId: _siteId,
}) => {
	const [selectedFormId, setSelectedFormId] = useState("");
	const [inspector, setInspector] = useState("");
	const [target, setTarget] = useState("");
	const [scheduledDate, setScheduledDate] = useState("");
	const [scheduledTime, setScheduledTime] = useState("");

	// Mock inspectors - in real app, fetch from API
	const inspectors = [
		"John Mwangi",
		"Mary Wanjiku",
		"Peter Kiprotich",
		"Sarah Njeri",
		"David Ochieng",
		"Grace Wanjiru",
		"James Kimani",
		"Lucy Muthoni",
	];

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();

		if (!selectedFormId || !inspector || !target || !scheduledDate || !scheduledTime) {
			alert("Please fill in all fields");
			return;
		}

		// In real app, submit to API
		console.log("Scheduling inspection:", {
			formId: selectedFormId,
			inspector,
			target,
			scheduledDate,
			scheduledTime,
		});

		// Reset form and close modal
		setSelectedFormId("");
		setInspector("");
		setTarget("");
		setScheduledDate("");
		setScheduledTime("");
		onClose();
	};

	if (!isOpen) return null;

	return (
		<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
			<div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
				<div className="flex items-center justify-between mb-4">
					<h3 className="text-lg font-semibold text-gray-900">Schedule Inspection</h3>
					<button
						onClick={onClose}
						className="text-gray-400 hover:text-gray-600"
					>
						<span className="sr-only">Close</span>
						<svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
							<path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
						</svg>
					</button>
				</div>

				<form onSubmit={handleSubmit} className="space-y-4">
					{/* Inspection Type Selection */}
					<div>
						<label className="block text-sm font-medium text-gray-700 mb-1">
							Inspection Type
						</label>
						<select
							value={selectedFormId}
							onChange={(e) => setSelectedFormId(e.target.value)}
							className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
							required
						>
							<option value="">Select inspection type...</option>
							{inspectionFormTypes.map((form) => (
								<option key={form.id} value={form.id}>
									{form.name}
								</option>
							))}
						</select>
					</div>

					{/* Inspector Selection */}
					<div>
						<label className="block text-sm font-medium text-gray-700 mb-1">
							Inspector
						</label>
						<select
							value={inspector}
							onChange={(e) => setInspector(e.target.value)}
							className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
							required
						>
							<option value="">Select inspector...</option>
							{inspectors.map((name) => (
								<option key={name} value={name}>
									{name}
								</option>
							))}
						</select>
					</div>

					{/* Target */}
					<div>
						<label className="block text-sm font-medium text-gray-700 mb-1">
							Target (Equipment/Area)
						</label>
						<input
							type="text"
							value={target}
							onChange={(e) => setTarget(e.target.value)}
							placeholder="e.g., Excavator CAT-320D, Site Office, Tools"
							className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
							required
						/>
					</div>

					{/* Date */}
					<div>
						<label className="block text-sm font-medium text-gray-700 mb-1">
							Scheduled Date
						</label>
						<input
							type="date"
							value={scheduledDate}
							onChange={(e) => setScheduledDate(e.target.value)}
							className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
							required
						/>
					</div>

					{/* Time */}
					<div>
						<label className="block text-sm font-medium text-gray-700 mb-1">
							Scheduled Time
						</label>
						<input
							type="time"
							value={scheduledTime}
							onChange={(e) => setScheduledTime(e.target.value)}
							className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
							required
						/>
					</div>

					{/* Actions */}
					<div className="flex space-x-3 pt-4">
						<button
							type="button"
							onClick={onClose}
							className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
						>
							Cancel
						</button>
						<button
							type="submit"
							className="flex-1 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
						>
							Schedule
						</button>
					</div>
				</form>
			</div>
		</div>
	);
};

export default ScheduledInspections;
