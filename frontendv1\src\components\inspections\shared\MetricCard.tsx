import React from "react";

interface MetricCardProps {
	title: string;
	value: string | number;
	icon: React.ReactNode;
	color: "blue" | "red" | "green" | "yellow" | "gray";
	onClick?: () => void;
	change?: number;
}

const MetricCard: React.FC<MetricCardProps> = ({
	title,
	value,
	icon,
	color,
	onClick,
	change,
}) => {
	const colorClasses = {
		blue: "bg-blue-50 text-blue-600 border-blue-200",
		red: "bg-red-50 text-red-600 border-red-200",
		green: "bg-green-50 text-green-600 border-green-200",
		yellow: "bg-yellow-50 text-yellow-600 border-yellow-200",
		gray: "bg-gray-50 text-gray-600 border-gray-200",
	};

	const changeColorClasses =
		change && change > 0
			? "text-green-600"
			: change && change < 0
				? "text-red-600"
				: "text-gray-500";

	return (
		<div
			className={`
        bg-white p-6 rounded-lg border shadow-sm transition-all duration-200
        ${onClick ? "cursor-pointer hover:shadow-md hover:border-gray-300" : ""}
        ${colorClasses[color]}
      `}
			onClick={onClick}
		>
			<div className="flex items-center justify-between">
				<div className="flex-1">
					<p className="text-sm font-medium text-gray-600 mb-1">{title}</p>
					<p className="text-2xl font-bold text-gray-900">{value}</p>
					{change !== undefined && (
						<p className={`text-sm ${changeColorClasses} mt-1`}>
							{change > 0 ? "+" : ""}
							{change} from last week
						</p>
					)}
				</div>
				<div className={`flex-shrink-0 p-3 rounded-lg ${colorClasses[color]}`}>
					{icon}
				</div>
			</div>
		</div>
	);
};

export default MetricCard;
