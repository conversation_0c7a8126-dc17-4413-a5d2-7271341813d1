import { useState, useRef, useEffect } from "react";
import { useLocation } from "react-router-dom";
import { CircleHelp, X } from "lucide-react";

const HelpButton = () => {
	const location = useLocation();
	const [isHelpOpen, setIsHelpOpen] = useState(false);
	const helpPanelRef = useRef<HTMLDivElement>(null);
	const buttonRef = useRef<HTMLButtonElement>(null);

	// Routes where help button should be hidden (form filling pages)
	const HIDE_HELP_ROUTES = [
		'/sites/new',
		'/demo/hot-work-form',
		'/demo/excavation-form',
		'/demo/confined-space-form',
		'/demo/ptw-form',
		'/demo/inspection-form',
		'/demo/work-at-height-form',
		'/toolbox/fill',
		'/toolbox/attendance',
		'/toolbox/summarize',
		'/permits/',
		'/forms/',
		'/inspections/form',
		'/standalone-observation'
	];

	// Check if current route should hide help button
	const shouldHideHelp = HIDE_HELP_ROUTES.some(route =>
		location.pathname === route ||
		location.pathname.startsWith(route) ||
		location.pathname.includes('/form') ||
		location.pathname.includes('/fill') ||
		location.pathname.includes('/create') ||
		location.pathname.includes('/edit')
	);

	// Handle clicks outside the help panel
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				isHelpOpen &&
				helpPanelRef.current &&
				buttonRef.current &&
				!helpPanelRef.current.contains(event.target as Node) &&
				!buttonRef.current.contains(event.target as Node)
			) {
				setIsHelpOpen(false);
			}
		};

		document.addEventListener("mousedown", handleClickOutside);
		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [isHelpOpen]);

	// Don't render if help should be hidden (after all hooks have been called)
	if (shouldHideHelp) {
		return null;
	}

	return (
		<>
			<button
				ref={buttonRef}
				onClick={() => setIsHelpOpen(!isHelpOpen)}
				className="fixed bottom-6 right-6 w-10 h-10 rounded-full bg-[#f3f2ee] text-green-500 flex items-center justify-center shadow-sm hover:bg-gray-200 transition-colors"
				aria-label="Help"
			>
				{isHelpOpen ? (
					<X className="h-5 w-5" />
				) : (
					<CircleHelp className="h-5 w-5" />
				)}
			</button>

			{isHelpOpen && (
				<div
					ref={helpPanelRef}
					className="fixed bottom-20 right-6 w-72 bg-white rounded-lg shadow-lg p-4 border border-gray-200"
				>
					<h3 className="font-medium text-gray-800 mb-2">Need Help?</h3>
					<p className="text-sm text-gray-600 mb-3">
						Get assistance with Workforce features and functionality.
					</p>
					<div className="space-y-2">
						<a
							href="/help/getting-started"
							className="block text-sm text-green-500 hover:text-green-600 hover:underline"
						>
							Getting Started Guide
						</a>
						<a
							href="/help/faq"
							className="block text-sm text-green-500 hover:text-green-600 hover:underline"
						>
							Frequently Asked Questions
						</a>
						<a
							href="/help/contact"
							className="block text-sm text-green-500 hover:text-green-600 hover:underline"
						>
							Contact Support
						</a>
					</div>
				</div>
			)}
		</>
	);
};

export default HelpButton;
