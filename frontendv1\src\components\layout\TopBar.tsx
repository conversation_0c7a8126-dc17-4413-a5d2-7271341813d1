import { Link, useNavigate } from 'react-router-dom';
import { useState, useRef, useEffect } from 'react';
import { ChevronLeft, ChevronUp, ChevronDown, LogOut, Search, Settings, UserRound, Info, X } from 'lucide-react';
import { useAuth } from '../../hooks/useAuthContext';
import { useTopBar } from '../../hooks/useTopBarContext';
import NotificationSystem from '../notifications/NotificationSystem';
import { WeatherIcon } from '../weather/WeatherIcon';

// Unified TopBar Icon Button Component
interface TopBarIconButtonProps {
  icon: React.ReactNode;
  onClick?: () => void;
  href?: string;
  title: string;
  variant?: 'default' | 'user';
  isActive?: boolean;
  className?: string;
}

const TopBarIconButton: React.FC<TopBarIconButtonProps> = ({
  icon,
  onClick,
  href,
  title,
  variant = 'default',
  isActive = false,
  className = ''
}) => {
  const baseClasses = `
    relative inline-flex items-center justify-center
    transition-all duration-200 ease-in-out
    focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2
    ${variant === 'user' ? 'p-1.5' : 'p-2'}
    ${variant === 'user' ? 'rounded-full' : 'rounded-lg'}
    ${isActive
      ? 'bg-green-50 text-green-600 shadow-sm'
      : 'text-gray-600 hover:text-green-600 hover:bg-gray-50'
    }
    ${className}
  `;

  if (href) {
    return (
      <Link to={href} className={baseClasses} title={title}>
        {icon}
      </Link>
    );
  }

  return (
    <button onClick={onClick} className={baseClasses} title={title}>
      {icon}
    </button>
  );
};

interface TopBarProps {
	rightActions?: React.ReactNode;
	onBack?: () => void;
	subtitle?: string;
}

const TopBar = ({ rightActions, onBack, subtitle }: TopBarProps) => {
  const navigate = useNavigate();
  const { user, logout } = useAuth();
  const { config } = useTopBar();

  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const userMenuRef = useRef<HTMLDivElement>(null);
  const userButtonRef = useRef<HTMLButtonElement>(null);
  const searchRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

	// Handle clicks outside the user menu and search
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			// Handle user menu
			if (
				userMenuOpen &&
				userMenuRef.current &&
				userButtonRef.current &&
				!userMenuRef.current.contains(event.target as Node) &&
				!userButtonRef.current.contains(event.target as Node)
			) {
				setUserMenuOpen(false);
			}

			// Handle search collapse
			if (
				isSearchExpanded &&
				searchRef.current &&
				!searchRef.current.contains(event.target as Node)
			) {
				setIsSearchExpanded(false);
			}
		};

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [userMenuOpen, isSearchExpanded]);

  // Handle logout
  const handleLogout = () => {
    logout();
    setUserMenuOpen(false);
    navigate('/login');
  };

  // Get user initials for avatar
  const getUserInitials = () => {
    if (!user) return 'U';
    return `${user.firstName.charAt(0)}${user.lastName.charAt(0)}`.toUpperCase();
  };

  // Handle search expand
  const handleSearchExpand = () => {
    setIsSearchExpanded(true);
    // Focus the input after the animation starts
    setTimeout(() => {
      searchInputRef.current?.focus();
    }, 100);
  };

  // Handle search collapse
  const handleSearchCollapse = () => {
    setIsSearchExpanded(false);
    setSearchTerm('');
  };

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    // TODO: Implement search functionality
  };

  // Handle search submit
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      // TODO: Implement search functionality
      console.log('Searching for:', searchTerm);
    }
  };

	return (
		<div className="border-b border-[#f3f2ee]">
			<div className="flex items-center px-6 py-3">
				<div className="flex-1">
					{/* Dynamic title with optional back button */}
					<div className="flex items-center gap-2">
						{config.showBack && (
							<button
								onClick={onBack || (() => navigate(-1))}
								className="p-2 -ml-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
								aria-label="Go back"
							>
								<ChevronLeft className="h-5 w-5" />
							</button>
						)}
						<div>
							{config.showTitle && (
								<h1 className="text-lg font-semibold text-gray-800">{config.title}</h1>
							)}
							{subtitle && (
								<p className="text-sm text-gray-600">{subtitle}</p>
							)}
						</div>
					</div>
				</div>

				{/* Modern Expandable Search - Only show if enabled */}
				{config.showSearch && (
					<div
						ref={searchRef}
						className={`relative mx-4 transition-all duration-300 ease-in-out ${
							isSearchExpanded ? 'w-80' : 'w-auto'
						}`}
					>
						{!isSearchExpanded ? (
							// Collapsed state - just the search icon
							<button
								onClick={handleSearchExpand}
								className="p-2 rounded-full text-gray-600 hover:bg-gray-100 hover:text-green-500 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
								title="Search"
							>
								<Search className="h-5 w-5" />
							</button>
						) : (
							// Expanded state - full search input
							<form onSubmit={handleSearchSubmit} className="relative">
								<div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
									<Search className="h-4 w-4 text-gray-500" />
								</div>
								<input
									ref={searchInputRef}
									type="text"
									value={searchTerm}
									onChange={(e) => handleSearchChange(e.target.value)}
									className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-full leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 sm:text-sm shadow-sm transition-all duration-200"
									placeholder="Search across all data..."
								/>
								{searchTerm && (
									<button
										type="button"
										onClick={handleSearchCollapse}
										className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors duration-200"
									>
										<X className="h-4 w-4" />
									</button>
								)}
							</form>
						)}
					</div>
				)}

				{/* Right Side Actions - Dynamic based on configuration */}
				<div className="flex items-center space-x-2">
					{/* Page-provided right actions (e.g., Save & Close) */}
					{rightActions}
					{/* Divider - only show if we have right actions and other elements */}
					{rightActions && (!config.minimal || config.showSiteInfo || config.showWeather || config.showNotifications) ?
						<div className="w-px h-6 bg-gray-200 mx-1" /> : null}
					{/* Site Info Icon - hidden in minimal mode */}
					{!config.minimal && config.showSiteInfo && config.siteId && (
						<TopBarIconButton
							icon={<Info className="h-5 w-5" />}
							href={`/sites/${config.siteId}/info`}
							title="Site Information"
						/>
					)}
					{/* Weather Icon - hidden in minimal mode */}
					{!config.minimal && config.showWeather && <WeatherIcon className="" />}
					{/* Notifications - hidden in minimal mode */}
					{!config.minimal && config.showNotifications && <NotificationSystem position="top-right" enableSound={true} />}
				</div>

				{/* User Menu */}
				{config.showUserMenu && (
				<div className="relative ml-2">
					<button
						ref={userButtonRef}
						className="flex items-center p-1.5 rounded-full text-gray-600 hover:bg-gray-50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
						onClick={() => setUserMenuOpen(!userMenuOpen)}
						title={user ? `${user.firstName} ${user.lastName}` : 'User Menu'}
					>
						{user?.avatar ? (
							<img
								src={user.avatar}
								alt={`${user.firstName} ${user.lastName}`}
								className="h-8 w-8 rounded-full object-cover ring-2 ring-white shadow-sm"
							/>
						) : (
							<div className="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center text-white text-sm font-medium shadow-sm ring-2 ring-white">
								{getUserInitials()}
							</div>
						)}
						<div className="ml-1.5 flex flex-col">
							<ChevronUp className={`h-2 w-2 transition-all duration-200 ${userMenuOpen ? 'opacity-100 text-green-600' : 'opacity-40'}`} />
							<ChevronDown className={`h-2 w-2 transition-all duration-200 ${userMenuOpen ? 'opacity-40' : 'opacity-100'}`} />
						</div>
					</button>

					{userMenuOpen && (
						<div
							ref={userMenuRef}
							className="absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg py-1 ring-1 ring-black ring-opacity-5 z-50"
						>
							{/* User Info Header */}
							{user && (
								<div className="px-4 py-3 border-b border-gray-100">
									<div className="flex items-center space-x-3">
										{user.avatar ? (
											<img
												src={user.avatar}
												alt={`${user.firstName} ${user.lastName}`}
												className="w-10 h-10 rounded-full object-cover"
											/>
										) : (
											<div className="w-10 h-10 rounded-full bg-green-500 text-white flex items-center justify-center text-sm font-medium">
												{getUserInitials()}
											</div>
										)}
										<div className="flex-1 min-w-0">
											<p className="text-sm font-medium text-gray-900 truncate">
												{user.firstName} {user.lastName}
											</p>
											<p className="text-xs text-gray-500 truncate">
												{user.email}
											</p>
											<span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 mt-1">
												{user.role.name}
											</span>
										</div>
									</div>
								</div>
							)}

							{/* Menu Items */}
							<div className="py-1">
								<Link
									to="/account"
									onClick={() => setUserMenuOpen(false)}
									className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
								>
									<UserRound className="h-4 w-4 mr-2 text-gray-500" />
									Account Settings
								</Link>
								<Link
									to="/settings"
									onClick={() => setUserMenuOpen(false)}
									className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
								>
									<Settings className="h-4 w-4 mr-2 text-gray-500" />
									System Settings
								</Link>
							</div>

							{/* Logout */}
							<div className="border-t border-gray-100 py-1">
								<button
									onClick={handleLogout}
									className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
								>
									<LogOut className="h-4 w-4 mr-2" />
									Sign out
								</button>
							</div>
						</div>
					)}
				</div>
				)}
			</div>
		</div>
	);
};

export default TopBar;
