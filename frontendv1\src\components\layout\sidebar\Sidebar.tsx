/**
 * Main Sidebar Component
 * Clean, focused implementation using the new architecture
 */

import React, { useEffect } from "react";
import { useLocation } from "react-router-dom";
import {
	SidebarProvider,
	useSidebar,
	useSidebarHover,
	useSidebarKeyboard,
} from "./SidebarProvider";
import { SidebarLogo } from "./SidebarLogo";
import { SidebarMenu } from "./SidebarMenu";

import { SidebarFlyout } from "./SidebarFlyout";
import { useSiteContext } from "../../../hooks/useSiteContext";
import { cssClasses, zIndexes } from "../../../styles/sidebar-tokens";

// ============================================================================
// SIDEBAR CONTENT COMPONENT
// ============================================================================

const SidebarContent: React.FC = () => {
	const location = useLocation();
	const { isSiteLevel } = useSiteContext();
	const { state, dispatch, activeMenuItem } = useSidebar();
	const {
		handleMouseEnter,
		handleMouseLeave,
		handleFlyoutMouseEnter,
		handleFlyoutMouseLeave,
	} = useSidebarHover();
	const { handleKeyDown } = useSidebarKeyboard();

	// Close flyout when route changes
	useEffect(() => {
		dispatch({ type: "COLLAPSE_MENU" });
	}, [location.pathname, dispatch]);

	// Handle click outside to close flyout
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			const sidebar = document.querySelector(
				`.${cssClasses.sidebar.container}`,
			);
			const flyout = document.querySelector(`.${cssClasses.flyout.container}`);
			const target = event.target as Node;

			const isOutsideSidebar = sidebar && !sidebar.contains(target);
			const isOutsideFlyout = flyout && !flyout.contains(target);

			if (isOutsideSidebar && isOutsideFlyout && state.expandedMenu) {
				dispatch({ type: "COLLAPSE_MENU" });
			}
		};

		document.addEventListener("mousedown", handleClickOutside);
		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [state.expandedMenu, dispatch]);

  return (
    <>
      {/* Main Sidebar Container */}
      <nav
        className={`${cssClasses.sidebar.container} fixed inset-y-0 left-0 flex flex-col items-center py-2 bg-[#f3f2ee]`}
        style={{ zIndex: zIndexes.sidebar, width: '72px' }}
        onMouseEnter={() => handleMouseEnter()}
        onMouseLeave={handleMouseLeave}
        onKeyDown={handleKeyDown}
        role="navigation"
        aria-label={isSiteLevel ? "Site Navigation" : "Main Navigation"}
      >
        <div className="flex flex-col items-center flex-1 min-h-0">
          {/* Logo */}
          <SidebarLogo />

					{/* Menu Items */}
					<SidebarMenu />
				</div>
			</nav>

			{/* Flyout Menu */}
			<SidebarFlyout
				menuItem={activeMenuItem}
				visible={state.flyoutVisible}
				onMouseEnter={handleFlyoutMouseEnter}
				onMouseLeave={handleFlyoutMouseLeave}
			/>
		</>
	);
};

// ============================================================================
// MAIN SIDEBAR COMPONENT WITH PROVIDER
// ============================================================================

const Sidebar: React.FC = () => {
	return (
		<SidebarProvider>
			<SidebarContent />
		</SidebarProvider>
	);
};

export default Sidebar;
