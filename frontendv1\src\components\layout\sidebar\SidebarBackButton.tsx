/**
 * Sidebar Back Button Component
 * Navigation button to return to main dashboard from site views
 */

import React from "react";
import { <PERSON> } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import {
	cssClasses,
	a11yConstants,
	getTransitionClasses,
} from "../../../styles/sidebar-tokens";

export const SidebarBackButton: React.FC = () => {
	const transitionClasses = getTransitionClasses("colors");

  return (
    <div className={`${cssClasses.sidebar.backButton} flex flex-col items-center mt-2 flex-shrink-0`}>
      <Link
        to="/"
        aria-label={a11yConstants.labels.backToDashboard}
        className="focus:outline-none focus:ring-2 focus:ring-green-500 rounded-lg"
      >
        <div className="flex flex-col items-center w-16 px-1">
          <div className={`p-1.5 rounded-md text-gray-600 hover:text-green-500 hover:bg-[#fdfdf9] ${transitionClasses}`}>
            <div className="scale-90">
              <ArrowLeft className="h-6 w-6" />
            </div>
          </div>
          <span className="text-xs mt-1 text-center text-gray-600 max-w-[4rem] leading-tight break-words">
            Back
          </span>
        </div>
      </Link>
    </div>
  );
};
