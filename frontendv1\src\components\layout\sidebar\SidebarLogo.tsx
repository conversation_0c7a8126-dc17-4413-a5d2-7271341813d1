/**
 * Sidebar Logo Component
 * Displays the company logo with proper accessibility and navigation
 */

import React from "react";
import { <PERSON> } from "react-router-dom";
import { cssClasses, a11yConstants } from "../../../styles/sidebar-tokens";

export const SidebarLogo: React.FC = () => {
  return (
    <div className={`${cssClasses.sidebar.logo} w-16 h-16 flex items-center justify-center mb-3 px-1 flex-shrink-0`}>
      <Link
        to="/"
        aria-label={a11yConstants.labels.home}
        className="focus:outline-none transition-all duration-200 hover:scale-105"
      >
        <img
          src="/image/logo.png"
          alt="Workforce Logo"
          className="w-full h-full object-contain"
          loading="eager"
        />
      </Link>
    </div>
  );
};
