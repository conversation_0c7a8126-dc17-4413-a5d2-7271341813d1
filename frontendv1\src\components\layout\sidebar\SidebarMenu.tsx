/**
 * Sidebar Menu Component
 * Renders the main navigation menu with optimized performance and accessibility
 */

import React from "react";
import { useSidebar } from "./SidebarProvider";
import { SidebarMenuItem } from "./SidebarMenuItem";
import { useSiteContext } from "../../../hooks/useSiteContext";
import { cssClasses, getSpacing } from "../../../styles/sidebar-tokens";

export const SidebarMenu: React.FC = () => {
	const { menuItems } = useSidebar();
	const { isSiteLevel } = useSiteContext();
	const spacing = getSpacing(isSiteLevel ? "site" : "company");

	return (
		<div
			className={`${cssClasses.sidebar.menu} flex flex-col items-center flex-1 overflow-y-auto overflow-x-visible scrollbar-hide`}
			style={{ gap: spacing.itemSpacing }}
			role="menu"
			aria-orientation="vertical"
		>
			{menuItems.map((item, index) => (
				<SidebarMenuItem
					key={item.name}
					item={item}
					index={index}
					isSiteLevel={isSiteLevel}
				/>
			))}
		</div>
	);
};
