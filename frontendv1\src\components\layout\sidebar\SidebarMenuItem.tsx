/**
 * Sidebar Menu Item Component
 * Individual menu item with hover behavior and accessibility
 */

import React, { useCallback } from "react";
import { Link } from "react-router-dom";
import { MenuItem, SiteMenuItem } from "../../../types/sidebar";
import {
	useSidebar,
	useSidebarHover,
	useSidebarAccessibility,
} from "./SidebarProvider";
import {
	cssClasses,
	getSpacing,
	getColorClasses,
	getTransitionClasses,
} from "../../../styles/sidebar-tokens";
interface SidebarMenuItemProps {
	item: MenuItem | SiteMenuItem;
	index: number;
	isSiteLevel: boolean;
}

export const SidebarMenuItem: React.FC<SidebarMenuItemProps> = ({
	item,
	index,
	isSiteLevel,
}) => {
	const { isMenuItemActive } = useSidebar();
	const { handleMouseEnter } = useSidebarHover();
	const { getMenuItemProps } = useSidebarAccessibility();

	const spacing = getSpacing(isSiteLevel ? "site" : "company");
	const isActive = isMenuItemActive(item);
	const colorClasses = getColorClasses(isActive);
	const transitionClasses = getTransitionClasses("colors");
	const menuItemProps = getMenuItemProps(item, index);

	const handleItemHover = useCallback(() => {
		if (item.submenu) {
			handleMouseEnter(item.name);
		}
	}, [item.submenu, item.name, handleMouseEnter]);

	const handleKeyDown = useCallback(
		(event: React.KeyboardEvent) => {
			if (event.key === "Enter" || event.key === " ") {
				event.preventDefault();
				if (item.submenu) {
					handleMouseEnter(item.name);
				}
			}
		},
		[item.submenu, item.name, handleMouseEnter],
	);

  return (
    <div
      className={`${cssClasses.sidebar.menuItem} relative group flex-shrink-0`}
      onMouseEnter={handleItemHover}
      onKeyDown={handleKeyDown}
      {...menuItemProps}
    >
      <Link
        to={item.path}
        aria-label={item.name}
        className="focus:outline-none rounded-lg"
      >
        <div className={`flex flex-col items-center ${spacing.containerWidth}`}>
          {/* Icon Container */}
          <div
            className={`
              ${spacing.iconPadding}
              rounded-md
              ${colorClasses.text}
              ${colorClasses.background}
              group-hover:text-green-500
              group-hover:bg-[#fdfdf9]
              ${transitionClasses}
            `}
					>
						<div className={spacing.iconSize}>{item.icon}</div>
					</div>

					{/* Label */}
					<span
						className={`
              ${spacing.textSize}
              mt-1
              text-center
              ${spacing.textLineHeight}
              ${colorClasses.text}
              ${colorClasses.fontWeight}
              ${spacing.textMaxWidth}
              break-words
              hyphens-auto
              overflow-hidden
              group-hover:text-green-500
              ${transitionClasses}
            `}
						style={{
							wordBreak: "break-word",
							overflowWrap: "break-word",
							hyphens: "auto",
						}}
					>
						{item.name}
					</span>
				</div>
			</Link>
		</div>
	);
};
