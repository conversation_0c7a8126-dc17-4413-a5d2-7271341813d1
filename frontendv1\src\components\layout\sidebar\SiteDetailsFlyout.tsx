/**
 * Site Details Flyout Component
 * Detailed site information shown when hovering over individual site menu items
 */

import React from "react";
import { Link } from "react-router-dom";
import {
	MapPin,
	Users,
	FileCheck,
	AlertTriangle,
	User,
	ExternalLink,
} from "lucide-react";

interface SiteData {
	id: string;
	name: string;
	healthStatus: "green" | "amber" | "red";
	workersOnSite: number;
	activePermits: number;
	openIncidents: number;
	projectManager: string;
	location: string;
	timeline?: string;
	currentPhase?: string;
	progressPercentage?: number;
}

interface SiteDetailsFlyoutProps {
	site: SiteData;
	className?: string;
}

export const SiteDetailsFlyout: React.FC<SiteDetailsFlyoutProps> = ({
	site,
	className = "",
}) => {
	return (
		<div
			className={`bg-white rounded-lg shadow-lg border border-gray-200 p-4 min-w-[280px] ${className}`}
		>
			{/* Site Header */}
			<div className="flex items-start justify-between mb-4">
				<div className="flex-1 min-w-0">
					<h3 className="font-semibold text-gray-900 text-base truncate">
						{site.name}
					</h3>
					<div className="flex items-center text-sm text-gray-600 mt-1">
						<MapPin className="h-4 w-4 mr-1 flex-shrink-0" />
						<span className="truncate">{site.location}</span>
					</div>
				</div>
			</div>

			{/* Project Information */}
			<div className="space-y-3 mb-4">
				<div className="flex items-center text-sm">
					<User className="h-4 w-4 text-gray-400 mr-2 flex-shrink-0" />
					<span className="text-gray-600 mr-2">PM:</span>
					<span className="font-medium text-gray-900">
						{site.projectManager}
					</span>
				</div>
			</div>

			{/* Site Metrics */}
			<div className="border-t border-gray-200 pt-3">
				<h4 className="text-sm font-medium text-gray-700 mb-3">
					Current Status
				</h4>
				<div className="grid grid-cols-3 gap-3">
					<div className="text-center">
						<div className="flex items-center justify-center mb-1">
							<Users className="h-4 w-4 text-blue-500" />
						</div>
						<div className="text-lg font-semibold text-gray-900">
							{site.workersOnSite}
						</div>
						<div className="text-xs text-gray-500">Workers</div>
					</div>

					<div className="text-center">
						<div className="flex items-center justify-center mb-1">
							<FileCheck className="h-4 w-4 text-green-500" />
						</div>
						<div className="text-lg font-semibold text-gray-900">
							{site.activePermits}
						</div>
						<div className="text-xs text-gray-500">Permits</div>
					</div>

					<div className="text-center">
						<div className="flex items-center justify-center mb-1">
							<AlertTriangle
								className={`h-4 w-4 ${site.openIncidents > 0 ? "text-red-500" : "text-gray-400"}`}
							/>
						</div>
						<div
							className={`text-lg font-semibold ${site.openIncidents > 0 ? "text-red-600" : "text-gray-900"}`}
						>
							{site.openIncidents}
						</div>
						<div className="text-xs text-gray-500">Incidents</div>
					</div>
				</div>
			</div>

			{/* Site Details Button */}
			<div className="border-t border-gray-200 pt-3 mt-3">
				<Link
					to={`/sites/${site.id}/info`}
					className="w-full flex items-center justify-center px-3 py-2 text-sm font-medium text-green-600 bg-green-50 hover:bg-green-100 rounded-md transition-colors duration-200"
				>
					<ExternalLink className="h-4 w-4 mr-2" />
					View Site Details
				</Link>
			</div>
		</div>
	);
};
