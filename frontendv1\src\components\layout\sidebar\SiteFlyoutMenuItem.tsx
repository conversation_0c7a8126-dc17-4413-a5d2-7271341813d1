/**
 * Site Flyout Menu Item Component
 * Special flyout item for sites with preview information
 */

import React from "react";
import { useLocation } from "react-router-dom";
import { Plus, Users, FileCheck, AlertTriangle } from "lucide-react";
import { SubmenuItem } from "../../../types/sidebar";
import { useSidebarAccessibility } from "./SidebarProvider";
import { isPathActive } from "../../../utils/routeUtils";
import { getFlyoutItemClasses } from "../../../styles/sidebar-tokens";
import { useHashNavigation } from "../../../hooks/useHashNavigation";

interface SiteFlyoutMenuItemProps {
	item: SubmenuItem;
	isActive: boolean;
	onClick?: () => void;
}

export const SiteFlyoutMenuItem: React.FC<SiteFlyoutMenuItemProps> = ({
	item,
	isActive:_isActive,
	onClick,
}) => {
	const location = useLocation();
	const { navigateToHash } = useHashNavigation();
	const { getSubmenuItemProps } = useSidebarAccessibility();

	const submenuItemProps = getSubmenuItemProps(item);
	const isCurrentPage = isPathActive(location.pathname, item.path);

	// Determine if this is an "add" action item
	const isAddAction = item.action === "add";

	const handleClick = (e: React.MouseEvent) => {
		e.preventDefault();
		navigateToHash(item.path);
		if (onClick) {
			onClick();
		}
	};

  // If this is the "Add New Site" item, render it like other flyout items
  if (isAddAction) {
    return (
      <button
        onClick={handleClick}
        aria-current={isCurrentPage ? "page" : undefined}
        className={`
          ${getFlyoutItemClasses(isCurrentPage, true)}
          group
          w-full text-left
        `}
        {...submenuItemProps}
      >
        <Plus className="h-4 w-4 mr-2 text-green-500 group-hover:text-green-500 group-hover:scale-110 flex-shrink-0 transition-all duration-300" />
        <span className="flex-1 transition-colors duration-300">{item.name}</span>
      </button>
    );
  }

	// For site items with siteData, render simplified summary for Dashboard flyout
	if (item.siteData) {
		const site = item.siteData;

		return (
			<button
				onClick={handleClick}
				aria-current={isCurrentPage ? "page" : undefined}
				className={`
          ${getFlyoutItemClasses(isCurrentPage, false)}
          ${isCurrentPage ? "active" : ""}
          !p-0 overflow-hidden w-full text-left
        `}
				{...submenuItemProps}
			>
				<div className="p-3">
					{/* Site Header */}
					<div className="flex items-center justify-between mb-2">
						<h4 className="font-medium text-gray-800 text-sm truncate flex-1">
							{site.name}
						</h4>
					</div>

					{/* Quick Metrics */}
					<div className="flex justify-between items-center text-xs text-gray-500">
						<div className="flex items-center space-x-1">
							<Users className="h-3 w-3" />
							<span>{site.workersOnSite}</span>
						</div>

						<div className="flex items-center space-x-1">
							<FileCheck className="h-3 w-3" />
							<span>{site.activePermits}</span>
						</div>

						<div className="flex items-center space-x-1">
							<AlertTriangle className="h-3 w-3" />
							<span>{site.openIncidents}</span>
						</div>
					</div>
				</div>
			</button>
		);
	}

	// Fallback for regular items without site data
	return (
		<button
			onClick={handleClick}
			aria-current={isCurrentPage ? "page" : undefined}
			className={`
        ${getFlyoutItemClasses(isCurrentPage, false)}
        ${isCurrentPage ? "active" : ""}
        w-full text-left
      `}
			{...submenuItemProps}
		>
			<span className="flex-1">{item.name}</span>
		</button>
	);
};
