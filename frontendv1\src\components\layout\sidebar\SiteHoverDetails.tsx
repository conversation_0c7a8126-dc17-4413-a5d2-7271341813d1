/**
 * Site Hover Details Component
 * Shows detailed site information when hovering over site menu items
 */

import React from "react";
import { SiteDetailsFlyout } from "./SiteDetailsFlyout";

interface SiteData {
	id: string;
	name: string;
	healthStatus: "green" | "amber" | "red";
	workersOnSite: number;
	activePermits: number;
	openIncidents: number;
	projectManager: string;
	location: string;
	timeline?: string;
	currentPhase?: string;
	progressPercentage?: number;
}

interface SiteHoverDetailsProps {
	site: SiteData;
	visible: boolean;
	onMouseEnter: () => void;
	onMouseLeave: () => void;
}

export const SiteHoverDetails: React.FC<SiteHoverDetailsProps> = ({
	site,
	visible,
	onMouseEnter,
	onMouseLeave,
}) => {
	if (!visible) return null;

	return (
		<div
			className="fixed z-50 pointer-events-auto"
			style={{
				left: "80px", // Position next to the sidebar
				top: "50%",
				transform: "translateY(-50%)",
			}}
			onMouseEnter={onMouseEnter}
			onMouseLeave={onMouseLeave}
		>
			<div className="animate-in slide-in-from-left-2 duration-200">
				<SiteDetailsFlyout site={site} />
			</div>
		</div>
	);
};
