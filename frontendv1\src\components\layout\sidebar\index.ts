/**
 * Sidebar Components Export Index
 * Centralized exports for all sidebar-related components and utilities
 */

// Main Components
export { default as Sidebar } from "./Sidebar";
export {
	SidebarProvider,
	useSidebar,
	useSidebarHover,
	useSidebarKeyboard,
	useSidebarAccessibility,
} from "./SidebarProvider";

// Sub-components
export { Sidebar<PERSON>ogo } from "./SidebarLogo";
export { SidebarMenu } from "./SidebarMenu";
export { SidebarMenuItem } from "./SidebarMenuItem";
// export { SidebarBackButton } from "./SidebarBackButton"; // Removed - back buttons now handled by TopBar
export { SidebarFlyout } from "./SidebarFlyout";
export { FlyoutMenuItem } from "./FlyoutMenuItem";
export { SiteFlyoutMenuItem } from "./SiteFlyoutMenuItem";
export { SiteDetailsFlyout } from "./SiteDetailsFlyout";
export { SiteHoverDetails } from "./SiteHoverDetails";

// Types (re-export for convenience)
export type {
	SidebarState,
	SidebarAction,
	SidebarContextValue,
	SidebarProviderProps,
	SidebarMenuItemProps,
	SidebarFlyoutProps,
	FlyoutMenuItemProps,
	UseSidebarHover,
	UseSidebarKeyboard,
	UseSidebarAccessibility,
} from "../../../types/sidebar";

// Design tokens (re-export for convenience)
export {
	sidebarTokens,
	defaultHoverConfig,
	zIndexes,
	breakpoints,
	animationVariants,
	a11yConstants,
	cssClasses,
	getSpacing,
	getColorClasses,
	getTransitionClasses,
} from "../../../styles/sidebar-tokens";
