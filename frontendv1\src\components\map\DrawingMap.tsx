import React, { useState, useCallback } from 'react';
import InteractiveMap, { DrawnPolygon, MapCenter } from './InteractiveMap';
import { 
  validateGeoJSON, 
  processGeoJSON, 
  calculatePolygonArea, 
  calculatePolygonPerimeter,
  calculatePolygonCenter,
  isPolygonSelfIntersecting,
  formatArea,
  formatDistance
} from '../../utils/geoJsonUtils';
import { AlertCircle, CheckCircle, Info } from 'lucide-react';

interface DrawingMapProps {
  center?: MapCenter;
  zoom?: number;
  height?: string;
  onPolygonComplete?: (polygon: DrawnPolygon, metrics: PolygonMetrics) => void;
  onPolygonChange?: (polygon: DrawnPolygon | null, metrics: PolygonMetrics | null) => void;
  initialPolygon?: DrawnPolygon;
  showLocationMarker?: boolean;
  locationMarkerText?: string;
  className?: string;
  showMetrics?: boolean;
  showValidation?: boolean;
}

export interface PolygonMetrics {
  area: number;
  areaFormatted: string;
  perimeter: number;
  perimeterFormatted: string;
  center: MapCenter;
  isValid: boolean;
  validationErrors: string[];
}

const DrawingMap: React.FC<DrawingMapProps> = ({
  center,
  zoom,
  height = '400px',
  onPolygonComplete,
  onPolygonChange,
  initialPolygon,
  showLocationMarker = false,
  locationMarkerText,
  className = '',
  showMetrics = true,
  showValidation = true
}) => {
  const [currentPolygon, setCurrentPolygon] = useState<DrawnPolygon | null>(initialPolygon || null);
  const [metrics, setMetrics] = useState<PolygonMetrics | null>(null);

  // Calculate metrics for a polygon
  const calculateMetrics = useCallback((polygon: DrawnPolygon): PolygonMetrics => {
    const coordinates = polygon.geometry.coordinates[0];
    const validationErrors: string[] = [];

    // Validate polygon
    const isValidGeoJSON = validateGeoJSON(polygon);
    if (!isValidGeoJSON) {
      validationErrors.push('Invalid GeoJSON structure');
    }

    // Check for self-intersection
    const isSelfIntersecting = isPolygonSelfIntersecting(coordinates);
    if (isSelfIntersecting) {
      validationErrors.push('Polygon edges cannot cross each other');
    }

    // Check minimum area (100 square meters)
    const area = calculatePolygonArea(coordinates);
    if (area < 100) {
      validationErrors.push('Area must be at least 100 square meters');
    }

    // Check minimum number of points
    if (coordinates.length < 4) {
      validationErrors.push('Polygon must have at least 3 points');
    }

    const perimeter = calculatePolygonPerimeter(coordinates);
    const polygonCenter = calculatePolygonCenter(coordinates);

    return {
      area,
      areaFormatted: formatArea(area),
      perimeter,
      perimeterFormatted: formatDistance(perimeter),
      center: polygonCenter,
      isValid: validationErrors.length === 0,
      validationErrors
    };
  }, []);

  // Handle polygon drawing
  const handlePolygonDrawn = useCallback((polygon: DrawnPolygon) => {
    const processedPolygon = processGeoJSON(polygon);
    if (!processedPolygon) {
      console.error('Failed to process polygon');
      return;
    }

    const polygonMetrics = calculateMetrics(processedPolygon);
    
    setCurrentPolygon(processedPolygon);
    setMetrics(polygonMetrics);

    // Call callbacks
    if (onPolygonChange) {
      onPolygonChange(processedPolygon, polygonMetrics);
    }

    if (onPolygonComplete && polygonMetrics.isValid) {
      onPolygonComplete(processedPolygon, polygonMetrics);
    }
  }, [calculateMetrics, onPolygonChange, onPolygonComplete]);

  // Handle polygon editing
  const handlePolygonEdited = useCallback((polygon: DrawnPolygon) => {
    const processedPolygon = processGeoJSON(polygon);
    if (!processedPolygon) {
      console.error('Failed to process edited polygon');
      return;
    }

    const polygonMetrics = calculateMetrics(processedPolygon);
    
    setCurrentPolygon(processedPolygon);
    setMetrics(polygonMetrics);

    if (onPolygonChange) {
      onPolygonChange(processedPolygon, polygonMetrics);
    }
  }, [calculateMetrics, onPolygonChange]);

  // Handle polygon deletion
  const handlePolygonDeleted = useCallback(() => {
    setCurrentPolygon(null);
    setMetrics(null);

    if (onPolygonChange) {
      onPolygonChange(null, null);
    }
  }, [onPolygonChange]);

  // Calculate initial metrics if initial polygon is provided
  React.useEffect(() => {
    if (initialPolygon && !metrics) {
      const initialMetrics = calculateMetrics(initialPolygon);
      setMetrics(initialMetrics);
    }
  }, [initialPolygon, metrics, calculateMetrics]);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Map */}
      <InteractiveMap
        center={center}
        zoom={zoom}
        height={height}
        onPolygonDrawn={handlePolygonDrawn}
        onPolygonEdited={handlePolygonEdited}
        onPolygonDeleted={handlePolygonDeleted}
        initialPolygon={initialPolygon}
        showDrawingTools={true}
        showLocationMarker={showLocationMarker}
        locationMarkerText={locationMarkerText}
      />

      {/* Validation Messages */}
      {showValidation && metrics && (
        <div className="space-y-2">
          {metrics.isValid ? (
            <div className="flex items-center p-3 bg-green-50 border border-green-200 rounded-md">
              <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
              <span className="text-sm text-green-700">
                Site boundary is valid and ready to use
              </span>
            </div>
          ) : (
            <div className="space-y-2">
              {metrics.validationErrors.map((error, index) => (
                <div key={index} className="flex items-start p-3 bg-red-50 border border-red-200 rounded-md">
                  <AlertCircle className="h-5 w-5 text-red-600 mr-2 mt-0.5 flex-shrink-0" />
                  <span className="text-sm text-red-700">{error}</span>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Metrics Display */}
      {showMetrics && metrics && (
        <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
          <div className="flex items-center mb-3">
            <Info className="h-5 w-5 text-blue-600 mr-2" />
            <h4 className="text-sm font-medium text-blue-900">Site Boundary Metrics</h4>
          </div>
          
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-blue-700 font-medium">Area:</span>
              <span className="text-blue-900 ml-2">{metrics.areaFormatted}</span>
            </div>
            <div>
              <span className="text-blue-700 font-medium">Perimeter:</span>
              <span className="text-blue-900 ml-2">{metrics.perimeterFormatted}</span>
            </div>
            <div className="col-span-2">
              <span className="text-blue-700 font-medium">Center:</span>
              <span className="text-blue-900 ml-2">
                {metrics.center.latitude.toFixed(6)}, {metrics.center.longitude.toFixed(6)}
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Instructions */}
      {!currentPolygon && (
        <div className="bg-gray-50 border border-gray-200 rounded-md p-4">
          <div className="text-sm text-gray-600">
            <p className="font-medium mb-2">How to draw your site boundary:</p>
            <ol className="list-decimal list-inside space-y-1">
              <li>Click the polygon tool (⬡) in the top-right corner of the map</li>
              <li>Click on the map to place points around your site boundary</li>
              <li>Click on the first point to close the polygon</li>
              <li>Use the edit tool to adjust points if needed</li>
            </ol>
          </div>
        </div>
      )}
    </div>
  );
};

export default DrawingMap;
