import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, Tile<PERSON>ayer, FeatureGroup, <PERSON><PERSON>, Popup, LayersControl } from 'react-leaflet';
import { EditControl } from 'react-leaflet-draw';
import L from 'leaflet';
import { Home, RotateCcw } from 'lucide-react';
import 'leaflet/dist/leaflet.css';
import 'leaflet-draw/dist/leaflet.draw.css';

// Fix for default markers in React-Leaflet
import icon from 'leaflet/dist/images/marker-icon.png';
import iconShadow from 'leaflet/dist/images/marker-shadow.png';

const DefaultIcon = L.icon({
  iconUrl: icon,
  shadowUrl: iconShadow,
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

L.Marker.prototype.options.icon = DefaultIcon;

export interface MapCenter {
  latitude: number;
  longitude: number;
}

export interface DrawnPolygon {
  type: 'Feature';
  properties: {
    name: string;
  };
  geometry: {
    type: 'Polygon';
    coordinates: number[][][];
  };
}

interface InteractiveMapProps {
  center?: MapCenter;
  zoom?: number;
  height?: string;
  onPolygonDrawn?: (polygon: DrawnPolygon) => void;
  onPolygonEdited?: (polygon: DrawnPolygon) => void;
  onPolygonDeleted?: () => void;
  initialPolygon?: DrawnPolygon;
  showDrawingTools?: boolean;
  showLocationMarker?: boolean;
  locationMarkerText?: string;
  className?: string;
  showLayerControl?: boolean;
  showNavigationTools?: boolean;
}

const InteractiveMap: React.FC<InteractiveMapProps> = ({
  center = { latitude: -1.2921, longitude: 36.8219 }, // Default to Nairobi
  zoom = 13,
  height = '400px',
  onPolygonDrawn,
  onPolygonEdited,
  onPolygonDeleted,
  initialPolygon,
  showDrawingTools = true,
  showLocationMarker = false,
  locationMarkerText = 'Selected Location',
  className = '',
  showLayerControl = true,
  showNavigationTools = true
}) => {
  const [drawnItems, setDrawnItems] = useState<L.FeatureGroup | null>(null);
  const mapRef = useRef<L.Map | null>(null);
  const [originalCenter] = useState(center);
  const [originalZoom] = useState(zoom);



  // Return to original focus point
  const returnToFocus = () => {
    if (mapRef.current) {
      mapRef.current.setView([originalCenter.latitude, originalCenter.longitude], originalZoom);
    }
  };

  // Reset map view (zoom out to show all drawn items or return to original view)
  const resetMapView = () => {
    if (mapRef.current) {
      if (drawnItems && drawnItems.getLayers().length > 0) {
        // Fit to drawn items
        const bounds = drawnItems.getBounds();
        if (bounds.isValid()) {
          mapRef.current.fitBounds(bounds, { padding: [20, 20] });
        }
      } else {
        // Return to original view
        mapRef.current.setView([originalCenter.latitude, originalCenter.longitude], originalZoom);
      }
    }
  };

  // Handle polygon creation
  const handleCreated = (e: any) => {
    const { layerType, layer } = e;
    
    if (layerType === 'polygon') {
      const drawnGeoJSON = layer.toGeoJSON();
      
      // Format to match our DrawnPolygon interface
      const formattedPolygon: DrawnPolygon = {
        type: 'Feature',
        properties: {
          name: 'Site Boundary'
        },
        geometry: {
          type: 'Polygon',
          coordinates: drawnGeoJSON.geometry.coordinates
        }
      };

      // Add the layer to the drawn items
      if (drawnItems) {
        drawnItems.addLayer(layer);
      }

      // Call the callback
      if (onPolygonDrawn) {
        onPolygonDrawn(formattedPolygon);
      }
    }
  };

  // Handle polygon editing
  const handleEdited = (e: any) => {
    const layers = e.layers;
    layers.eachLayer((layer: any) => {
      const drawnGeoJSON = layer.toGeoJSON();
      
      const formattedPolygon: DrawnPolygon = {
        type: 'Feature',
        properties: {
          name: 'Site Boundary'
        },
        geometry: {
          type: 'Polygon',
          coordinates: drawnGeoJSON.geometry.coordinates
        }
      };

      if (onPolygonEdited) {
        onPolygonEdited(formattedPolygon);
      }
    });
  };

  // Handle polygon deletion
  const handleDeleted = () => {
    if (onPolygonDeleted) {
      onPolygonDeleted();
    }
  };

  // Load initial polygon if provided
  useEffect(() => {
    if (initialPolygon && drawnItems && mapRef.current) {
      // Clear existing layers
      drawnItems.clearLayers();
      
      // Create polygon layer from GeoJSON
      const polygonLayer = L.geoJSON(initialPolygon, {
        style: {
          color: '#FF8C00',
          weight: 2,
          opacity: 0.9,
          fillColor: '#FFD580',
          fillOpacity: 0.15
        }
      });

      // Add to drawn items
      polygonLayer.eachLayer((layer) => {
        drawnItems.addLayer(layer);
      });

      // Fit map to polygon bounds
      const bounds = polygonLayer.getBounds();
      if (bounds.isValid()) {
        mapRef.current.fitBounds(bounds, { padding: [20, 20] });
      }
    }
  }, [initialPolygon, drawnItems]);

  return (
    <div className={`relative ${className}`} style={{ height }}>
      {/* Navigation Tools */}
      {showNavigationTools && (
        <div className="absolute bottom-2 left-2 z-[1000] flex flex-col space-y-2">
          <button
            onClick={returnToFocus}
            className="bg-white hover:bg-gray-50 border border-gray-300 rounded-md p-2 shadow-sm transition-colors"
            title="Return to focus point"
          >
            <Home className="h-4 w-4 text-gray-600" />
          </button>
          <button
            onClick={resetMapView}
            className="bg-white hover:bg-gray-50 border border-gray-300 rounded-md p-2 shadow-sm transition-colors"
            title="Reset map view"
          >
            <RotateCcw className="h-4 w-4 text-gray-600" />
          </button>
        </div>
      )}

      <MapContainer
        center={[center.latitude, center.longitude]}
        zoom={zoom}
        style={{ height: '100%', width: '100%' }}
        ref={mapRef}
      >
        {showLayerControl ? (
          <LayersControl position="topright">
            {/* Street Map */}
            <LayersControl.BaseLayer checked name="Street Map">
              <TileLayer
                attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                maxZoom={20}
              />
            </LayersControl.BaseLayer>

            {/* Satellite View */}
            <LayersControl.BaseLayer name="Satellite">
              <TileLayer
                attribution='&copy; <a href="https://www.esri.com/">Esri</a> &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community'
                url="https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}"
                maxZoom={20}
              />
            </LayersControl.BaseLayer>

            {/* Terrain View */}
            <LayersControl.BaseLayer name="Terrain">
              <TileLayer
                attribution='Map data: &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors, <a href="http://viewfinderpanoramas.org">SRTM</a> | Map style: &copy; <a href="https://opentopomap.org">OpenTopoMap</a> (<a href="https://creativecommons.org/licenses/by-sa/3.0/">CC-BY-SA</a>)'
                url="https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png"
                maxZoom={17}
              />
            </LayersControl.BaseLayer>
          </LayersControl>
        ) : (
          /* Default OpenStreetMap tiles when layer control is disabled */
          <TileLayer
            attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
            url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            maxZoom={20}
          />
        )}

        {/* Location marker */}
        {showLocationMarker && (
          <Marker position={[center.latitude, center.longitude]}>
            <Popup>{locationMarkerText}</Popup>
          </Marker>
        )}

        {/* Drawing tools */}
        {showDrawingTools && (
          <FeatureGroup
            ref={(featureGroupRef) => {
              if (featureGroupRef) {
                setDrawnItems(featureGroupRef);
              }
            }}
          >
            <EditControl
              position="topright"
              onCreated={handleCreated}
              onEdited={handleEdited}
              onDeleted={handleDeleted}
              draw={{
                rectangle: false,
                circle: false,
                circlemarker: false,
                marker: false,
                polyline: false,
                polygon: {
                  allowIntersection: false,
                  drawError: {
                    color: '#e1e100',
                    message: '<strong>Error:</strong> Polygon edges cannot cross!'
                  },
                  shapeOptions: {
                    color: '#FF8C00',        // Orange border
                    weight: 2,               // Border thickness
                    opacity: 0.9,            // Border opacity
                    fillColor: '#FFD580',    // Light orange fill
                    fillOpacity: 0.15        // Fill transparency
                  }
                }
              }}
              edit={{
                featureGroup: drawnItems || undefined,
                remove: true
              }}
            />
          </FeatureGroup>
        )}
      </MapContainer>
    </div>
  );
};

export default InteractiveMap;
