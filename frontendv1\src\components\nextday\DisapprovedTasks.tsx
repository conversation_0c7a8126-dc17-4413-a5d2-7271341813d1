import React, { useState, useEffect } from 'react';
import { AlertTriangle, Calendar, Clock, MapPin, Shield, Search } from 'lucide-react';
import { Task } from '../../types/tasks';

interface DisapprovedTask {
  task: Task;
  reason: string;
  disapprovedBy: string;
  disapprovedAt: Date;
}

interface DisapprovedTasksProps {
  siteId: string;
}

const DisapprovedTasks: React.FC<DisapprovedTasksProps> = ({ siteId }) => {
  const [disapprovedTasks, setDisapprovedTasks] = useState<DisapprovedTask[]>([]);
  const [filteredTasks, setFilteredTasks] = useState<DisapprovedTask[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchDisapprovedTasks();
  }, [siteId]);

  useEffect(() => {
    // Apply search filter
    if (searchTerm) {
      setFilteredTasks(disapprovedTasks.filter(item => 
        item.task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.task.taskNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.task.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.reason.toLowerCase().includes(searchTerm.toLowerCase())
      ));
    } else {
      setFilteredTasks(disapprovedTasks);
    }
  }, [disapprovedTasks, searchTerm]);

  const fetchDisapprovedTasks = async () => {
    // In a real app, this would be an API call
    // For now, using mock data
    const mockDisapprovedTasks: DisapprovedTask[] = [
      {
        task: {
          id: "task-rejected-1",
          taskNumber: "TSK-2024-098",
          title: "Roof Tiling - Section A",
          description: "Install roof tiles on Section A",
          category: "construction",
          location: "Roof - Section A",
          siteId: "site-1",
          plannedStartDate: new Date(new Date().setDate(new Date().getDate() - 1)),
          plannedEndDate: new Date(new Date().setDate(new Date().getDate() - 1)),
          estimatedDuration: 8,
          status: "blocked",
          priority: "medium",
          progressPercentage: 0,
          createdBy: "supervisor-1",
          createdByName: "John Smith",
          assignedSupervisor: "supervisor-1",
          assignedSupervisorName: "John Smith",
          assignedWorkers: [],
          dependencies: [],
          requiresPermit: true,
          permitTypes: ["height-work"],
          riskLevel: "high",
          safetyRequirements: ["fall-protection"],
          requiredPPE: ["hard-hat", "safety-harness"],
          requiredTrainings: ["working-at-heights"],
          requiredCertifications: [],
          ramsDocuments: [],
          attachments: [],
          qualityChecks: [],
          complianceRequirements: [],
          history: [],
          tags: ["roofing"],
          customFields: {},
          createdAt: new Date(),
          updatedAt: new Date()
        },
        reason: "Insufficient fall protection equipment available for tomorrow",
        disapprovedBy: "Safety Manager",
        disapprovedAt: new Date(new Date().setDate(new Date().getDate() - 1))
      },
      {
        task: {
          id: "task-rejected-2",
          taskNumber: "TSK-2024-099",
          title: "Excavation - Utility Trench",
          description: "Excavate trench for utility lines",
          category: "construction",
          location: "East Side - Ground Level",
          siteId: "site-1",
          plannedStartDate: new Date(new Date().setDate(new Date().getDate() - 2)),
          plannedEndDate: new Date(new Date().setDate(new Date().getDate() - 2)),
          estimatedDuration: 4,
          status: "blocked",
          priority: "medium",
          progressPercentage: 0,
          createdBy: "supervisor-3",
          createdByName: "Mike Davis",
          assignedSupervisor: "supervisor-3",
          assignedSupervisorName: "Mike Davis",
          assignedWorkers: [],
          dependencies: [],
          requiresPermit: true,
          permitTypes: ["excavation"],
          riskLevel: "medium",
          safetyRequirements: ["utility-marking"],
          requiredPPE: ["hard-hat", "safety-boots"],
          requiredTrainings: ["excavation-safety"],
          requiredCertifications: [],
          ramsDocuments: [],
          attachments: [],
          qualityChecks: [],
          complianceRequirements: [],
          history: [],
          tags: ["excavation"],
          customFields: {},
          createdAt: new Date(),
          updatedAt: new Date()
        },
        reason: "Utility marking not completed. Reschedule after marking is complete.",
        disapprovedBy: "Project Manager",
        disapprovedAt: new Date(new Date().setDate(new Date().getDate() - 2))
      }
    ];
    
    setDisapprovedTasks(mockDisapprovedTasks);
    setFilteredTasks(mockDisapprovedTasks);
    setLoading(false);
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Disapproved Tasks</h2>
      </div>

      {/* Search Filter */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search disapproved tasks..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
          />
        </div>
      </div>

      {/* Tasks List */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        {loading ? (
          <div className="text-center py-10">Loading disapproved tasks...</div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredTasks.length === 0 ? (
              <div className="p-12 text-center">
                <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No disapproved tasks found</h3>
                <p className="text-gray-500">
                  {searchTerm ? 'Try adjusting your search to see more results.' : 'There are no disapproved tasks for this site.'}
                </p>
              </div>
            ) : (
              filteredTasks.map((item) => (
                <div key={item.task.id} className="p-6 hover:bg-gray-50 border-l-4 border-l-red-500">
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-medium text-gray-900">{item.task.title}</h3>
                        <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                          item.task.priority === 'high' ? 'bg-red-100 text-red-800' :
                          item.task.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {item.task.priority.toUpperCase()}
                        </span>
                        <span className="px-2 py-1 text-xs font-medium rounded-full bg-red-100 text-red-800">
                          DISAPPROVED
                        </span>
                      </div>

                      <p className="text-sm text-gray-600 mb-3">
                        {item.task.description}
                      </p>

                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm text-gray-500">
                        <div className="flex items-center">
                          <MapPin className="h-4 w-4 mr-1" />
                          {item.task.location}
                        </div>
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-1" />
                          {item.task.estimatedDuration}h estimated
                        </div>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          Planned: {formatDate(item.task.plannedStartDate)}
                        </div>
                        <div className="flex items-center">
                          <AlertTriangle className="h-4 w-4 mr-1" />
                          {item.task.riskLevel} risk
                        </div>
                      </div>

                      <div className="bg-red-50 border border-red-100 rounded-lg p-4 my-4">
                        <p className="text-sm text-red-700">{item.reason}</p>
                      </div>

                      <div className="flex justify-between text-xs text-gray-500">
                        <div>
                          Disapproved by: <span className="font-medium">{item.disapprovedBy}</span>
                        </div>
                        <div>
                          Date: {formatDate(item.disapprovedAt)}
                        </div>
                      </div>

                      <div className="mt-3 flex items-center space-x-4 text-xs text-gray-400">
                        <span>
                          {item.task.category} • {item.task.taskNumber}
                        </span>
                        <span>Supervisor: {item.task.assignedSupervisorName}</span>
                        {item.task.requiresPermit && (
                          <span className="flex items-center text-amber-600">
                            <Shield className="h-3 w-3 mr-1" />
                            Permit Required
                          </span>
                        )}
                      </div>

                      {item.task.requiresPermit && item.task.permitTypes && (
                        <div className="mt-3 text-xs">
                          <span className="text-gray-500">Required permits: </span>
                          {item.task.permitTypes.map(permit => (
                            <span key={permit} className="bg-purple-100 text-purple-800 px-2 py-1 rounded mr-1">
                              {permit}
                            </span>
                          ))}
                        </div>
                      )}
                      
                      {item.task.requiredPPE && item.task.requiredPPE.length > 0 && (
                        <div className="mt-2 text-xs">
                          <span className="text-gray-500">Required PPE: </span>
                          {item.task.requiredPPE.map(ppe => (
                            <span key={ppe} className="bg-blue-100 text-blue-800 px-2 py-1 rounded mr-1">
                              {ppe}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default DisapprovedTasks;


