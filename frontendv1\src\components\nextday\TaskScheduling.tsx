import React, { useState, useEffect } from 'react';
import { Clock, MapPin, User, Calendar, Shield, CheckCircle, Search } from 'lucide-react';
import { Task } from '../../types/tasks';

interface Worker {
  id: string;
  name: string;
  trade: string;
  availability: boolean;
  certifications: string[];
}

interface TaskAssignment {
  taskId: string;
  workerIds: string[];
}

interface TaskSchedulingProps {
  siteId: string;
}

const TaskScheduling: React.FC<TaskSchedulingProps> = ({ siteId }) => {
  const [predefinedTasks, setPredefinedTasks] = useState<Task[]>([]);
  const [workers, setWorkers] = useState<Worker[]>([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [selectedTaskIds, setSelectedTaskIds] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [taskAssignments, setTaskAssignments] = useState<Record<string, string[]>>({});
  const [activeTaskId, setActiveTaskId] = useState<string | null>(null);

  useEffect(() => {
    // Fetch predefined tasks and workers
    fetchPredefinedTasks();
    fetchWorkers();
  }, [siteId]);

  const fetchPredefinedTasks = async () => {
    // In a real app, this would be an API call
    // For now, using mock data
    const mockTasks: Task[] = [
      {
        id: "task-next-1",
        taskNumber: "TSK-2024-101",
        title: "Morning Safety Briefing",
        description: "Conduct safety briefing for all workers on site",
        category: "safety",
        location: "Main Office - Site A",
        siteId: "site-1",
        plannedStartDate: new Date(new Date().setDate(new Date().getDate() + 1)),
        plannedEndDate: new Date(new Date().setDate(new Date().getDate() + 1)),
        estimatedDuration: 1,
        status: "todo",
        priority: "high",
        progressPercentage: 0,
        createdBy: "supervisor-1",
        createdByName: "John Smith",
        assignedSupervisor: "supervisor-1",
        assignedSupervisorName: "John Smith",
        assignedWorkers: [],
        dependencies: [],
        requiresPermit: false,
        riskLevel: "low",
        safetyRequirements: [],
        requiredPPE: ["hard-hat", "safety-vest"],
        requiredTrainings: ["safety-orientation"],
        requiredCertifications: [],
        ramsDocuments: [],
        attachments: [],
        qualityChecks: [],
        complianceRequirements: [],
        history: [],
        tags: ["safety", "daily"],
        customFields: {},
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: "task-next-2",
        taskNumber: "TSK-2024-102",
        title: "Electrical Wiring - Floor 3",
        description: "Install electrical wiring for Floor 3 office spaces",
        category: "electrical",
        location: "Zone B - Floor 3",
        siteId: "site-1",
        plannedStartDate: new Date(new Date().setDate(new Date().getDate() + 1)),
        plannedEndDate: new Date(new Date().setDate(new Date().getDate() + 1)),
        estimatedDuration: 8,
        status: "todo",
        priority: "medium",
        progressPercentage: 0,
        createdBy: "supervisor-2",
        createdByName: "Sarah Johnson",
        assignedSupervisor: "supervisor-2",
        assignedSupervisorName: "Sarah Johnson",
        assignedWorkers: [],
        dependencies: [],
        requiresPermit: true,
        riskLevel: "medium",
        safetyRequirements: ["Lock-out/Tag-out procedure"],
        requiredPPE: ["hard-hat", "safety-vest", "insulated-gloves"],
        requiredTrainings: ["electrical-safety"],
        requiredCertifications: ["electrician-license"],
        ramsDocuments: [],
        attachments: [],
        qualityChecks: [],
        complianceRequirements: [],
        history: [],
        tags: ["electrical", "installation"],
        customFields: {},
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: "task-next-3",
        taskNumber: "TSK-2024-103",
        title: "Concrete Pouring - Foundation",
        description: "Pour concrete for foundation at Building C",
        category: "construction",
        location: "Building C - Foundation",
        siteId: "site-1",
        plannedStartDate: new Date(new Date().setDate(new Date().getDate() + 1)),
        plannedEndDate: new Date(new Date().setDate(new Date().getDate() + 1)),
        estimatedDuration: 6,
        status: "todo",
        priority: "high",
        progressPercentage: 0,
        createdBy: "supervisor-1",
        createdByName: "John Smith",
        assignedSupervisor: "supervisor-1",
        assignedSupervisorName: "John Smith",
        assignedWorkers: [],
        dependencies: [],
        requiresPermit: false,
        riskLevel: "medium",
        safetyRequirements: [],
        requiredPPE: ["hard-hat", "safety-vest", "safety-boots", "gloves"],
        requiredTrainings: ["safety-orientation", "heavy-machinery"],
        requiredCertifications: [],
        ramsDocuments: [],
        attachments: [],
        qualityChecks: [],
        complianceRequirements: [],
        history: [],
        tags: ["construction", "concrete"],
        customFields: {},
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: "task-next-4",
        taskNumber: "TSK-2024-104",
        title: "HVAC Installation - Building A",
        description: "Install HVAC system in Building A, Floor 2",
        category: "hvac",
        location: "Building A - Floor 2",
        siteId: "site-1",
        plannedStartDate: new Date(new Date().setDate(new Date().getDate() + 1)),
        plannedEndDate: new Date(new Date().setDate(new Date().getDate() + 1)),
        estimatedDuration: 8,
        status: "todo",
        priority: "medium",
        progressPercentage: 0,
        createdBy: "supervisor-3",
        createdByName: "Mike Davis",
        assignedSupervisor: "supervisor-3",
        assignedSupervisorName: "Mike Davis",
        assignedWorkers: [],
        dependencies: [],
        requiresPermit: true,
        riskLevel: "medium",
        safetyRequirements: ["Working at height procedures"],
        requiredPPE: ["hard-hat", "safety-vest", "safety-harness"],
        requiredTrainings: ["working-at-height", "hvac-systems"],
        requiredCertifications: ["hvac-technician"],
        ramsDocuments: [],
        attachments: [],
        qualityChecks: [],
        complianceRequirements: [],
        history: [],
        tags: ["hvac", "installation"],
        customFields: {},
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: "task-next-5",
        taskNumber: "TSK-2024-105",
        title: "Plumbing Installation - Restrooms",
        description: "Install plumbing fixtures in Building B restrooms",
        category: "plumbing",
        location: "Building B - Floor 1",
        siteId: "site-1",
        plannedStartDate: new Date(new Date().setDate(new Date().getDate() + 1)),
        plannedEndDate: new Date(new Date().setDate(new Date().getDate() + 1)),
        estimatedDuration: 5,
        status: "todo",
        priority: "low",
        progressPercentage: 0,
        createdBy: "supervisor-2",
        createdByName: "Sarah Johnson",
        assignedSupervisor: "supervisor-2",
        assignedSupervisorName: "Sarah Johnson",
        assignedWorkers: [],
        dependencies: [],
        requiresPermit: false,
        riskLevel: "low",
        safetyRequirements: [],
        requiredPPE: ["hard-hat", "safety-vest", "gloves"],
        requiredTrainings: ["safety-orientation"],
        requiredCertifications: ["plumber-license"],
        ramsDocuments: [],
        attachments: [],
        qualityChecks: [],
        complianceRequirements: [],
        history: [],
        tags: ["plumbing", "installation"],
        customFields: {},
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
    
    setPredefinedTasks(mockTasks);
    setLoading(false);
  };

  const fetchWorkers = async () => {
    // In a real app, this would be an API call
    // For now, using mock data
    const mockWorkers: Worker[] = [
      {
        id: "worker-1",
        name: "Mike Johnson",
        trade: "General Labor",
        availability: true,
        certifications: ["safety-orientation", "heavy-machinery"]
      },
      {
        id: "worker-2",
        name: "Sarah Williams",
        trade: "Electrician",
        availability: true,
        certifications: ["safety-orientation", "electrical-safety", "electrician-license"]
      },
      {
        id: "worker-3",
        name: "Robert Chen",
        trade: "HVAC Technician",
        availability: true,
        certifications: ["safety-orientation", "hvac-systems", "hvac-technician", "working-at-height"]
      },
      {
        id: "worker-4",
        name: "Emily Rodriguez",
        trade: "Plumber",
        availability: true,
        certifications: ["safety-orientation", "plumber-license"]
      },
      {
        id: "worker-5",
        name: "David Kim",
        trade: "Carpenter",
        availability: true,
        certifications: ["safety-orientation", "heavy-machinery"]
      },
      {
        id: "worker-6",
        name: "Lisa Patel",
        trade: "Safety Officer",
        availability: true,
        certifications: ["safety-orientation", "first-aid", "safety-management"]
      },
      {
        id: "worker-7",
        name: "James Wilson",
        trade: "Electrician",
        availability: false, // Not available tomorrow
        certifications: ["safety-orientation", "electrical-safety", "electrician-license"]
      },
      {
        id: "worker-8",
        name: "Maria Garcia",
        trade: "General Labor",
        availability: true,
        certifications: ["safety-orientation"]
      },
      {
        id: "worker-9",
        name: "Thomas Brown",
        trade: "HVAC Technician",
        availability: true,
        certifications: ["safety-orientation", "hvac-systems"]
      },
      {
        id: "worker-10",
        name: "Sophia Lee",
        trade: "Plumber",
        availability: true,
        certifications: ["safety-orientation", "plumber-license", "working-at-height"]
      }
    ];
    
    setWorkers(mockWorkers);
  };

  const handleTaskSelection = (taskId: string) => {
    setSelectedTaskIds(prev => {
      if (prev.includes(taskId)) {
        // If deselecting a task, remove its assignments
        const newAssignments = { ...taskAssignments };
        delete newAssignments[taskId];
        setTaskAssignments(newAssignments);
        
        return prev.filter(id => id !== taskId);
      } else {
        // Initialize empty worker selection for this task
        setTaskAssignments(prev => ({
          ...prev,
          [taskId]: []
        }));
        
        return [...prev, taskId];
      }
    });
    
    // Set this as the active task for worker selection
    setActiveTaskId(taskId);
  };

  const handleWorkerSelection = (workerId: string) => {
    if (!activeTaskId) return;
    
    setTaskAssignments(prev => {
      const currentWorkers = prev[activeTaskId] || [];
      
      if (currentWorkers.includes(workerId)) {
        return {
          ...prev,
          [activeTaskId]: currentWorkers.filter(id => id !== workerId)
        };
      } else {
        return {
          ...prev,
          [activeTaskId]: [...currentWorkers, workerId]
        };
      }
    });
  };

  const handleSubmit = async () => {
    if (selectedTaskIds.length === 0) {
      alert('Please select at least one task');
      return;
    }

    // Check if any selected task has no workers assigned
    const hasEmptyAssignments = selectedTaskIds.some(taskId => 
      !taskAssignments[taskId] || taskAssignments[taskId].length === 0
    );

    if (hasEmptyAssignments) {
      alert('Please assign workers to all selected tasks');
      return;
    }

    setSubmitting(true);

    try {
      // Create task assignments for each selected task and its workers
      const assignments: TaskAssignment[] = selectedTaskIds.map(taskId => ({
        taskId,
        workerIds: taskAssignments[taskId] || []
      }));

      // In a real app, this would be an API call to save the assignments
      console.log('Submitting task assignments:', assignments);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      alert('Task assignments submitted successfully for tomorrow!');

      // Reset selections after successful submission
      setSelectedTaskIds([]);
      setTaskAssignments({});
      setActiveTaskId(null);

    } catch (error) {
      console.error('Error submitting task assignments:', error);
      alert('Failed to submit task assignments. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const isWorkerQualifiedForTask = (worker: Worker, task: Task) => {
    // Check if worker has all required certifications for the task
    const hasRequiredCertifications = task.requiredCertifications.every(
      cert => worker.certifications.includes(cert)
    );

    // Check if worker has all required trainings for the task
    const hasRequiredTrainings = task.requiredTrainings.every(
      training => worker.certifications.includes(training)
    );

    return hasRequiredCertifications && hasRequiredTrainings && worker.availability;
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  // Get the active task for worker selection
  const activeTask = activeTaskId
    ? predefinedTasks.find(task => task.id === activeTaskId)
    : null;

  // Filter workers based on search term
  const filteredWorkers = workers.filter(worker =>
    worker.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    worker.trade.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Next Day Task Planning</h2>
      </div>

      {loading ? (
        <div className="text-center py-10">Loading tasks...</div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Tasks List - Left Column */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
              <div className="p-4 border-b border-gray-200">
                <h3 className="text-lg font-medium">Tasks for Tomorrow</h3>
                <p className="text-sm text-gray-500 mt-1">
                  Select tasks to assign workers ({selectedTaskIds.length} selected)
                </p>
              </div>

              <div className="p-4">
                <div className="relative mb-4">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search tasks..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                {predefinedTasks.length === 0 ? (
                  <div className="text-center py-10 text-gray-500">
                    No tasks scheduled for tomorrow
                  </div>
                ) : (
                  <div className="space-y-4 max-h-[600px] overflow-y-auto">
                    {predefinedTasks.map(task => {
                      const isSelected = selectedTaskIds.includes(task.id);
                      const isActive = activeTaskId === task.id;
                      const assignedWorkerCount = taskAssignments[task.id]?.length || 0;
                      
                      return (
                        <div 
                          key={task.id} 
                          className={`p-3 border rounded-lg cursor-pointer transition-all ${
                            isActive ? 'border-blue-500 bg-blue-50' : 
                            isSelected ? 'border-blue-300 bg-blue-50' : 
                            'border-gray-200 hover:border-gray-300'
                          }`}
                          onClick={() => handleTaskSelection(task.id)}
                        >
                          {/* Task card content */}
                          <div className="flex justify-between items-start">
                            <div>
                              <div className="flex items-center">
                                <input
                                  type="checkbox"
                                  checked={isSelected}
                                  onChange={() => handleTaskSelection(task.id)}
                                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2"
                                  onClick={(e) => e.stopPropagation()}
                                />
                                <h3 className="font-medium">{task.title}</h3>
                              </div>
                              <p className="text-xs text-gray-500">{task.taskNumber}</p>
                            </div>
                            <div className="flex items-center space-x-2">
                              {assignedWorkerCount > 0 && (
                                <span className="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full">
                                  {assignedWorkerCount} worker{assignedWorkerCount !== 1 ? 's' : ''}
                                </span>
                              )}
                              <span className={`px-2 py-0.5 text-xs font-medium rounded-full ${
                                task.priority === 'high' ? 'bg-red-100 text-red-800' :
                                task.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                                'bg-green-100 text-green-800'
                              }`}>
                                {task.priority.toUpperCase()}
                              </span>
                            </div>
                          </div>
                          
                          <div className="mt-2 flex flex-wrap items-center gap-3 text-xs text-gray-500">
                            <div className="flex items-center">
                              <MapPin className="h-3 w-3 mr-1" />
                              {task.location}
                            </div>
                            <div className="flex items-center">
                              <Clock className="h-3 w-3 mr-1" />
                              {task.estimatedDuration}h
                            </div>
                            <div className="flex items-center">
                              <Calendar className="h-3 w-3 mr-1" />
                              {formatDate(task.plannedStartDate)}
                            </div>
                            <div className="flex items-center">
                              <Shield className="h-3 w-3 mr-1" />
                              {task.riskLevel} risk
                            </div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Workers List - Right Column */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
              <div className="p-4 border-b border-gray-200">
                <h3 className="text-lg font-medium">Available Workers</h3>
                {activeTask ? (
                  <p className="text-sm text-gray-500 mt-1">
                    Select workers for: <span className="font-medium">{activeTask.title}</span>
                  </p>
                ) : (
                  <p className="text-sm text-gray-500 mt-1">
                    Select a task first to assign workers
                  </p>
                )}
              </div>
              
              <div className="p-4 border-b border-gray-200">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search workers..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>
              
              <div className="p-4">
                {!activeTask ? (
                  <div className="text-center py-6 text-gray-500">
                    <User className="h-12 w-12 mx-auto mb-2 text-gray-400" />
                    <p>Select a task to see eligible workers</p>
                  </div>
                ) : filteredWorkers.length === 0 ? (
                  <div className="text-center py-6 text-gray-500">
                    <User className="h-12 w-12 mx-auto mb-2 text-gray-400" />
                    <p>No workers match your search</p>
                  </div>
                ) : (
                  <div className="space-y-3 max-h-[600px] overflow-y-auto">
                    {filteredWorkers.map(worker => {
                      const isQualified = activeTask ? isWorkerQualifiedForTask(worker, activeTask) : false;
                      const isSelected = activeTaskId && taskAssignments[activeTaskId]?.includes(worker.id);
                      
                      return (
                        <div 
                          key={worker.id} 
                          className={`p-3 border rounded-lg cursor-pointer ${
                            isSelected ? 'border-blue-500 bg-blue-50' :
                            !worker.availability ? 'bg-gray-100 opacity-60' : 
                            !isQualified ? 'bg-red-50 border-red-100' : 'hover:border-gray-300'
                          }`}
                          onClick={() => {
                            if (worker.availability && isQualified && activeTaskId) {
                              handleWorkerSelection(worker.id);
                            }
                          }}
                        >
                          <div className="flex justify-between items-center">
                            <div>
                              <div className="flex items-center">
                                {activeTaskId && (
                                  <input
                                    type="checkbox"
                                    checked={isSelected===''|| isSelected === null? false: isSelected}
                                    onChange={() => handleWorkerSelection(worker.id)}
                                    disabled={!worker.availability || !isQualified}
                                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded mr-2"
                                    onClick={(e) => e.stopPropagation()}
                                  />
                                )}
                                <h4 className="font-medium">{worker.name}</h4>
                              </div>
                              <p className="text-xs text-gray-600">{worker.trade}</p>
                              
                              {!worker.availability && (
                                <p className="text-xs text-red-500 mt-1">Not available</p>
                              )}
                              
                              {activeTask && !isQualified && worker.availability && (
                                <p className="text-xs text-red-500 mt-1">Missing qualifications</p>
                              )}
                            </div>
                            
                            <div className="flex items-center">
                              {isSelected && (
                                <CheckCircle className="h-5 w-5 text-blue-600" />
                              )}
                            </div>
                          </div>
                          
                          {worker.certifications.length > 0 && (
                            <div className="mt-2">
                              <div className="flex flex-wrap gap-1">
                                {worker.certifications.map(cert => (
                                  <span 
                                    key={cert} 
                                    className={`text-xs px-2 py-1 rounded ${
                                      activeTask && activeTask.requiredCertifications.includes(cert)
                                        ? 'bg-green-100 text-green-800'
                                        : 'bg-gray-100 text-gray-800'
                                    }`}
                                  >
                                    {cert}
                                  </span>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
              
              <div className="p-4 border-t border-gray-200">
                <button
                  className={`w-full py-2 px-4 rounded-md ${
                    selectedTaskIds.length > 0 && !hasEmptyAssignments()
                      ? 'bg-blue-600 hover:bg-blue-700 text-white'
                      : 'bg-gray-200 text-gray-500 cursor-not-allowed'
                  }`}
                  disabled={selectedTaskIds.length === 0 || hasEmptyAssignments() || submitting}
                  onClick={handleSubmit}
                >
                  {submitting ? 'Submitting...' : `Assign Workers to ${selectedTaskIds.length} Task${selectedTaskIds.length !== 1 ? 's' : ''}`}
                </button>
                
                {selectedTaskIds.length > 0 && (
                  <div className="mt-2">
                    <p className="text-sm text-center text-gray-600">
                      {getTotalAssignedWorkers()} worker{getTotalAssignedWorkers() !== 1 ? 's' : ''} assigned across {selectedTaskIds.length} task{selectedTaskIds.length !== 1 ? 's' : ''}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );

  // Helper function to check if any selected task has no workers assigned
  function hasEmptyAssignments() {
    return selectedTaskIds.some(taskId => 
      !taskAssignments[taskId] || taskAssignments[taskId].length === 0
    );
  }

  // Helper function to get total number of worker assignments
  function getTotalAssignedWorkers() {
    return Object.values(taskAssignments).reduce((total, workers) => total + workers.length, 0);
  }
};

export default TaskScheduling;

