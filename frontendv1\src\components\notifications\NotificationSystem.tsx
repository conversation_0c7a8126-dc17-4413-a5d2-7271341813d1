import React, { useState, useCallback } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  Bell,
  Clock,
  User,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Loader2,
  AlertCircle
} from 'lucide-react';
import { useNotifications } from '../../hooks/useNotifications';
import {
  AppNotification,
  NotificationSystemProps,
  NotificationPriority,
} from '../../types/notifications';

const NotificationSystem: React.FC<NotificationSystemProps> = ({
  position: _position = 'top-right',
  maxNotifications = 5,
  // autoHideDelay = 5000,
  // enableSound = true,
  enableRealTime = true
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const navigate = useNavigate();

  // Use the real notification hook
  const {
    notifications,
    loading,
    error,
    unreadCount,
    markAsRead,
    markAllAsRead
  } = useNotifications({
    take: maxNotifications * 2, // Get more than we display for better UX
    enableRealTime,
    autoRefresh: true,
    refreshInterval: 30000
  });

  // Handle notification click
  const handleNotificationClick = useCallback(async (notification: AppNotification) => {
    if (!notification.readAt) {
      try {
        await markAsRead(notification.id);
      } catch (error) {
        console.error('Failed to mark notification as read:', error);
      }
    }

    // Close dropdown
    setIsOpen(false);

    // Navigate to action URL if available
    if (notification.actionUrl) {
      navigate(notification.actionUrl);
    }
  }, [markAsRead]);

  // Auto-hide low priority notifications
  // const handleAutoHide = useCallback((notification: AppNotification) => {
  //   if (notification.priority === NotificationPriority.LOW && autoHideDelay > 0) {
  //     setTimeout(() => {
  //       if (!notification.readAt) {
  //         markAsRead(notification.id);
  //       }
  //     }, autoHideDelay);
  //   }
  // }, [autoHideDelay, markAsRead]);

  // Parse notification metadata
  // const parseMetadata = useCallback((notification: AppNotification): any => {
  //   if (!notification.metadata) return {};

  //   try {
  //     return notification.metadata;
  //   } catch {
  //     return {};
  //   }
  // }, []);

  const getNotificationIcon = (type: string) => {
    const iconSize = "h-5 w-5";
    switch (type) {
      case 'training_expiring':
        return <Clock className={iconSize} />;
      case 'training_expired':
        return <AlertTriangle className={iconSize} />;
      case 'training_assigned':
      case 'worker_added':
        return <User className={iconSize} />;
      case 'training_completed':
        return <CheckCircle className={iconSize} />;
      case 'system_alert':
        return <AlertCircle className={iconSize} />;
      case 'reminder':
        return <Calendar className={iconSize} />;
      default:
        return <Bell className={iconSize} />;
    }
  };

  const getPriorityStyles = (priority: NotificationPriority) => {
    switch (priority) {
      case NotificationPriority.CRITICAL:
        return {
          icon: 'text-red-600 bg-red-100',
          border: 'border-l-red-500',
          badge: 'bg-red-100 text-red-800 border-red-200'
        };
      case NotificationPriority.HIGH:
        return {
          icon: 'text-orange-600 bg-orange-100',
          border: 'border-l-orange-500',
          badge: 'bg-orange-100 text-orange-800 border-orange-200'
        };
      case NotificationPriority.MEDIUM:
        return {
          icon: 'text-yellow-600 bg-yellow-100',
          border: 'border-l-yellow-500',
          badge: 'bg-yellow-100 text-yellow-800 border-yellow-200'
        };
      case NotificationPriority.LOW:
        return {
          icon: 'text-blue-600 bg-blue-100',
          border: 'border-l-blue-500',
          badge: 'bg-blue-100 text-blue-800 border-blue-200'
        };
      default:
        return {
          icon: 'text-gray-600 bg-gray-100',
          border: 'border-l-gray-500',
          badge: 'bg-gray-100 text-gray-800 border-gray-200'
        };
    }
  };

  const getPriorityLabel = (priority: NotificationPriority) => {
    switch (priority) {
      case NotificationPriority.CRITICAL: return 'Critical';
      case NotificationPriority.HIGH: return 'High';
      case NotificationPriority.MEDIUM: return 'Medium';
      case NotificationPriority.LOW: return 'Low';
      default: return 'Normal';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 60000);
    const diffHours = Math.floor(diffMs / 3600000);
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    if (diffDays < 7) return `${diffDays}d ago`;
    return date.toLocaleDateString();
  };



  // const getPositionClasses = () => {
  //   switch (position) {
  //     case 'top-left': return 'top-4 left-4';
  //     case 'top-right': return 'top-4 right-4';
  //     case 'bottom-left': return 'bottom-4 left-4';
  //     case 'bottom-right': return 'bottom-4 right-4';
  //     default: return 'top-4 right-4';
  //   }
  // };

  return (
    <>
      {/* Notification Bell */}
      <div className="relative">
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="relative inline-flex items-center justify-center p-2 rounded-lg text-gray-600 hover:text-green-600 hover:bg-gray-50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
          title="Notifications"
        >
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-medium shadow-sm">
              {unreadCount > 9 ? '9+' : unreadCount}
            </span>
          )}
        </button>

        {/* Notification Dropdown - Simplified */}
        {isOpen && (
          <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-xl border border-gray-200 z-50 overflow-hidden">
            {/* Header */}
            <div className="px-4 py-3 border-b border-gray-100">
              <div className="flex items-center justify-between">
                <h3 className="text-sm font-semibold text-gray-900">Notifications</h3>
                <div className="flex items-center space-x-2">
                  {unreadCount > 0 && (
                    <>
                      <span className="text-xs text-gray-500">{unreadCount} new</span>
                      <button
                        onClick={markAllAsRead}
                        className="text-xs text-blue-600 hover:text-blue-700 font-medium"
                      >
                        Mark all read
                      </button>
                    </>
                  )}
                  <Link
                    to="/notifications"
                    onClick={() => setIsOpen(false)}
                    className="text-xs text-green-600 hover:text-green-700 font-medium"
                  >
                    View all
                  </Link>
                </div>
              </div>
            </div>

            {/* Notifications List */}
            <div className="max-h-80 overflow-y-auto">
              {loading ? (
                <div className="p-6 text-center">
                  <Loader2 className="h-8 w-8 text-gray-400 mx-auto mb-2 animate-spin" />
                  <p className="text-sm text-gray-500">Loading notifications...</p>
                </div>
              ) : error ? (
                <div className="p-6 text-center">
                  <AlertCircle className="h-8 w-8 text-red-400 mx-auto mb-2" />
                  <p className="text-sm text-red-600">Failed to load notifications</p>
                  <button
                    onClick={() => window.location.reload()}
                    className="text-xs text-red-600 hover:text-red-700 mt-1"
                  >
                    Try again
                  </button>
                </div>
              ) : notifications.length === 0 ? (
                <div className="p-6 text-center">
                  <Bell className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm text-gray-500">No notifications</p>
                </div>
              ) : (
                notifications.slice(0, 4).map((notification) => {
                  const priorityStyles = getPriorityStyles(notification.priority);
                  // const metadata = parseMetadata(notification);

                  return (
                    <div
                      key={notification.id}
                      className={`p-3 border-b border-gray-100 hover:bg-gray-50 transition-colors cursor-pointer ${!notification.readAt ? 'bg-blue-50/30' : 'bg-white'
                        }`}
                      onClick={() => handleNotificationClick(notification)}
                    >
                      <div className="flex items-start space-x-3">
                        {/* Icon */}
                        <div className={`flex-shrink-0 p-1.5 rounded-lg ${priorityStyles.icon}`}>
                          {getNotificationIcon(notification.type)}
                        </div>

                        {/* Content */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <h4 className="text-sm font-medium text-gray-900 truncate">
                                {notification.title}
                              </h4>
                              <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                                {notification.message}
                              </p>
                              <div className="flex items-center space-x-2 mt-1">
                                <span className="text-xs text-gray-500">
                                  {formatTimestamp(notification.createdAt)}
                                </span>
                                {(notification.priority === NotificationPriority.HIGH || notification.priority === NotificationPriority.CRITICAL) && (
                                  <span className={`inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium ${priorityStyles.badge}`}>
                                    {getPriorityLabel(notification.priority)}
                                  </span>
                                )}
                              </div>
                            </div>
                            {!notification.readAt && (
                              <div className="w-2 h-2 bg-blue-500 rounded-full ml-2 mt-1"></div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })
              )}

              {/* View All Footer */}
              {notifications.length > 4 && (
                <div className="p-3 bg-gray-50 border-t border-gray-100">
                  <Link
                    to="/notifications"
                    onClick={() => setIsOpen(false)}
                    className="block text-center text-sm text-green-600 hover:text-green-700 font-medium"
                  >
                    View all {notifications.length} notifications
                  </Link>
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Click outside to close */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </>
  );
};

export default NotificationSystem;
