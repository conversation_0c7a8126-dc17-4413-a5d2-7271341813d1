import React from 'react';
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';
import {
  Bell,
  Clock,
  User,
  AlertTriangle,
  CheckCircle,
  Calendar,
  AlertCircle,
  X
} from 'lucide-react';
import { AppNotification, NotificationPriority } from '../../types/notifications';

interface NotificationToastProps {
  notification: AppNotification;
  onClose?: () => void;
  onMarkAsRead?: (id: number) => void;
}

const NotificationToast: React.FC<NotificationToastProps> = ({
  notification,
  onClose,
  onMarkAsRead
}) => {
  const getNotificationIcon = (type: string) => {
    const iconSize = "h-5 w-5";
    switch (type) {
      case 'training_expiring':
        return <Clock className={iconSize} />;
      case 'training_expired':
        return <AlertTriangle className={iconSize} />;
      case 'training_assigned':
      case 'training_completed':
      case 'worker_added':
        return <User className={iconSize} />;
      case 'system_alert':
        return <AlertCircle className={iconSize} />;
      case 'permit_expiring':
      case 'permit_expired':
        return <CheckCircle className={iconSize} />;
      case 'safety_incident':
        return <AlertTriangle className={iconSize} />;
      case 'reminder':
        return <Calendar className={iconSize} />;
      default:
        return <Bell className={iconSize} />;
    }
  };

  const getPriorityStyles = (priority: NotificationPriority) => {
    switch (priority) {
      case NotificationPriority.CRITICAL:
        return {
          icon: 'text-red-600 bg-red-100',
          border: 'border-l-red-500',
          bg: 'bg-red-50'
        };
      case NotificationPriority.HIGH:
        return {
          icon: 'text-orange-600 bg-orange-100',
          border: 'border-l-orange-500',
          bg: 'bg-orange-50'
        };
      case NotificationPriority.MEDIUM:
        return {
          icon: 'text-yellow-600 bg-yellow-100',
          border: 'border-l-yellow-500',
          bg: 'bg-yellow-50'
        };
      case NotificationPriority.LOW:
        return {
          icon: 'text-blue-600 bg-blue-100',
          border: 'border-l-blue-500',
          bg: 'bg-blue-50'
        };
      default:
        return {
          icon: 'text-gray-600 bg-gray-100',
          border: 'border-l-gray-500',
          bg: 'bg-gray-50'
        };
    }
  };

  const priorityStyles = getPriorityStyles(notification.priority);

  const handleMarkAsRead = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onMarkAsRead) {
      onMarkAsRead(notification.id);
    }
  };

  const handleClose = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onClose) {
      onClose();
    }
  };

  const navigate = useNavigate();

  const handleClick = () => {
    if (notification.actionUrl) {
      navigate(notification.actionUrl);
    }
  };

  return (
    <div
      className={`relative p-4 border-l-4 ${priorityStyles.border} ${priorityStyles.bg} rounded-r-lg shadow-lg cursor-pointer hover:shadow-xl transition-shadow`}
      onClick={handleClick}
    >
      {/* Close button */}
      <button
        onClick={handleClose}
        className="absolute top-2 right-2 p-1 text-gray-400 hover:text-gray-600 rounded-full hover:bg-white/50 transition-colors"
      >
        <X className="h-4 w-4" />
      </button>

      <div className="flex items-start space-x-3 pr-6">
        {/* Icon */}
        <div className={`flex-shrink-0 p-2 rounded-lg ${priorityStyles.icon}`}>
          {getNotificationIcon(notification.type)}
        </div>

        {/* Content */}
        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between mb-1">
            <h4 className="text-sm font-semibold text-gray-900 truncate">
              {notification.title}
            </h4>
          </div>

          <p className="text-sm text-gray-700 mb-2 line-clamp-2">
            {notification.message}
          </p>

          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-500">
              Just now
            </span>

            <div className="flex items-center space-x-2">
              {notification.actionLabel && (
                <span className="text-xs text-blue-600 font-medium">
                  {notification.actionLabel}
                </span>
              )}

              {!notification.readAt && (
                <button
                  onClick={handleMarkAsRead}
                  className="text-xs text-gray-500 hover:text-gray-700 font-medium"
                >
                  Mark as read
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Toast notification functions
export const showNotificationToast = (
  notification: AppNotification,
  onMarkAsRead?: (id: number) => void
) => {
  const toastId = `notification-${notification.id}`;

  // Determine toast type based on priority
  const toastType = (() => {
    switch (notification.priority) {
      case NotificationPriority.CRITICAL:
        return 'error';
      case NotificationPriority.HIGH:
        return 'warning';
      case NotificationPriority.MEDIUM:
        return 'info';
      case NotificationPriority.LOW:
      default:
        return 'default';
    }
  })();

  // Determine auto-close delay based on priority
  const autoClose = (() => {
    switch (notification.priority) {
      case NotificationPriority.CRITICAL:
        return false; // Don't auto-close critical notifications
      case NotificationPriority.HIGH:
        return 10000; // 10 seconds
      case NotificationPriority.MEDIUM:
        return 7000; // 7 seconds
      case NotificationPriority.LOW:
      default:
        return 5000; // 5 seconds
    }
  })();

  toast(
    <NotificationToast
      notification={notification}
      onMarkAsRead={onMarkAsRead}
      onClose={() => toast.dismiss(toastId)}
    />,
    {
      toastId,
      type: toastType,
      autoClose,
      closeButton: false,
      className: 'notification-toast',

      hideProgressBar: true,
      position: 'top-right',
    }
  );
};

export const dismissNotificationToast = (notificationId: number) => {
  toast.dismiss(`notification-${notificationId}`);
};

export default NotificationToast;
