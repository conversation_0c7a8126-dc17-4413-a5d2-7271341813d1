# Notification System

A comprehensive real-time notification system for the Workforce Management application.

## Features

- **Real-time Notifications**: WebSocket-based real-time notification delivery
- **Multiple Channels**: In-app, email, and SMS notification support
- **Priority Levels**: Critical, High, Medium, and Low priority notifications
- **User Preferences**: Configurable notification preferences per notification type
- **Error Handling**: Comprehensive error handling with retry logic
- **Toast Notifications**: Visual toast notifications with sound alerts
- **Browser Notifications**: System notifications when app is not active
- **Mobile Support**: Responsive design for mobile devices

## Components

### NotificationSystem
Main notification bell component that shows unread count and dropdown list.

```tsx
import NotificationSystem from './components/notifications/NotificationSystem';

<NotificationSystem 
  position="top-right"
  maxNotifications={5}
  autoHideDelay={5000}
  enableSound={true}
  enableRealTime={true}
/>
```

### NotificationProvider
Context provider that manages real-time subscriptions and toast notifications.

```tsx
import NotificationProvider from './components/notifications/NotificationProvider';

<NotificationProvider enableToasts={true} enableSounds={true}>
  <App />
</NotificationProvider>
```

### NotificationToast
Individual toast notification component with actions.

```tsx
import { showNotificationToast } from './components/notifications/NotificationToast';

showNotificationToast(notification, markAsRead);
```

## Hooks

### useNotifications
Main hook for fetching and managing notifications.

```tsx
import { useNotifications } from './hooks/useNotifications';

const { 
  notifications, 
  loading, 
  error, 
  unreadCount, 
  markAsRead, 
  markAllAsRead,
  refetch 
} = useNotifications({
  take: 50,
  enableRealTime: true,
  autoRefresh: true
});
```

### useNotificationPreferences
Hook for managing user notification preferences.

```tsx
import { useNotificationPreferences } from './hooks/useNotificationPreferences';

const { 
  preferences, 
  loading, 
  error, 
  updatePreferences, 
  sendTestNotification 
} = useNotificationPreferences();
```

## Testing

### Running Tests

```bash
# Run all notification tests
npm test -- --testPathPattern=notifications

# Run specific test file
npm test notifications.test.tsx

# Run tests in watch mode
npm test -- --watch --testPathPattern=notifications
```

### Test Scenarios

The test suite covers:

1. **Hook Testing**
   - Loading notifications
   - Marking notifications as read
   - Real-time subscription handling
   - Error handling and retry logic

2. **Component Testing**
   - Notification system rendering
   - Toast notification display
   - User interactions (click, mark as read)
   - Preference management

3. **Integration Testing**
   - Complete notification flow
   - Real-time updates
   - Error recovery

### Test Utilities

Use the provided test utilities for consistent testing:

```tsx
import { 
  createMockNotification,
  createNotificationMocks,
  createHighPriorityNotificationScenario
} from '../utils/notificationTestUtils';

// Create mock data
const notification = createMockNotification({
  priority: NotificationPriority.HIGH,
  type: 'training_expiring'
});

// Create GraphQL mocks
const mocks = createNotificationMocks([notification], 1);

// Use predefined scenarios
const { notifications, mocks } = createHighPriorityNotificationScenario();
```

## Manual Testing

### Basic Functionality

1. **Notification Display**
   - Navigate to any page in the application
   - Check that the notification bell appears in the header
   - Verify unread count badge shows correct number

2. **Notification Dropdown**
   - Click the notification bell
   - Verify dropdown opens with list of notifications
   - Check that unread notifications are highlighted
   - Verify "Mark all as read" button appears when there are unread notifications

3. **Mark as Read**
   - Click on an unread notification
   - Verify it's marked as read (highlighting removed)
   - Check that unread count decreases

4. **Real-time Updates**
   - Open the application in two browser tabs
   - In one tab, create a new notification (if admin)
   - Verify the notification appears in the other tab without refresh

### Notification Preferences

1. **Access Preferences**
   - Go to Notifications page
   - Click "Preferences" button
   - Verify preferences page loads

2. **Update Preferences**
   - Toggle different notification channels (In-App, Email, SMS)
   - Change minimum priority levels
   - Click "Save" and verify success message

3. **Test Notifications**
   - Click "Send Test Notification" button
   - Verify test notification appears
   - Check that it respects your preference settings

### Error Handling

1. **Network Errors**
   - Disconnect internet connection
   - Try to mark notifications as read
   - Verify error messages appear
   - Reconnect and verify retry works

2. **WebSocket Errors**
   - Block WebSocket connections in browser dev tools
   - Verify fallback to polling works
   - Check that notifications still update

### Browser Notifications

1. **Permission Request**
   - Clear browser notification permissions
   - Reload the application
   - Verify permission request appears

2. **System Notifications**
   - Minimize or switch to another tab
   - Create a critical priority notification
   - Verify system notification appears

### Sound Notifications

1. **Sound Playback**
   - Ensure browser allows audio playback
   - Create high or critical priority notification
   - Verify notification sound plays

2. **Sound Preferences**
   - Go to notification settings
   - Toggle sound notifications off
   - Create notification and verify no sound plays

## Troubleshooting

### Common Issues

1. **Notifications Not Loading**
   - Check browser console for GraphQL errors
   - Verify backend notification service is running
   - Check network connectivity

2. **Real-time Not Working**
   - Verify WebSocket connection in Network tab
   - Check if WebSocket URL is correct in environment variables
   - Ensure backend WebSocket server is running

3. **Browser Notifications Blocked**
   - Check browser notification permissions
   - Guide users to enable notifications in browser settings
   - Provide fallback to in-app notifications only

4. **Sounds Not Playing**
   - Check if sound files exist in `/public/sounds/`
   - Verify browser allows audio playback
   - Check if user has sound preferences disabled

### Performance Issues

1. **Too Many Notifications**
   - Implement pagination for large notification lists
   - Consider archiving old notifications
   - Optimize GraphQL queries with proper limits

2. **Memory Leaks**
   - Ensure WebSocket connections are properly closed
   - Clean up event listeners in useEffect cleanup
   - Monitor component unmounting

## Configuration

### Environment Variables

```env
# GraphQL endpoints
VITE_GRAPHQL_URI_1=http://localhost:4000/graphql
VITE_GRAPHQL_WS_URI=ws://localhost:4000/graphql

# Notification settings
VITE_NOTIFICATION_SOUND_ENABLED=true
VITE_NOTIFICATION_TOAST_POSITION=top-right
VITE_NOTIFICATION_AUTO_CLOSE_DELAY=5000
```

### Backend Requirements

The notification system requires the following backend GraphQL operations:

- `myNotifications` query
- `unreadNotificationCount` query
- `markNotificationAsRead` mutation
- `markAllNotificationsAsRead` mutation
- `onNotification` subscription
- `notificationPreferences` query
- `updateNotificationPreferences` mutation

## Best Practices

1. **Performance**
   - Use pagination for large notification lists
   - Implement proper caching strategies
   - Debounce real-time updates

2. **User Experience**
   - Provide clear visual feedback for actions
   - Use appropriate priority levels
   - Respect user preferences

3. **Error Handling**
   - Always provide fallback mechanisms
   - Show meaningful error messages
   - Implement retry logic for transient errors

4. **Testing**
   - Test all notification types and priorities
   - Verify error scenarios
   - Test on different devices and browsers
