import React, { useState } from 'react';
import { Download, Calendar, DollarSign, Clock, FileText, Users, Filter } from 'lucide-react';
import { Worker } from '../../types';

interface SitePayrollRecord {
  workerId: number;
  workerName: string;
  nationalId: string;
  trade: string;
  company: string;
  regularHours: number;
  overtimeHours: number;
  totalHours: number;
  grossPay: number;
  totalDeductions: number;
  netPay: number;
  status: 'draft' | 'processed' | 'paid';
}

interface SitePayrollTabProps {
  workers: Worker[];
  siteId: string;
  siteName: string;
}

const SitePayrollTab: React.FC<SitePayrollTabProps> = ({ workers, siteId: _siteId, siteName }) => {
  const [selectedPeriod, setSelectedPeriod] = useState('current-month');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [selectedTrade, setSelectedTrade] = useState<string>('all');

  // Generate mock payroll data based on workers
  const generatePayrollData = (): SitePayrollRecord[] => {
    return workers.map((worker) => {
      const baseHourlyRate = 450; // KES per hour
      const regularHours = Math.floor(Math.random() * 40) + 160; // 160-200 hours
      const overtimeHours = Math.floor(Math.random() * 20); // 0-20 OT hours
      const totalHours = regularHours + overtimeHours;
      const grossPay = (regularHours * baseHourlyRate) + (overtimeHours * baseHourlyRate * 1.5);
      
      // Calculate deductions (simplified)
      const paye = grossPay * 0.15; // 15% PAYE
      const nssf = Math.min(grossPay * 0.06, 2160); // 6% NSSF, capped
      const shif = grossPay * 0.0275; // 2.75% SHIF
      const housingLevy = grossPay * 0.015; // 1.5% Housing Levy
      const totalDeductions = paye + nssf + shif + housingLevy;
      const netPay = grossPay - totalDeductions;

      const statuses: ('draft' | 'processed' | 'paid')[] = ['draft', 'processed', 'paid'];
      const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];

      return {
        workerId: worker.id,
        workerName: worker.name,
        nationalId: worker.nationalId,
        trade: worker.trades[0]?.name || 'General',
        company: worker.company,
        regularHours,
        overtimeHours,
        totalHours,
        grossPay: Math.round(grossPay),
        totalDeductions: Math.round(totalDeductions),
        netPay: Math.round(netPay),
        status: randomStatus
      };
    });
  };

  const payrollData = generatePayrollData();

  // Filter payroll data
  const filteredPayrollData = payrollData.filter(record => {
    const statusMatch = selectedStatus === 'all' || record.status === selectedStatus;
    const tradeMatch = selectedTrade === 'all' || record.trade === selectedTrade;
    return statusMatch && tradeMatch;
  });

  // Get unique trades for filter
  const uniqueTrades = Array.from(new Set(payrollData.map(record => record.trade)));

  // Calculate totals
  const totals = filteredPayrollData.reduce(
    (acc, record) => ({
      totalHours: acc.totalHours + record.totalHours,
      grossPay: acc.grossPay + record.grossPay,
      totalDeductions: acc.totalDeductions + record.totalDeductions,
      netPay: acc.netPay + record.netPay,
    }),
    { totalHours: 0, grossPay: 0, totalDeductions: 0, netPay: 0 }
  );

  const handleExportPayroll = () => {
    // Create CSV content
    const headers = [
      'Worker Name',
      'National ID',
      'Trade',
      'Company',
      'Regular Hours',
      'Overtime Hours',
      'Total Hours',
      'Gross Pay (KES)',
      'Total Deductions (KES)',
      'Net Pay (KES)',
      'Status'
    ];

    const csvContent = [
      headers.join(','),
      ...filteredPayrollData.map(record => [
        `"${record.workerName}"`,
        record.nationalId,
        `"${record.trade}"`,
        `"${record.company}"`,
        record.regularHours,
        record.overtimeHours,
        record.totalHours,
        record.grossPay,
        record.totalDeductions,
        record.netPay,
        record.status
      ].join(','))
    ].join('\n');

    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${siteName}_Payroll_${selectedPeriod}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleExportManHours = () => {
    // Create CSV content for man hours
    const headers = [
      'Worker Name',
      'National ID',
      'Trade',
      'Company',
      'Regular Hours',
      'Overtime Hours',
      'Total Hours',
      'Lifetime Man Hours'
    ];

    const csvContent = [
      headers.join(','),
      ...workers.map(worker => {
        const payrollRecord = payrollData.find(p => p.workerId === worker.id);
        return [
          `"${worker.name}"`,
          worker.nationalId,
          `"${worker.trades[0]?.name || 'General'}"`,
          `"${worker.company}"`,
          payrollRecord?.regularHours || 0,
          payrollRecord?.overtimeHours || 0,
          payrollRecord?.totalHours || 0,
          worker.manHours
        ].join(',');
      })
    ].join('\n');

    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${siteName}_ManHours_${selectedPeriod}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <div className="space-y-6">
      {/* Controls and Filters */}
      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div className="flex flex-wrap items-center gap-4">
          {/* Period Selector */}
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-gray-500" />
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="border border-gray-300 rounded-md text-sm p-2"
            >
              <option value="current-month">Current Month</option>
              <option value="last-month">Last Month</option>
              <option value="last-3-months">Last 3 Months</option>
              <option value="custom">Custom Range</option>
            </select>
          </div>

          {/* Status Filter */}
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value)}
              className="border border-gray-300 rounded-md text-sm p-2"
            >
              <option value="all">All Status</option>
              <option value="draft">Draft</option>
              <option value="processed">Processed</option>
              <option value="paid">Paid</option>
            </select>
          </div>

          {/* Trade Filter */}
          <select
            value={selectedTrade}
            onChange={(e) => setSelectedTrade(e.target.value)}
            className="border border-gray-300 rounded-md text-sm p-2"
          >
            <option value="all">All Trades</option>
            {uniqueTrades.map(trade => (
              <option key={trade} value={trade}>{trade}</option>
            ))}
          </select>
        </div>

        {/* Export Buttons */}
        <div className="flex space-x-3">
          <button
            onClick={handleExportManHours}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
          >
            <Clock className="h-4 w-4 mr-2" />
            Export Man Hours
          </button>
          <button
            onClick={handleExportPayroll}
            className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700"
          >
            <Download className="h-4 w-4 mr-2" />
            Export Payroll
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Workers</p>
              <p className="text-2xl font-semibold text-gray-900">{filteredPayrollData.length}</p>
            </div>
            <Users className="h-8 w-8 text-blue-500" />
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Hours</p>
              <p className="text-2xl font-semibold text-gray-900">{totals.totalHours.toLocaleString()}</p>
            </div>
            <Clock className="h-8 w-8 text-green-500" />
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Gross Payroll</p>
              <p className="text-2xl font-semibold text-gray-900">KES {totals.grossPay.toLocaleString()}</p>
            </div>
            <DollarSign className="h-8 w-8 text-yellow-500" />
          </div>
        </div>
        
        <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">Net Payroll</p>
              <p className="text-2xl font-semibold text-gray-900">KES {totals.netPay.toLocaleString()}</p>
            </div>
            <FileText className="h-8 w-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Payroll Table */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Worker
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Trade
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Hours
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Gross Pay
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Deductions
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Net Pay
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredPayrollData.map((record) => (
                <tr key={record.workerId} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{record.workerName}</div>
                      <div className="text-sm text-gray-500">ID: {record.nationalId}</div>
                      <div className="text-sm text-gray-500">{record.company}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {record.trade}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div>
                      <div className="font-medium">{record.totalHours}h total</div>
                      <div className="text-gray-500">{record.regularHours}h reg + {record.overtimeHours}h OT</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    KES {record.grossPay.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">
                    -KES {record.totalDeductions.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                    KES {record.netPay.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      record.status === 'paid' ? 'bg-green-100 text-green-800' :
                      record.status === 'processed' ? 'bg-blue-100 text-blue-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {record.status.charAt(0).toUpperCase() + record.status.slice(1)}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default SitePayrollTab;
