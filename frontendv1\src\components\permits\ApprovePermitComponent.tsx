import React, { useState } from 'react';
import { Calendar, Check, X, ChevronRight, ChevronDown, File as FileIcon, Folder as FolderIcon, ChevronLeft, Menu } from 'lucide-react';
import { Folder, File, ApprovePermitComponentProps } from '../../types/approvePermit';

const ApprovePermitComponent: React.FC<ApprovePermitComponentProps> = ({
  folders,
  showButtons,
  onFileClick,
  onDateClick,
  documentTitle = "Document Viewer",
  documentChildren,
  extraDetailsChildren
}) => {
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set());
  const [selectedTopLevelFolder, setSelectedTopLevelFolder] = useState<string | null>(null);
  const [showExtraDetails, setShowExtraDetails] = useState(false);
  const [calendarOpen, setCalendarOpen] = useState(false);
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [showArea1, setShowArea1] = useState(true);
  const [selectedFile, setSelectedFile] = useState<string | null>(null);
  const [reviewMode, setReviewMode] = useState(false);
  const [reviewComment, setReviewComment] = useState('');

  const toggleFolder = (folderId: string) => {
    const newExpanded = new Set(expandedFolders);
    if (newExpanded.has(folderId)) {
      newExpanded.delete(folderId);
    } else {
      newExpanded.add(folderId);
    }
    setExpandedFolders(newExpanded);
  };

  const handleFileClick = (file: File, path: string) => {
    setSelectedFile(file.id);
    if (onFileClick) {
      onFileClick(file.id, path, file.name);
    }
  };

  const handleTopLevelFolderClick = (folderId: string) => {
    setSelectedTopLevelFolder(folderId);
  };

  const handleCalendarClick = () => {
    if (showExtraDetails && calendarOpen) {
      // If calendar is open, close area 3
      setShowExtraDetails(false);
      setCalendarOpen(false);
    } else {
      // Open area 3 with calendar
      setShowExtraDetails(true);
      setCalendarOpen(true);
      setReviewMode(false); // Close review mode if open
    }
  };

  const handleReviewClick = () => {
    setShowExtraDetails(true);
    setReviewMode(true);
    setCalendarOpen(false); // Close calendar if open
  };

  const handleApprove = () => {
    console.log('Approving permit for folder:', selectedTopLevelFolder);
    console.log('Review comment:', reviewComment);
    // Reset review mode after approval
    setReviewMode(false);
    setShowExtraDetails(false);
    setReviewComment('');
  };

  const handleDisapprove = () => {
    console.log('Disapproving permit for folder:', selectedTopLevelFolder);
    console.log('Review comment:', reviewComment);
    // Reset review mode after disapproval
    setReviewMode(false);
    setShowExtraDetails(false);
    setReviewComment('');
  };

  // Calendar helper functions
  const getDaysInMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth() + 1, 0).getDate();
  };

  const getFirstDayOfMonth = (date: Date) => {
    return new Date(date.getFullYear(), date.getMonth(), 1).getDay();
  };

  const handleDateClick = (day: number) => {
    const clickedDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), day);
    setSelectedDate(clickedDate);
    if (onDateClick) {
      onDateClick(clickedDate);
    }
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1);
      } else {
        newDate.setMonth(prev.getMonth() + 1);
      }
      return newDate;
    });
  };

  const navigateYear = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev);
      if (direction === 'prev') {
        newDate.setFullYear(prev.getFullYear() - 1);
      } else {
        newDate.setFullYear(prev.getFullYear() + 1);
      }
      return newDate;
    });
  };

  const isToday = (day: number) => {
    const today = new Date();
    return today.getDate() === day &&
           today.getMonth() === currentDate.getMonth() &&
           today.getFullYear() === currentDate.getFullYear();
  };

  const isSelected = (day: number) => {
    if (!selectedDate) return false;
    return selectedDate.getDate() === day &&
           selectedDate.getMonth() === currentDate.getMonth() &&
           selectedDate.getFullYear() === currentDate.getFullYear();
  };

  const renderFolder = (folder: Folder, level: number = 0, path: string = ""): React.ReactNode => {
    const isExpanded = expandedFolders.has(folder.id);
    const currentPath = path ? `${path}/${folder.name}` : folder.name;
    const isTopLevel = level === 0;

    return (
      <div key={folder.id} className="select-none">
        <div
          className={`flex items-center py-1 px-2 hover:bg-gray-100 cursor-pointer rounded ${
            isTopLevel && selectedTopLevelFolder === folder.id ? 'bg-blue-100' : ''
          }`}
          style={{ paddingLeft: `${level * 16 + 8}px` }}
          onClick={() => {
            toggleFolder(folder.id);
            if (isTopLevel) {
              handleTopLevelFolderClick(folder.id);
            }
          }}
        >
          {folder.folders.length > 0 || folder.files.length > 0 ? (
            isExpanded ? (
              <ChevronDown className="h-4 w-4 mr-1 text-gray-500" />
            ) : (
              <ChevronRight className="h-4 w-4 mr-1 text-gray-500" />
            )
          ) : (
            <div className="w-5 mr-1" />
          )}
          <FolderIcon className="h-4 w-4 mr-2 text-blue-500" />
          <span className="text-sm text-gray-700">{folder.name}</span>
        </div>

        {isExpanded && (
          <div>
            {folder.files.map((file) => (
              <div
                key={file.id}
                className="flex items-center py-1 px-2 hover:bg-gray-100 cursor-pointer rounded"
                style={{ paddingLeft: `${(level + 1) * 16 + 8}px` }}
                onClick={() => handleFileClick(file, `${currentPath}/${file.name}`)}
              >
                <div className="w-5 mr-1" />
                <FileIcon className="h-4 w-4 mr-2 text-gray-500" />
                <span className="text-sm text-gray-600">{file.name}</span>
              </div>
            ))}
            {folder.folders.map((subFolder) =>
              renderFolder(subFolder, level + 1, currentPath)
            )}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Toolbar */}
      <div className="bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={() => setShowArea1(!showArea1)}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md"
            title="Toggle Folder View"
          >
            <Menu className="h-5 w-5" />
          </button>
          <div className="text-sm text-gray-600">
            {new Date().toLocaleDateString('en-US', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            })}
          </div>
          <button
            onClick={handleCalendarClick}
            className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-md"
            title="Open Calendar"
          >
            <Calendar className="h-5 w-5" />
          </button>
        </div>

        {showButtons && selectedFile && (
          <div className="flex space-x-2">
            <button
              onClick={handleReviewClick}
              disabled={!selectedTopLevelFolder}
              className="px-4 py-2 border border-blue-600 text-blue-600 rounded-md text-sm font-medium hover:bg-blue-50 disabled:border-gray-300 disabled:text-gray-300 disabled:cursor-not-allowed transition-colors"
            >
              Review
            </button>
          </div>
        )}
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex overflow-hidden">
        {/* Area 1: Folder View */}
        {showArea1 && (
          <div className="w-64 bg-white border-r border-gray-200 overflow-y-auto">
            <div className="p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Permit Files</h3>
              <div className="space-y-1">
                {folders.map((folder) => renderFolder(folder))}
              </div>
            </div>
          </div>
        )}

        {/* Area 2: Document View */}
        <div className="flex-1 bg-white flex flex-col overflow-hidden">
          <div className="border-b border-gray-200 px-4 py-3">
            <h2 className="text-lg font-semibold text-gray-900">{documentTitle}</h2>
          </div>
          <div className="flex-1 overflow-auto p-4">
            {documentChildren || (
              <div className="flex items-center justify-center h-full text-gray-500">
                <div className="text-center">
                  <FileIcon className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <p>Select a file to view its contents</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Area 3: Extra Details (Conditional) */}
        {showExtraDetails && (
          <div className="w-64 bg-white border-l border-gray-200 flex flex-col overflow-hidden">
            <div className="border-b border-gray-200 px-4 py-3 flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">
                {calendarOpen ? 'Calendar' : reviewMode ? 'Review' : 'Details'}
              </h3>
              <button
                onClick={() => {
                  setShowExtraDetails(false);
                  setCalendarOpen(false);
                  setReviewMode(false);
                }}
                className="p-1 text-gray-400 hover:text-gray-600 rounded"
              >
                <X className="h-5 w-5" />
              </button>
            </div>
            <div className="flex-1 overflow-auto p-4">
              {reviewMode ? (
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Review Comments
                    </label>
                    <textarea
                      value={reviewComment}
                      onChange={(e) => setReviewComment(e.target.value)}
                      placeholder="Add your review comments here..."
                      className="w-full h-32 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none"
                    />
                  </div>
                  <div className="flex space-x-2">
                    <button
                      onClick={handleApprove}
                      className="flex-1 px-4 py-2 border border-green-600 text-green-600 rounded-md text-sm font-medium hover:bg-green-50 transition-colors"
                    >
                      <Check className="h-4 w-4 mr-1 inline" />
                      Approve
                    </button>
                    <button
                      onClick={handleDisapprove}
                      className="flex-1 px-4 py-2 border border-red-600 text-red-600 rounded-md text-sm font-medium hover:bg-red-50 transition-colors"
                    >
                      <X className="h-4 w-4 mr-1 inline" />
                      Disapprove
                    </button>
                  </div>
                </div>
              ) : calendarOpen ? (
                <div className="space-y-4">
                  {/* Calendar Header */}
                  <div className="flex items-center justify-between mb-4">
                    <button
                      onClick={() => navigateYear('prev')}
                      className="p-1 hover:bg-gray-100 rounded"
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </button>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => navigateMonth('prev')}
                        className="p-1 hover:bg-gray-100 rounded"
                      >
                        <ChevronLeft className="h-3 w-3" />
                      </button>
                      <h3 className="text-lg font-semibold text-gray-900 min-w-[140px] text-center">
                        {currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                      </h3>
                      <button
                        onClick={() => navigateMonth('next')}
                        className="p-1 hover:bg-gray-100 rounded"
                      >
                        <ChevronRight className="h-3 w-3" />
                      </button>
                    </div>
                    <button
                      onClick={() => navigateYear('next')}
                      className="p-1 hover:bg-gray-100 rounded"
                    >
                      <ChevronRight className="h-4 w-4" />
                    </button>
                  </div>

                  {/* Calendar Grid */}
                  <div className="bg-white rounded-lg border border-gray-200 p-3">
                    {/* Day Headers */}
                    <div className="grid grid-cols-7 gap-1 mb-2">
                      {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                        <div key={day} className="text-center text-xs font-medium text-gray-500 py-1">
                          {day}
                        </div>
                      ))}
                    </div>

                    {/* Calendar Days */}
                    <div className="grid grid-cols-7 gap-1">
                      {/* Empty cells for days before month starts */}
                      {Array.from({ length: getFirstDayOfMonth(currentDate) }, (_, i) => (
                        <div key={`empty-${i}`} className="h-8" />
                      ))}

                      {/* Days of the month */}
                      {Array.from({ length: getDaysInMonth(currentDate) }, (_, i) => {
                        const day = i + 1;
                        return (
                          <button
                            key={day}
                            onClick={() => handleDateClick(day)}
                            className={`h-8 w-8 text-sm rounded-md flex items-center justify-center transition-colors ${
                              isSelected(day)
                                ? 'bg-blue-600 text-white'
                                : isToday(day)
                                ? 'bg-blue-100 text-blue-600 font-medium'
                                : 'hover:bg-gray-100 text-gray-700'
                            }`}
                          >
                            {day}
                          </button>
                        );
                      })}
                    </div>
                  </div>

                  {/* Selected Date Display */}
                  {selectedDate && (
                    <div className="bg-blue-50 p-3 rounded-lg">
                      <p className="text-sm text-blue-700">
                        <strong>Selected:</strong> {selectedDate.toLocaleDateString('en-US', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </p>
                    </div>
                  )}
                </div>
              ) : (
                extraDetailsChildren || (
                  <div className="text-center text-gray-500">
                    <p>No additional details available</p>
                  </div>
                )
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ApprovePermitComponent;
