import React, { useState, useMemo } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Clock } from "lucide-react";
import UniversalFilter, { TagOption } from "../shared/UniversalFilter";
import PermitCard, { PermitCardData } from "./PermitCard";

interface PendingPermitsProps {
  siteId: string;
}

interface PendingTask {
  id: string;
  title: string;
  site: string;
  scheduledDate: string;
  permitTypes: string[];
  requestedBy: string;
  description: string;
  hazards: string[];
  requiredCompetencies: string[];
  location: string; // Specific area within site
}

// Map permit types to our card component format
const permitTypeMapping: Record<string, PermitCardData['permitType']> = {
  'hot-work': 'hot-work',
  'confined-space': 'confined-space',
  'work-at-height': 'work-at-height',
  'excavation': 'excavation',
  'electrical': 'electrical',
  'general': 'general'
};

// Mock data for pending tasks
const mockPendingTasks: PendingTask[] = [
  {
    id: "TSK-1001",
    title: "Hot work welding on slab A",
    site: "Site Alpha",
    scheduledDate: "2024-08-19",
    permitTypes: ["hot-work"],
    requestedBy: "Eng. Mwangi",
    description: "Welding structural reinforcement on concrete slab A, Level 3",
    hazards: ["Hot surfaces", "Sparks and flames", "Toxic fumes", "Burns"],
    requiredCompetencies: ["Hot Work Permit", "Site Safety Induction"],
    location: "Building A - Level 3"
  },
  {
    id: "TSK-1002",
    title: "Install rooftop HVAC",
    site: "Site Beta",
    scheduledDate: "2024-08-19",
    permitTypes: ["work-at-height"],
    requestedBy: "Eng. A. Musa",
    description: "Installation of HVAC units on building rooftop, 4-story building",
    hazards: ["Falls from height", "Heavy lifting", "Electrical hazards"],
    requiredCompetencies: ["Working at Heights", "Site Safety Induction"],
    location: "Rooftop Access"
  },
  {
    id: "TSK-1003",
    title: "Excavation near utilities",
    site: "Site Alpha",
    scheduledDate: "2024-08-19",
    permitTypes: ["excavation"],
    requestedBy: "Eng. Otieno",
    description: "Excavation for foundation footing near existing utility lines",
    hazards: ["Underground utilities", "Cave-in", "Heavy machinery"],
    requiredCompetencies: ["Excavation Safety", "Site Safety Induction"],
    location: "Main Entrance"
  },
  {
    id: "TSK-1004",
    title: "Electrical panel installation",
    site: "Site Beta",
    scheduledDate: "2024-08-20",
    permitTypes: ["electrical"],
    requestedBy: "Eng. Wanjiku",
    description: "Installation of main electrical distribution panel in basement",
    hazards: ["Electrical shock", "Arc flash", "Burns"],
    requiredCompetencies: ["Electrical Certification", "LOTO Procedures", "Site Safety Induction"],
    location: "Basement - Electrical Room"
  },
  {
    id: "TSK-1005",
    title: "Confined space tank cleaning",
    site: "Site Alpha",
    scheduledDate: "2024-08-20",
    permitTypes: ["confined-space"],
    requestedBy: "Eng. Kimani",
    description: "Cleaning and inspection of water storage tank",
    hazards: ["Oxygen deficiency", "Toxic gases", "Entrapment"],
    requiredCompetencies: ["Confined Space Entry", "Gas Testing", "Emergency Response"],
    location: "Storage Area - Tank 3"
  },
  {
    id: "TSK-1006",
    title: "General maintenance work",
    site: "Site Beta",
    scheduledDate: "2024-08-21",
    permitTypes: ["general"],
    requestedBy: "Eng. Ochieng",
    description: "Routine maintenance of building facilities and equipment",
    hazards: ["Minor cuts", "Slips and falls"],
    requiredCompetencies: ["Site Safety Induction"],
    location: "Maintenance Workshop"
  }
];

const PendingPermits: React.FC<PendingPermitsProps> = ({ siteId }) => {
  const navigate = useNavigate();
  const { siteId: paramSiteId } = useParams();
  const currentSiteId = siteId || paramSiteId;

  // Search and filter state
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPermitType, setSelectedPermitType] = useState("all");

  // Permit type filter options for the new search component
  const permitTypeFilters: TagOption[] = [
    { id: "hot-work", name: "Hot Work" },
    { id: "confined-space", name: "Confined Space" },
    { id: "work-at-height", name: "Work at Height" },
    { id: "excavation", name: "Excavation" },
    { id: "electrical", name: "Electrical" },
    { id: "general", name: "General Work" }
  ];

  // Filter and search logic
  const filteredTasks = useMemo(() => {
    return mockPendingTasks.filter(task => {
      // Search filter - includes engineer name and location in search
      const matchesSearch = searchQuery === "" ||
        task.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        task.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        task.requestedBy.toLowerCase().includes(searchQuery.toLowerCase()) ||
        task.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
        task.site.toLowerCase().includes(searchQuery.toLowerCase());

      // Permit type filter
      const matchesPermitType = selectedPermitType === "all" ||
        task.permitTypes.includes(selectedPermitType);

      return matchesSearch && matchesPermitType;
    });
  }, [searchQuery, selectedPermitType]);

  // Convert tasks to card data
  const cardData: PermitCardData[] = useMemo(() => {
    return filteredTasks.map(task => ({
      id: task.id,
      title: task.title,
      description: task.description,
      permitType: permitTypeMapping[task.permitTypes[0]] || 'general',
      location: task.location, // Use specific location within site, not site name
      engineerName: task.requestedBy,
      isBookmarked: false, // Could be based on user preferences
      onClick: () => handleTaskClick(task),
      onGeneratePermit: () => handleTaskClick(task)
    }));
  }, [filteredTasks]);


  const getPermitRoute = (permitType: string) => {
    const routeMap: { [key: string]: string } = {
      'hot-work': 'hot-work',
      'work-at-height': 'work-at-height',
      'excavation': 'excavation',
      'confined-space': 'confined-space',
      'electrical': 'electrical',
      'general': 'general-work'
    };
    return routeMap[permitType] || 'general-work';
  };

  const handleTaskClick = (task: PendingTask) => {
    // Navigate directly to the permit form for this task
    if (task.permitTypes.length > 0) {
      const permitRoute = getPermitRoute(task.permitTypes[0]);
      navigate(`/sites/${currentSiteId}/${permitRoute}/form`);
    }
  };

  // Handle permit type filter change
  const handlePermitTypeChange = (type: string) => {
    setSelectedPermitType(type);
  };

  // Handle search query change
  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Pending Permits</h2>
          <p className="text-sm text-gray-600">Tasks scheduled requiring permit generation. Use the search and filters to find specific permits.</p>
        </div>
        <div className="text-sm text-gray-600">
          Total {filteredTasks.length} permits
        </div>
      </div>

      {/* Search and Filter */}
      <UniversalFilter
        variant="search-tags"
        searchQuery={searchQuery}
        onSearchChange={handleSearchChange}
        searchPlaceholder="Search by task name, description, engineer, or location..."
        tags={[{ id: "all", name: "All" }, ...permitTypeFilters]}
        selectedTagId={selectedPermitType}
        onTagChange={handlePermitTypeChange}
      />

      {/* Tasks Grid - Responsive: 1 col (mobile) -> 2 cols (small) -> 3 cols (medium/13") -> 4 cols (large) */}
      <div className="grid permits-grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6">
        {cardData.map((card) => (
          <PermitCard key={card.id} data={card} />
        ))}
      </div>

      {/* Empty state */}
      {filteredTasks.length === 0 && (
        <div className="text-center py-12">
          <div className="text-gray-400 mb-4">
            <Clock className="h-12 w-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">No pending permits found</h3>
          <p className="text-gray-600">
            {searchQuery || selectedPermitType !== "all"
              ? "Try adjusting your search or filter criteria."
              : "All tasks have permits generated or no tasks are scheduled."}
          </p>
        </div>
      )}
    </div>
  );
};

export default PendingPermits;
