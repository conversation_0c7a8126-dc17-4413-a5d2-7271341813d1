import {
  FileText,
  Shield,
  AlertTriangle,
  History,
  File,
  Image,
  Video,
  User,
  Users,
  CheckCircle,
  Camera,
  FolderOpen
} from 'lucide-react';
import { Permit } from '../../types/permits';
import { ExplorerItem } from '../shared/VSCodeInterface';

// interface PermitExplorerProps {
//   permit: Permit;
// }

const createPermitExplorerItems = (permit: Permit): ExplorerItem[] => {
  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return <File className="h-4 w-4 text-red-500" />;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return <Image className="h-4 w-4 text-blue-500" />;
      case 'mp4':
      case 'avi':
      case 'mov':
        return <Video className="h-4 w-4 text-purple-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };

  // Build the explorer tree structure for permits
  const explorerItems: ExplorerItem[] = [
    // Primary Document
    {
      id: 'permit-form',
      name: 'Permit Form',
      type: 'file',
      icon: <FileText className="h-4 w-4 text-blue-600" />,
      data: { type: 'permit-form', permit }
    },

    // Supporting Materials
    {
      id: 'assigned-workers',
      name: 'Assigned Workers',
      type: 'folder',
      icon: <Users className="h-4 w-4 text-green-600" />,
      badge: permit.assignedWorkers?.length.toString() || '0',
      badgeColor: 'bg-green-100 text-green-800',
      children: permit.assignedWorkers?.map(worker => ({
        id: `worker-${worker.workerId}`,
        name: worker.workerName,
        type: 'file' as const,
        icon: <User className="h-4 w-4 text-green-500" />,
        data: { type: 'worker', worker }
      })) || []
    },
    {
      id: 'supporting-documents',
      name: 'Supporting Documents',
      type: 'folder',
      icon: <FolderOpen className="h-4 w-4 text-gray-600" />,
      badge: permit.attachments?.length.toString() || '2',
      badgeColor: 'bg-gray-100 text-gray-800',
      children: permit.attachments?.map(doc => ({
        id: `doc-${doc.id}`,
        name: doc.name,
        type: 'file' as const,
        icon: getFileIcon(doc.name),
        data: { type: 'document', document: doc }
      })) || [
        {
          id: 'safety-checklist',
          name: 'Safety Checklist.pdf',
          type: 'file' as const,
          icon: <File className="h-4 w-4 text-red-500" />,
          data: { type: 'document', document: { id: 'safety-checklist', name: 'Safety Checklist.pdf', type: 'pdf' } }
        },
        {
          id: 'risk-assessment-doc',
          name: 'Risk Assessment.pdf',
          type: 'file' as const,
          icon: <File className="h-4 w-4 text-red-500" />,
          data: { type: 'document', document: { id: 'risk-assessment-doc', name: 'Risk Assessment.pdf', type: 'pdf' } }
        }
      ]
    },
    {
      id: 'work-photos',
      name: 'Work Photos',
      type: 'folder',
      icon: <Camera className="h-4 w-4 text-purple-600" />,
      badge: '3',
      badgeColor: 'bg-purple-100 text-purple-800',
      children: [
        {
          id: 'photo-before',
          name: 'Before Work - Site Photo.jpg',
          type: 'file' as const,
          icon: <Image className="h-4 w-4 text-blue-500" />,
          data: { type: 'photo', photo: { id: 'photo-before', name: 'Before Work - Site Photo.jpg', type: 'image' } }
        },
        {
          id: 'photo-equipment',
          name: 'Equipment Setup.jpg',
          type: 'file' as const,
          icon: <Image className="h-4 w-4 text-blue-500" />,
          data: { type: 'photo', photo: { id: 'photo-equipment', name: 'Equipment Setup.jpg', type: 'image' } }
        },
        {
          id: 'photo-safety',
          name: 'Safety Measures.jpg',
          type: 'file' as const,
          icon: <Image className="h-4 w-4 text-blue-500" />,
          data: { type: 'photo', photo: { id: 'photo-safety', name: 'Safety Measures.jpg', type: 'image' } }
        }
      ]
    },
    {
      id: 'risk-assessments',
      name: 'Risk Assessments',
      type: 'folder',
      icon: <Shield className="h-4 w-4 text-orange-600" />,
      badge: '2',
      badgeColor: 'bg-orange-100 text-orange-800',
      children: [
        {
          id: 'rams',
          name: 'RAMS Document',
          type: 'file' as const,
          icon: <Shield className="h-4 w-4 text-orange-500" />,
          data: { type: 'risk-assessment', riskAssessment: permit.riskAssessment }
        },
        {
          id: 'daily-assessment',
          name: 'Daily Risk Assessment',
          type: 'file' as const,
          icon: <AlertTriangle className="h-4 w-4 text-yellow-500" />,
          data: { type: 'daily-risk-assessment', dailyRiskAssessments: permit.dailyRiskAssessments }
        }
      ]
    },
    {
      id: 'permit-approvals',
      name: 'Permit Approvals',
      type: 'folder',
      icon: <CheckCircle className="h-4 w-4 text-green-600" />,
      badge: permit.approvals?.length.toString() || '0',
      badgeColor: 'bg-green-100 text-green-800',
      children: permit.approvals?.map((approval, index) => ({
        id: `approval-${index}`,
        name: `${approval.approverRole} Approval`,
        type: 'file' as const,
        icon: <CheckCircle className="h-4 w-4 text-green-500" />,
        data: { type: 'approval', approval }
      })) || [
        {
          id: 'supervisor-approval',
          name: 'Supervisor Approval',
          type: 'file' as const,
          icon: <CheckCircle className="h-4 w-4 text-green-500" />,
          data: { type: 'approval', approval: { approverRole: 'Supervisor', status: 'approved' } }
        },
        {
          id: 'safety-approval',
          name: 'Safety Officer Approval',
          type: 'file' as const,
          icon: <CheckCircle className="h-4 w-4 text-green-500" />,
          data: { type: 'approval', approval: { approverRole: 'Safety Officer', status: 'approved' } }
        }
      ]
    },
    {
      id: 'permit-history',
      name: 'Permit History',
      type: 'file',
      icon: <History className="h-4 w-4 text-gray-500" />,
      data: { type: 'audit-trail', permit }
    }
  ];

  return explorerItems;
};

export default createPermitExplorerItems;
