import React, { useState, useEffect, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import {
	Calendar,
	Download,
	
	Users,
	ExternalLink,
} from "lucide-react";
import { Permit, PermitFilters } from "../../types/permits";
import PermitStatusBadge from "./shared/PermitStatusBadge";
import UniversalFilter, { TagOption } from "../shared/UniversalFilter";
import Pagination from "../shared/Pagination";

interface PermitHistoryProps {
	siteId: string;
}

// Mock historical permits data
const mockHistoricalPermits: Permit[] = [
	{
		id: "permit-h1",
		permitNumber: "HW-2024-003",
		permitType: {
			id: "hot-work",
			name: "Hot Work Permit",
			description: "For welding, cutting, and other hot work activities",
			category: "High Risk",
			defaultValidityHours: 8,
			requiredTrainings: ["hot-work-safety"],
			requiredCertifications: ["welding-cert"],
			
			template: {} as any,
			isActive: true,
			createdAt: new Date(),
			updatedAt: new Date(),
		},
		title: "Pipe Welding - Basement Level",
		description: "Welding operations for plumbing installation",
		location: "Zone C - Basement",
		siteId: "site-1",
		requestedDate: new Date("2024-01-14T08:00:00"),
		validFrom: new Date("2024-01-14T09:00:00"),
		validUntil: new Date("2024-01-14T17:00:00"),
		actualStartTime: new Date("2024-01-14T09:15:00"),
		actualEndTime: new Date("2024-01-14T16:45:00"),
		status: "closed",
		priority: "medium",
		requestedBy: "supervisor-1",
		requestedByName: "John Smith",
		supervisorId: "supervisor-1",
		supervisorName: "John Smith",
		engineerId: "engineer-3",
		engineerName: "Lisa Rodriguez",
		taskReference: "TSK-2024-003",
		assignedWorkers: [
			{
				workerId: "worker-4",
				workerName: "Tom Anderson",
				primaryTrade: "Plumber",
				role: "worker",
				hasRequiredTraining: true,
				hasRequiredCertifications: true,
				acknowledgedAt: new Date("2024-01-14T08:45:00"),
			},
		],
		approvals: [],
		riskAssessment: {} as any,
		dailyRiskAssessments: [],
		formData: {},
		attachments: [],
		createdAt: new Date("2024-01-14T07:30:00"),
		updatedAt: new Date("2024-01-14T16:45:00"),
		history: [],
	},
	{
		id: "permit-h2",
		permitNumber: "WH-2024-001",
		permitType: {
			id: "work-at-height",
			name: "Work at Height Permit",
			description: "For work above 2 meters",
			category: "High Risk",
			defaultValidityHours: 8,
			requiredTrainings: ["work-at-height"],
			requiredCertifications: ["harness-cert"],
			
			template: {} as any,
			isActive: true,
			createdAt: new Date(),
			updatedAt: new Date(),
		},
		title: "Roof Installation - Building A",
		description: "Installation of roofing materials on Building A",
		location: "Zone A - Roof Level",
		siteId: "site-1",
		requestedDate: new Date("2024-01-13T08:00:00"),
		validFrom: new Date("2024-01-13T09:00:00"),
		validUntil: new Date("2024-01-13T17:00:00"),
		actualStartTime: new Date("2024-01-13T09:00:00"),
		actualEndTime: new Date("2024-01-13T17:00:00"),
		status: "closed",
		priority: "high",
		requestedBy: "supervisor-3",
		requestedByName: "Mark Davis",
		supervisorId: "supervisor-3",
		supervisorName: "Mark Davis",
		engineerId: "engineer-1",
		engineerName: "Sarah Wilson",
		taskReference: "TSK-2024-004",
		assignedWorkers: [
			{
				workerId: "worker-5",
				workerName: "Chris Wilson",
				primaryTrade: "Roofer",
				role: "supervisor",
				hasRequiredTraining: true,
				hasRequiredCertifications: true,
				acknowledgedAt: new Date("2024-01-13T08:30:00"),
			},
			{
				workerId: "worker-6",
				workerName: "Alex Johnson",
				primaryTrade: "Roofer",
				role: "worker",
				hasRequiredTraining: true,
				hasRequiredCertifications: true,
				acknowledgedAt: new Date("2024-01-13T08:30:00"),
			},
		],
		approvals: [],
		riskAssessment: {} as any,
		dailyRiskAssessments: [],
		formData: {},
		attachments: [],
		createdAt: new Date("2024-01-13T07:00:00"),
		updatedAt: new Date("2024-01-13T17:00:00"),
		history: [],
	},
	{
		id: "permit-h3",
		permitNumber: "GW-2024-005",
		permitType: {
			id: "general-work",
			name: "General Work Permit",
			description: "For general construction activities",
			category: "Standard",
			defaultValidityHours: 8,
			requiredTrainings: ["general-safety"],
			requiredCertifications: [],
			
			template: {} as any,
			isActive: true,
			createdAt: new Date(),
			updatedAt: new Date(),
		},
		title: "Electrical Installation - Floor 2",
		description: "Installation of electrical wiring and fixtures",
		location: "Zone B - Floor 2",
		siteId: "site-1",
		requestedDate: new Date("2024-01-12T08:00:00"),
		status: "cancelled",
		priority: "low",
		requestedBy: "supervisor-2",
		requestedByName: "Sarah Johnson",
		assignedWorkers: [],
		approvals: [],
		riskAssessment: {} as any,
		dailyRiskAssessments: [],
		formData: {},
		attachments: [],
		createdAt: new Date("2024-01-12T07:30:00"),
		updatedAt: new Date("2024-01-12T10:00:00"),
		history: [],
	},
];

const PermitHistory: React.FC<PermitHistoryProps> = ({ siteId }) => {
	const navigate = useNavigate();
	const [permits, _setPermits] = useState<Permit[]>(mockHistoricalPermits);
	const [_filteredPermits, setFilteredPermits] = useState<Permit[]>(
		mockHistoricalPermits,
	);
	const [filters, _setFilters] = useState<PermitFilters>({
		search: "",
		status: "all",
		permitType: "",
		priority: "all",
		assignedWorker: "",
		dateRange: {},
		location: "",
	});
	const [dateRange, setDateRange] = useState({
		start: "",
		end: "",
	});

	// New search and filter state for standardized interface
	const [searchQuery, setSearchQuery] = useState("");
	const [selectedPermitType, setSelectedPermitType] = useState("all");

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 20;

  // Permit type filters for the new interface
  const permitTypeFilters: TagOption[] = [
    { id: "hot-work", name: "Hot Work" },
    { id: "confined-space", name: "Confined Space" },
    { id: "work-at-height", name: "Work at Height" },
    { id: "excavation", name: "Excavation" },
    { id: "electrical", name: "Electrical" },
    { id: "general", name: "General Work" },
  ];

  // New filtering logic using the standardized search interface
  const filteredPermitsAll = useMemo(() => {
    return permits.filter((permit) => {
      // Search filter
      const matchesSearch = searchQuery === "" ||
        permit.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        permit.permitNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
        permit.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        permit.location.toLowerCase().includes(searchQuery.toLowerCase());

      // Permit type filter - fix: use permitType.id instead of type
      const matchesPermitType = selectedPermitType === "all" ||
        permit.permitType.id === selectedPermitType;

      // Date range filter
      let matchesDateRange = true;
      if (dateRange.start) {
        matchesDateRange = matchesDateRange && permit.requestedDate >= new Date(dateRange.start);
      }
      if (dateRange.end) {
        matchesDateRange = matchesDateRange && permit.requestedDate <= new Date(dateRange.end);
      }

      return matchesSearch && matchesPermitType && matchesDateRange;
    });
  }, [permits, searchQuery, selectedPermitType, dateRange]);

  // Paginated results
  const filteredPermitsNew = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredPermitsAll.slice(startIndex, endIndex);
  }, [filteredPermitsAll, currentPage, itemsPerPage]);

  // Pagination calculations
  const totalPages = Math.ceil(filteredPermitsAll.length / itemsPerPage);

  useEffect(() => {
    // Fetch historical permits for the site
    console.log(`Fetching permit history for site ${siteId}`);
  }, [siteId]);

  useEffect(() => {
    // Apply filters
    let filtered = permits;

    if (filters.search) {
      filtered = filtered.filter(
        (permit) =>
          permit.title.toLowerCase().includes(filters.search.toLowerCase()) ||
          permit.permitNumber
            .toLowerCase()
            .includes(filters.search.toLowerCase()) ||
          permit.description
            .toLowerCase()
            .includes(filters.search.toLowerCase()),
      );
    }

    if (filters.status !== "all") {
      filtered = filtered.filter((permit) => permit.status === filters.status);
    }

    if (filters.priority !== "all") {
      filtered = filtered.filter(
        (permit) => permit.priority === filters.priority,
      );
    }

    if (filters.location) {
      filtered = filtered.filter((permit) =>
        permit.location.toLowerCase().includes(filters.location.toLowerCase()),
      );
    }

    if (dateRange.start) {
      filtered = filtered.filter(
        (permit) => permit.requestedDate >= new Date(dateRange.start),
      );
    }

    setFilteredPermits(filtered);
  }, [permits, filters, dateRange]);

  // Handlers integrated directly via setters in UniversalFilter; no extra wrappers needed

  // Tag change is handled by passing setSelectedPermitType directly

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  // Function to extract task ID from task reference and navigate
  const handleTaskReferenceClick = (taskReference: string) => {
    // Extract task ID from reference (e.g., "TSK-2024-001" -> "task-1")
    // This is a simplified mapping - in real app, you'd have proper ID mapping
    const taskIdMap: { [key: string]: string } = {
      'TSK-2024-001': 'task-1',
      'TSK-2024-002': 'task-2',
      'TSK-2024-003': 'task-3',
      'TSK-2024-004': 'task-4'
    };

    const taskId = taskIdMap[taskReference] || 'task-1';
    navigate(`/sites/${siteId}/tasks/${taskId}`);
  };

  // Function to navigate to permit detail page
  const handlePermitClick = (permit: Permit) => {
    const permitTypeMap: { [key: string]: string } = {
      'hot-work': 'hot-work',
      'confined-space': 'confined-space',
      'work-at-height': 'work-at-height',
      'excavation': 'excavation',
      'general-work': 'general-work'
    };

    const permitType = permitTypeMap[permit.permitType.id] || 'general-work';
    navigate(`/sites/${siteId}/permits/${permitType}/${permit.id}`);
  };

  const formatDuration = (start?: Date, end?: Date) => {
    if (!start || !end) return "N/A";
    const diff = end.getTime() - start.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}h ${minutes}m`;
  };

  const handleDownloadPDF = (permit: Permit) => {
    console.log("Downloading PDF for permit:", permit.id);
    // TODO: Implement PDF download functionality
    // This would typically call an API endpoint to generate and download the PDF
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Permit History</h2>
          <p className="text-sm text-gray-600">Historical permit records and completed activities</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-600">
            Total {filteredPermitsAll.length} permits
          </div>
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-gray-400" />
            <input
              type="date"
              value={dateRange.start}
              onChange={(e) => setDateRange({ ...dateRange, start: e.target.value })}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Start date"
            />
            <span className="text-gray-400">to</span>
            <input
              type="date"
              value={dateRange.end}
              onChange={(e) => setDateRange({ ...dateRange, end: e.target.value })}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="End date"
            />
          </div>
        </div>
      </div>

      {/* Search and Filter */}
      <UniversalFilter
        variant="search-tags"
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        searchPlaceholder="Search by permit title, number, description, or location..."
        tags={[{ id: "all", name: "All" }, ...permitTypeFilters]}
        selectedTagId={selectedPermitType}
        onTagChange={setSelectedPermitType}
      />

      {/* Permits Table */}
      <div className="bg-white rounded-lg border-2 border-gray-200 shadow-sm overflow-hidden" style={{ borderRadius: '5px' }}>
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Permit Details
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Timeline
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Task Ref
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Persons
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredPermitsNew.map((permit) => (
              <tr key={permit.id} className="hover:bg-gray-50 transition-colors">
                {/* Permit Details */}
                <td className="px-6 py-4">
                  <div>
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="text-sm font-medium text-gray-900">
                        {permit.permitNumber}
                      </h4>
                      <PermitStatusBadge status={permit.status} size="sm" />
                    </div>
                    <p className="text-xs text-gray-600 mb-1">
                      {permit.permitType.name}
                    </p>
                    <p className="text-xs text-gray-500">
                      {permit.title} • {permit.location}
                    </p>
                  </div>
                </td>


								{/* Timeline */}
								<td className="px-6 py-4">
									<div>
										<p className="text-sm text-gray-900 mb-1">
											{permit.requestedDate.toLocaleDateString('en-US', {
												month: 'short',
												day: 'numeric',
												year: 'numeric'
											})}
										</p>
										<p className="text-xs text-gray-500">
											{formatDuration(permit.actualStartTime, permit.actualEndTime)}
										</p>
									</div>
								</td>

								{/* Task Ref */}
								<td className="px-6 py-4">
									{permit.taskReference ? (
										<button
											onClick={() => handleTaskReferenceClick(permit.taskReference!)}
											className="flex items-center text-sm text-blue-600 font-mono hover:text-blue-800 hover:underline transition-colors"
											title="Click to view related task"
										>
											{permit.taskReference}
											<ExternalLink className="h-3 w-3 ml-1" />
										</button>
									) : (
										<div className="text-sm text-gray-400 font-mono">N/A</div>
									)}
								</td>

								{/* Persons */}
								<td className="px-6 py-4">
									<div>
										<div className="flex items-center text-sm text-gray-900 mb-1">
											<Users className="h-4 w-4 mr-1" />
											{permit.assignedWorkers?.length || 0} persons
										</div>
										{permit.supervisorName && (
											<p className="text-xs text-gray-600">
												Supervisor: {permit.supervisorName}
											</p>
										)}
									</div>
								</td>
								<td className="px-6 py-4 text-right text-sm font-medium">
									<div className="flex justify-end space-x-2">
										<button
											onClick={() => handleDownloadPDF(permit)}
											className="px-3 py-1 text-xs border border-green-300 text-green-700 rounded hover:bg-green-50 transition-colors flex items-center"
											style={{ borderRadius: '5px' }}
											title="Download PDF"
										>
											<Download className="h-3 w-3 mr-1" />
											PDF
										</button>
										<button
											onClick={() => handlePermitClick(permit)}
											className="px-3 py-1 text-xs border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors"
											style={{ borderRadius: '5px' }}
											title="View Details"
										>
											View
										</button>
									</div>
								</td>
							</tr>
						))}
					</tbody>
				</table>

				{filteredPermitsAll.length === 0 && (
					<div className="p-12 text-center">
						<Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
						<h3 className="text-lg font-medium text-gray-900 mb-2">
							No permits found
						</h3>
						<p className="text-gray-500">
							{searchQuery || selectedPermitType !== "all" || dateRange.start || dateRange.end
								? "Try adjusting your search criteria or date range."
								: "No historical permits available for this site."}
						</p>
					</div>
				)}

				{/* Pagination */}
				{filteredPermitsAll.length > 0 && (
					<Pagination
						currentPage={currentPage}
						totalPages={totalPages}
						totalItems={filteredPermitsAll.length}
						itemsPerPage={itemsPerPage}
						onPageChange={handlePageChange}
					/>
				)}
			</div>
		</div>
	);
};

export default PermitHistory;
