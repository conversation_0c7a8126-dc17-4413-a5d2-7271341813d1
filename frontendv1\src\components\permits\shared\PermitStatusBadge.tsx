import React from "react";
import {
	CheckCircle,
	FileText,
	Ban,
} from "lucide-react";
import { PermitStatus } from "../../../types/permits";

interface PermitStatusBadgeProps {
	status: PermitStatus;
	size?: "sm" | "md" | "lg";
	showIcon?: boolean;
}

const PermitStatusBadge: React.FC<PermitStatusBadgeProps> = ({
	status,
	size = "md",
	showIcon = true,
}) => {
	const getStatusConfig = (status: PermitStatus) => {
		switch (status) {
			case "open":
				return {
					icon: <CheckCircle className="h-4 w-4" />,
					label: "Open",
					classes: "bg-green-100 text-green-800 border-green-200",
				};
			// case "draft":
			// 	return {
			// 		icon: <FileText className="h-4 w-4" />,
			// 		label: "Draft",
			// 		classes: "bg-gray-100 text-gray-800 border-gray-200",
			// 	};
			// case "pending-approval":
			// 	return {
			// 		icon: <Clock className="h-4 w-4" />,
			// 		label: "Pending Approval",
			// 		classes: "bg-amber-100 text-amber-800 border-amber-200",
			// 	};
			// case "approved":
			// 	return {
			// 		icon: <CheckCircle className="h-4 w-4" />,
			// 		label: "Approved",
			// 		classes: "bg-blue-100 text-blue-800 border-blue-200",
			// 	};
			// case "active":
			// 	return {
			// 		icon: <CheckCircle className="h-4 w-4" />,
			// 		label: "Active",
			// 		classes: "bg-green-100 text-green-800 border-green-200",
			// 	};
			// case "expired":
			// 	return {
			// 		icon: <AlertTriangle className="h-4 w-4" />,
			// 		label: "Expired",
			// 		classes: "bg-orange-100 text-orange-800 border-orange-200",
			// 	};
			case "closed":
				return {
					icon: <CheckCircle className="h-4 w-4" />,
					label: "Closed",
					classes: "bg-gray-100 text-gray-800 border-gray-200",
				};
			// case "rejected":
			// 	return {
			// 		icon: <XCircle className="h-4 w-4" />,
			// 		label: "Rejected",
			// 		classes: "bg-red-100 text-red-800 border-red-200",
			// 	};
			case "cancelled":
				return {
					icon: <Ban className="h-4 w-4" />,
					label: "Cancelled",
					classes: "bg-red-100 text-red-800 border-red-200",
				};
			default:
				return {
					icon: <FileText className="h-4 w-4" />,
					label: status,
					classes: "bg-gray-100 text-gray-800 border-gray-200",
				};
		}
	};

	const getSizeClasses = (size: string) => {
		switch (size) {
			case "sm":
				return "px-2 py-1 text-xs";
			case "lg":
				return "px-3 py-2 text-sm";
			default:
				return "px-2.5 py-1.5 text-xs";
		}
	};

	const config = getStatusConfig(status);
	const sizeClasses = getSizeClasses(size);

	return (
		<span
			className={`
      inline-flex items-center font-medium rounded-full border
      ${config.classes}
      ${sizeClasses}
    `}
		>
			{showIcon && <span className="mr-1">{config.icon}</span>}
			{config.label}
		</span>
	);
};

export default PermitStatusBadge;
