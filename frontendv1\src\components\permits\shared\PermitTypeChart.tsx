import React from "react";

interface PermitTypeData {
  [key: string]: number;
}

interface PermitTypeChartProps {
  data: PermitTypeData;
}

const PermitTypeChart: React.FC<PermitTypeChartProps> = ({ data }) => {
  const permitTypeColors: { [key: string]: string } = {
    'hot-work': 'bg-red-500',
    'confined-space': 'bg-purple-500',
    'work-at-height': 'bg-blue-500',
    'excavation': 'bg-orange-500',
    'electrical': 'bg-yellow-500',
    'general': 'bg-green-500'
  };

  const total = Object.values(data).reduce((sum, count) => sum + count, 0);

  return (
    <div className="space-y-3">
      {Object.entries(data).map(([type, count]) => {
        const percentage = total > 0 ? (count / total) * 100 : 0;
        return (
          <div key={type} className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div 
                className={`w-3 h-3 rounded-full ${permitTypeColors[type] || 'bg-gray-500'}`}
              />
              <span className="text-sm capitalize">
                {type.replace('-', ' ')}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-16 bg-gray-200 rounded-full h-2">
                <div 
                  className={`h-2 rounded-full ${permitTypeColors[type] || 'bg-gray-500'}`}
                  style={{ width: `${percentage}%` }}
                />
              </div>
              <span className="text-sm font-medium text-gray-700 w-6 text-right">{count}</span>
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default PermitTypeChart;
