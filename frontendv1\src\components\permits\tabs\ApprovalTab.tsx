import React from 'react';
import { CheckCircle, User, Calendar, Clock, FileText } from 'lucide-react';

interface ApprovalTabProps {
  approval: {
    approverRole: string;
    status: string;
    approverName?: string;
    approvedAt?: Date;
    comments?: string;
  };
}

const ApprovalTab: React.FC<ApprovalTabProps> = ({ approval }) => {
  if (!approval) {
    return (
      <div className="p-6">
        <div className="text-center text-gray-500">
          <CheckCircle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <h3 className="text-lg font-medium mb-2">Approval Information</h3>
          <p>No approval data available.</p>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
        return 'text-green-600 bg-green-100';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100';
      case 'rejected':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
        return <CheckCircle className="h-8 w-8 text-green-500" />;
      case 'pending':
        return <Clock className="h-8 w-8 text-yellow-500" />;
      case 'rejected':
        return <CheckCircle className="h-8 w-8 text-red-500" />;
      default:
        return <CheckCircle className="h-8 w-8 text-gray-500" />;
    }
  };

  return (
    <div className="p-6 max-w-4xl">
      <div className="bg-white rounded-lg border-2 border-gray-200 shadow-sm" style={{ borderRadius: '5px' }}>
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50" style={{ borderRadius: '5px 5px 0 0' }}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {getStatusIcon(approval.status)}
              <div>
                <h1 className="text-xl font-bold text-gray-900">{approval.approverRole} Approval</h1>
                <p className="text-sm text-gray-600">Permit approval status and details</p>
              </div>
            </div>
            <span className={`px-3 py-1 text-sm font-medium rounded-full ${getStatusColor(approval.status)}`}>
              {approval.status.charAt(0).toUpperCase() + approval.status.slice(1)}
            </span>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Approval Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Approval Details</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <div className="w-24 text-sm font-medium text-gray-500">Role:</div>
                  <div className="text-sm text-gray-900">{approval.approverRole}</div>
                </div>
                <div className="flex items-center">
                  <div className="w-24 text-sm font-medium text-gray-500">Status:</div>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(approval.status)}`}>
                    {approval.status.charAt(0).toUpperCase() + approval.status.slice(1)}
                  </span>
                </div>
                <div className="flex items-center">
                  <User className="h-4 w-4 text-gray-400 mr-2" />
                  <div className="w-22 text-sm font-medium text-gray-500">Approver:</div>
                  <div className="text-sm text-gray-900">{approval.approverName || 'Sarah Wilson'}</div>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Timeline</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                  <div className="w-24 text-sm font-medium text-gray-500">Date:</div>
                  <div className="text-sm text-gray-900">
                    {approval.approvedAt?.toLocaleDateString() || new Date().toLocaleDateString()}
                  </div>
                </div>
                <div className="flex items-center">
                  <Clock className="h-4 w-4 text-gray-400 mr-2" />
                  <div className="w-24 text-sm font-medium text-gray-500">Time:</div>
                  <div className="text-sm text-gray-900">
                    {approval.approvedAt?.toLocaleTimeString() || new Date().toLocaleTimeString()}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Comments */}
          {approval.comments && (
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Comments</h3>
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <FileText className="h-5 w-5 text-gray-400 mt-0.5" />
                  <div className="flex-1">
                    <p className="text-sm text-gray-900">{approval.comments}</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Approval Checklist */}
          <div className="border-t border-gray-200 pt-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Approval Checklist</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="text-sm text-gray-900">Risk assessment reviewed and approved</span>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="text-sm text-gray-900">Control measures verified as adequate</span>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="text-sm text-gray-900">Worker competencies validated</span>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="text-sm text-gray-900">Equipment and tools inspected</span>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <span className="text-sm text-gray-900">Emergency procedures confirmed</span>
              </div>
            </div>
          </div>

          {/* Digital Signature */}
          <div className="border-t border-gray-200 pt-6 mt-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Digital Signature</h3>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <User className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <p className="font-medium text-blue-900">{approval.approverName || 'Sarah Wilson'}</p>
                  <p className="text-sm text-blue-700">{approval.approverRole}</p>
                  <p className="text-xs text-blue-600">
                    Digitally signed on {approval.approvedAt?.toLocaleDateString() || new Date().toLocaleDateString()}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApprovalTab;
