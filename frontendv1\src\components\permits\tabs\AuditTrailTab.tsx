import React from 'react';
import { History, User, Calendar, Clock, FileText, CheckCircle, AlertTriangle } from 'lucide-react';
import { Permit } from '../../../types/permits';

interface AuditTrailTabProps {
  permit: Permit;
}

const AuditTrailTab: React.FC<AuditTrailTabProps> = ({ permit }) => {
  // Mock audit trail data
  const auditTrail = [
    {
      id: 1,
      action: 'Permit Created',
      user: permit.requestedByName,
      timestamp: permit.requestedDate,
      details: 'Initial permit request submitted',
      type: 'create'
    },
    {
      id: 2,
      action: 'Risk Assessment Added',
      user: 'Safety Officer',
      timestamp: new Date(permit.requestedDate.getTime() + 30 * 60000), // 30 minutes later
      details: 'RAMS document uploaded and reviewed',
      type: 'update'
    },
    {
      id: 3,
      action: 'Supervisor Approval',
      user: permit.supervisorName || '<PERSON> Smith',
      timestamp: new Date(permit.requestedDate.getTime() + 60 * 60000), // 1 hour later
      details: 'Permit approved by site supervisor',
      type: 'approval'
    },
    {
      id: 4,
      action: 'Safety Officer Approval',
      user: '<PERSON>',
      timestamp: new Date(permit.requestedDate.getTime() + 90 * 60000), // 1.5 hours later
      details: 'Final safety approval granted',
      type: 'approval'
    },
    {
      id: 5,
      action: 'Permit Activated',
      user: 'System',
      timestamp: permit.actualStartTime || permit.validFrom,
      details: 'Permit became active and work commenced',
      type: 'status'
    }
  ];

  const getActionIcon = (type: string) => {
    switch (type) {
      case 'create':
        return <FileText className="h-5 w-5 text-blue-500" />;
      case 'update':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'approval':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'status':
        return <Clock className="h-5 w-5 text-purple-500" />;
      default:
        return <History className="h-5 w-5 text-gray-500" />;
    }
  };

  const getActionColor = (type: string) => {
    switch (type) {
      case 'create':
        return 'border-blue-200 bg-blue-50';
      case 'update':
        return 'border-yellow-200 bg-yellow-50';
      case 'approval':
        return 'border-green-200 bg-green-50';
      case 'status':
        return 'border-purple-200 bg-purple-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  return (
    <div className="p-6 max-w-4xl">
      <div className="bg-white rounded-lg border-2 border-gray-200 shadow-sm" style={{ borderRadius: '5px' }}>
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50" style={{ borderRadius: '5px 5px 0 0' }}>
          <div className="flex items-center space-x-4">
            <History className="h-8 w-8 text-gray-500" />
            <div>
              <h1 className="text-xl font-bold text-gray-900">Audit Trail</h1>
              <p className="text-sm text-gray-600">Complete history of permit activities and changes</p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Permit Summary */}
          <div className="mb-8 p-4 bg-gray-50 rounded-lg border border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Permit Summary</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-500">Permit Number:</span>
                <span className="ml-2 text-gray-900 font-mono">{permit.permitNumber}</span>
              </div>
              <div>
                <span className="font-medium text-gray-500">Current Status:</span>
                <span className="ml-2 text-gray-900">{permit.status.charAt(0).toUpperCase() + permit.status.slice(1)}</span>
              </div>
              <div>
                <span className="font-medium text-gray-500">Total Events:</span>
                <span className="ml-2 text-gray-900">{auditTrail.length}</span>
              </div>
            </div>
          </div>

          {/* Timeline */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-6">Activity Timeline</h3>
            <div className="space-y-4">
              {auditTrail.map((entry, index) => (
                <div key={entry.id} className="relative">
                  {/* Timeline line */}
                  {index < auditTrail.length - 1 && (
                    <div className="absolute left-6 top-12 w-0.5 h-8 bg-gray-300"></div>
                  )}
                  
                  {/* Timeline entry */}
                  <div className={`flex items-start space-x-4 p-4 rounded-lg border ${getActionColor(entry.type)}`}>
                    <div className="flex-shrink-0 w-12 h-12 rounded-full bg-white border-2 border-gray-300 flex items-center justify-center">
                      {getActionIcon(entry.type)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h4 className="text-sm font-medium text-gray-900">{entry.action}</h4>
                        <div className="flex items-center text-xs text-gray-500">
                          <Calendar className="h-3 w-3 mr-1" />
                          {entry.timestamp?.toLocaleDateString()}
                          <Clock className="h-3 w-3 ml-2 mr-1" />
                          {entry.timestamp?.toLocaleTimeString()}
                        </div>
                      </div>
                      
                      <div className="flex items-center mb-2">
                        <User className="h-3 w-3 text-gray-400 mr-1" />
                        <span className="text-sm text-gray-600">{entry.user}</span>
                      </div>
                      
                      <p className="text-sm text-gray-700">{entry.details}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Additional Information */}
          <div className="border-t border-gray-200 pt-6 mt-8">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Audit Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Data Retention</h4>
                <p className="text-sm text-gray-600">
                  This audit trail is maintained for regulatory compliance and will be retained for 7 years 
                  as per company policy and local regulations.
                </p>
              </div>
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Integrity</h4>
                <p className="text-sm text-gray-600">
                  All entries are timestamped and digitally signed. This audit trail cannot be modified 
                  or deleted to ensure data integrity.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuditTrailTab;
