import React from 'react';
import { Permit } from '../../../types/permits';

interface PermitFormTabProps {
  permit: Permit;
}

const PermitFormTab: React.FC<PermitFormTabProps> = ({ permit }) => {
  const getPermitConfig = () => {
    switch (permit.permitType.id) {
      case 'excavation':
        return {
          title: 'Excavation Permit to Work',
          headerTitle: 'EXCAVATION PERMIT TO WORK',
          headerColor: 'orange',
          description: 'This permit is valid for one day ONLY. Tick as appropriate.',
          serialNumber: 'EXC-2024-001'
        };
      case 'confined-space':
        return {
          title: 'Confined Space Entry Permit',
          headerTitle: 'CONFINED SPACE ENTRY PERMIT',
          headerColor: 'blue',
          description: 'This permit is valid for the specified work period only.',
          serialNumber: 'CSE-2024-002'
        };
      case 'general-work':
        return {
          title: 'General Work Permit',
          headerTitle: 'GENERAL WORK PERMIT',
          headerColor: 'green',
          description: 'This permit is valid for one day ONLY.',
          serialNumber: 'GWP-2024-003'
        };
      case 'hot-work':
        return {
          title: 'Hot Work Permit',
          headerTitle: 'HOT WORK PERMIT',
          headerColor: 'red',
          description: 'This permit is valid for one day only.',
          serialNumber: 'HWP-2024-004'
        };
      case 'work-at-height':
        return {
          title: 'Work at Height Permit',
          headerTitle: 'WORK AT HEIGHT PERMIT',
          headerColor: 'purple',
          description: 'This permit is valid for one day ONLY.',
          serialNumber: 'WAH-2024-005'
        };
      default:
        return {
          title: 'Work Permit',
          headerTitle: 'WORK PERMIT',
          headerColor: 'gray',
          description: 'This permit is valid for one day ONLY.',
          serialNumber: 'WP-2024-001'
        };
    }
  };

  const getHeaderColorClasses = (color: string) => {
    switch (color) {
      case 'red':
        return 'bg-red-50 border-red-200 text-red-800';
      case 'blue':
        return 'bg-blue-50 border-blue-200 text-blue-800';
      case 'green':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'orange':
        return 'bg-orange-50 border-orange-200 text-orange-800';
      case 'purple':
        return 'bg-purple-50 border-purple-200 text-purple-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  const config = getPermitConfig();

  return (
    <div className="h-full bg-gray-50 overflow-auto">
      <div className="p-6">
        <div className="space-y-4">
          {/* Header Section */}
          <div className={`border rounded-lg p-4 ${getHeaderColorClasses(config.headerColor)}`}>
            <div className="text-center">
              <div className="flex items-center justify-center gap-4 mb-2">
                <h2 className="text-lg font-bold">{config.headerTitle}</h2>
                <span className="text-xs text-gray-500 font-light">Serial No: {config.serialNumber}</span>
              </div>
              <div className="text-xs text-red-600 font-medium">
                {config.description}
              </div>
            </div>
          </div>

          {/* Basic Information Section */}
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Project Name</label>
                <div className="text-sm text-gray-900 p-2 bg-gray-50 border border-gray-200 rounded">
                  {permit.title}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
                <div className="text-sm text-gray-900 p-2 bg-gray-50 border border-gray-200 rounded">
                  {permit.location}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Start Date & Time</label>
                <div className="text-sm text-gray-900 p-2 bg-gray-50 border border-gray-200 rounded">
                  {permit.validFrom?.toLocaleString()}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">End Date & Time</label>
                <div className="text-sm text-gray-900 p-2 bg-gray-50 border border-gray-200 rounded">
                  {permit.validUntil?.toLocaleString()}
                </div>
              </div>
            </div>
          </div>

          {/* Work Description Section */}
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Work Description</h3>
            <div className="text-sm text-gray-900 p-3 bg-gray-50 border border-gray-200 rounded">
              {permit.description}
            </div>
          </div>

          {/* Hazards and Precautions Section */}
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Hazards and Precautions</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Identified Hazards</label>
                <div className="space-y-2">
                  {permit.permitType.id === 'hot-work' && (
                    <>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Fire/explosion risk</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Burns from hot surfaces</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Toxic fumes</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">UV radiation exposure</span>
                      </div>
                    </>
                  )}
                  {permit.permitType.id === 'confined-space' && (
                    <>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Oxygen deficiency</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Toxic gases</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Engulfment hazard</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Entrapment risk</span>
                      </div>
                    </>
                  )}
                  {permit.permitType.id === 'work-at-height' && (
                    <>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Falls from height</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Unstable surfaces</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Weather conditions</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Equipment failure</span>
                      </div>
                    </>
                  )}
                  {permit.permitType.id === 'excavation' && (
                    <>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Cave-in hazard</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Utility strikes</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Falls into excavation</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Heavy equipment hazards</span>
                      </div>
                    </>
                  )}
                  {permit.permitType.id === 'general-work' && (
                    <>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Slips, trips, and falls</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Manual handling injuries</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Tool-related injuries</span>
                      </div>
                    </>
                  )}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">Control Measures</label>
                <div className="space-y-2">
                  {permit.permitType.id === 'hot-work' && (
                    <>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Fire watch assigned</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Fire extinguisher available</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Adequate ventilation</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Proper PPE provided</span>
                      </div>
                    </>
                  )}
                  {permit.permitType.id === 'confined-space' && (
                    <>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Atmospheric testing completed</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Ventilation system active</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Entry attendant assigned</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Emergency rescue plan</span>
                      </div>
                    </>
                  )}
                  {permit.permitType.id === 'work-at-height' && (
                    <>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Fall protection system</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Safety harnesses provided</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Guardrails installed</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Weather monitoring</span>
                      </div>
                    </>
                  )}
                  {permit.permitType.id === 'excavation' && (
                    <>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Shoring system installed</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Utilities located and marked</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Safe entry/exit provided</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Competent person supervision</span>
                      </div>
                    </>
                  )}
                  {permit.permitType.id === 'general-work' && (
                    <>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Risk assessment completed</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Safe work procedures</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Proper tools provided</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" checked readOnly className="rounded" />
                        <span className="text-sm">Training completed</span>
                      </div>
                    </>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Permit Authorization Section */}
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Permit Authorization</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Issued By</label>
                <div className="text-sm text-gray-900 p-2 bg-gray-50 border border-gray-200 rounded">
                  {permit.requestedByName}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Issue Date</label>
                <div className="text-sm text-gray-900 p-2 bg-gray-50 border border-gray-200 rounded">
                  {permit.requestedDate.toLocaleDateString()}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PermitFormTab;
