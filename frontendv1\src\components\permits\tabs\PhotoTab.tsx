import React from 'react';
import { Image, Download, Eye, Calendar, User, Camera } from 'lucide-react';

interface PhotoTabProps {
  photo: {
    id: string;
    name: string;
    type: string;
    size?: string;
    takenBy?: string;
    takenAt?: Date;
  };
}

const PhotoTab: React.FC<PhotoTabProps> = ({ photo }) => {
  if (!photo) {
    return (
      <div className="p-6">
        <div className="text-center text-gray-500">
          <Image className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <h3 className="text-lg font-medium mb-2">Photo Not Found</h3>
          <p>The requested photo could not be loaded.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-4xl">
      <div className="bg-white rounded-lg border-2 border-gray-200 shadow-sm" style={{ borderRadius: '5px' }}>
        {/* Header */}
        <div className="px-6 py-4 border-b border-gray-200 bg-gray-50" style={{ borderRadius: '5px 5px 0 0' }}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <Camera className="h-8 w-8 text-blue-500" />
              <div>
                <h1 className="text-xl font-bold text-gray-900">{photo.name}</h1>
                <p className="text-sm text-gray-600">Permit Photo</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <button className="flex items-center px-3 py-2 text-sm border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors">
                <Eye className="h-4 w-4 mr-1" />
                Full Size
              </button>
              <button className="flex items-center px-3 py-2 text-sm border border-green-300 text-green-700 rounded hover:bg-green-50 transition-colors">
                <Download className="h-4 w-4 mr-1" />
                Download
              </button>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Photo Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Photo Details</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <div className="w-24 text-sm font-medium text-gray-500">Name:</div>
                  <div className="text-sm text-gray-900">{photo.name}</div>
                </div>
                <div className="flex items-center">
                  <div className="w-24 text-sm font-medium text-gray-500">Type:</div>
                  <div className="text-sm text-gray-900">JPEG Image</div>
                </div>
                <div className="flex items-center">
                  <div className="w-24 text-sm font-medium text-gray-500">Size:</div>
                  <div className="text-sm text-gray-900">{photo.size || '1.8 MB'}</div>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Capture Information</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <User className="h-4 w-4 text-gray-400 mr-2" />
                  <div className="w-24 text-sm font-medium text-gray-500">Taken by:</div>
                  <div className="text-sm text-gray-900">{photo.takenBy || 'John Smith'}</div>
                </div>
                <div className="flex items-center">
                  <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                  <div className="w-24 text-sm font-medium text-gray-500">Date:</div>
                  <div className="text-sm text-gray-900">
                    {photo.takenAt?.toLocaleDateString() || new Date().toLocaleDateString()}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Photo Preview */}
          <div className="border-t border-gray-200 pt-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Photo Preview</h3>
            <div className="bg-gray-100 border-2 border-gray-300 rounded-lg overflow-hidden">
              <div className="aspect-video bg-gradient-to-br from-blue-100 to-blue-200 flex items-center justify-center">
                <div className="text-center">
                  <Camera className="h-16 w-16 mx-auto mb-4 text-blue-400" />
                  <h4 className="text-lg font-medium text-blue-900 mb-2">Photo Preview</h4>
                  <p className="text-blue-700 mb-4">
                    {photo.name}
                  </p>
                  <div className="flex justify-center space-x-4">
                    <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors">
                      <Eye className="h-4 w-4 mr-2" />
                      View Full Size
                    </button>
                    <button className="flex items-center px-4 py-2 border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors">
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PhotoTab;
