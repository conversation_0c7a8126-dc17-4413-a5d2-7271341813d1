import { useState, useEffect } from "react";
import {
	Search,
	Filter,
	User,
	Calendar,
	AlertTriangle,
	CheckCircle,
	Clock,
	Eye,
	UserX } from "lucide-react";

interface PPEWorkerAssignment {
	id: string;
	workerId: string;
	workerName: string;
	workerPosition: string;
	ppeItemId: string;
	ppeItemName: string;
	ppeItemSku: string;
	ppeCategory: string;
	assignedDate: Date;
	lastInspectedDate?: Date;
	condition: "excellent" | "good" | "worn" | "needs-replacement" | "damaged";
	status: "active" | "returned" | "lost" | "damaged";
	batchNumber?: string;
	expiryDate?: Date;
	expectedLifespanDays: number;
	notes?: string;
}

interface PPEWorkerRelationProps {
	siteId: string;
}

const PPEWorkerRelation = ({ siteId }: PPEWorkerRelationProps) => {
	const [assignments, setAssignments] = useState<PPEWorkerAssignment[]>([]);
	const [filteredAssignments, setFilteredAssignments] = useState<PPEWorkerAssignment[]>([]);
	const [searchTerm, setSearchTerm] = useState("");
	const [conditionFilter, setConditionFilter] = useState("");
	const [statusFilter, setStatusFilter] = useState("");
	const [isLoading, setIsLoading] = useState(true);

	useEffect(() => {
		// Mock data for PPE-Worker assignments
		const mockAssignments: PPEWorkerAssignment[] = [
			{
				id: "assign-001",
				workerId: "worker-001",
				workerName: "John Mwangi",
				workerPosition: "Site Supervisor",
				ppeItemId: "ppe-001",
				ppeItemName: "Safety Helmet - Hard Hat",
				ppeItemSku: "PPE-HH-001",
				ppeCategory: "Head Protection",
				assignedDate: new Date("2024-01-15"),
				lastInspectedDate: new Date("2024-12-01"),
				condition: "good",
				status: "active",
				batchNumber: "HH-2024-001",
				expiryDate: new Date("2027-12-31"),
				expectedLifespanDays: 1095,
				notes: "Regular use, good condition" },
			{
				id: "assign-002",
				workerId: "worker-002",
				workerName: "Sarah Ochieng",
				workerPosition: "Construction Worker",
				ppeItemId: "ppe-002",
				ppeItemName: "Safety Vest - High Visibility",
				ppeItemSku: "PPE-SV-001",
				ppeCategory: "Body Protection",
				assignedDate: new Date("2024-11-01"),
				lastInspectedDate: new Date("2024-12-10"),
				condition: "worn",
				status: "active",
				batchNumber: "SV-2024-002",
				expectedLifespanDays: 365,
				notes: "Shows signs of wear, monitor closely" },
			{
				id: "assign-003",
				workerId: "worker-003",
				workerName: "David Kimani",
				workerPosition: "Equipment Operator",
				ppeItemId: "ppe-003",
				ppeItemName: "Safety Gloves - Cut Resistant",
				ppeItemSku: "PPE-GL-001",
				ppeCategory: "Hand Protection",
				assignedDate: new Date("2024-10-01"),
				lastInspectedDate: new Date("2024-12-15"),
				condition: "needs-replacement",
				status: "active",
				batchNumber: "GL-2024-003",
				expiryDate: new Date("2025-01-15"),
				expectedLifespanDays: 90,
				notes: "Exceeded lifespan, replacement needed" },
			{
				id: "assign-004",
				workerId: "worker-004",
				workerName: "Grace Wanjiku",
				workerPosition: "Safety Officer",
				ppeItemId: "ppe-004",
				ppeItemName: "Safety Boots - Steel Toe",
				ppeItemSku: "PPE-SB-001",
				ppeCategory: "Foot Protection",
				assignedDate: new Date("2024-06-01"),
				lastInspectedDate: new Date("2024-11-30"),
				condition: "excellent",
				status: "active",
				batchNumber: "SB-2024-001",
				expiryDate: new Date("2026-06-01"),
				expectedLifespanDays: 730,
				notes: "Recently assigned, excellent condition" },
			{
				id: "assign-005",
				workerId: "worker-002",
				workerName: "Sarah Ochieng",
				workerPosition: "Construction Worker",
				ppeItemId: "ppe-005",
				ppeItemName: "Safety Goggles",
				ppeItemSku: "PPE-SG-001",
				ppeCategory: "Eye Protection",
				assignedDate: new Date("2024-09-15"),
				condition: "damaged",
				status: "returned",
				batchNumber: "SG-2024-001",
				expectedLifespanDays: 180,
				notes: "Returned due to damage, replacement issued" },
		];

		setTimeout(() => {
			setAssignments(mockAssignments);
			setFilteredAssignments(mockAssignments);
			setIsLoading(false);
		}, 500);
	}, [siteId]);

	// Filter logic
	useEffect(() => {
		let filtered = assignments.filter((assignment) => {
			const matchesSearch =
				assignment.workerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
				assignment.ppeItemName.toLowerCase().includes(searchTerm.toLowerCase()) ||
				assignment.ppeItemSku.toLowerCase().includes(searchTerm.toLowerCase()) ||
				assignment.workerPosition.toLowerCase().includes(searchTerm.toLowerCase());

			const matchesCondition = !conditionFilter || assignment.condition === conditionFilter;
			const matchesStatus = !statusFilter || assignment.status === statusFilter;

			return matchesSearch && matchesCondition && matchesStatus;
		});

		setFilteredAssignments(filtered);
	}, [assignments, searchTerm, conditionFilter, statusFilter]);

	const getConditionBadge = (condition: string) => {
		const badges = {
			excellent: "bg-green-100 text-green-800",
			good: "bg-blue-100 text-blue-800",
			worn: "bg-yellow-100 text-yellow-800",
			"needs-replacement": "bg-orange-100 text-orange-800",
			damaged: "bg-red-100 text-red-800" };
		return badges[condition as keyof typeof badges] || "bg-gray-100 text-gray-800";
	};

	const getConditionIcon = (condition: string) => {
		switch (condition) {
			case "excellent":
			case "good":
				return <CheckCircle className="h-4 w-4 text-green-500" />;
			case "worn":
				return <Clock className="h-4 w-4 text-yellow-500" />;
			case "needs-replacement":
			case "damaged":
				return <AlertTriangle className="h-4 w-4 text-red-500" />;
			default:
				return <CheckCircle className="h-4 w-4 text-gray-500" />;
		}
	};

	const getStatusBadge = (status: string) => {
		const badges = {
			active: "bg-green-100 text-green-800",
			returned: "bg-gray-100 text-gray-800",
			lost: "bg-red-100 text-red-800",
			damaged: "bg-red-100 text-red-800" };
		return badges[status as keyof typeof badges] || "bg-gray-100 text-gray-800";
	};

	const getDaysInUse = (assignedDate: Date) => {
		const today = new Date();
		return Math.ceil((today.getTime() - assignedDate.getTime()) / (1000 * 60 * 60 * 24));
	};

	if (isLoading) {
		return (
			<div className="space-y-6">
				<div className="animate-pulse">
					<div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
					<div className="h-32 bg-gray-200 rounded mb-4"></div>
					<div className="space-y-3">
						{[...Array(5)].map((_, i) => (
							<div key={i} className="h-16 bg-gray-200 rounded"></div>
						))}
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<h2 className="text-2xl font-bold">PPE-Worker Relations</h2>
				<div className="flex gap-2">
					<button className="flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
						<UserX className="h-4 w-4 mr-2" />
						Return PPE
					</button>
					<button className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700">
						<User className="h-4 w-4 mr-2" />
						Assign PPE
					</button>
				</div>
			</div>

			{/* Summary Cards */}
			<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Total Assignments</p>
							<p className="text-2xl font-bold">{assignments.length}</p>
						</div>
						<User className="h-8 w-8 text-blue-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Active</p>
							<p className="text-2xl font-bold text-green-600">
								{assignments.filter((a) => a.status === "active").length}
							</p>
						</div>
						<CheckCircle className="h-8 w-8 text-green-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Need Replacement</p>
							<p className="text-2xl font-bold text-orange-600">
								{assignments.filter((a) => a.condition === "needs-replacement").length}
							</p>
						</div>
						<AlertTriangle className="h-8 w-8 text-orange-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Damaged/Lost</p>
							<p className="text-2xl font-bold text-red-600">
								{assignments.filter((a) => a.condition === "damaged" || a.status === "lost").length}
							</p>
						</div>
						<AlertTriangle className="h-8 w-8 text-red-500" />
					</div>
				</div>
			</div>

			{/* Filters */}
			<div className="bg-white rounded-lg border border-gray-200 p-4">
				<div className="flex flex-col sm:flex-row gap-4 items-center">
					<div className="flex-1 relative">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
						<input
							type="text"
							placeholder="Search by worker name, PPE item, SKU, or position..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
						/>
					</div>
					<div className="flex items-center gap-2">
						<Filter className="h-4 w-4 text-gray-400" />
						<select
							value={conditionFilter}
							onChange={(e) => setConditionFilter(e.target.value)}
							className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
						>
							<option value="">All Conditions</option>
							<option value="excellent">Excellent</option>
							<option value="good">Good</option>
							<option value="worn">Worn</option>
							<option value="needs-replacement">Needs Replacement</option>
							<option value="damaged">Damaged</option>
						</select>
						<select
							value={statusFilter}
							onChange={(e) => setStatusFilter(e.target.value)}
							className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
						>
							<option value="">All Status</option>
							<option value="active">Active</option>
							<option value="returned">Returned</option>
							<option value="lost">Lost</option>
							<option value="damaged">Damaged</option>
						</select>
					</div>
				</div>
			</div>

			{/* PPE-Worker Relations Table */}
			<div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
				<div className="overflow-x-auto">
					<table className="min-w-full divide-y divide-gray-200">
						<thead className="bg-gray-50">
							<tr>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Worker
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Assigned PPE Item
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Condition
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Assignment Details
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Status
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Actions
								</th>
							</tr>
						</thead>
						<tbody className="bg-white divide-y divide-gray-200">
							{filteredAssignments.map((assignment) => (
								<tr key={assignment.id} className="hover:bg-gray-50">
									<td className="px-6 py-4 whitespace-nowrap">
										<div className="flex items-center">
											<div className="flex-shrink-0 h-10 w-10">
												<div className="h-10 w-10 rounded-full bg-green-500 flex items-center justify-center text-white text-sm font-medium">
													{assignment.workerName.split(' ').map(n => n[0]).join('')}
												</div>
											</div>
											<div className="ml-4">
												<div className="text-sm font-medium text-gray-900">
													{assignment.workerName}
												</div>
												<div className="text-sm text-gray-500">
													{assignment.workerPosition}
												</div>
											</div>
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<div>
											<div className="text-sm font-medium text-gray-900">
												{assignment.ppeItemName}
											</div>
											<div className="text-sm text-gray-500">
												SKU: {assignment.ppeItemSku}
											</div>
											<div className="text-sm text-gray-500">
												{assignment.ppeCategory}
											</div>
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<div className="flex items-center space-x-2">
											{getConditionIcon(assignment.condition)}
											<span className={`px-2 py-1 text-xs font-medium rounded-full ${getConditionBadge(assignment.condition)}`}>
												{assignment.condition.charAt(0).toUpperCase() + assignment.condition.slice(1).replace('-', ' ')}
											</span>
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<div className="text-sm text-gray-900">
											<div className="flex items-center space-x-1 mb-1">
												<Calendar className="h-3 w-3 text-gray-400" />
												<span>Assigned: {assignment.assignedDate.toLocaleDateString()}</span>
											</div>
											{assignment.lastInspectedDate && (
												<div className="flex items-center space-x-1 mb-1">
													<Eye className="h-3 w-3 text-gray-400" />
													<span>Inspected: {assignment.lastInspectedDate.toLocaleDateString()}</span>
												</div>
											)}
											<div className="text-xs text-gray-500">
												{getDaysInUse(assignment.assignedDate)} days in use
											</div>
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<span className={`px-2 py-1 text-xs font-medium rounded-full ${getStatusBadge(assignment.status)}`}>
											{assignment.status.charAt(0).toUpperCase() + assignment.status.slice(1)}
										</span>
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
										<div className="flex space-x-2">
											<button
												className="text-blue-600 hover:text-blue-900"
												title="View Details"
											>
												<Eye className="h-4 w-4" />
											</button>
											{assignment.status === "active" && (
												<button
													className="text-orange-600 hover:text-orange-900"
													title="Return PPE"
												>
													<UserX className="h-4 w-4" />
												</button>
											)}
										</div>
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>
			</div>
		</div>
	);
};

export default PPEWorkerRelation;
