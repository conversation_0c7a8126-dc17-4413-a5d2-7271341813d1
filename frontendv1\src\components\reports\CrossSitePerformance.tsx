import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON>s,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';
import {
  TrendingUp,
  Shield<PERSON>heck,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Download
} from 'lucide-react';
import { mockSites } from '../../data/mockTenantData';

interface SitePerformanceData {
  siteId: string;
  siteName: string;
  safetyScore: number;
  productivity: number;
  compliance: number;
  workersOnSite: number;
  incidentCount: number;
  trainingCompliance: number;
  overallScore: number;
}

const CrossSitePerformance: React.FC = () => {
  const [performanceData, setPerformanceData] = useState<SitePerformanceData[]>([]);
  const [selectedMetric, setSelectedMetric] = useState<'safety' | 'productivity' | 'compliance'>('safety');
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d');

  useEffect(() => {
    // Generate mock performance data based on existing sites
    const mockPerformanceData: SitePerformanceData[] = mockSites.map((site) => ({
      siteId: site.id,
      siteName: site.name,
      safetyScore: 85 + Math.random() * 15, // 85-100
      productivity: 75 + Math.random() * 20, // 75-95
      compliance: 80 + Math.random() * 15, // 80-95
      workersOnSite: site.workersOnSite,
      incidentCount: Math.floor(Math.random() * 5), // 0-4 incidents
      trainingCompliance: 75 + Math.random() * 20, // 75-95
      overallScore: 80 + Math.random() * 15 // 80-95
    }));

    setPerformanceData(mockPerformanceData);
  }, [timeRange]);

  const getMetricData = () => {
    switch (selectedMetric) {
      case 'safety':
        return performanceData.map(site => ({
          name: site.siteName.split(' ')[0], // Shortened name
          value: Math.round(site.safetyScore),
          fullName: site.siteName
        }));
      case 'productivity':
        return performanceData.map(site => ({
          name: site.siteName.split(' ')[0],
          value: Math.round(site.productivity),
          fullName: site.siteName
        }));
      case 'compliance':
        return performanceData.map(site => ({
          name: site.siteName.split(' ')[0],
          value: Math.round(site.compliance),
          fullName: site.siteName
        }));
      default:
        return [];
    }
  };

  const getTopPerformingSites = () => {
    return [...performanceData]
      .sort((a, b) => b.overallScore - a.overallScore)
      .slice(0, 3);
  };

  const getBottomPerformingSites = () => {
    return [...performanceData]
      .sort((a, b) => a.overallScore - b.overallScore)
      .slice(0, 3);
  };

  const getAverageMetrics = () => {
    if (performanceData.length === 0) return { safety: 0, productivity: 0, compliance: 0 };
    
    const totals = performanceData.reduce(
      (acc, site) => ({
        safety: acc.safety + site.safetyScore,
        productivity: acc.productivity + site.productivity,
        compliance: acc.compliance + site.compliance
      }),
      { safety: 0, productivity: 0, compliance: 0 }
    );

    return {
      safety: Math.round(totals.safety / performanceData.length),
      productivity: Math.round(totals.productivity / performanceData.length),
      compliance: Math.round(totals.compliance / performanceData.length)
    };
  };

  const averageMetrics = getAverageMetrics();
  const topSites = getTopPerformingSites();
  const bottomSites = getBottomPerformingSites();

  // Colors for charts
  // const COLORS = ['#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4', '#F97316', '#84CC16', '#EC4899'];

  const handleRefresh = () => {
    // Simulate refresh
    console.log('Refreshing site performance data...');
  };

  const handleExport = () => {
    // Simulate export
    console.log('Exporting site performance report...');
  };

  return (
    <div className="space-y-6">
      {/* Controls and Actions */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
          <div className="flex space-x-4">
            <select
              value={selectedMetric}
              onChange={(e) => setSelectedMetric(e.target.value as any)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
            >
              <option value="safety">Safety Score</option>
              <option value="productivity">Productivity</option>
              <option value="compliance">Compliance</option>
            </select>
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value as any)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
            >
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
              <option value="90d">Last 90 Days</option>
            </select>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={handleRefresh}
              className="flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Refresh</span>
            </button>
            <button
              onClick={handleExport}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700 transition-colors"
            >
              <Download className="h-4 w-4" />
              <span>Export</span>
            </button>
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Average Safety Score</p>
              <p className="text-2xl font-semibold text-green-600">{averageMetrics.safety}%</p>
            </div>
            <ShieldCheck className="h-8 w-8 text-green-500" />
          </div>
        </div>
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Average Productivity</p>
              <p className="text-2xl font-semibold text-blue-600">{averageMetrics.productivity}%</p>
            </div>
            <TrendingUp className="h-8 w-8 text-blue-500" />
          </div>
        </div>
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Average Compliance</p>
              <p className="text-2xl font-semibold text-purple-600">{averageMetrics.compliance}%</p>
            </div>
            <CheckCircle className="h-8 w-8 text-purple-500" />
          </div>
        </div>
      </div>

      {/* Main Chart */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium mb-4">
          {selectedMetric.charAt(0).toUpperCase() + selectedMetric.slice(1)} by Site
        </h3>
        <div className="h-80">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={getMetricData()}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis domain={[0, 100]} />
              <Tooltip
                formatter={(value: any) => [
                  `${value}%`,
                  selectedMetric.charAt(0).toUpperCase() + selectedMetric.slice(1)
                ]}
                labelFormatter={(label: any, payload: any) => {
                  const item = payload?.[0]?.payload;
                  return item?.fullName || label;
                }}
              />
              <Bar 
                dataKey="value" 
                fill={selectedMetric === 'safety' ? '#10B981' : selectedMetric === 'productivity' ? '#3B82F6' : '#8B5CF6'}
                radius={[4, 4, 0, 0]}
              />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Performance Rankings */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Performing Sites */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium mb-4 flex items-center">
            <TrendingUp className="h-5 w-5 text-green-500 mr-2" />
            Top Performing Sites
          </h3>
          <div className="space-y-3">
            {topSites.map((site, index) => (
              <div key={site.siteId} className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                    {index + 1}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{site.siteName}</p>
                    <p className="text-sm text-gray-600">{site.workersOnSite} workers</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-green-600">{Math.round(site.overallScore)}%</p>
                  <p className="text-xs text-gray-500">Overall Score</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Sites Needing Attention */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium mb-4 flex items-center">
            <AlertTriangle className="h-5 w-5 text-orange-500 mr-2" />
            Sites Needing Attention
          </h3>
          <div className="space-y-3">
            {bottomSites.map((site, index) => (
              <div key={site.siteId} className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-6 h-6 bg-orange-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                    {performanceData.length - index}
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{site.siteName}</p>
                    <p className="text-sm text-gray-600">{site.incidentCount} incidents</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-orange-600">{Math.round(site.overallScore)}%</p>
                  <p className="text-xs text-gray-500">Overall Score</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CrossSitePerformance;
