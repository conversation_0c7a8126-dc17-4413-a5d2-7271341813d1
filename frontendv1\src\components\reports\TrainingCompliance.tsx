import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  <PERSON><PERSON>hart,
  Line,
  <PERSON><PERSON>hart,
  Pie,
  Cell,
} from 'recharts';
import {
  Clock,
  CheckCircle,
  AlertTriangle,
  Users,
  RefreshCw,
  Download
} from 'lucide-react';
import { mockSites } from '../../data/mockTenantData';
// import { mockWorkers, mockTrainings } from '../../data/mockData';

interface TrainingComplianceData {
  siteId: string;
  siteName: string;
  totalWorkers: number;
  compliantWorkers: number;
  expiringCertifications: number;
  expiredCertifications: number;
  complianceRate: number;
  trainingPrograms: number;
}

interface ComplianceStats {
  overallCompliance: number;
  totalWorkers: number;
  compliantWorkers: number;
  expiringCertifications: number;
  expiredCertifications: number;
  complianceByTrade: { trade: string; compliance: number; workers: number }[];
  complianceBySite: TrainingComplianceData[];
  complianceTrend: { month: string; compliance: number }[];
  expirationTimeline: { period: string; count: number }[];
}

const TrainingCompliance: React.FC = () => {
  const [complianceData, setComplianceData] = useState<ComplianceStats | null>(null);
  const [selectedView, setSelectedView] = useState<'overview' | 'by-site' | 'by-trade' | 'expiration'>('overview');
  const [timeRange, setTimeRange] = useState<'30d' | '90d' | '1y'>('90d');

  useEffect(() => {
    // Generate mock compliance data
    const siteComplianceData: TrainingComplianceData[] = mockSites.map(site => {
      const siteWorkers = Math.floor(Math.random() * 50) + 20; // 20-70 workers per site
      const complianceRate = 75 + Math.random() * 20; // 75-95% compliance
      const compliantWorkers = Math.floor(siteWorkers * (complianceRate / 100));
      const expiringCertifications = Math.floor(Math.random() * 8) + 2; // 2-10 expiring
      const expiredCertifications = Math.floor(Math.random() * 5); // 0-5 expired

      return {
        siteId: site.id,
        siteName: site.name,
        totalWorkers: siteWorkers,
        compliantWorkers,
        expiringCertifications,
        expiredCertifications,
        complianceRate: Math.round(complianceRate),
        trainingPrograms: Math.floor(Math.random() * 10) + 5 // 5-15 programs
      };
    });

    // Calculate overall stats
    const totalWorkers = siteComplianceData.reduce((sum, site) => sum + site.totalWorkers, 0);
    const compliantWorkers = siteComplianceData.reduce((sum, site) => sum + site.compliantWorkers, 0);
    const expiringCertifications = siteComplianceData.reduce((sum, site) => sum + site.expiringCertifications, 0);
    const expiredCertifications = siteComplianceData.reduce((sum, site) => sum + site.expiredCertifications, 0);
    const overallCompliance = Math.round((compliantWorkers / totalWorkers) * 100);

    // Generate compliance by trade
    const trades = ['Carpenter', 'Electrician', 'Plumber', 'Mason', 'Welder', 'Operator'];
    const complianceByTrade = trades.map(trade => ({
      trade,
      compliance: Math.round(70 + Math.random() * 25), // 70-95% compliance
      workers: Math.floor(Math.random() * 40) + 10 // 10-50 workers per trade
    }));

    // Generate compliance trend (last 6 months)
    const complianceTrend = [];
    for (let i = 5; i >= 0; i--) {
      const date = new Date();
      date.setMonth(date.getMonth() - i);
      const month = date.toLocaleDateString('en-US', { month: 'short' });
      const compliance = 80 + Math.random() * 15; // 80-95% with some variation
      complianceTrend.push({ month, compliance: Math.round(compliance) });
    }

    // Generate expiration timeline
    const expirationTimeline = [
      { period: 'Next 30 days', count: Math.floor(Math.random() * 15) + 5 },
      { period: 'Next 60 days', count: Math.floor(Math.random() * 20) + 10 },
      { period: 'Next 90 days', count: Math.floor(Math.random() * 25) + 15 },
      { period: 'Beyond 90 days', count: Math.floor(Math.random() * 30) + 20 }
    ];

    setComplianceData({
      overallCompliance,
      totalWorkers,
      compliantWorkers,
      expiringCertifications,
      expiredCertifications,
      complianceByTrade,
      complianceBySite: siteComplianceData,
      complianceTrend,
      expirationTimeline
    });
  }, [timeRange]);

  if (!complianceData) {
    return <div>Loading...</div>;
  }

  // const COLORS = ['#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#3B82F6', '#EC4899'];

  const handleRefresh = () => {
    console.log('Refreshing training compliance data...');
  };

  const handleExport = () => {
    console.log('Exporting training compliance report...');
  };

  return (
    <div className="space-y-6">
      {/* Controls and Actions */}
      <div className="bg-white rounded-lg border border-gray-200 p-4">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center space-y-4 sm:space-y-0">
          <div className="flex space-x-4">
            <select
              value={selectedView}
              onChange={(e) => setSelectedView(e.target.value as any)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
            >
              <option value="overview">Overview</option>
              <option value="by-site">By Site</option>
              <option value="by-trade">By Trade</option>
              <option value="expiration">Expiration Timeline</option>
            </select>
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value as any)}
              className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
            >
              <option value="30d">Last 30 Days</option>
              <option value="90d">Last 90 Days</option>
              <option value="1y">Last Year</option>
            </select>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={handleRefresh}
              className="flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Refresh</span>
            </button>
            <button
              onClick={handleExport}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700 transition-colors"
            >
              <Download className="h-4 w-4" />
              <span>Export</span>
            </button>
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Overall Compliance</p>
              <p className="text-2xl font-semibold text-green-600">{complianceData.overallCompliance}%</p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </div>
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Total Workers</p>
              <p className="text-2xl font-semibold text-blue-600">{complianceData.totalWorkers}</p>
            </div>
            <Users className="h-8 w-8 text-blue-500" />
          </div>
        </div>
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Expiring Soon</p>
              <p className="text-2xl font-semibold text-orange-600">{complianceData.expiringCertifications}</p>
            </div>
            <Clock className="h-8 w-8 text-orange-500" />
          </div>
        </div>
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">Expired</p>
              <p className="text-2xl font-semibold text-red-600">{complianceData.expiredCertifications}</p>
            </div>
            <AlertTriangle className="h-8 w-8 text-red-500" />
          </div>
        </div>
      </div>

      {selectedView === 'overview' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Compliance Trend */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium mb-4">Compliance Trend</h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={complianceData.complianceTrend}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis domain={[70, 100]} />
                  <Tooltip formatter={(value) => [`${value}%`, 'Compliance Rate']} />
                  <Line 
                    type="monotone" 
                    dataKey="compliance" 
                    stroke="#10B981" 
                    strokeWidth={3}
                    dot={{ fill: '#10B981', strokeWidth: 2, r: 4 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Compliance Status Distribution */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium mb-4">Compliance Status</h3>
            <div className="h-64">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={[
                      { name: 'Compliant', value: complianceData.compliantWorkers, color: '#10B981' },
                      { name: 'Expiring Soon', value: complianceData.expiringCertifications, color: '#F59E0B' },
                      { name: 'Expired', value: complianceData.expiredCertifications, color: '#EF4444' }
                    ]}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {[
                      { color: '#10B981' },
                      { color: '#F59E0B' },
                      { color: '#EF4444' }
                    ].map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>
        </div>
      )}

      {selectedView === 'by-site' && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium mb-4">Compliance by Site</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={complianceData.complianceBySite.map(site => ({
                name: site.siteName.split(' ')[0],
                compliance: site.complianceRate,
                fullName: site.siteName
              }))}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis domain={[0, 100]} />
                <Tooltip 
                  formatter={(value) => [`${value}%`, 'Compliance Rate']}
                  labelFormatter={(label, payload) => {
                    const item = payload?.[0]?.payload;
                    return item?.fullName || label;
                  }}
                />
                <Bar dataKey="compliance" fill="#10B981" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      )}

      {selectedView === 'by-trade' && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium mb-4">Compliance by Trade</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={complianceData.complianceByTrade}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="trade" />
                <YAxis domain={[0, 100]} />
                <Tooltip formatter={(value) => [`${value}%`, 'Compliance Rate']} />
                <Bar dataKey="compliance" fill="#8B5CF6" radius={[4, 4, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      )}

      {selectedView === 'expiration' && (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium mb-4">Certification Expiration Timeline</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={complianceData.expirationTimeline}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="period" />
                <YAxis />
                <Tooltip formatter={(value) => [`${value}`, 'Certifications']} />
                <Bar dataKey="count" radius={[4, 4, 0, 0]}>
                  {complianceData.expirationTimeline.map((_entry, index) => (
                    <Cell 
                      key={`cell-${index}`} 
                      fill={index === 0 ? '#EF4444' : index === 1 ? '#F59E0B' : index === 2 ? '#3B82F6' : '#10B981'} 
                    />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      )}
    </div>
  );
};

export default TrainingCompliance;
