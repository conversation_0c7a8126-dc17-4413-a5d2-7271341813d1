import React from 'react';
import { <PERSON><PERSON><PERSON>riangle, <PERSON>Checks, MessageSquare, Clock, TrendingDown, Activity, FileText, Eye } from 'lucide-react';
import KPICard from '../permits/shared/KPICard';
import QuickActions, { QuickActionItem } from '../shared/QuickActions';
import { useSafetyData } from './hooks/useSafetyData';

interface SafetyDashboardProps {
	siteId: string;
	onNavigateToTab?: (tabId: string) => void;
}



// Helper functions for HSE Alerts table
const getHSEAlerts = (dashboardData: any) => {
	const alerts = [];

	// Add incidents
	if (dashboardData?.recentIncidents) {
		dashboardData.recentIncidents.forEach((incident: any) => {
			alerts.push({ ...incident, type: 'incident' });
		});
	}

	// Add toolbox talks
	if (dashboardData?.recentToolboxTalks) {
		dashboardData.recentToolboxTalks.forEach((toolbox: any) => {
			alerts.push({ ...toolbox, type: 'toolbox-talk' });
		});
	}

	// Add observations
	if (dashboardData?.recentObservations) {
		dashboardData.recentObservations.forEach((observation: any) => {
			alerts.push({ ...observation, type: 'observation' });
		});
	}

	// Sort by date (most recent first)
	return alerts.sort((a, b) => {
		const dateA = new Date(a.createdAt || a.dateOfIncident || a.date || 0);
		const dateB = new Date(b.createdAt || b.dateOfIncident || b.date || 0);
		return dateB.getTime() - dateA.getTime();
	}).slice(0, 10); // Show latest 10 alerts
};

const getAlertIcon = (type: string) => {
	switch (type) {
		case 'incident':
			return <AlertTriangle className="h-4 w-4 text-red-500" />;
		case 'toolbox-talk':
			return <ListChecks className="h-4 w-4 text-green-500" />;
		case 'observation':
			return <MessageSquare className="h-4 w-4 text-blue-500" />;
		default:
			return <Activity className="h-4 w-4 text-gray-500" />;
	}
};

const getAlertSubType = (alert: any) => {
	switch (alert.type) {
		case 'incident':
			return alert.incidentType?.replace('-', ' ') || 'General';
		case 'toolbox-talk':
			return alert.status || 'Scheduled';
		case 'observation':
			return alert.observationType?.replace('-', ' ') || 'General';
		default:
			return '';
	}
};

const getAlertDescription = (alert: any) => {
	switch (alert.type) {
		case 'incident':
			return alert.description || 'Incident reported';
		case 'toolbox-talk':
			return alert.topic || 'Safety briefing';
		case 'observation':
			return alert.description || 'Safety observation';
		default:
			return '';
	}
};

const formatAlertDate = (dateString: string) => {
	if (!dateString) return '';
	const date = new Date(dateString);
	return date.toLocaleDateString('en-GB', {
		day: '2-digit',
		month: 'short',
		year: 'numeric'
	});
};

const formatAlertTime = (timeString: string) => {
	if (!timeString) return '';
	const date = new Date(timeString);
	return date.toLocaleTimeString('en-GB', {
		hour: '2-digit',
		minute: '2-digit'
	});
};

const SafetyDashboard: React.FC<SafetyDashboardProps> = ({
	siteId,
	onNavigateToTab,
}) => {
	const { data: dashboardData, isLoading } = useSafetyData(siteId, "dashboard");

	const handleAlertClick = (alert: any) => {
		switch (alert.type) {
			case 'incident':
				onNavigateToTab?.('incidents');
				break;
			case 'toolbox-talk':
				onNavigateToTab?.('toolbox-talks');
				break;
			case 'observation':
				onNavigateToTab?.('observations');
				break;
			default:
				break;
		}
	};

  const quickActions: QuickActionItem[] = [
    {
      title: 'Start Toolbox',
      description: 'Begin daily safety briefing',
      icon: <ListChecks className="h-6 w-6 text-green-600" />,
      onClick: () => onNavigateToTab?.('toolbox-talks')
    },
    {
      title: 'Report Incident',
      description: 'Report a new safety incident',
      icon: <AlertTriangle className="h-6 w-6 text-red-600" />,
      onClick: () => onNavigateToTab?.('incidents')
    },
    {
      title: 'Review RAMS',
      description: 'Access RAMS documents',
      icon: <FileText className="h-6 w-6 text-purple-600" />,
      onClick: () => onNavigateToTab?.('rams')
    },
    {
      title: 'Log Observation',
      description: 'Record safety observation',
      icon: <Eye className="h-6 w-6 text-blue-600" />,
      onClick: () => onNavigateToTab?.('observations')
    }
  ];

	if (isLoading) {
		return (
			<div className="space-y-6">
				<div className="flex items-center justify-center py-12">
					<div className="text-center">
						<div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"></div>
						<p className="mt-2 text-sm text-gray-500">Loading HSE dashboard...</p>
					</div>
				</div>
			</div>
		);
	}

  return (
    <div className="space-y-6">
      {/* HSE KPI Cards - Matching Permit Dashboard Design */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <KPICard
          title="Total Incidents"
          value={dashboardData?.totalIncidents || 0}
          change={dashboardData?.incidentTrend === 'down' ? -5 : dashboardData?.incidentTrend === 'up' ? 8 : 0}
          icon={<AlertTriangle className="h-6 w-6 text-red-500" />}
          onClick={() => onNavigateToTab?.('incidents')}
        />
        <KPICard
          title="Toolbox Talks"
          value={dashboardData?.toolboxTalks || 12}
          icon={<ListChecks className="h-6 w-6 text-green-500" />}
          onClick={() => onNavigateToTab?.('toolbox-talks')}
        />
        <KPICard
          title="Safety Observations"
          value={dashboardData?.safetyObservations || 24}
          change={12}
          icon={<MessageSquare className="h-6 w-6 text-blue-500" />}
          onClick={() => onNavigateToTab?.('observations')}
        />
        <KPICard
          title="RAMS Documents"
          value={dashboardData?.ramsDocuments || 8}
          icon={<FileText className="h-6 w-6 text-purple-500" />}
          onClick={() => onNavigateToTab?.('rams')}
        />
      </div>

			{/* Quick Actions */}
			<div className="mb-2">
				<h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
				<QuickActions actions={quickActions} />
			</div>

			{/* HSE Alerts List View */}
			<div className="bg-white rounded-lg border-2 border-gray-200 shadow-sm overflow-hidden" style={{ borderRadius: '5px' }}>
				<div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
					<div>
						<h3 className="text-lg font-medium text-gray-900">HSE Alerts</h3>
					</div>
					<button
						onClick={() => onNavigateToTab?.("incidents")}
						className="text-sm text-green-600 hover:text-green-800"
					>
						View All
					</button>
				</div>
				<table className="min-w-full divide-y divide-gray-200">
					<thead className="bg-gray-50">
						<tr>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date & Time</th>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">About/Description</th>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
						</tr>
					</thead>
					<tbody className="bg-white divide-y divide-gray-200">
						{getHSEAlerts(dashboardData).map((alert: any) => (
							<tr key={alert.id} className="hover:bg-gray-50 transition-colors">
								<td className="px-6 py-4">
									<div className="text-sm text-gray-900">
										{formatAlertDate(alert.createdAt || alert.dateOfIncident || alert.date)}
									</div>
									<div className="text-xs text-gray-500">
										{formatAlertTime(alert.createdAt || alert.timeOfIncident || alert.timeStarted)}
									</div>
								</td>
								<td className="px-6 py-4">
									<div className="flex items-center">
										{getAlertIcon(alert.type)}
										<div className="ml-2">
											<div className="text-sm font-medium text-gray-900 capitalize">
												{alert.type.replace('-', ' ')}
											</div>
											<div className="text-xs text-gray-500">
												{getAlertSubType(alert)}
											</div>
										</div>
									</div>
								</td>
								<td className="px-6 py-4">
									<div className="text-sm text-gray-900 line-clamp-2">
										{getAlertDescription(alert)}
									</div>
								</td>
								<td className="px-6 py-4">
									<button
										onClick={() => handleAlertClick(alert)}
										className="px-3 py-1 text-xs border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors"
										style={{ borderRadius: '5px' }}
									>
										View
									</button>
								</td>
							</tr>
						))}
					</tbody>
				</table>
			</div>
		</div>
	);
};

export default SafetyDashboard;
