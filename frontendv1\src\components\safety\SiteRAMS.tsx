import React, { useState } from 'react';
import { FileText, ExternalLink, Upload, Link, Calendar, AlertTriangle } from 'lucide-react';
import KPICard from '../dashboard/KPICard';
import SafetyTable from './shared/SafetyTable';
import { useSafetyData } from './hooks/useSafetyData';
import { SiteRAMS } from './types/safety';

interface SiteRAMSProps {
	siteId: string;
}

const StatusBadge: React.FC<{ status: string }> = ({ status }) => {
	const getStatusConfig = () => {
		switch (status) {
			case "active":
				return { className: "bg-green-100 text-green-800" };
			case "archived":
				return { className: "bg-gray-100 text-gray-800" };
			case "expired":
				return { className: "bg-red-100 text-red-800" };
			default:
				return { className: "bg-gray-100 text-gray-800" };
		}
	};

	const config = getStatusConfig();
	return (
		<span
			className={`px-2 py-1 text-xs font-medium rounded-full capitalize ${config.className}`}
		>
			{status}
		</span>
	);
};

const SourceTypeBadge: React.FC<{ sourceType: string }> = ({ sourceType }) => {
	const getSourceConfig = () => {
		switch (sourceType) {
			case "upload":
				return {
					label: "Site Upload",
					className: "bg-blue-100 text-blue-800",
					icon: <Upload className="h-3 w-3" />,
				};
			case "master-link":
				return {
					label: "Master Link",
					className: "bg-purple-100 text-purple-800",
					icon: <Link className="h-3 w-3" />,
				};
			default:
				return {
					label: sourceType,
					className: "bg-gray-100 text-gray-800",
					icon: null,
				};
		}
	};

	const config = getSourceConfig();
	return (
		<span
			className={`px-2 py-1 text-xs font-medium rounded-full flex items-center space-x-1 ${config.className}`}
		>
			{config.icon}
			<span>{config.label}</span>
		</span>
	);
};

const SiteRAMSComponent: React.FC<SiteRAMSProps> = ({ siteId }) => {
	const [showUploadModal, setShowUploadModal] = useState(false);
	const { data: ramsDocuments, isLoading } = useSafetyData(siteId, "rams");

	const handleUploadRAMS = () => {
		setShowUploadModal(true);
	};

	const handleRAMSUploaded = () => {
		setShowUploadModal(false);
		// Refresh data would happen here
	};

	const isExpiringSoon = (expiryDate?: string) => {
		if (!expiryDate) return false;
		const expiry = new Date(expiryDate);
		const thirtyDaysFromNow = new Date();
		thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
		return expiry <= thirtyDaysFromNow;
	};

	const isExpired = (expiryDate?: string) => {
		if (!expiryDate) return false;
		return new Date(expiryDate) < new Date();
	};

	const columns = [
		{
			header: "Title",
			accessor: "title",
			sortable: true,
		},
		{
			header: "Version",
			accessor: "version",
		},
		{
			header: "Effective Date",
			accessor: "effectiveDate",
			renderCell: (rams: SiteRAMS) =>
				new Date(rams.effectiveDate).toLocaleDateString(),
		},
		{
			header: "Expiry Date",
			accessor: "expiryDate",
			renderCell: (rams: SiteRAMS) => {
				if (!rams.expiryDate) return "N/A";
				const expiryDate = new Date(rams.expiryDate).toLocaleDateString();
				const expiringSoon = isExpiringSoon(rams.expiryDate);
				const expired = isExpired(rams.expiryDate);

				return (
					<div className="flex items-center space-x-2">
						<span>{expiryDate}</span>
						{(expired || expiringSoon) && (
							<AlertTriangle
								className={`h-4 w-4 ${expired ? "text-red-500" : "text-yellow-500"}`}
							/>
						)}
					</div>
				);
			},
		},
		{
			header: "Source",
			accessor: "sourceType",
			renderCell: (rams: SiteRAMS) => (
				<SourceTypeBadge sourceType={rams.sourceType} />
			),
		},
		{
			header: "Status",
			accessor: "status",
			renderCell: (rams: SiteRAMS) => <StatusBadge status={rams.status} />,
		},
		{
			header: "Uploaded By",
			accessor: "uploadedByName",
		},
		{
			header: "Actions",
			accessor: "actions",
			renderCell: (rams: SiteRAMS) => (
				<div className="flex space-x-2">
					<button
						onClick={() => window.open(rams.documentUrl, "_blank")}
						className="text-blue-600 hover:text-blue-800 flex items-center space-x-1"
					>
						<ExternalLink className="h-4 w-4" />
						<span>View</span>
					</button>
					<button className="text-gray-600 hover:text-gray-800">Edit</button>
				</div>
			),
		},
	];

	// Calculate KPI data
	const activeRAMS =
		ramsDocuments?.filter((r: SiteRAMS) => r.status === "active").length || 0;
	const expiringSoon =
		ramsDocuments?.filter(
			(r: SiteRAMS) =>
				r.expiryDate &&
				isExpiringSoon(r.expiryDate) &&
				!isExpired(r.expiryDate),
		).length || 0;
	const expired =
		ramsDocuments?.filter(
			(r: SiteRAMS) =>
				r.status === "expired" || (r.expiryDate && isExpired(r.expiryDate)),
		).length || 0;
	const totalDocuments = ramsDocuments?.length || 0;

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<h2 className="text-xl font-semibold">Site RAMS Management</h2>
				<button
					onClick={handleUploadRAMS}
					className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2"
				>
					<FileText className="h-4 w-4" />
					<span>Upload/Link RAMS</span>
				</button>
			</div>

      {/* Quick stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <KPICard
          title="Active RAMS"
          value={activeRAMS}
          icon={<FileText className="h-6 w-6 text-green-500" />}
        />
        <KPICard
          title="Expiring Soon"
          value={expiringSoon}
          icon={<Calendar className="h-6 w-6 text-orange-500" />}
        />
        <KPICard
          title="Expired"
          value={expired}
          icon={<AlertTriangle className="h-6 w-6 text-red-500" />}
        />
        <KPICard
          title="Total Documents"
          value={totalDocuments}
          icon={<FileText className="h-6 w-6 text-blue-500" />}
        />
      </div>

			{/* RAMS documents table */}
			<div className="bg-white rounded-lg border">
				<div className="p-6">
					<h3 className="text-lg font-medium mb-4">RAMS Documents</h3>
					<SafetyTable
						data={ramsDocuments || []}
						columns={columns}
						isLoading={isLoading}
						searchable={true}
						searchPlaceholder="Search RAMS documents..."
					/>
				</div>
			</div>

			{/* Upload RAMS modal */}
			{showUploadModal && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
					<div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
						<h3 className="text-lg font-semibold mb-4">
							Upload/Link RAMS Document
						</h3>
						<div className="space-y-4">
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-2">
									Choose Option
								</label>
								<div className="space-y-2">
									<label className="flex items-center">
										<input
											type="radio"
											name="rams-option"
											value="upload"
											className="mr-2"
										/>
										<Upload className="h-4 w-4 mr-2" />
										Upload new document
									</label>
									<label className="flex items-center">
										<input
											type="radio"
											name="rams-option"
											value="link"
											className="mr-2"
										/>
										<Link className="h-4 w-4 mr-2" />
										Link to master RAMS
									</label>
								</div>
							</div>
							<p className="text-gray-600 text-sm">
								Full RAMS upload/link form will be implemented here.
							</p>
						</div>
						<div className="flex justify-end space-x-3 mt-6">
							<button
								onClick={() => setShowUploadModal(false)}
								className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
							>
								Cancel
							</button>
							<button
								onClick={handleRAMSUploaded}
								className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
							>
								Upload/Link
							</button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default SiteRAMSComponent;
