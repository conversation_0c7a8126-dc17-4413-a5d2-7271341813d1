import { useState } from "react";
import { SafetyFilters } from "../types/safety";

export const useSafetyFilters = () => {
	const [filters, setFilters] = useState<SafetyFilters>({});

	const updateFilter = (key: keyof SafetyFilters, value: any) => {
		setFilters((prev) => ({
			...prev,
			[key]: value,
		}));
	};

	const clearFilters = () => {
		setFilters({});
	};

	const clearFilter = (key: keyof SafetyFilters) => {
		setFilters((prev) => {
			const newFilters = { ...prev };
			delete newFilters[key];
			return newFilters;
		});
	};

	return {
		filters,
		updateFilter,
		clearFilters,
		clearFilter,
	};
};
