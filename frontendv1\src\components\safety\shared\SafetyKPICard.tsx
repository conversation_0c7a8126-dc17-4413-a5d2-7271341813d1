import React from "react";
import { TrendingUp, TrendingDown, Minus } from "lucide-react";

interface SafetyKPICardProps {
	title: string;
	value: string | number;
	trend?: "up" | "down" | "stable";
	status?: "good" | "warning" | "danger";
	change?: number;
	icon?: React.ReactNode;
}

const SafetyKPICard: React.FC<SafetyKPICardProps> = ({
	title,
	value,
	trend,
	status = "good",
	change,
	icon,
}) => {
	const getStatusColor = () => {
		switch (status) {
			case "good":
				return "border-green-200 bg-green-50";
			case "warning":
				return "border-yellow-200 bg-yellow-50";
			case "danger":
				return "border-red-200 bg-red-50";
			default:
				return "border-gray-200 bg-white";
		}
	};

	const getValueColor = () => {
		switch (status) {
			case "good":
				return "text-green-900";
			case "warning":
				return "text-yellow-900";
			case "danger":
				return "text-red-900";
			default:
				return "text-gray-900";
		}
	};

	const getTrendIcon = () => {
		switch (trend) {
			case "up":
				return <TrendingUp className="h-4 w-4 text-green-500" />;
			case "down":
				return <TrendingDown className="h-4 w-4 text-red-500" />;
			case "stable":
				return <Minus className="h-4 w-4 text-gray-500" />;
			default:
				return null;
		}
	};

	const getTrendColor = () => {
		// For safety metrics, 'down' trend is usually good (fewer incidents)
		// 'up' trend might be bad for incidents but good for observations
		switch (trend) {
			case "up":
				return title.toLowerCase().includes("incident")
					? "text-red-500"
					: "text-green-500";
			case "down":
				return title.toLowerCase().includes("incident")
					? "text-green-500"
					: "text-red-500";
			default:
				return "text-gray-500";
		}
	};

	return (
		<div className={`p-6 rounded-lg border ${getStatusColor()} shadow-sm`}>
			<div className="flex justify-between items-start">
				<div className="flex-1">
					<p className="text-sm font-medium text-gray-600">{title}</p>
					<p className={`mt-2 text-2xl font-semibold ${getValueColor()}`}>
						{value}
					</p>
					{(change !== undefined || trend) && (
						<div className="mt-2 flex items-center space-x-1">
							{trend && getTrendIcon()}
							{change !== undefined && (
								<span className={`text-sm font-medium ${getTrendColor()}`}>
									{change >= 0 ? "+" : ""}
									{change}%
								</span>
							)}
							{change !== undefined && (
								<span className="text-sm text-gray-500">vs. last month</span>
							)}
						</div>
					)}
				</div>
				{icon && <div className="text-gray-400 ml-4">{icon}</div>}
			</div>
		</div>
	);
};

export default SafetyKPICard;
