import React, { useState } from "react";
import { ChevronUp, ChevronDown, Search } from "lucide-react";

interface Column {
	header: string;
	accessor: string;
	sortable?: boolean;
	renderCell?: (item: any) => React.ReactNode;
}

interface SafetyTableProps {
	data: any[];
	columns: Column[];
	isLoading?: boolean;
	searchable?: boolean;
	searchPlaceholder?: string;
}

const SafetyTable: React.FC<SafetyTableProps> = ({
	data,
	columns,
	isLoading = false,
	searchable = false,
	searchPlaceholder = "Search...",
}) => {
	const [sortConfig, setSortConfig] = useState<{
		key: string;
		direction: "asc" | "desc";
	} | null>(null);
	const [searchTerm, setSearchTerm] = useState("");

	const handleSort = (key: string) => {
		let direction: "asc" | "desc" = "asc";
		if (
			sortConfig &&
			sortConfig.key === key &&
			sortConfig.direction === "asc"
		) {
			direction = "desc";
		}
		setSortConfig({ key, direction });
	};

	const getSortedData = () => {
		let sortedData = [...data];

		// Apply search filter
		if (searchTerm) {
			sortedData = sortedData.filter((item) =>
				Object.values(item).some((value) =>
					String(value).toLowerCase().includes(searchTerm.toLowerCase()),
				),
			);
		}

		// Apply sorting
		if (sortConfig) {
			sortedData.sort((a, b) => {
				const aValue = a[sortConfig.key];
				const bValue = b[sortConfig.key];

				if (aValue < bValue) {
					return sortConfig.direction === "asc" ? -1 : 1;
				}
				if (aValue > bValue) {
					return sortConfig.direction === "asc" ? 1 : -1;
				}
				return 0;
			});
		}

		return sortedData;
	};

	const sortedData = getSortedData();

	if (isLoading) {
		return (
			<div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
				<div className="animate-pulse">
					<div className="bg-gray-50 h-12"></div>
					{[...Array(5)].map((_, i) => (
						<div
							key={i}
							className="border-t border-gray-200 h-16 bg-gray-100"
						></div>
					))}
				</div>
			</div>
		);
	}

	return (
		<div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
			{searchable && (
				<div className="p-4 border-b border-gray-200">
					<div className="relative">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
						<input
							type="text"
							placeholder={searchPlaceholder}
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
						/>
					</div>
				</div>
			)}

			<div className="overflow-x-auto">
				<table className="min-w-full divide-y divide-gray-200">
					<thead className="bg-gray-50">
						<tr>
							{columns.map((column) => (
								<th
									key={column.accessor}
									className={`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${
										column.sortable ? "cursor-pointer hover:bg-gray-100" : ""
									}`}
									onClick={() => column.sortable && handleSort(column.accessor)}
								>
									<div className="flex items-center space-x-1">
										<span>{column.header}</span>
										{column.sortable && (
											<div className="flex flex-col">
												<ChevronUp
													className={`h-3 w-3 ${
														sortConfig?.key === column.accessor &&
														sortConfig.direction === "asc"
															? "text-green-600"
															: "text-gray-400"
													}`}
												/>
												<ChevronDown
													className={`h-3 w-3 -mt-1 ${
														sortConfig?.key === column.accessor &&
														sortConfig.direction === "desc"
															? "text-green-600"
															: "text-gray-400"
													}`}
												/>
											</div>
										)}
									</div>
								</th>
							))}
						</tr>
					</thead>
					<tbody className="bg-white divide-y divide-gray-200">
						{sortedData.length > 0 ? (
							sortedData.map((item, index) => (
								<tr key={item.id || index} className="hover:bg-gray-50">
									{columns.map((column) => (
										<td
											key={column.accessor}
											className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
										>
											{column.renderCell
												? column.renderCell(item)
												: item[column.accessor]}
										</td>
									))}
								</tr>
							))
						) : (
							<tr>
								<td
									colSpan={columns.length}
									className="px-6 py-12 text-center text-gray-500"
								>
									{searchTerm ? "No results found" : "No data available"}
								</td>
							</tr>
						)}
					</tbody>
				</table>
			</div>
		</div>
	);
};

export default SafetyTable;
