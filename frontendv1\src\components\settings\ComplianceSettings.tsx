import { useState } from "react";
import { Calcula<PERSON>, Shield, <PERSON><PERSON>he<PERSON>, Save, Info } from "lucide-react";

interface PayrollCompliance {
	paye: {
		personalRelief: number;
		taxBands: Array<{
			lowerLimit: number;
			upperLimit: number | null;
			rate: number;
		}>;
	};
	nssf: {
		employeeRate: number;
		employerRate: number;
		lowerLimit: number;
		upperLimit: number;
	};
	shif: {
		rate: number;
		minimumContribution: number;
	};
	housingLevy: {
		rate: number;
	};
	remittanceDates: {
		paye: number;
		nssf: number;
		shif: number;
	};
}

const ComplianceSettings = () => {
	const [activeSection, setActiveSection] = useState("payroll");
	const [hasChanges, setHasChanges] = useState(false);

	// Current 2025 Kenyan tax rates
	const [payrollSettings, setPayrollSettings] = useState<PayrollCompliance>({
		paye: {
			personalRelief: 2400,
			taxBands: [
				{ lowerLimit: 0, upperLimit: 24000, rate: 10 },
				{ lowerLimit: 24001, upperLimit: 32333, rate: 25 },
				{ lowerLimit: 32334, upperLimit: 500000, rate: 30 },
				{ lowerLimit: 500001, upperLimit: 800000, rate: 32.5 },
				{ lowerLimit: 800001, upperLimit: null, rate: 35 },
			],
		},
		nssf: {
			employeeRate: 6,
			employerRate: 6,
			lowerLimit: 7000,
			upperLimit: 36000,
		},
		shif: {
			rate: 2.75,
			minimumContribution: 300,
		},
		housingLevy: {
			rate: 1.5,
		},
		remittanceDates: {
			paye: 9,
			nssf: 15,
			shif: 15,
		},
	});

	const sections = [
		{ id: "payroll", label: "Payroll Compliance", icon: Calculator },
		{ id: "safety", label: "Safety Compliance", icon: Shield },
		{ id: "general", label: "General Compliance", icon: FileCheck },
	];

	const handleSave = () => {
		// TODO: Implement save functionality
		console.log("Saving compliance settings:", payrollSettings);
		setHasChanges(false);
	};

	const updatePayrollSetting = (path: string, value: any) => {
		setPayrollSettings((prev) => {
			const keys = path.split(".");
			const newSettings = { ...prev };
			let current: any = newSettings;

			for (let i = 0; i < keys.length - 1; i++) {
				current = current[keys[i]];
			}
			current[keys[keys.length - 1]] = value;

			return newSettings;
		});
		setHasChanges(true);
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
				<div>
					<h2 className="text-2xl font-bold text-gray-900">
						Compliance Settings
					</h2>
					<p className="text-sm text-gray-600">
						Configure regulatory compliance for Kenyan labor laws
					</p>
				</div>
				<button
					onClick={handleSave}
					disabled={!hasChanges}
					className="bg-green-500 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center hover:bg-green-600 transition-colors disabled:opacity-50"
				>
					<Save className="h-4 w-4 mr-2" />
					Save Changes
				</button>
			</div>

			{/* Section Navigation */}
			<div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
				{sections.map((section) => (
					<button
						key={section.id}
						onClick={() => setActiveSection(section.id)}
						className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${
							activeSection === section.id
								? "bg-white text-gray-900 shadow-sm"
								: "text-gray-600 hover:text-gray-900"
						}`}
					>
						<section.icon className="h-4 w-4 mr-2" />
						{section.label}
					</button>
				))}
			</div>

			{/* Content */}
			<div className="bg-white rounded-lg border border-gray-200 p-6">
				{activeSection === "payroll" && (
					<div className="space-y-6">
						<h3 className="text-lg font-semibold text-gray-900">
							Payroll Compliance Settings
						</h3>

						{/* PAYE Settings */}
						<div className="border border-gray-200 rounded-lg p-4">
							<h4 className="font-medium text-gray-900 mb-4">
								PAYE (Pay As You Earn) Tax
							</h4>
							<div className="space-y-4">
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-1">
										Personal Relief (KES per month)
									</label>
									<input
										type="number"
										value={payrollSettings.paye.personalRelief}
										onChange={(e) =>
											updatePayrollSetting(
												"paye.personalRelief",
												parseInt(e.target.value),
											)
										}
										className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
									/>
								</div>
								<div>
									<h5 className="font-medium text-gray-700 mb-2">
										Tax Bands (2025 Rates)
									</h5>
									<div className="space-y-2">
										{payrollSettings.paye.taxBands.map((band, index) => (
											<div
												key={index}
												className="grid grid-cols-4 gap-2 items-center"
											>
												<input
													type="number"
													placeholder="Lower limit"
													value={band.lowerLimit}
													onChange={(e) => {
														const newBands = [...payrollSettings.paye.taxBands];
														newBands[index].lowerLimit = parseInt(
															e.target.value,
														);
														updatePayrollSetting("paye.taxBands", newBands);
													}}
													className="border border-gray-300 rounded-md px-3 py-2 text-sm"
												/>
												<input
													type="number"
													placeholder="Upper limit"
													value={band.upperLimit || ""}
													onChange={(e) => {
														const newBands = [...payrollSettings.paye.taxBands];
														newBands[index].upperLimit = e.target.value
															? parseInt(e.target.value)
															: null;
														updatePayrollSetting("paye.taxBands", newBands);
													}}
													className="border border-gray-300 rounded-md px-3 py-2 text-sm"
												/>
												<input
													type="number"
													step="0.1"
													placeholder="Rate %"
													value={band.rate}
													onChange={(e) => {
														const newBands = [...payrollSettings.paye.taxBands];
														newBands[index].rate = parseFloat(e.target.value);
														updatePayrollSetting("paye.taxBands", newBands);
													}}
													className="border border-gray-300 rounded-md px-3 py-2 text-sm"
												/>
												<span className="text-sm text-gray-500">
													{band.upperLimit
														? `${band.lowerLimit.toLocaleString()} - ${band.upperLimit.toLocaleString()}`
														: `${band.lowerLimit.toLocaleString()}+`}
												</span>
											</div>
										))}
									</div>
								</div>
							</div>
						</div>

						{/* NSSF Settings */}
						<div className="border border-gray-200 rounded-lg p-4">
							<h4 className="font-medium text-gray-900 mb-4">
								NSSF (National Social Security Fund)
							</h4>
							<div className="grid grid-cols-2 gap-4">
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-1">
										Employee Rate (%)
									</label>
									<input
										type="number"
										step="0.1"
										value={payrollSettings.nssf.employeeRate}
										onChange={(e) =>
											updatePayrollSetting(
												"nssf.employeeRate",
												parseFloat(e.target.value),
											)
										}
										className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
									/>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-1">
										Employer Rate (%)
									</label>
									<input
										type="number"
										step="0.1"
										value={payrollSettings.nssf.employerRate}
										onChange={(e) =>
											updatePayrollSetting(
												"nssf.employerRate",
												parseFloat(e.target.value),
											)
										}
										className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
									/>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-1">
										Lower Limit (KES)
									</label>
									<input
										type="number"
										value={payrollSettings.nssf.lowerLimit}
										onChange={(e) =>
											updatePayrollSetting(
												"nssf.lowerLimit",
												parseInt(e.target.value),
											)
										}
										className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
									/>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-1">
										Upper Limit (KES)
									</label>
									<input
										type="number"
										value={payrollSettings.nssf.upperLimit}
										onChange={(e) =>
											updatePayrollSetting(
												"nssf.upperLimit",
												parseInt(e.target.value),
											)
										}
										className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
									/>
								</div>
							</div>
						</div>

						{/* SHIF Settings */}
						<div className="border border-gray-200 rounded-lg p-4">
							<h4 className="font-medium text-gray-900 mb-4">
								SHIF (Social Health Insurance Fund)
							</h4>
							<div className="grid grid-cols-2 gap-4">
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-1">
										Rate (%)
									</label>
									<input
										type="number"
										step="0.01"
										value={payrollSettings.shif.rate}
										onChange={(e) =>
											updatePayrollSetting(
												"shif.rate",
												parseFloat(e.target.value),
											)
										}
										className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
									/>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-1">
										Minimum Contribution (KES)
									</label>
									<input
										type="number"
										value={payrollSettings.shif.minimumContribution}
										onChange={(e) =>
											updatePayrollSetting(
												"shif.minimumContribution",
												parseInt(e.target.value),
											)
										}
										className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
									/>
								</div>
							</div>
						</div>

						{/* Housing Levy */}
						<div className="border border-gray-200 rounded-lg p-4">
							<h4 className="font-medium text-gray-900 mb-4">Housing Levy</h4>
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-1">
									Rate (%)
								</label>
								<input
									type="number"
									step="0.1"
									value={payrollSettings.housingLevy.rate}
									onChange={(e) =>
										updatePayrollSetting(
											"housingLevy.rate",
											parseFloat(e.target.value),
										)
									}
									className="w-full max-w-xs border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
								/>
							</div>
						</div>

						{/* Remittance Dates */}
						<div className="border border-gray-200 rounded-lg p-4">
							<h4 className="font-medium text-gray-900 mb-4">
								Remittance Dates (Day of Month)
							</h4>
							<div className="grid grid-cols-3 gap-4">
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-1">
										PAYE
									</label>
									<input
										type="number"
										min="1"
										max="31"
										value={payrollSettings.remittanceDates.paye}
										onChange={(e) =>
											updatePayrollSetting(
												"remittanceDates.paye",
												parseInt(e.target.value),
											)
										}
										className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
									/>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-1">
										NSSF
									</label>
									<input
										type="number"
										min="1"
										max="31"
										value={payrollSettings.remittanceDates.nssf}
										onChange={(e) =>
											updatePayrollSetting(
												"remittanceDates.nssf",
												parseInt(e.target.value),
											)
										}
										className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
									/>
								</div>
								<div>
									<label className="block text-sm font-medium text-gray-700 mb-1">
										SHIF
									</label>
									<input
										type="number"
										min="1"
										max="31"
										value={payrollSettings.remittanceDates.shif}
										onChange={(e) =>
											updatePayrollSetting(
												"remittanceDates.shif",
												parseInt(e.target.value),
											)
										}
										className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
									/>
								</div>
							</div>
						</div>
					</div>
				)}

				{activeSection === "safety" && (
					<div className="space-y-6">
						<h3 className="text-lg font-semibold text-gray-900">
							Safety Compliance Settings
						</h3>
						<div className="text-center py-12">
							<Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
							<h4 className="text-lg font-medium text-gray-900 mb-2">
								Safety Compliance Configuration
							</h4>
							<p className="text-gray-600">
								OSH Act compliance settings, safety standards, and certification
								requirements will be configured here.
							</p>
						</div>
					</div>
				)}

				{activeSection === "general" && (
					<div className="space-y-6">
						<h3 className="text-lg font-semibold text-gray-900">
							General Compliance Settings
						</h3>
						<div className="text-center py-12">
							<FileCheck className="h-12 w-12 text-gray-400 mx-auto mb-4" />
							<h4 className="text-lg font-medium text-gray-900 mb-2">
								General Compliance Configuration
							</h4>
							<p className="text-gray-600">
								General regulatory compliance settings and documentation
								requirements will be configured here.
							</p>
						</div>
					</div>
				)}
			</div>

			{/* Information Notice */}
			<div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
				<div className="flex">
					<div className="flex-shrink-0">
						<Info className="h-5 w-5 text-blue-400" />
					</div>
					<div className="ml-3">
						<h3 className="text-sm font-medium text-blue-800">
							Compliance Information
						</h3>
						<div className="mt-2 text-sm text-blue-700">
							<p>
								These settings are configured according to current Kenyan tax
								and labor laws as of 2025. Please consult with your tax advisor
								or HR specialist before making changes. The system will
								automatically calculate deductions based on these rates.
							</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default ComplianceSettings;
