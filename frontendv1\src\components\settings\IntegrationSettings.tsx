import { useState } from "react";
import {
	Plug,
	CheckCircle,
	XCircle,
	Settings,
	Eye,
	EyeOff,
	RefreshCw,
} from "lucide-react";

interface Integration {
	id: string;
	name: string;
	description: string;
	status: "connected" | "disconnected" | "error";
	lastSync?: Date;
	config: Record<string, any>;
}

const IntegrationSettings = () => {
	const [integrations, _setIntegrations] = useState<Integration[]>([
		{
			id: "hikvision",
			name: "Hikvision Access Control",
			description:
				"Biometric terminals for worker time tracking and site access",
			status: "connected",
			lastSync: new Date("2025-01-15T10:30:00"),
			config: {
				serverUrl: "https://*************:8443",
				username: "admin",
				password: "••••••••",
				syncInterval: 300, // 5 minutes
			},
		},
		{
			id: "mpesa",
			name: "M-Pesa Integration",
			description: "Mobile money payments for worker salaries and allowances",
			status: "connected",
			lastSync: new Date("2025-01-15T09:45:00"),
			config: {
				consumerKey: "xxxxxxxxxxxxxxxxxxx",
				consumerSecret: "••••••••••••••••••••",
				environment: "sandbox",
				shortcode: "174379",
				passkey: "••••••••••••••••••••••••••••••••••••••••••••••••",
			},
		},
		{
			id: "email",
			name: "Email Service",
			description: "SMTP configuration for system notifications and reports",
			status: "disconnected",
			config: {
				smtpHost: "smtp.gmail.com",
				smtpPort: 587,
				username: "<EMAIL>",
				password: "••••••••",
				encryption: "tls",
			},
		},
	]);

	const [selectedIntegration, setSelectedIntegration] =
		useState<Integration | null>(null);
	const [showPasswords, setShowPasswords] = useState<Record<string, boolean>>(
		{},
	);

	const getStatusIcon = (status: Integration["status"]) => {
		switch (status) {
			case "connected":
				return <CheckCircle className="h-5 w-5 text-green-500" />;
			case "error":
				return <XCircle className="h-5 w-5 text-red-500" />;
			default:
				return <XCircle className="h-5 w-5 text-gray-400" />;
		}
	};

	const getStatusText = (status: Integration["status"]) => {
		switch (status) {
			case "connected":
				return "Connected";
			case "error":
				return "Error";
			default:
				return "Disconnected";
		}
	};

	const formatLastSync = (date?: Date) => {
		if (!date) return "Never";
		const now = new Date();
		const diffInMinutes = Math.floor(
			(now.getTime() - date.getTime()) / (1000 * 60),
		);

		if (diffInMinutes < 1) return "Just now";
		if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
		if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`;
		return date.toLocaleDateString();
	};

	const handleTestConnection = (integrationId: string) => {
		// TODO: Implement connection test
		console.log("Testing connection for:", integrationId);
	};

	const handleTogglePassword = (field: string) => {
		setShowPasswords((prev) => ({
			...prev,
			[field]: !prev[field],
		}));
	};

	const renderConfigField = (
		key: string,
		value: any,
		integration: Integration,
	) => {
		const isPassword =
			key.toLowerCase().includes("password") ||
			key.toLowerCase().includes("secret") ||
			key.toLowerCase().includes("passkey");

		if (isPassword) {
			return (
				<div className="relative">
					<input
						type={
							showPasswords[`${integration.id}-${key}`] ? "text" : "password"
						}
						value={value}
						onChange={() => {}} // TODO: Implement change handler
						className="w-full border border-gray-300 rounded-md px-3 py-2 pr-10 focus:ring-green-500 focus:border-green-500"
					/>
					<button
						type="button"
						onClick={() => handleTogglePassword(`${integration.id}-${key}`)}
						className="absolute inset-y-0 right-0 pr-3 flex items-center"
					>
						{showPasswords[`${integration.id}-${key}`] ? (
							<EyeOff className="h-4 w-4 text-gray-400" />
						) : (
							<Eye className="h-4 w-4 text-gray-400" />
						)}
					</button>
				</div>
			);
		}

		if (typeof value === "boolean") {
			return (
				<select
					value={value.toString()}
					onChange={() => {}} // TODO: Implement change handler
					className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
				>
					<option value="true">True</option>
					<option value="false">False</option>
				</select>
			);
		}

		if (typeof value === "number") {
			return (
				<input
					type="number"
					value={value}
					onChange={() => {}} // TODO: Implement change handler
					className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
				/>
			);
		}

		return (
			<input
				type="text"
				value={value}
				onChange={() => {}} // TODO: Implement change handler
				className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-green-500 focus:border-green-500"
			/>
		);
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
				<div>
					<h2 className="text-2xl font-bold text-gray-900">
						Integration Settings
					</h2>
					<p className="text-sm text-gray-600">
						Manage third-party integrations and API connections
					</p>
				</div>
			</div>

			{/* Integrations List */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				{integrations.map((integration) => (
					<div
						key={integration.id}
						className="bg-white rounded-lg border border-gray-200 p-6"
					>
						<div className="flex items-start justify-between mb-4">
							<div className="flex items-center">
								<div className="p-2 bg-blue-100 rounded-lg mr-3">
									<Plug className="h-6 w-6 text-blue-600" />
								</div>
								<div>
									<h3 className="text-lg font-semibold text-gray-900">
										{integration.name}
									</h3>
									<p className="text-sm text-gray-600">
										{integration.description}
									</p>
								</div>
							</div>
							<div className="flex items-center space-x-2">
								{getStatusIcon(integration.status)}
								<span
									className={`text-sm font-medium ${
										integration.status === "connected"
											? "text-green-600"
											: integration.status === "error"
												? "text-red-600"
												: "text-gray-600"
									}`}
								>
									{getStatusText(integration.status)}
								</span>
							</div>
						</div>

						<div className="space-y-2 mb-4">
							<div className="flex justify-between text-sm">
								<span className="text-gray-600">Last Sync:</span>
								<span className="text-gray-900">
									{formatLastSync(integration.lastSync)}
								</span>
							</div>
							{integration.status === "connected" && (
								<div className="flex justify-between text-sm">
									<span className="text-gray-600">Status:</span>
									<span className="text-green-600">Active</span>
								</div>
							)}
						</div>

						<div className="flex space-x-2">
							<button
								onClick={() => setSelectedIntegration(integration)}
								className="flex-1 bg-gray-100 text-gray-700 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-200 transition-colors flex items-center justify-center"
							>
								<Settings className="h-4 w-4 mr-2" />
								Configure
							</button>
							<button
								onClick={() => handleTestConnection(integration.id)}
								className="flex-1 bg-green-500 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-600 transition-colors flex items-center justify-center"
							>
								<RefreshCw className="h-4 w-4 mr-2" />
								Test
							</button>
						</div>
					</div>
				))}
			</div>

			{/* Configuration Modal */}
			{selectedIntegration && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
					<div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto">
						<div className="flex items-center justify-between mb-6">
							<h3 className="text-lg font-semibold text-gray-900">
								Configure {selectedIntegration.name}
							</h3>
							<button
								onClick={() => setSelectedIntegration(null)}
								className="text-gray-400 hover:text-gray-600"
							>
								<XCircle className="h-6 w-6" />
							</button>
						</div>

						<div className="space-y-4">
							{Object.entries(selectedIntegration.config).map(
								([key, value]) => (
									<div key={key}>
										<label className="block text-sm font-medium text-gray-700 mb-1 capitalize">
											{key
												.replace(/([A-Z])/g, " $1")
												.replace(/^./, (str) => str.toUpperCase())}
										</label>
										{renderConfigField(key, value, selectedIntegration)}
									</div>
								),
							)}
						</div>

						{/* Integration-specific help text */}
						<div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
							<h4 className="text-sm font-medium text-blue-800 mb-2">
								Configuration Help
							</h4>
							<div className="text-sm text-blue-700">
								{selectedIntegration.id === "hikvision" && (
									<p>
										Configure your Hikvision access control system for biometric
										time tracking. Ensure the server URL is accessible from your
										network and credentials have appropriate permissions for
										device management.
									</p>
								)}
								{selectedIntegration.id === "mpesa" && (
									<p>
										Set up M-Pesa integration for salary disbursements. You'll
										need to register your application with Safaricom and obtain
										the necessary API credentials. Use sandbox environment for
										testing.
									</p>
								)}
								{selectedIntegration.id === "email" && (
									<p>
										Configure SMTP settings for sending system notifications,
										reports, and alerts. Ensure your email provider allows SMTP
										access and use app-specific passwords if two-factor
										authentication is enabled.
									</p>
								)}
							</div>
						</div>

						<div className="flex justify-end space-x-2 mt-6">
							<button
								onClick={() => setSelectedIntegration(null)}
								className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
							>
								Cancel
							</button>
							<button
								onClick={() => handleTestConnection(selectedIntegration.id)}
								className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
							>
								Test Connection
							</button>
							<button className="px-4 py-2 bg-green-500 text-white rounded-md text-sm font-medium hover:bg-green-600">
								Save Configuration
							</button>
						</div>
					</div>
				</div>
			)}

			{/* Integration Status Summary */}
			<div className="bg-white rounded-lg border border-gray-200 p-6">
				<h3 className="text-lg font-semibold text-gray-900 mb-4">
					Integration Status Summary
				</h3>
				<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
					<div className="text-center p-4 bg-green-50 rounded-lg">
						<div className="text-2xl font-bold text-green-600">
							{integrations.filter((i) => i.status === "connected").length}
						</div>
						<div className="text-sm text-green-600">Connected</div>
					</div>
					<div className="text-center p-4 bg-gray-50 rounded-lg">
						<div className="text-2xl font-bold text-gray-600">
							{integrations.filter((i) => i.status === "disconnected").length}
						</div>
						<div className="text-sm text-gray-600">Disconnected</div>
					</div>
					<div className="text-center p-4 bg-red-50 rounded-lg">
						<div className="text-2xl font-bold text-red-600">
							{integrations.filter((i) => i.status === "error").length}
						</div>
						<div className="text-sm text-red-600">Errors</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default IntegrationSettings;
