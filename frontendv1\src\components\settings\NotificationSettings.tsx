import { useState } from "react";
import { Link } from "react-router-dom";
import {
	Bell,
	Mail,
	MessageSquare,
	Save,
	Plus,
	Edit,
	Trash2,
	Settings,
	Volume2,
	Monitor
} from "lucide-react";

interface NotificationTemplate {
	id: string;
	name: string;
	type: "email" | "sms" | "push";
	trigger: string;
	subject?: string;
	content: string;
	enabled: boolean;
	recipients: string[];
}

interface NotificationSettings {
	email: {
		enabled: boolean;
		frequency: "immediate" | "hourly" | "daily";
	};
	sms: {
		enabled: boolean;
		frequency: "immediate" | "hourly" | "daily";
	};
	push: {
		enabled: boolean;
		frequency: "immediate" | "hourly" | "daily";
	};
}

const NotificationSettings = () => {
	const [settings, setSettings] = useState<NotificationSettings>({
		email: {
			enabled: true,
			frequency: "immediate",
		},
		sms: {
			enabled: true,
			frequency: "immediate",
		},
		push: {
			enabled: true,
			frequency: "immediate",
		},
	});

	const [templates, setTemplates] = useState<NotificationTemplate[]>([
		{
			id: "1",
			name: "Incident Report",
			type: "email",
			trigger: "incident_created",
			subject: "New Incident Reported - {{site_name}}",
			content:
				"A new incident has been reported at {{site_name}}. Severity: {{severity}}. Please review immediately.",
			enabled: true,
			recipients: ["<EMAIL>", "<EMAIL>"],
		},
		{
			id: "2",
			name: "Worker Late Arrival",
			type: "sms",
			trigger: "worker_late",
			content:
				"Worker {{worker_name}} is late for work at {{site_name}}. Expected: {{expected_time}}, Actual: {{actual_time}}",
			enabled: true,
			recipients: ["supervisor"],
		},
		{
			id: "3",
			name: "Training Expiry Warning",
			type: "email",
			trigger: "training_expiring",
			subject: "Training Certification Expiring Soon",
			content:
				"The following training certifications will expire within 30 days: {{training_list}}. Please schedule renewal.",
			enabled: true,
			recipients: ["<EMAIL>"],
		},
	]);

	const [selectedTemplate, setSelectedTemplate] =
		useState<NotificationTemplate | null>(null);
	const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
	const [hasChanges, setHasChanges] = useState(false);

	const handleSettingsChange = (
		type: keyof NotificationSettings,
		field: string,
		value: any,
	) => {
		setSettings((prev) => ({
			...prev,
			[type]: {
				...prev[type],
				[field]: value,
			},
		}));
		setHasChanges(true);
	};

	const handleSave = () => {
		// TODO: Implement save functionality
		console.log("Saving notification settings:", settings);
		setHasChanges(false);
	};

	const handleDeleteTemplate = (templateId: string) => {
		if (
			window.confirm(
				"Are you sure you want to delete this notification template?",
			)
		) {
			setTemplates((prev) => prev.filter((t) => t.id !== templateId));
		}
	};

	const handleToggleTemplate = (templateId: string) => {
		setTemplates((prev) =>
			prev.map((template) =>
				template.id === templateId
					? { ...template, enabled: !template.enabled }
					: template,
			),
		);
	};

	const getTypeIcon = (type: NotificationTemplate["type"]) => {
		switch (type) {
			case "email":
				return <Mail className="h-4 w-4" />;
			case "sms":
				return <MessageSquare className="h-4 w-4" />;
			case "push":
				return <Bell className="h-4 w-4" />;
		}
	};

	const getTypeColor = (type: NotificationTemplate["type"]) => {
		switch (type) {
			case "email":
				return "bg-blue-100 text-blue-800";
			case "sms":
				return "bg-green-100 text-green-800";
			case "push":
				return "bg-purple-100 text-purple-800";
		}
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
				<div>
					<h2 className="text-2xl font-bold text-gray-900">
						Notification Settings
					</h2>
					<p className="text-sm text-gray-600">
						Configure notification preferences and templates
					</p>
				</div>
				<div className="flex gap-2">
					<button
						onClick={() => setIsCreateModalOpen(true)}
						className="bg-green-500 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center hover:bg-green-600 transition-colors"
					>
						<Plus className="h-4 w-4 mr-2" />
						Add Template
					</button>
					<button
						onClick={handleSave}
						disabled={!hasChanges}
						className="bg-green-500 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center hover:bg-green-600 transition-colors disabled:opacity-50"
					>
						<Save className="h-4 w-4 mr-2" />
						Save Changes
					</button>
				</div>
			</div>

			{/* User Notification Preferences */}
			<div className="bg-white rounded-lg border border-gray-200 p-6">
				<div className="flex items-center justify-between mb-4">
					<div>
						<h3 className="text-lg font-semibold text-gray-900 mb-1">
							Personal Notification Preferences
						</h3>
						<p className="text-sm text-gray-600">
							Configure how you receive notifications for different events
						</p>
					</div>
					<Link
						to="/notifications/preferences"
						className="inline-flex items-center px-4 py-2 text-sm font-medium text-green-600 hover:text-green-700 hover:bg-green-50 rounded-lg transition-colors"
					>
						<Settings className="h-4 w-4 mr-2" />
						Manage Preferences
					</Link>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
					<div className="flex items-center p-4 bg-gray-50 rounded-lg">
						<Monitor className="h-8 w-8 text-blue-600 mr-3" />
						<div>
							<h4 className="text-sm font-medium text-gray-900">In-App Notifications</h4>
							<p className="text-xs text-gray-600">Real-time notifications in the application</p>
						</div>
					</div>

					<div className="flex items-center p-4 bg-gray-50 rounded-lg">
						<Volume2 className="h-8 w-8 text-green-600 mr-3" />
						<div>
							<h4 className="text-sm font-medium text-gray-900">Sound Alerts</h4>
							<p className="text-xs text-gray-600">Audio notifications for important events</p>
						</div>
					</div>

					<div className="flex items-center p-4 bg-gray-50 rounded-lg">
						<Bell className="h-8 w-8 text-purple-600 mr-3" />
						<div>
							<h4 className="text-sm font-medium text-gray-900">Browser Notifications</h4>
							<p className="text-xs text-gray-600">System notifications when app is not active</p>
						</div>
					</div>
				</div>
			</div>

			<div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
				{/* Global Settings */}
				<div className="lg:col-span-1">
					<div className="bg-white rounded-lg border border-gray-200 p-6">
						<h3 className="text-lg font-semibold text-gray-900 mb-4">
							Global Settings
						</h3>
						<div className="space-y-6">
							{/* Email Settings */}
							<div>
								<div className="flex items-center justify-between mb-3">
									<div className="flex items-center">
										<Mail className="h-5 w-5 text-blue-500 mr-2" />
										<span className="font-medium text-gray-900">Email</span>
									</div>
									<label className="relative inline-flex items-center cursor-pointer">
										<input
											type="checkbox"
											checked={settings.email.enabled}
											onChange={(e) =>
												handleSettingsChange(
													"email",
													"enabled",
													e.target.checked,
												)
											}
											className="sr-only peer"
										/>
										<div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
									</label>
								</div>
								{settings.email.enabled && (
									<div>
										<label className="block text-sm font-medium text-gray-700 mb-1">
											Frequency
										</label>
										<select
											value={settings.email.frequency}
											onChange={(e) =>
												handleSettingsChange(
													"email",
													"frequency",
													e.target.value,
												)
											}
											className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
										>
											<option value="immediate">Immediate</option>
											<option value="hourly">Hourly Digest</option>
											<option value="daily">Daily Digest</option>
										</select>
									</div>
								)}
							</div>

							{/* SMS Settings */}
							<div>
								<div className="flex items-center justify-between mb-3">
									<div className="flex items-center">
										<MessageSquare className="h-5 w-5 text-green-500 mr-2" />
										<span className="font-medium text-gray-900">SMS</span>
									</div>
									<label className="relative inline-flex items-center cursor-pointer">
										<input
											type="checkbox"
											checked={settings.sms.enabled}
											onChange={(e) =>
												handleSettingsChange("sms", "enabled", e.target.checked)
											}
											className="sr-only peer"
										/>
										<div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
									</label>
								</div>
								{settings.sms.enabled && (
									<div>
										<label className="block text-sm font-medium text-gray-700 mb-1">
											Frequency
										</label>
										<select
											value={settings.sms.frequency}
											onChange={(e) =>
												handleSettingsChange("sms", "frequency", e.target.value)
											}
											className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
										>
											<option value="immediate">Immediate</option>
											<option value="hourly">Hourly Digest</option>
											<option value="daily">Daily Digest</option>
										</select>
									</div>
								)}
							</div>

							{/* Push Settings */}
							<div>
								<div className="flex items-center justify-between mb-3">
									<div className="flex items-center">
										<Bell className="h-5 w-5 text-purple-500 mr-2" />
										<span className="font-medium text-gray-900">
											Push Notifications
										</span>
									</div>
									<label className="relative inline-flex items-center cursor-pointer">
										<input
											type="checkbox"
											checked={settings.push.enabled}
											onChange={(e) =>
												handleSettingsChange(
													"push",
													"enabled",
													e.target.checked,
												)
											}
											className="sr-only peer"
										/>
										<div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-green-600"></div>
									</label>
								</div>
								{settings.push.enabled && (
									<div>
										<label className="block text-sm font-medium text-gray-700 mb-1">
											Frequency
										</label>
										<select
											value={settings.push.frequency}
											onChange={(e) =>
												handleSettingsChange(
													"push",
													"frequency",
													e.target.value,
												)
											}
											className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
										>
											<option value="immediate">Immediate</option>
											<option value="hourly">Hourly Digest</option>
											<option value="daily">Daily Digest</option>
										</select>
									</div>
								)}
							</div>
						</div>
					</div>
				</div>

				{/* Notification Templates */}
				<div className="lg:col-span-2">
					<div className="bg-white rounded-lg border border-gray-200 p-6">
						<h3 className="text-lg font-semibold text-gray-900 mb-4">
							Notification Templates
						</h3>
						<div className="space-y-4">
							{templates.map((template) => (
								<div
									key={template.id}
									className="border border-gray-200 rounded-lg p-4"
								>
									<div className="flex items-start justify-between">
										<div className="flex-1">
											<div className="flex items-center mb-2">
												<h4 className="font-medium text-gray-900 mr-3">
													{template.name}
												</h4>
												<span
													className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(template.type)}`}
												>
													{getTypeIcon(template.type)}
													<span className="ml-1 capitalize">
														{template.type}
													</span>
												</span>
												<span
													className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${template.enabled
														? "bg-green-100 text-green-800"
														: "bg-gray-100 text-gray-800"
														}`}
												>
													{template.enabled ? "Enabled" : "Disabled"}
												</span>
											</div>
											<p className="text-sm text-gray-600 mb-2">
												Trigger:{" "}
												<span className="font-mono bg-gray-100 px-1 rounded">
													{template.trigger}
												</span>
											</p>
											{template.subject && (
												<p className="text-sm text-gray-600 mb-2">
													Subject: {template.subject}
												</p>
											)}
											<p className="text-sm text-gray-600">
												{template.content.length > 100
													? `${template.content.substring(0, 100)}...`
													: template.content}
											</p>
										</div>
										<div className="flex items-center space-x-2 ml-4">
											<label className="relative inline-flex items-center cursor-pointer">
												<input
													type="checkbox"
													checked={template.enabled}
													onChange={() => handleToggleTemplate(template.id)}
													className="sr-only peer"
												/>
												<div className="w-9 h-5 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-4 after:w-4 after:transition-all peer-checked:bg-green-600"></div>
											</label>
											<button
												onClick={() => setSelectedTemplate(template)}
												className="text-green-600 hover:text-green-900 p-1"
												title="Edit template"
											>
												<Edit className="h-4 w-4" />
											</button>
											<button
												onClick={() => handleDeleteTemplate(template.id)}
												className="text-red-600 hover:text-red-900 p-1"
												title="Delete template"
											>
												<Trash2 className="h-4 w-4" />
											</button>
										</div>
									</div>
								</div>
							))}

							{templates.length === 0 && (
								<div className="text-center py-8">
									<Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
									<h4 className="text-lg font-medium text-gray-900 mb-2">
										No notification templates
									</h4>
									<p className="text-gray-600 mb-4">
										Create your first notification template to get started.
									</p>
									<button
										onClick={() => setIsCreateModalOpen(true)}
										className="bg-green-500 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-600 transition-colors"
									>
										Create Template
									</button>
								</div>
							)}
						</div>
					</div>
				</div>
			</div>

			{/* Template Modal */}
			{(isCreateModalOpen || selectedTemplate) && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
					<div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto">
						<h3 className="text-lg font-semibold mb-4">
							{selectedTemplate ? "Edit Template" : "Create New Template"}
						</h3>
						<p className="text-gray-600 mb-4">
							Template form with content editor will be implemented here.
						</p>

						<div className="space-y-4 mb-6">
							<div className="p-3 bg-gray-50 rounded">
								<strong>Available Variables:</strong>
								<div className="text-sm text-gray-600 mt-1">
									{"{worker_name}"}, {"{site_name}"}, {"{severity}"},{" "}
									{"{expected_time}"}, {"{actual_time}"}, {"{training_list}"}
								</div>
							</div>
						</div>

						<div className="flex justify-end space-x-2">
							<button
								onClick={() => {
									setIsCreateModalOpen(false);
									setSelectedTemplate(null);
								}}
								className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
							>
								Cancel
							</button>
							<button className="px-4 py-2 bg-green-500 text-white rounded-md text-sm font-medium hover:bg-green-600">
								{selectedTemplate ? "Update Template" : "Create Template"}
							</button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default NotificationSettings;
