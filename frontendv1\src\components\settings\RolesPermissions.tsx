import { useState } from "react";
import { Plus, Shield, Users, Edit, Trash2 } from "lucide-react";

interface Role {
	id: string;
	name: string;
	description: string;
	permissions: string[];
	isSystemRole: boolean;
	userCount: number;
	createdAt: Date;
	updatedAt: Date;
}

const RolesPermissions = () => {
	const [roles, setRoles] = useState<Role[]>([
		{
			id: "admin",
			name: "Administrator",
			description: "Full system access with all permissions",
			permissions: ["*"],
			isSystemRole: true,
			userCount: 2,
			createdAt: new Date("2024-01-01"),
			updatedAt: new Date("2024-01-01"),
		},
		{
			id: "manager",
			name: "Site Manager",
			description: "Manage site operations and workers",
			permissions: ["sites.manage", "workers.manage", "reports.view"],
			isSystemRole: false,
			userCount: 5,
			createdAt: new Date("2024-01-01"),
			updatedAt: new Date("2024-12-15"),
		},
		{
			id: "supervisor",
			name: "Supervisor",
			description: "Supervise daily operations and worker activities",
			permissions: ["workers.view", "timelog.manage", "incidents.create"],
			isSystemRole: false,
			userCount: 12,
			createdAt: new Date("2024-01-01"),
			updatedAt: new Date("2024-11-20"),
		},
		{
			id: "hr",
			name: "HR Manager",
			description: "Manage human resources and compliance",
			permissions: ["users.manage", "training.manage", "compliance.view"],
			isSystemRole: false,
			userCount: 3,
			createdAt: new Date("2024-01-01"),
			updatedAt: new Date("2024-10-05"),
		},
	]);

	const [selectedRole, setSelectedRole] = useState<Role | null>(null);
	const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);

	const handleDeleteRole = (roleId: string) => {
		const role = roles.find((r) => r.id === roleId);
		if (role?.isSystemRole) {
			alert("System roles cannot be deleted.");
			return;
		}
		if (role?.userCount && role?.userCount > 0) {
			alert("Cannot delete role that is assigned to users.");
			return;
		}
		if (window.confirm("Are you sure you want to delete this role?")) {
			setRoles((prev) => prev.filter((r) => r.id !== roleId));
		}
	};

	const getPermissionCount = (permissions: string[]) => {
		if (permissions.includes("*")) return "All permissions";
		return `${permissions.length} permission${permissions.length !== 1 ? "s" : ""}`;
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
				<div>
					<h2 className="text-2xl font-bold text-gray-900">
						Roles & Permissions
					</h2>
					<p className="text-sm text-gray-600">
						Define custom roles with granular permissions
					</p>
				</div>
				<button
					onClick={() => setIsCreateModalOpen(true)}
					className="bg-green-500 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center hover:bg-green-600 transition-colors"
				>
					<Plus className="h-4 w-4 mr-2" />
					Create Role
				</button>
			</div>

			{/* Roles Table */}
			<div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
				<div className="px-6 py-4 border-b border-gray-200">
					<h3 className="text-lg font-semibold text-gray-900">System Roles</h3>
				</div>
				<div className="overflow-x-auto">
					<table className="min-w-full divide-y divide-gray-200">
						<thead className="bg-gray-50">
							<tr>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Role Name
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Description
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Permissions
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Users
								</th>
								<th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
									Actions
								</th>
							</tr>
						</thead>
						<tbody className="bg-white divide-y divide-gray-200">
							{roles.map((role) => (
								<tr key={role.id} className="hover:bg-gray-50">
									<td className="px-6 py-4 whitespace-nowrap">
										<div className="flex items-center">
											<div className="flex-shrink-0">
												<div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
													<Shield className="h-4 w-4 text-blue-600" />
												</div>
											</div>
											<div className="ml-4">
												<div className="flex items-center">
													<span className="text-sm font-medium text-gray-900">
														{role.name}
													</span>
													{role.isSystemRole && (
														<span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
															System
														</span>
													)}
												</div>
											</div>
										</div>
									</td>
									<td className="px-6 py-4">
										<div className="text-sm text-gray-900 max-w-xs">
											{role.description}
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<span className="text-sm text-gray-500">
											{getPermissionCount(role.permissions)}
										</span>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<div className="flex items-center">
											<Users className="h-4 w-4 text-gray-400 mr-1" />
											<span className="text-sm text-gray-900">
												{role.userCount}
											</span>
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
										<div className="flex items-center justify-end space-x-2">
											<button
												onClick={() => setSelectedRole(role)}
												className="text-green-600 hover:text-green-900 p-1"
												title="Edit permissions"
											>
												<Edit className="h-4 w-4" />
											</button>
											{!role.isSystemRole && (
												<button
													onClick={() => handleDeleteRole(role.id)}
													className="text-red-600 hover:text-red-900 p-1"
													title="Delete role"
													disabled={role.userCount > 0}
												>
													<Trash2 className="h-4 w-4" />
												</button>
											)}
										</div>
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>
			</div>

			{/* Permission Matrix Info */}
			<div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
				<div className="flex">
					<div className="flex-shrink-0">
						<Shield className="h-5 w-5 text-blue-400" />
					</div>
					<div className="ml-3">
						<h3 className="text-sm font-medium text-blue-800">
							Permission System
						</h3>
						<div className="mt-2 text-sm text-blue-700">
							<p>
								Roles define what users can do in the system. Each role has
								specific permissions that control access to different features
								and actions. System roles cannot be modified or deleted.
							</p>
						</div>
					</div>
				</div>
			</div>

			{/* TODO: Add RoleModal component for create/edit */}
			{(isCreateModalOpen || selectedRole) && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
					<div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto">
						<h3 className="text-lg font-semibold mb-4">
							{selectedRole
								? `Edit Role: ${selectedRole.name}`
								: "Create New Role"}
						</h3>
						<p className="text-gray-600 mb-4">
							Role form with permission matrix will be implemented here.
						</p>

						{/* Preview of permission categories */}
						<div className="space-y-4 mb-6">
							<h4 className="font-medium text-gray-900">
								Permission Categories:
							</h4>
							<div className="grid grid-cols-2 gap-4 text-sm">
								<div className="p-3 bg-gray-50 rounded">
									<strong>User Management</strong>
									<div className="text-gray-600">
										Create, edit, delete users
									</div>
								</div>
								<div className="p-3 bg-gray-50 rounded">
									<strong>Site Management</strong>
									<div className="text-gray-600">Manage site operations</div>
								</div>
								<div className="p-3 bg-gray-50 rounded">
									<strong>Worker Management</strong>
									<div className="text-gray-600">Manage worker records</div>
								</div>
								<div className="p-3 bg-gray-50 rounded">
									<strong>Reports & Analytics</strong>
									<div className="text-gray-600">View and generate reports</div>
								</div>
							</div>
						</div>

						<div className="flex justify-end space-x-2">
							<button
								onClick={() => {
									setIsCreateModalOpen(false);
									setSelectedRole(null);
								}}
								className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
							>
								Cancel
							</button>
							<button className="px-4 py-2 bg-green-500 text-white rounded-md text-sm font-medium hover:bg-green-600">
								{selectedRole ? "Update Role" : "Create Role"}
							</button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default RolesPermissions;
