import {
	UserPlus,
	Download,
	FileText,
	Settings as SettingsIcon,
	CheckCircle,
	AlertCircle,
} from "lucide-react";

interface SettingsOverviewProps {
	onNavigateToTab: (tabId: string) => void;
}

interface SystemHealthCardProps {
	title: string;
	value: string | number;
	status: "healthy" | "warning" | "error";
	onClick?: () => void;
}

interface QuickActionCardProps {
	title: string;
	icon: React.ComponentType<{ className?: string }>;
	onClick: () => void;
}

interface ActivityItem {
	id: string;
	action: string;
	user: string;
	timestamp: string;
	type: "user" | "system" | "security";
}

const SystemHealthCard = ({
	title,
	value,
	status,
	onClick,
}: SystemHealthCardProps) => {
	const statusConfig = {
		healthy: { color: "text-green-600", bg: "bg-green-50", icon: CheckCircle },
		warning: { color: "text-amber-600", bg: "bg-amber-50", icon: AlertCircle },
		error: { color: "text-red-600", bg: "bg-red-50", icon: AlertCircle },
	};

	const config = statusConfig[status];
	const StatusIcon = config.icon;

	return (
		<div
			className={`
        bg-white p-6 rounded-lg border border-gray-200 shadow-sm transition-all duration-200
        ${onClick ? "cursor-pointer hover:shadow-md hover:border-gray-300" : ""}
      `}
			onClick={onClick}
		>
			<div className="flex items-center justify-between">
				<div>
					<p className="text-sm font-medium text-gray-500">{title}</p>
					<p className="mt-2 text-2xl font-semibold text-gray-900">{value}</p>
				</div>
				<div className={`p-3 rounded-full ${config.bg}`}>
					<StatusIcon className={`h-6 w-6 ${config.color}`} />
				</div>
			</div>
		</div>
	);
};

const QuickActionCard = ({
	title,
	icon: Icon,
	onClick,
}: QuickActionCardProps) => (
	<button
		onClick={onClick}
		className="
      bg-white p-4 rounded-lg border border-gray-200 shadow-sm 
      hover:shadow-md hover:border-gray-300 transition-all duration-200
      flex flex-col items-center text-center space-y-2
    "
	>
		<div className="p-3 bg-green-50 rounded-full">
			<Icon className="h-6 w-6 text-green-600" />
		</div>
		<span className="text-sm font-medium text-gray-700">{title}</span>
	</button>
);

const ActivityFeed = ({ activities }: { activities: ActivityItem[] }) => (
	<div className="space-y-3">
		{activities.map((activity) => (
			<div
				key={activity.id}
				className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg"
			>
				<div className="flex-shrink-0">
					<div
						className={`
            w-2 h-2 rounded-full mt-2
            ${
							activity.type === "security"
								? "bg-red-500"
								: activity.type === "system"
									? "bg-blue-500"
									: "bg-green-500"
						}
          `}
					/>
				</div>
				<div className="flex-1 min-w-0">
					<p className="text-sm text-gray-900">{activity.action}</p>
					<p className="text-xs text-gray-500">
						by {activity.user} • {activity.timestamp}
					</p>
				</div>
			</div>
		))}
	</div>
);

const SettingsOverview = ({ onNavigateToTab }: SettingsOverviewProps) => {
	// Mock data - replace with actual API calls
	const systemStats = {
		activeUsers: 42,
		totalRoles: 8,
		systemUptime: "99.9%",
		lastBackup: "2 hours ago",
	};

	const recentActivity: ActivityItem[] = [
		{
			id: "1",
			action: "New user account created for John Mwangi",
			user: "Admin",
			timestamp: "10 minutes ago",
			type: "user",
		},
		{
			id: "2",
			action: "System backup completed successfully",
			user: "System",
			timestamp: "2 hours ago",
			type: "system",
		},
		{
			id: "3",
			action: "Role permissions updated for Site Manager",
			user: "Admin",
			timestamp: "4 hours ago",
			type: "security",
		},
		{
			id: "4",
			action: "Company profile information updated",
			user: "HR Manager",
			timestamp: "1 day ago",
			type: "user",
		},
	];

	const handleBackup = () => {
		// TODO: Implement backup functionality
		console.log("Starting backup...");
	};

	const handleViewLogs = () => {
		// TODO: Implement logs viewer
		console.log("Opening logs...");
	};

	return (
		<div className="space-y-6">
			{/* System Health Cards */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
				<SystemHealthCard
					title="Active Users"
					value={systemStats.activeUsers}
					status="healthy"
					onClick={() => onNavigateToTab("users")}
				/>
				<SystemHealthCard
					title="System Uptime"
					value={systemStats.systemUptime}
					status="healthy"
				/>
				<SystemHealthCard
					title="Data Backup"
					value={systemStats.lastBackup}
					status="healthy"
				/>
				<SystemHealthCard
					title="Integration Status"
					value="All Connected"
					status="healthy"
					onClick={() => onNavigateToTab("integrations")}
				/>
			</div>

			{/* Quick Actions */}
			<div className="bg-white rounded-lg p-6 border border-gray-200 shadow-sm">
				<h3 className="text-lg font-semibold text-gray-900 mb-4">
					Quick Actions
				</h3>
				<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
					<QuickActionCard
						title="Add New User"
						icon={UserPlus}
						onClick={() => onNavigateToTab("users")}
					/>
					<QuickActionCard
						title="Backup Data"
						icon={Download}
						onClick={handleBackup}
					/>
					<QuickActionCard
						title="System Logs"
						icon={FileText}
						onClick={handleViewLogs}
					/>
					<QuickActionCard
						title="Update Settings"
						icon={SettingsIcon}
						onClick={() => onNavigateToTab("system")}
					/>
				</div>
			</div>

			{/* Recent Activity and System Information */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				{/* Recent Activity */}
				<div className="bg-white rounded-lg p-6 border border-gray-200 shadow-sm">
					<h3 className="text-lg font-semibold text-gray-900 mb-4">
						Recent Activity
					</h3>
					<ActivityFeed activities={recentActivity} />
				</div>

				{/* System Information */}
				<div className="bg-white rounded-lg p-6 border border-gray-200 shadow-sm">
					<h3 className="text-lg font-semibold text-gray-900 mb-4">
						System Information
					</h3>
					<div className="space-y-4">
						<div className="flex justify-between items-center py-2 border-b border-gray-100">
							<span className="text-sm text-gray-600">Version</span>
							<span className="text-sm font-medium text-gray-900">v2.1.0</span>
						</div>
						<div className="flex justify-between items-center py-2 border-b border-gray-100">
							<span className="text-sm text-gray-600">Database</span>
							<span className="text-sm font-medium text-gray-900">
								PostgreSQL 14.2
							</span>
						</div>
						<div className="flex justify-between items-center py-2 border-b border-gray-100">
							<span className="text-sm text-gray-600">Storage Used</span>
							<span className="text-sm font-medium text-gray-900">
								2.4 GB / 10 GB
							</span>
						</div>
						<div className="flex justify-between items-center py-2">
							<span className="text-sm text-gray-600">License</span>
							<span className="text-sm font-medium text-green-600">Active</span>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default SettingsOverview;
