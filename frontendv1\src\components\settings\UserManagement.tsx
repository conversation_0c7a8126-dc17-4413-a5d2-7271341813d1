import { useState, useMemo } from "react";
import {
	UserPlus,
	Upload,
	Search,
	Filter,
	Edit,
	Trash2,
	User<PERSON>he<PERSON>,
	UserX,
	Mail,
	Phone,
	Users,
} from "lucide-react";

interface User {
	id: string;
	email: string;
	firstName: string;
	lastName: string;
	phone?: string;
	role: {
		id: string;
		name: string;
	};
	assignedSites: Array<{
		id: string;
		name: string;
	}>;
	status: "active" | "inactive" | "suspended";
	lastLogin?: Date;
	createdAt: Date;
	avatar?: string;
}

interface UserFilters {
	search?: string;
	role?: string;
	status?: string;
	site?: string;
}

const UserManagement = () => {
	const [users, setUsers] = useState<User[]>([
		{
			id: "1",
			email: "<EMAIL>",
			firstName: "John",
			lastName: "Mwangi",
			phone: "+254 700 123 456",
			role: { id: "admin", name: "Administrator" },
			assignedSites: [
				{ id: "site1", name: "Westlands Site" },
				{ id: "site2", name: "Mombasa Road" },
			],
			status: "active",
			lastLogin: new Date("2025-01-15T10:30:00"),
			createdAt: new Date("2024-12-01T00:00:00"),
		},
		{
			id: "2",
			email: "<EMAIL>",
			firstName: "Sarah",
			lastName: "Ochieng",
			phone: "+254 700 234 567",
			role: { id: "manager", name: "Site Manager" },
			assignedSites: [{ id: "site2", name: "Mombasa Road" }],
			status: "active",
			lastLogin: new Date("2025-01-15T09:15:00"),
			createdAt: new Date("2024-11-15T00:00:00"),
		},
		{
			id: "3",
			email: "<EMAIL>",
			firstName: "David",
			lastName: "Kimani",
			phone: "+254 700 345 678",
			role: { id: "supervisor", name: "Supervisor" },
			assignedSites: [{ id: "site3", name: "Thika Highway" }],
			status: "inactive",
			lastLogin: new Date("2025-01-10T16:45:00"),
			createdAt: new Date("2024-10-20T00:00:00"),
		},
	]);

	const [selectedUser, setSelectedUser] = useState<User | null>(null);
	const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
	const [filters, setFilters] = useState<UserFilters>({});
	const [showFilters, setShowFilters] = useState(false);

	const filteredUsers = useMemo(() => {
		return users.filter((user) => {
			if (filters.role && user.role.id !== filters.role) return false;
			if (filters.status && user.status !== filters.status) return false;
			if (
				filters.site &&
				!user.assignedSites.some((site) => site.id === filters.site)
			)
				return false;
			if (filters.search) {
				const searchTerm = filters.search.toLowerCase();
				return (
					user.firstName.toLowerCase().includes(searchTerm) ||
					user.lastName.toLowerCase().includes(searchTerm) ||
					user.email.toLowerCase().includes(searchTerm)
				);
			}
			return true;
		});
	}, [users, filters]);

	const handleToggleStatus = (userId: string) => {
		setUsers((prev) =>
			prev.map((user) =>
				user.id === userId
					? {
							...user,
							status: user.status === "active" ? "inactive" : "active",
						}
					: user,
			),
		);
	};

	const handleDeleteUser = (userId: string) => {
		if (window.confirm("Are you sure you want to delete this user?")) {
			setUsers((prev) => prev.filter((user) => user.id !== userId));
		}
	};

	const getStatusBadge = (status: User["status"]) => {
		const config = {
			active: { bg: "bg-green-100", text: "text-green-800", label: "Active" },
			inactive: { bg: "bg-gray-100", text: "text-gray-800", label: "Inactive" },
			suspended: { bg: "bg-red-100", text: "text-red-800", label: "Suspended" },
		};

		const { bg, text, label } = config[status];
		return (
			<span
				className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${bg} ${text}`}
			>
				{label}
			</span>
		);
	};

	const formatLastLogin = (date?: Date) => {
		if (!date) return "Never";
		const now = new Date();
		const diffInHours = Math.floor(
			(now.getTime() - date.getTime()) / (1000 * 60 * 60),
		);

		if (diffInHours < 1) return "Just now";
		if (diffInHours < 24) return `${diffInHours}h ago`;
		if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`;
		return date.toLocaleDateString();
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
				<div>
					<h2 className="text-2xl font-bold text-gray-900">User Management</h2>
					<p className="text-sm text-gray-600">
						Manage user accounts and permissions
					</p>
				</div>
				<div className="flex gap-2">
					<button
						onClick={() => setIsCreateModalOpen(true)}
						className="bg-green-500 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center hover:bg-green-600 transition-colors"
					>
						<UserPlus className="h-4 w-4 mr-2" />
						Add User
					</button>
					<button className="border border-gray-300 text-gray-700 px-4 py-2 rounded-md text-sm font-medium flex items-center hover:bg-gray-50 transition-colors">
						<Upload className="h-4 w-4 mr-2" />
						Import Users
					</button>
				</div>
			</div>

			{/* Search and Filters */}
			<div className="bg-white rounded-lg border border-gray-200 p-4">
				<div className="flex flex-col sm:flex-row gap-4">
					<div className="flex-1 relative">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
						<input
							type="text"
							placeholder="Search users by name or email..."
							value={filters.search || ""}
							onChange={(e) =>
								setFilters((prev) => ({ ...prev, search: e.target.value }))
							}
							className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
						/>
					</div>
					<button
						onClick={() => setShowFilters(!showFilters)}
						className="flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
					>
						<Filter className="h-4 w-4 mr-2" />
						Filters
					</button>
				</div>

				{/* Filter Options */}
				{showFilters && (
					<div className="mt-4 pt-4 border-t border-gray-200">
						<div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-1">
									Role
								</label>
								<select
									value={filters.role || ""}
									onChange={(e) =>
										setFilters((prev) => ({
											...prev,
											role: e.target.value || undefined,
										}))
									}
									className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
								>
									<option value="">All Roles</option>
									<option value="admin">Administrator</option>
									<option value="manager">Site Manager</option>
									<option value="supervisor">Supervisor</option>
									<option value="hr">HR Manager</option>
								</select>
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-1">
									Status
								</label>
								<select
									value={filters.status || ""}
									onChange={(e) =>
										setFilters((prev) => ({
											...prev,
											status: e.target.value || undefined,
										}))
									}
									className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
								>
									<option value="">All Statuses</option>
									<option value="active">Active</option>
									<option value="inactive">Inactive</option>
									<option value="suspended">Suspended</option>
								</select>
							</div>
							<div>
								<label className="block text-sm font-medium text-gray-700 mb-1">
									Site
								</label>
								<select
									value={filters.site || ""}
									onChange={(e) =>
										setFilters((prev) => ({
											...prev,
											site: e.target.value || undefined,
										}))
									}
									className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm"
								>
									<option value="">All Sites</option>
									<option value="site1">Westlands Site</option>
									<option value="site2">Mombasa Road</option>
									<option value="site3">Thika Highway</option>
								</select>
							</div>
						</div>
					</div>
				)}
			</div>

			{/* Users Table */}
			<div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
				<div className="overflow-x-auto">
					<table className="min-w-full divide-y divide-gray-200">
						<thead className="bg-gray-50">
							<tr>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									User
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Role
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Sites
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Status
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Last Login
								</th>
								<th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
									Actions
								</th>
							</tr>
						</thead>
						<tbody className="bg-white divide-y divide-gray-200">
							{filteredUsers.map((user) => (
								<tr key={user.id} className="hover:bg-gray-50">
									<td className="px-6 py-4 whitespace-nowrap">
										<div className="flex items-center">
											<div className="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center">
												<span className="text-sm font-medium text-green-600">
													{user.firstName[0]}
													{user.lastName[0]}
												</span>
											</div>
											<div className="ml-4">
												<div className="text-sm font-medium text-gray-900">
													{user.firstName} {user.lastName}
												</div>
												<div className="text-sm text-gray-500 flex items-center">
													<Mail className="h-3 w-3 mr-1" />
													{user.email}
												</div>
												{user.phone && (
													<div className="text-sm text-gray-500 flex items-center">
														<Phone className="h-3 w-3 mr-1" />
														{user.phone}
													</div>
												)}
											</div>
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<span className="text-sm text-gray-900">
											{user.role.name}
										</span>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<div className="text-sm text-gray-900">
											{user.assignedSites.length > 0 ? (
												<div className="space-y-1">
													{user.assignedSites.slice(0, 2).map((site) => (
														<div
															key={site.id}
															className="text-xs bg-gray-100 px-2 py-1 rounded"
														>
															{site.name}
														</div>
													))}
													{user.assignedSites.length > 2 && (
														<div className="text-xs text-gray-500">
															+{user.assignedSites.length - 2} more
														</div>
													)}
												</div>
											) : (
												<span className="text-gray-500">No sites assigned</span>
											)}
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										{getStatusBadge(user.status)}
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
										{formatLastLogin(user.lastLogin)}
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
										<div className="flex items-center justify-end space-x-2">
											<button
												onClick={() => setSelectedUser(user)}
												className="text-green-600 hover:text-green-900 p-1"
												title="Edit user"
											>
												<Edit className="h-4 w-4" />
											</button>
											<button
												onClick={() => handleToggleStatus(user.id)}
												className={`p-1 ${user.status === "active" ? "text-gray-600 hover:text-gray-900" : "text-green-600 hover:text-green-900"}`}
												title={
													user.status === "active"
														? "Deactivate user"
														: "Activate user"
												}
											>
												{user.status === "active" ? (
													<UserX className="h-4 w-4" />
												) : (
													<UserCheck className="h-4 w-4" />
												)}
											</button>
											<button
												onClick={() => handleDeleteUser(user.id)}
												className="text-red-600 hover:text-red-900 p-1"
												title="Delete user"
											>
												<Trash2 className="h-4 w-4" />
											</button>
										</div>
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>

				{filteredUsers.length === 0 && (
					<div className="text-center py-12">
						<Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
						<h3 className="text-lg font-medium text-gray-900 mb-2">
							No users found
						</h3>
						<p className="text-gray-600 mb-4">
							{filters.search || filters.role || filters.status || filters.site
								? "Try adjusting your search or filter criteria."
								: "Get started by adding your first user."}
						</p>
						{!filters.search &&
							!filters.role &&
							!filters.status &&
							!filters.site && (
								<button
									onClick={() => setIsCreateModalOpen(true)}
									className="bg-green-500 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-600 transition-colors"
								>
									Add First User
								</button>
							)}
					</div>
				)}
			</div>

			{/* TODO: Add UserModal component for create/edit */}
			{(isCreateModalOpen || selectedUser) && (
				<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
					<div className="bg-white rounded-lg p-6 w-full max-w-md">
						<h3 className="text-lg font-semibold mb-4">
							{selectedUser ? "Edit User" : "Create New User"}
						</h3>
						<p className="text-gray-600 mb-4">
							User form will be implemented here.
						</p>
						<div className="flex justify-end space-x-2">
							<button
								onClick={() => {
									setIsCreateModalOpen(false);
									setSelectedUser(null);
								}}
								className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
							>
								Cancel
							</button>
							<button className="px-4 py-2 bg-green-500 text-white rounded-md text-sm font-medium hover:bg-green-600">
								{selectedUser ? "Update" : "Create"}
							</button>
						</div>
					</div>
				</div>
			)}
		</div>
	);
};

export default UserManagement;
