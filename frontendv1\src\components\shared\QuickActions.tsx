import React from "react";

export interface QuickActionItem {
	title: string;
	description: string;
	icon: React.ReactNode;
	onClick: () => void;
	className?: string;
}

interface QuickActionsProps {
	actions: QuickActionItem[];
	className?: string;
}

const QuickActions: React.FC<QuickActionsProps> = ({ actions, className = "" }) => {
	return (
		<div className={`grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 ${className}`}>
			{actions.map((action, index) => (
				<button
					key={`${action.title}-${index}`}
					onClick={action.onClick}
					className={`bg-[#f0fdf4] p-4 rounded-lg border border-[#24c45c] shadow-sm hover:shadow-md transition-shadow text-left ${action.className || ""}`}
				>
					<div className="flex items-center">
						<div className="flex-shrink-0">
							{action.icon}
						</div>
						<div className="ml-3">
							<h3 className="text-sm font-medium text-gray-900">{action.title}</h3>
							<p className="text-xs text-gray-500">{action.description}</p>
						</div>
					</div>
				</button>
			))}
		</div>
	);
};

export default QuickActions;


