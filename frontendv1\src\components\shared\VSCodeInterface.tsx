import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, ChevronRight, X, ChevronLeft, FileText, User, Image, Shield, CheckCircle, History, Folder, FolderOpen } from 'lucide-react';

export interface ExplorerItem {
  id: string;
  name: string;
  type: 'folder' | 'file' | 'link';
  icon: React.ReactNode;
  children?: ExplorerItem[];
  data?: any;
  badge?: string;
  badgeColor?: string;
}

export interface VSCodeTab {
  id: string;
  title: string;
  type: string;
  data?: any;
  closable?: boolean;
}

interface VSCodeInterfaceProps {
  explorerItems: ExplorerItem[];
  tabs: VSCodeTab[];
  activeTabId: string | null;
  selectedItem: string | null;
  onItemSelect: (itemId: string, itemType: string, itemData?: any) => void;
  onTabChange: (tabId: string) => void;
  onTabClose: (tabId: string) => void;
  renderTabContent: (tab: VSCodeTab) => React.ReactNode;
  className?: string;
  hideExplorer?: boolean;
}

const VSCodeInterface: React.FC<VSCodeInterfaceProps> = ({
  explorerItems,
  tabs,
  activeTabId,
  selectedItem,
  onItemSelect,
  onTabChange,
  onTabClose,
  renderTabContent,
  className = "",
  hideExplorer = false
}) => {
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set());
  const tabsContainerRef = useRef<HTMLDivElement>(null);
  const [showScrollButtons, setShowScrollButtons] = useState(false);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(false);

  // Check if scrolling is needed and update scroll button states
  const updateScrollState = () => {
    if (!tabsContainerRef.current) return;

    const container = tabsContainerRef.current;
    const tabsWrapper = container.querySelector('div');

    if (!tabsWrapper) return;

    const hasOverflow = tabsWrapper.scrollWidth > container.clientWidth;
    const isAtStart = container.scrollLeft <= 0;
    const isAtEnd = container.scrollLeft >= container.scrollWidth - container.clientWidth - 1;

    setShowScrollButtons(hasOverflow);
    setCanScrollLeft(hasOverflow && !isAtStart);
    setCanScrollRight(hasOverflow && !isAtEnd);
  };

  // Update scroll state on mount and when tabs change
  useEffect(() => {
    updateScrollState();

    const container = tabsContainerRef.current;
    if (container) {
      container.addEventListener('scroll', updateScrollState);
      return () => container.removeEventListener('scroll', updateScrollState);
    }
  }, [tabs]);

  // Handle window resize
  useEffect(() => {
    const handleResize = () => updateScrollState();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Scroll functions
  const scrollLeft = () => {
    if (!tabsContainerRef.current) return;
    const container = tabsContainerRef.current;
    const scrollAmount = Math.min(200, container.clientWidth / 2);
    container.scrollBy({ left: -scrollAmount, behavior: 'smooth' });
  };

  const scrollRight = () => {
    if (!tabsContainerRef.current) return;
    const container = tabsContainerRef.current;
    const scrollAmount = Math.min(200, container.clientWidth / 2);
    container.scrollBy({ left: scrollAmount, behavior: 'smooth' });
  };

  // Scroll active tab into view
  const scrollTabIntoView = (tabId: string) => {
    if (!tabsContainerRef.current) return;

    const container = tabsContainerRef.current;
    const tabElement = container.querySelector(`[data-tab-id="${tabId}"]`) as HTMLElement;

    if (tabElement) {
      const containerRect = container.getBoundingClientRect();
      const tabRect = tabElement.getBoundingClientRect();

      if (tabRect.left < containerRect.left) {
        // Tab is to the left of visible area
        container.scrollBy({
          left: tabRect.left - containerRect.left - 20,
          behavior: 'smooth'
        });
      } else if (tabRect.right > containerRect.right) {
        // Tab is to the right of visible area
        container.scrollBy({
          left: tabRect.right - containerRect.right + 20,
          behavior: 'smooth'
        });
      }
    }
  };

  // Scroll to active tab when it changes
  useEffect(() => {
    if (activeTabId) {
      scrollTabIntoView(activeTabId);
    }
  }, [activeTabId]);

  const getTabIcon = (type: string) => {
    switch (type) {
      case 'details':
      case 'permit-form':
        return <FileText className="h-4 w-4" />;
      case 'document':
        return <FileText className="h-4 w-4" />;
      case 'worker':
        return <User className="h-4 w-4" />;
      case 'photo':
        return <Image className="h-4 w-4" />;
      case 'risk-assessment':
      case 'daily-risk-assessment':
        return <Shield className="h-4 w-4" />;
      case 'approval':
        return <CheckCircle className="h-4 w-4" />;
      case 'audit-trail':
        return <History className="h-4 w-4" />;
      default:
        return <FileText className="h-4 w-4" />;
    }
  };

  const toggleExpanded = (itemId: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(itemId)) {
      newExpanded.delete(itemId);
    } else {
      newExpanded.add(itemId);
    }
    setExpandedItems(newExpanded);
  };



  const renderExplorerItem = (item: ExplorerItem, level: number = 0): React.ReactNode => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.has(item.id);
    const isSelected = selectedItem === item.id;

    return (
      <div key={item.id}>
        <div
          className={`group relative flex items-center gap-2 px-2 py-1.5 text-sm cursor-pointer rounded-md mx-1 my-0.5 transition-colors ${
            isSelected ? 'bg-blue-50 text-blue-700' : 'text-gray-700 hover:bg-gray-100'
          }`}
          style={{ paddingLeft: `${8 + level * 16}px` }}
          onClick={() => {
            if (hasChildren) {
              toggleExpanded(item.id);
            } else {
              onItemSelect(item.id, item.data?.type || 'file', item.data);
            }
          }}
        >
          {isSelected && (
            <span className="absolute left-0 top-0 h-full w-0.5 bg-blue-500 rounded-r" />
          )}

          {hasChildren ? (
            <button
              className="flex h-5 w-5 items-center justify-center rounded hover:bg-gray-200"
              onClick={(e) => { e.stopPropagation(); toggleExpanded(item.id); }}
              title={isExpanded ? 'Collapse' : 'Expand'}
            >
              {isExpanded ? (
                <ChevronDown className="h-3.5 w-3.5" />
              ) : (
                <ChevronRight className="h-3.5 w-3.5" />
              )}
            </button>
          ) : (
            <div className="w-5" />
          )}

          <div className="flex-shrink-0 text-gray-500 group-hover:text-gray-700">
            {hasChildren
              ? (isExpanded ? <FolderOpen className="h-4 w-4" /> : <Folder className="h-4 w-4" />)
              : (item.icon || <FileText className="h-4 w-4" />)
            }
          </div>

          <span className="flex-1 truncate">{item.name}</span>

          {item.badge && (
            <span className={`px-1.5 py-0.5 text-[10px] leading-4 rounded-full ${item.badgeColor || 'bg-gray-100 text-gray-800'}`}>
              {item.badge}
            </span>
          )}
        </div>

        {hasChildren && isExpanded && (
          <div>
            {item.children!.map(child => renderExplorerItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  const activeTab = tabs.find(tab => tab.id === activeTabId);

  return (
    <div className={`h-full flex overflow-hidden min-w-0 ${className}`}>
      {/* Left Panel - Explorer */}
      {!hideExplorer && (
        <div className="w-80 flex-shrink-0 border-r border-gray-200 bg-gray-50 flex flex-col">
          <div className="flex-1 overflow-auto">
            {explorerItems.map(item => renderExplorerItem(item))}
          </div>
        </div>
      )}

      {/* Right Panel - Tabs */}
      <div className="flex-1 bg-white min-w-0 flex flex-col">
        {/* Tab Bar */}
        <div className="flex items-center border-b border-gray-200 bg-[#f8f9fa] relative min-w-0">
          {/* Left Scroll Button */}
          {showScrollButtons && (
            <button
              onClick={scrollLeft}
              disabled={!canScrollLeft}
              className={`flex-shrink-0 w-10 h-10 flex items-center justify-center border-r border-gray-200 transition-colors ${
                canScrollLeft
                  ? 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  : 'text-gray-300 cursor-not-allowed'
              }`}
              title="Scroll left"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>
          )}

          {/* Scrollable Tabs Container */}
          <div
            ref={tabsContainerRef}
            className="flex-1 min-w-0 overflow-x-auto scrollbar-hide"
            style={{
              scrollbarWidth: 'none', // Firefox
              msOverflowStyle: 'none', // IE/Edge
            }}
          >
            <div className="flex min-w-max">
              {tabs.map((tab) => (
                <div
                  key={tab.id}
                  data-tab-id={tab.id}
                  className={`flex items-center space-x-2 px-4 py-3 border-r border-gray-200 cursor-pointer flex-shrink-0 transition-colors ${
                    activeTabId === tab.id
                      ? 'bg-white border-b-2 border-blue-500 text-blue-600'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                  onClick={() => onTabChange(tab.id)}
                  style={{ minWidth: 'max-content' }}
                >
                  <div className="flex-shrink-0">
                    {getTabIcon(tab.type)}
                  </div>
                  <span className="text-sm font-medium whitespace-nowrap max-w-[200px] truncate">
                    {tab.title}
                  </span>
                  {(tab.closable !== false) && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onTabClose(tab.id);
                      }}
                      className="flex-shrink-0 p-1 hover:bg-gray-200 rounded transition-colors"
                      title="Close tab"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  )}
                </div>
              ))}
            </div>
          </div>

          {/* Right Scroll Button */}
          {showScrollButtons && (
            <button
              onClick={scrollRight}
              disabled={!canScrollRight}
              className={`flex-shrink-0 w-10 h-10 flex items-center justify-center border-l border-gray-200 transition-colors ${
                canScrollRight
                  ? 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  : 'text-gray-300 cursor-not-allowed'
              }`}
              title="Scroll right"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
          )}
        </div>

        {/* Tab Content */}
        <div className="flex-1 overflow-auto bg-[#fafaf8] min-w-0">
          {activeTab && renderTabContent(activeTab)}
        </div>
      </div>
    </div>
  );
};

export default VSCodeInterface;
