import React, { useState, useRef } from 'react';
import {
  Plus,
  Search,
  Map,
  Layers,
  AlertTriangle,
  Shield,
  Edit,
  Trash2,
  Save
} from 'lucide-react';

interface AreasDefinitionStepProps {
  data: any;
  onComplete: (data: any) => void;
  onNext: () => void;
  onPrevious: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
}

interface GeoCoordinate {
  latitude: number;
  longitude: number;
  elevation?: number;
  osm_node_id?: string;
  accuracy?: number;
}

interface SiteArea {
  id: string;
  name: string;
  code: string;
  type: 'construction' | 'storage' | 'office' | 'parking' | 'safety' | 'utility';
  description: string;
  coordinates: GeoCoordinate[];
  size: number;
  classification: 'restricted' | 'controlled' | 'public';
  safetyLevel: 'high_risk' | 'medium_risk' | 'low_risk';
  accessRequirements: string[];
  ppeRequirements: string[];
  equipmentAllowed: string[];
  maxOccupancy?: number;
}

interface SafetyZone {
  id: string;
  name: string;
  type: 'exclusion' | 'caution' | 'emergency_assembly' | 'first_aid';
  coordinates: GeoCoordinate[];
  radius?: number;
  restrictions: string[];
  emergencyProcedures?: string[];
}

interface AreasData {
  totalArea: number;
  boundaryCoordinates: GeoCoordinate[];
  layoutPlanUrl?: string;
  mainAreas: SiteArea[];
  safetyZones: SafetyZone[];
  coordinateAcquisitionMethod: 'interactive_map' | 'address_geocoding' | 'manual_entry' | 'gps_import';
}

const AREA_TYPES = [
  { value: 'construction', label: 'Construction Zone', color: 'bg-orange-100 text-orange-800' },
  { value: 'storage', label: 'Storage Area', color: 'bg-blue-100 text-blue-800' },
  { value: 'office', label: 'Office/Admin', color: 'bg-green-100 text-green-800' },
  { value: 'parking', label: 'Parking', color: 'bg-gray-100 text-gray-800' },
  { value: 'safety', label: 'Safety Zone', color: 'bg-red-100 text-red-800' },
  { value: 'utility', label: 'Utilities', color: 'bg-yellow-100 text-yellow-800' }
];

const SAFETY_LEVELS = [
  { value: 'low_risk', label: 'Low Risk', color: 'bg-green-100 text-green-800' },
  { value: 'medium_risk', label: 'Medium Risk', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'high_risk', label: 'High Risk', color: 'bg-red-100 text-red-800' }
];

const CLASSIFICATION_LEVELS = [
  { value: 'public', label: 'Public Access', color: 'bg-green-100 text-green-800' },
  { value: 'controlled', label: 'Controlled Access', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'restricted', label: 'Restricted Access', color: 'bg-red-100 text-red-800' }
];

const AreasDefinitionStep: React.FC<AreasDefinitionStepProps> = ({
  data,
  onComplete
}) => {
  const [areasData, setAreasData] = useState<AreasData>({
    totalArea: 0,
    boundaryCoordinates: [],
    mainAreas: [],
    safetyZones: [],
    coordinateAcquisitionMethod: 'interactive_map',
    ...data
  });

  const [currentView, setCurrentView] = useState<'map' | 'areas' | 'safety'>('map');
  const [selectedArea, setSelectedArea] = useState<SiteArea | null>(null);
  const [addressSearch, setAddressSearch] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const mapRef = useRef<HTMLDivElement>(null);

  // Mock OpenStreetMap integration
  const handleAddressSearch = async () => {
    if (!addressSearch.trim()) return;
    
    try {
      // Mock geocoding results
      const mockResults = [
        {
          display_name: `${addressSearch}, Nairobi, Kenya`,
          latitude: -1.2921 + (Math.random() - 0.5) * 0.01,
          longitude: 36.8219 + (Math.random() - 0.5) * 0.01,
          accuracy: 10
        }
      ];
      setSearchResults(mockResults);
    } catch (error) {
      console.error('Address search failed:', error);
    }
  };

  const jumpToLocation = () => {
    // TODO: Implement map centering when map component is added
    setSearchResults([]);
  };





  const addNewArea = () => {
    const newArea: SiteArea = {
      id: `area_${Date.now()}`,
      name: '',
      code: '',
      type: 'construction',
      description: '',
      coordinates: [],
      size: 0,
      classification: 'controlled',
      safetyLevel: 'medium_risk',
      accessRequirements: [],
      ppeRequirements: [],
      equipmentAllowed: []
    };
    
    setSelectedArea(newArea);
    setCurrentView('areas');
  };

  const saveArea = () => {
    if (!selectedArea || !selectedArea.name || selectedArea.coordinates.length < 3) return;

    const updatedAreas = selectedArea.id.startsWith('area_') &&
      !areasData.mainAreas.find(a => a.id === selectedArea.id)
      ? [...areasData.mainAreas, selectedArea]
      : areasData.mainAreas.map(area => area.id === selectedArea.id ? selectedArea : area);

    handleDataChange({ mainAreas: updatedAreas });

    setSelectedArea(null);
  };

  const deleteArea = (areaId: string) => {
    const updatedAreas = areasData.mainAreas.filter(area => area.id !== areaId);
    handleDataChange({ mainAreas: updatedAreas });

    if (selectedArea?.id === areaId) {
      setSelectedArea(null);
    }
  };

  // Call onComplete when user makes significant changes (not on every render)
  const handleDataChange = (newData: Partial<AreasData>) => {
    const updatedData = { ...areasData, ...newData };
    setAreasData(updatedData);
    onComplete(updatedData);
  };

  const isValid = areasData.boundaryCoordinates.length >= 3 && areasData.mainAreas.length > 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h3 className="text-lg font-medium text-gray-900">Site Areas & Locations Definition</h3>
        <p className="mt-1 text-sm text-gray-500">
          Define site boundaries, create main and sub-areas, set classifications and access requirements, and configure safety zones
        </p>
      </div>

      {/* View Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setCurrentView('map')}
            className={`group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              currentView === 'map'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Map className="h-5 w-5 mr-2" />
            Interactive Map
          </button>
          <button
            onClick={() => setCurrentView('areas')}
            className={`group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              currentView === 'areas'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Layers className="h-5 w-5 mr-2" />
            Areas ({areasData.mainAreas.length})
          </button>
          <button
            onClick={() => setCurrentView('safety')}
            className={`group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
              currentView === 'safety'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Shield className="h-5 w-5 mr-2" />
            Safety Zones ({areasData.safetyZones.length})
          </button>
        </nav>
      </div>

      {/* Content based on current view */}
      {currentView === 'map' && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Map Controls */}
          <div className="space-y-4">
            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 mb-3">Address Search</h3>
              <div className="flex space-x-2">
                <input
                  type="text"
                  value={addressSearch}
                  onChange={(e) => setAddressSearch(e.target.value)}
                  placeholder="Search for address or landmark..."
                  className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <button
                  onClick={handleAddressSearch}
                  className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
                >
                  <Search className="h-4 w-4" />
                </button>
              </div>
              
              {searchResults.length > 0 && (
                <div className="mt-3 space-y-2">
                  {searchResults.map((result, index) => (
                    <button
                      key={index}
                      onClick={() => jumpToLocation()}
                      className="w-full text-left p-2 bg-white border border-gray-200 rounded-md hover:bg-gray-50"
                    >
                      <div className="text-sm font-medium">{result.display_name}</div>
                      <div className="text-xs text-gray-500">
                        {result.latitude.toFixed(6)}, {result.longitude.toFixed(6)}
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>

            <div className="bg-gray-50 rounded-lg p-4">
              <h3 className="font-semibold text-gray-900 mb-3">Drawing Tools</h3>
              <div className="space-y-2">
                <button
                  onClick={() => {/* Start boundary drawing */}}
                  className="w-full px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
                >
                  Draw Site Boundary
                </button>
                <button
                  onClick={addNewArea}
                  className="w-full px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm"
                >
                  <Plus className="h-4 w-4 mr-2 inline" />
                  Add Work Area
                </button>
              </div>
            </div>

            {areasData.totalArea > 0 && (
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="font-semibold text-blue-900 mb-2">Site Summary</h3>
                <div className="text-sm text-blue-800">
                  <div>Total Area: {(areasData.totalArea / 10000).toFixed(2)} hectares</div>
                  <div>Boundary Points: {areasData.boundaryCoordinates.length}</div>
                  <div>Work Areas: {areasData.mainAreas.length}</div>
                </div>
              </div>
            )}
          </div>

          {/* Map Container */}
          <div className="lg:col-span-2">
            <div 
              ref={mapRef}
              className="w-full h-96 bg-gray-100 border border-gray-300 rounded-lg flex items-center justify-center"
            >
              <div className="text-center text-gray-500">
                <Map className="h-12 w-12 mx-auto mb-2" />
                <p>Interactive OpenStreetMap will be rendered here</p>
                <p className="text-sm">Click "Draw Site Boundary" to start defining your site</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {currentView === 'areas' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Areas List */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">Work Areas</h3>
              <button
                onClick={addNewArea}
                className="flex items-center px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Area
              </button>
            </div>

            <div className="space-y-3">
              {areasData.mainAreas.map((area) => (
                <div
                  key={area.id}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    selectedArea?.id === area.id 
                      ? 'border-blue-500 bg-blue-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedArea(area)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <h4 className="font-medium text-gray-900">{area.name || 'Unnamed Area'}</h4>
                      <p className="text-sm text-gray-500">{area.code}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        AREA_TYPES.find(t => t.value === area.type)?.color
                      }`}>
                        {AREA_TYPES.find(t => t.value === area.type)?.label}
                      </span>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          deleteArea(area.id);
                        }}
                        className="p-1 text-red-600 hover:bg-red-50 rounded"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between text-sm text-gray-600">
                    <span>{(area.size / 10000).toFixed(3)} hectares</span>
                    <span className={`px-2 py-1 rounded-full text-xs ${
                      SAFETY_LEVELS.find(s => s.value === area.safetyLevel)?.color
                    }`}>
                      {SAFETY_LEVELS.find(s => s.value === area.safetyLevel)?.label}
                    </span>
                  </div>
                </div>
              ))}

              {areasData.mainAreas.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <Layers className="h-12 w-12 mx-auto mb-2" />
                  <p>No work areas defined yet</p>
                  <p className="text-sm">Click "Add Area" to create your first work area</p>
                </div>
              )}
            </div>
          </div>

          {/* Area Details Form */}
          <div className="bg-gray-50 rounded-lg p-6">
            {selectedArea ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">Area Details</h3>
                  <button
                    onClick={saveArea}
                    className="flex items-center px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm"
                  >
                    <Save className="h-4 w-4 mr-1" />
                    Save
                  </button>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Area Name *
                    </label>
                    <input
                      type="text"
                      value={selectedArea.name}
                      onChange={(e) => setSelectedArea({...selectedArea, name: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., Main Construction Area"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Area Code *
                    </label>
                    <input
                      type="text"
                      value={selectedArea.code}
                      onChange={(e) => setSelectedArea({...selectedArea, code: e.target.value})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="e.g., MAIN-CONST"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Area Type *
                    </label>
                    <select
                      value={selectedArea.type}
                      onChange={(e) => setSelectedArea({...selectedArea, type: e.target.value as any})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
                    >
                      {AREA_TYPES.map(type => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Safety Level
                    </label>
                    <select
                      value={selectedArea.safetyLevel}
                      onChange={(e) => setSelectedArea({...selectedArea, safetyLevel: e.target.value as any})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      {SAFETY_LEVELS.map(level => (
                        <option key={level.value} value={level.value}>
                          {level.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Access Classification
                    </label>
                    <select
                      value={selectedArea.classification}
                      onChange={(e) => setSelectedArea({...selectedArea, classification: e.target.value as any})}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      {CLASSIFICATION_LEVELS.map(level => (
                        <option key={level.value} value={level.value}>
                          {level.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Description
                    </label>
                    <textarea
                      value={selectedArea.description}
                      onChange={(e) => setSelectedArea({...selectedArea, description: e.target.value})}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Describe the purpose and activities in this area..."
                    />
                  </div>

                  {selectedArea.coordinates.length > 0 && (
                    <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                      <div className="text-sm text-blue-800">
                        <div>Area Size: {(selectedArea.size / 10000).toFixed(3)} hectares</div>
                        <div>Boundary Points: {selectedArea.coordinates.length}</div>
                      </div>
                    </div>
                  )}

                  {selectedArea.coordinates.length === 0 && (
                    <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
                      <div className="flex items-center">
                        <AlertTriangle className="h-4 w-4 text-yellow-600 mr-2" />
                        <span className="text-sm text-yellow-800">
                          No coordinates defined. Use the map to draw this area.
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Edit className="h-12 w-12 mx-auto mb-2" />
                <p>Select an area to edit its details</p>
                <p className="text-sm">Or create a new area to get started</p>
              </div>
            )}
          </div>
        </div>
      )}

      {currentView === 'safety' && (
        <div className="text-center py-12 text-gray-500">
          <Shield className="h-12 w-12 mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Safety Zones</h3>
          <p>Define emergency assembly points, exclusion zones, and first aid stations</p>
          <button className="mt-4 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700">
            Add Safety Zone
          </button>
        </div>
      )}

      {/* Validation Messages */}
      {!isValid && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2" />
            <div className="text-sm text-yellow-800">
              <p className="font-medium">Please complete the following:</p>
              <ul className="mt-1 list-disc list-inside">
                {areasData.boundaryCoordinates.length < 3 && (
                  <li>Define site boundaries (minimum 3 points)</li>
                )}
                {areasData.mainAreas.length === 0 && (
                  <li>Create at least one work area</li>
                )}
              </ul>
            </div>
          </div>
        </div>
      )}


    </div>
  );
};

export default AreasDefinitionStep;
