import React, { useState, useEffect } from 'react';
import { Shield, FileText, AlertTriangle, CheckCircle, Clock, Plus, X, Building } from 'lucide-react';
import RegulatoryDocumentUpload, { RegulatoryDocument } from '../regulatory/RegulatoryDocumentUpload';

interface RegulatoryData {
  // Building Permit
  buildingPermitRequired: boolean;
  buildingPermitStatus: string;
  buildingPermitNumber: string;
  buildingPermitAuthority: string;
  buildingPermitDate: string;
  buildingPermitExpiry: string;
  buildingPermitDocument: RegulatoryDocument | null;

  // NEMA EIA
  nemaEiaRequired: boolean;
  nemaEiaStatus: string;
  nemaEiaNumber: string;
  nemaEiaDate: string;
  nemaEiaDocument: RegulatoryDocument | null;

  // NCA Registration
  ncaRegistrationRequired: boolean;
  ncaRegistrationStatus: string;
  ncaRegistrationNumber: string;
  ncaRegistrationDate: string;
  ncaRegistrationDocument: RegulatoryDocument | null;

  // Fire Safety
  fireSafetyRequired: boolean;
  fireSafetyStatus: string;
  fireSafetyNumber: string;
  fireSafetyDate: string;
  fireSafetyDocument: RegulatoryDocument | null;

  // Overall status
  overallComplianceStatus: string;

  // Additional approvals
  additionalApprovals: Array<{
    type: string;
    authority: string;
    status: string;
    referenceNumber: string;
    date: string;
    notes: string;
    document: RegulatoryDocument | null;
  }>;
}

interface RegulatoryStepProps {
  data: RegulatoryData;
  onComplete: (data: RegulatoryData) => void;
  onNext: () => void;
  onPrevious: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
  onCreateSite?: () => void;
  isCreating?: boolean;
}

const STATUS_OPTIONS = [
  { value: 'Not Started', label: 'Not Started', color: 'gray' },
  { value: 'In Progress', label: 'In Progress', color: 'yellow' },
  { value: 'Approved', label: 'Approved', color: 'green' },
  { value: 'Rejected', label: 'Rejected', color: 'red' }
];



const APPROVAL_TYPES = [
  'Special Permit',
  'Local Approval',
  'Utility Connection',
  'Road Access Permit',
  'Waste Management License',
  'Water Connection',
  'Electrical Connection',
  'Other'
];

const RegulatoryStep: React.FC<RegulatoryStepProps> = ({
  data,
  onComplete
}) => {
  const [formData, setFormData] = useState<RegulatoryData>(data);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newApproval, setNewApproval] = useState({
    type: '',
    authority: '',
    status: 'Not Started',
    referenceNumber: '',
    date: '',
    notes: '',
    document: null as RegulatoryDocument | null
  });

  const handleInputChange = (field: keyof RegulatoryData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleAddApproval = () => {
    if (newApproval.type && newApproval.authority) {
      const updatedData = {
        ...formData,
        additionalApprovals: [...formData.additionalApprovals, { ...newApproval }]
      };
      setFormData(updatedData);
      setNewApproval({
        type: '',
        authority: '',
        status: 'Not Started',
        referenceNumber: '',
        date: '',
        notes: '',
        document: null
      });
      setShowAddForm(false);
    }
  };

  const handleRemoveApproval = (index: number) => {
    const updatedData = {
      ...formData,
      additionalApprovals: formData.additionalApprovals.filter((_, i) => i !== index)
    };
    setFormData(updatedData);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Approved':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'In Progress':
        return <Clock className="h-5 w-5 text-yellow-500" />;
      case 'Rejected':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      default:
        return <FileText className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Approved':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'In Progress':
        return 'bg-yellow-50 border-yellow-200 text-yellow-800';
      case 'Rejected':
        return 'bg-red-50 border-red-200 text-red-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };

  // Calculate overall compliance status
  const calculateOverallStatus = (data: RegulatoryData): string => {
    const requiredApprovals = [];

    if (data.buildingPermitRequired) requiredApprovals.push(data.buildingPermitStatus);
    if (data.nemaEiaRequired) requiredApprovals.push(data.nemaEiaStatus);
    if (data.ncaRegistrationRequired) requiredApprovals.push(data.ncaRegistrationStatus);
    if (data.fireSafetyRequired) requiredApprovals.push(data.fireSafetyStatus);

    // Add additional approvals
    data.additionalApprovals.forEach(approval => {
      requiredApprovals.push(approval.status);
    });

    if (requiredApprovals.length === 0) return 'Complete';

    const allApproved = requiredApprovals.every(status => status === 'Approved');
    const anyInProgress = requiredApprovals.some(status => status === 'In Progress');
    const anyRejected = requiredApprovals.some(status => status === 'Rejected');

    if (allApproved) return 'Complete';
    if (anyInProgress || anyRejected) return 'In Progress';
    return 'Incomplete';
  };

  // Auto-save on data change and calculate overall status
  useEffect(() => {
    const timer = setTimeout(() => {
      const updatedData = {
        ...formData,
        overallComplianceStatus: calculateOverallStatus(formData)
      };
      setFormData(updatedData);
      onComplete(updatedData);
    }, 1000);

    return () => clearTimeout(timer);
  }, [formData, onComplete]);

  return (
    <div className="p-6 md:p-8">
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <Shield className="h-6 w-6 text-blue-600 mr-3" />
          <h2 className="text-2xl font-bold text-gray-900">Regulatory Compliance</h2>
        </div>
        <p className="text-gray-600">
          Track required approvals and permits for your construction project.
          This ensures compliance with local regulations and smooth project execution.
        </p>
      </div>

      <div className="space-y-8">
        {/* Mandatory Approvals */}
        <div className="bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Required Approvals</h3>

          {/* Building Permit */}
          <div className="mb-8 p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <Building className="h-5 w-5 text-blue-600 mr-2" />
                <h4 className="text-lg font-medium text-gray-900">Building Permit</h4>
              </div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.buildingPermitRequired}
                  onChange={(e) => handleInputChange('buildingPermitRequired', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-600">Required</span>
              </label>
            </div>

            {formData.buildingPermitRequired && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select
                      value={formData.buildingPermitStatus}
                      onChange={(e) => handleInputChange('buildingPermitStatus', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      {STATUS_OPTIONS.map(option => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Permit Number</label>
                    <input
                      type="text"
                      value={formData.buildingPermitNumber}
                      onChange={(e) => handleInputChange('buildingPermitNumber', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter permit number"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Issuing Authority</label>
                    <input
                      type="text"
                      value={formData.buildingPermitAuthority}
                      onChange={(e) => handleInputChange('buildingPermitAuthority', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="e.g., Nairobi City County"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Issue Date</label>
                    <input
                      type="date"
                      value={formData.buildingPermitDate}
                      onChange={(e) => handleInputChange('buildingPermitDate', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Expiry Date</label>
                    <input
                      type="date"
                      value={formData.buildingPermitExpiry}
                      onChange={(e) => handleInputChange('buildingPermitExpiry', e.target.value)}
                      className="w-full max-w-xs px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                <RegulatoryDocumentUpload
                  approvalType="Building Permit"
                  document={formData.buildingPermitDocument}
                  onDocumentChange={(doc) => handleInputChange('buildingPermitDocument', doc)}
                  required={formData.buildingPermitStatus === 'Approved'}
                />
              </div>
            )}
          </div>

          {/* NEMA EIA */}
          <div className="mb-8 p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <Shield className="h-5 w-5 text-green-600 mr-2" />
                <h4 className="text-lg font-medium text-gray-900">NEMA Environmental Impact Assessment</h4>
              </div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.nemaEiaRequired}
                  onChange={(e) => handleInputChange('nemaEiaRequired', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-600">Required</span>
              </label>
            </div>

            {formData.nemaEiaRequired && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select
                      value={formData.nemaEiaStatus}
                      onChange={(e) => handleInputChange('nemaEiaStatus', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      {STATUS_OPTIONS.map(option => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">EIA Number</label>
                    <input
                      type="text"
                      value={formData.nemaEiaNumber}
                      onChange={(e) => handleInputChange('nemaEiaNumber', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter EIA number"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Issue Date</label>
                    <input
                      type="date"
                      value={formData.nemaEiaDate}
                      onChange={(e) => handleInputChange('nemaEiaDate', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                <RegulatoryDocumentUpload
                  approvalType="NEMA EIA"
                  document={formData.nemaEiaDocument}
                  onDocumentChange={(doc) => handleInputChange('nemaEiaDocument', doc)}
                  required={formData.nemaEiaStatus === 'Approved'}
                />
              </div>
            )}
          </div>

          {/* NCA Registration */}
          <div className="mb-8 p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <FileText className="h-5 w-5 text-purple-600 mr-2" />
                <h4 className="text-lg font-medium text-gray-900">NCA Registration</h4>
              </div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.ncaRegistrationRequired}
                  onChange={(e) => handleInputChange('ncaRegistrationRequired', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-600">Required</span>
              </label>
            </div>

            {formData.ncaRegistrationRequired && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select
                      value={formData.ncaRegistrationStatus}
                      onChange={(e) => handleInputChange('ncaRegistrationStatus', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      {STATUS_OPTIONS.map(option => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Registration Number</label>
                    <input
                      type="text"
                      value={formData.ncaRegistrationNumber}
                      onChange={(e) => handleInputChange('ncaRegistrationNumber', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter NCA registration number"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Registration Date</label>
                    <input
                      type="date"
                      value={formData.ncaRegistrationDate}
                      onChange={(e) => handleInputChange('ncaRegistrationDate', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                <RegulatoryDocumentUpload
                  approvalType="NCA Registration"
                  document={formData.ncaRegistrationDocument}
                  onDocumentChange={(doc) => handleInputChange('ncaRegistrationDocument', doc)}
                  required={formData.ncaRegistrationStatus === 'Approved'}
                />
              </div>
            )}
          </div>

          {/* Fire Safety */}
          <div className="mb-8 p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
                <h4 className="text-lg font-medium text-gray-900">Fire Safety Certificate</h4>
              </div>
              <label className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.fireSafetyRequired}
                  onChange={(e) => handleInputChange('fireSafetyRequired', e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="ml-2 text-sm text-gray-600">Required</span>
              </label>
            </div>

            {formData.fireSafetyRequired && (
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select
                      value={formData.fireSafetyStatus}
                      onChange={(e) => handleInputChange('fireSafetyStatus', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      {STATUS_OPTIONS.map(option => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Certificate Number</label>
                    <input
                      type="text"
                      value={formData.fireSafetyNumber}
                      onChange={(e) => handleInputChange('fireSafetyNumber', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter certificate number"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Issue Date</label>
                    <input
                      type="date"
                      value={formData.fireSafetyDate}
                      onChange={(e) => handleInputChange('fireSafetyDate', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                <RegulatoryDocumentUpload
                  approvalType="Fire Safety Certificate"
                  document={formData.fireSafetyDocument}
                  onDocumentChange={(doc) => handleInputChange('fireSafetyDocument', doc)}
                  required={formData.fireSafetyStatus === 'Approved'}
                />
              </div>
            )}
          </div>
        </div>

        {/* Overall Compliance Status */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <div className="flex items-center mb-4">
            <CheckCircle className="h-6 w-6 text-blue-600 mr-3" />
            <h3 className="text-lg font-semibold text-gray-900">Overall Compliance Status</h3>
          </div>

          <div className="flex items-center space-x-4">
            <div className={`inline-flex items-center px-4 py-2 rounded-full text-sm font-medium ${getStatusColor(formData.overallComplianceStatus)}`}>
              {getStatusIcon(formData.overallComplianceStatus)}
              <span className="ml-2">{formData.overallComplianceStatus}</span>
            </div>

            <div className="text-sm text-gray-600">
              {formData.overallComplianceStatus === 'Complete' && 'All required approvals are in place'}
              {formData.overallComplianceStatus === 'In Progress' && 'Some approvals are pending or in progress'}
              {formData.overallComplianceStatus === 'Incomplete' && 'Required approvals need to be initiated'}
            </div>
          </div>
        </div>

        {/* Additional Approvals */}
        <div className="bg-white border border-gray-200 rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">
              Additional Approvals ({formData.additionalApprovals.length})
            </h3>
            <button
              onClick={() => setShowAddForm(true)}
              className="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm flex items-center"
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Approval
            </button>
          </div>

          <div className="p-6">
            {/* Add Form */}
            {showAddForm && (
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <h4 className="text-md font-medium text-gray-900 mb-4">Add New Approval</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Type *</label>
                    <select
                      value={newApproval.type}
                      onChange={(e) => setNewApproval(prev => ({ ...prev, type: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">Select approval type</option>
                      {APPROVAL_TYPES.map(type => (
                        <option key={type} value={type}>{type}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Authority *</label>
                    <input
                      type="text"
                      value={newApproval.authority}
                      onChange={(e) => setNewApproval(prev => ({ ...prev, authority: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Issuing authority"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select
                      value={newApproval.status}
                      onChange={(e) => setNewApproval(prev => ({ ...prev, status: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      {STATUS_OPTIONS.map(option => (
                        <option key={option.value} value={option.value}>{option.label}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Reference Number</label>
                    <input
                      type="text"
                      value={newApproval.referenceNumber}
                      onChange={(e) => setNewApproval(prev => ({ ...prev, referenceNumber: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Reference number"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Issue Date</label>
                    <input
                      type="date"
                      value={newApproval.date}
                      onChange={(e) => setNewApproval(prev => ({ ...prev, date: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-1">Notes</label>
                    <textarea
                      value={newApproval.notes}
                      onChange={(e) => setNewApproval(prev => ({ ...prev, notes: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      rows={2}
                      placeholder="Additional notes or comments"
                    />
                  </div>
                </div>

                <RegulatoryDocumentUpload
                  approvalType={newApproval.type || 'Additional Approval'}
                  document={newApproval.document}
                  onDocumentChange={(doc) => setNewApproval(prev => ({ ...prev, document: doc }))}
                  required={newApproval.status === 'Approved'}
                />
                <div className="flex justify-end space-x-2 mt-4">
                  <button
                    onClick={() => setShowAddForm(false)}
                    className="px-3 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleAddApproval}
                    disabled={!newApproval.type || !newApproval.authority}
                    className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Add Approval
                  </button>
                </div>
              </div>
            )}

            {/* Approval List */}
            {formData.additionalApprovals.length > 0 ? (
              <div className="space-y-4">
                {formData.additionalApprovals.map((approval, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">{approval.type}</div>
                        <div className="text-sm text-gray-600 mt-1">
                          {approval.authority}
                          {approval.referenceNumber && ` • ${approval.referenceNumber}`}
                          {approval.date && ` • ${new Date(approval.date).toLocaleDateString()}`}
                        </div>
                        {approval.notes && (
                          <div className="text-sm text-gray-500 mt-1">{approval.notes}</div>
                        )}
                      </div>
                      <button
                        onClick={() => handleRemoveApproval(index)}
                        className="p-1 text-red-400 hover:text-red-600 ml-2"
                      >
                        <X className="h-4 w-4" />
                      </button>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(approval.status)}`}>
                        {getStatusIcon(approval.status)}
                        <span className="ml-1">{approval.status}</span>
                      </div>

                      {approval.document && (
                        <div className="flex items-center text-sm text-green-600">
                          <FileText className="h-4 w-4 mr-1" />
                          Document uploaded
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Shield className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                <p>No additional approvals added yet</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default RegulatoryStep;
