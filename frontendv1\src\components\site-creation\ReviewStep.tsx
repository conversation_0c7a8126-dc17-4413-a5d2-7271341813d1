import React from 'react';
import { FileText } from 'lucide-react';
import PlaceholderStep from './PlaceholderStep';

interface ReviewStepProps {
  data: any;
  onComplete: (data: any) => void;
  onNext: () => void;
  onPrevious: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
  onCreateSite: () => void;
  isCreating: boolean;
}



const ReviewStep: React.FC<ReviewStepProps> = (props) => {
  return (
    <PlaceholderStep
      {...props}
      title="Final Validation & Site Creation"
      description="Run final validation, review summary, resolve blocking issues, and create the site"
      icon={FileText}
    />
  );
};

export default ReviewStep;
