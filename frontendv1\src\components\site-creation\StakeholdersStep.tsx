import React, { useState, useEffect } from 'react';
import { Users, Mail, Building2, Plus, X } from 'lucide-react';

interface StakeholdersData {
  clientEmail: string;
  clientPhone: string;
  projectManagerEmail: string;
  projectManagerPhone: string;
  mainContractorName: string;
  mainContractorEmail: string;
  mainContractorCompany: string;
  architectName: string;
  architectEmail: string;
  architectCompany: string;
  additionalStakeholders: Array<{
    role: string;
    name: string;
    company: string;
    email: string;
    phone: string;
    specialization: string;
  }>;
}

interface StakeholdersStepProps {
  data: StakeholdersData;
  onComplete: (data: StakeholdersData) => void;
  onNext: () => void;
  onPrevious: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
  onCreateSite?: () => void;
  isCreating?: boolean;
}

const STAKEHOLDER_ROLES = [
  'Sub-Contractor',
  'Specialist',
  'Consultant',
  'Supplier',
  'Inspector',
  'Safety Officer',
  'Quality Controller',
  'Engineer'
];

const StakeholdersStep: React.FC<StakeholdersStepProps> = ({
  data,
  onComplete
}) => {
  const [formData, setFormData] = useState<StakeholdersData>(data);
  const [showAddForm, setShowAddForm] = useState(false);
  const [newStakeholder, setNewStakeholder] = useState({
    role: '',
    name: '',
    company: '',
    email: '',
    phone: '',
    specialization: ''
  });

  const handleInputChange = (field: keyof StakeholdersData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleAddStakeholder = () => {
    if (newStakeholder.name.trim() && newStakeholder.role) {
      const updatedData = {
        ...formData,
        additionalStakeholders: [...formData.additionalStakeholders, { ...newStakeholder }]
      };
      setFormData(updatedData);
      setNewStakeholder({
        role: '',
        name: '',
        company: '',
        email: '',
        phone: '',
        specialization: ''
      });
      setShowAddForm(false);
    }
  };

  const handleRemoveStakeholder = (index: number) => {
    const updatedData = {
      ...formData,
      additionalStakeholders: formData.additionalStakeholders.filter((_, i) => i !== index)
    };
    setFormData(updatedData);
  };

  // Auto-save on data change
  useEffect(() => {
    const timer = setTimeout(() => {
      onComplete(formData);
    }, 1000);

    return () => clearTimeout(timer);
  }, [formData, onComplete]);

  return (
    <div className="p-6 md:p-8">
      <div className="mb-8">
        <div className="flex items-center mb-4">
          <Users className="h-6 w-6 text-blue-600 mr-3" />
          <h2 className="text-2xl font-bold text-gray-900">Stakeholders</h2>
        </div>
        <p className="text-gray-600">
          Define key contacts and roles for your project. This ensures everyone 
          knows who to contact for different aspects of the construction.
        </p>
      </div>

      <div className="space-y-8">
        {/* Mandatory Stakeholders */}
        <div className="bg-gray-50 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Key Contacts</h3>
          
          {/* Client Details */}
          <div className="mb-6">
            <h4 className="text-md font-medium text-gray-900 mb-3 flex items-center">
              <Building2 className="h-4 w-4 mr-2" />
              Client Contact Details
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address *
                </label>
                <input
                  type="email"
                  value={formData.clientEmail}
                  onChange={(e) => handleInputChange('clientEmail', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number
                </label>
                <input
                  type="tel"
                  value={formData.clientPhone}
                  onChange={(e) => handleInputChange('clientPhone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="+254 700 000 000"
                />
              </div>
            </div>
          </div>

          {/* Project Manager Details */}
          <div className="mb-6">
            <h4 className="text-md font-medium text-gray-900 mb-3 flex items-center">
              <Users className="h-4 w-4 mr-2" />
              Project Manager Contact Details
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address *
                </label>
                <input
                  type="email"
                  value={formData.projectManagerEmail}
                  onChange={(e) => handleInputChange('projectManagerEmail', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number
                </label>
                <input
                  type="tel"
                  value={formData.projectManagerPhone}
                  onChange={(e) => handleInputChange('projectManagerPhone', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="+254 700 000 000"
                />
              </div>
            </div>
          </div>

          {/* Main Contractor */}
          <div className="mb-6">
            <h4 className="text-md font-medium text-gray-900 mb-3">Main Contractor</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Contractor Name
                </label>
                <input
                  type="text"
                  value={formData.mainContractorName}
                  onChange={(e) => handleInputChange('mainContractorName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="John Doe"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Company
                </label>
                <input
                  type="text"
                  value={formData.mainContractorCompany}
                  onChange={(e) => handleInputChange('mainContractorCompany', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="ABC Construction Ltd"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address
                </label>
                <input
                  type="email"
                  value={formData.mainContractorEmail}
                  onChange={(e) => handleInputChange('mainContractorEmail', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
          </div>

          {/* Architect */}
          <div>
            <h4 className="text-md font-medium text-gray-900 mb-3">Architect</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Architect Name
                </label>
                <input
                  type="text"
                  value={formData.architectName}
                  onChange={(e) => handleInputChange('architectName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Jane Smith"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Company
                </label>
                <input
                  type="text"
                  value={formData.architectCompany}
                  onChange={(e) => handleInputChange('architectCompany', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="XYZ Architects"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Email Address
                </label>
                <input
                  type="email"
                  value={formData.architectEmail}
                  onChange={(e) => handleInputChange('architectEmail', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Additional Stakeholders */}
        <div className="bg-white border border-gray-200 rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">
              Additional Stakeholders ({formData.additionalStakeholders.length})
            </h3>
            <button
              onClick={() => setShowAddForm(true)}
              className="px-3 py-1 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm flex items-center"
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Stakeholder
            </button>
          </div>

          <div className="p-6">
            {/* Add Form */}
            {showAddForm && (
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <h4 className="text-md font-medium text-gray-900 mb-4">Add New Stakeholder</h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Role *</label>
                    <select
                      value={newStakeholder.role}
                      onChange={(e) => setNewStakeholder(prev => ({ ...prev, role: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="">Select role</option>
                      {STAKEHOLDER_ROLES.map(role => (
                        <option key={role} value={role}>{role}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Name *</label>
                    <input
                      type="text"
                      value={newStakeholder.name}
                      onChange={(e) => setNewStakeholder(prev => ({ ...prev, name: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Full name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Company</label>
                    <input
                      type="text"
                      value={newStakeholder.company}
                      onChange={(e) => setNewStakeholder(prev => ({ ...prev, company: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Company name"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Email</label>
                    <input
                      type="email"
                      value={newStakeholder.email}
                      onChange={(e) => setNewStakeholder(prev => ({ ...prev, email: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>
                <div className="flex justify-end space-x-2 mt-4">
                  <button
                    onClick={() => setShowAddForm(false)}
                    className="px-3 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleAddStakeholder}
                    disabled={!newStakeholder.name.trim() || !newStakeholder.role}
                    className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Add Stakeholder
                  </button>
                </div>
              </div>
            )}

            {/* Stakeholder List */}
            {formData.additionalStakeholders.length > 0 ? (
              <div className="space-y-3">
                {formData.additionalStakeholders.map((stakeholder, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-md">
                    <div>
                      <div className="font-medium text-gray-900">{stakeholder.name}</div>
                      <div className="text-sm text-gray-600">
                        {stakeholder.role} {stakeholder.company && `• ${stakeholder.company}`}
                      </div>
                      {stakeholder.email && (
                        <div className="text-sm text-gray-500 flex items-center mt-1">
                          <Mail className="h-3 w-3 mr-1" />
                          {stakeholder.email}
                        </div>
                      )}
                    </div>
                    <button
                      onClick={() => handleRemoveStakeholder(index)}
                      className="p-1 text-red-400 hover:text-red-600"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                <Users className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                <p>No additional stakeholders added yet</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default StakeholdersStep;
