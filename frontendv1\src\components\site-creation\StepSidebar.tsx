import React from 'react';

interface StepData {
  id: number;
  title: string;
  description: string;
  isCompleted: boolean;
  isActive: boolean;
  hasErrors: boolean;
  errorCount: number;
}

interface StepSidebarProps {
  steps: StepData[];
  currentStep: number;
  onStepClick: (stepId: number) => void;
  progressPercentage: number;
}

const StepSidebar: React.FC<StepSidebarProps> = ({
  steps,
  currentStep,
  onStepClick
}) => {
  return (
    <div className="step-sidebar">
      <div className="step-list">
        {steps.map((step, index) => {
          const isClickable = step.isCompleted || step.id === currentStep || step.id === currentStep + 1;

          return (
            <React.Fragment key={step.id}>
              <div
                className={`step-item ${
                  isClickable
                    ? 'cursor-pointer hover:bg-gray-50 rounded-lg p-3 -m-3 transition-colors'
                    : 'cursor-not-allowed'
                }`}
                onClick={() => isClickable && onStepClick(step.id)}
              >
                <div className="step-content">
                  <h3 className={`step-title ${
                    step.isActive
                      ? 'text-blue-600'
                      : step.isCompleted
                      ? 'text-green-600'
                      : step.hasErrors
                      ? 'text-red-600'
                      : 'text-gray-500'
                  }`}>
                    {step.title}
                  </h3>
                  <p className={`step-description ${
                    step.isActive
                      ? 'text-blue-500'
                      : step.isCompleted
                      ? 'text-green-500'
                      : step.hasErrors
                      ? 'text-red-500'
                      : 'text-gray-500'
                  }`}>
                    {step.description}
                    {step.hasErrors && step.errorCount && (
                      <span className="ml-1 text-red-600">
                        ({step.errorCount} error{step.errorCount > 1 ? 's' : ''})
                      </span>
                    )}
                  </p>
                </div>

                <div className="relative w-9 flex-shrink-0 flex justify-center">
                  {index > 0 && (
                    <div
                      aria-hidden="true"
                      className={`absolute bottom-full -mb-[1px] left-1/2 -translate-x-1/2 h-4 border-l-2 border-dotted ${
                        steps[index - 1]?.isCompleted
                          ? 'border-green-500'
                          : 'border-gray-300'
                      }`}
                    />
                  )}
                  <div className={`step-circle ${
                    step.isCompleted
                      ? 'completed'
                      : step.isActive
                      ? 'current'
                      : step.hasErrors
                      ? 'error'
                      : 'pending'
                  }`}>
                    {step.isCompleted ? (
                      <span className="text-xs font-bold">✓</span>
                    ) : step.hasErrors ? (
                      <span className="text-xs font-bold">!</span>
                    ) : (
                      <span className="text-sm font-medium">{step.id}</span>
                    )}
                  </div>

                  {index < steps.length - 1 && (
                    <div
                      aria-hidden="true"
                      className={`absolute top-full -mt-[1px] left-1/2 -translate-x-1/2 h-12 border-l-2 border-dotted ${
                        step.isCompleted
                          ? 'border-green-500'
                          : 'border-gray-300'
                      }`}
                    />
                  )}
                </div>
              </div>

            </React.Fragment>
          );
        })}
      </div>
    </div>
  );
};

export default StepSidebar;
