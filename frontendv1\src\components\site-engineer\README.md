# Site Engineer Components

This directory contains all reusable components specifically designed for site engineer functionality. These components are modular, well-typed, and optimized for mobile-first usage.

## Components

### TaskCreationTab.tsx
Handles the complete task creation workflow with three main steps:

#### **Step 1: Category Selection**
- Displays predefined task categories (Excavation, Electrical, Construction, etc.)
- Each category shows available templates count
- Large, touch-friendly cards with hover effects
- Category-specific icons and color coding

#### **Step 2: Template Selection**
- Shows templates within the selected category
- Displays estimated duration and risk level for each template
- Risk level color coding (low=green, medium=yellow, high=orange, critical=red)
- Back navigation to category selection

#### **Step 3: Task Details Form**
- Work description (required)
- Location selection from predefined list
- Date and time scheduling
- Urgency level selection (Normal/Urgent)
- Additional notes (optional)
- Task summary preview
- Add to task list functionality

### TaskReviewTab.tsx
Manages the review and submission of created tasks:

#### **Features**
- **Empty State**: Helpful guidance when no tasks exist
- **Task List**: Comprehensive display of all created tasks
- **Task Cards**: Individual task information with remove functionality
- **Summary Statistics**: Total tasks, urgent tasks, duration, high-risk tasks
- **Submission Overview**: Next steps and process explanation

#### **Task Information Display**
- Task number and template name
- Category and urgency indicators
- Work description in formatted text box
- Location and schedule information
- Duration and risk level badges
- Additional notes (when present)

### WeatherTab.tsx
Displays comprehensive weather information for work planning:

#### **Current Conditions**
- Temperature and weather conditions
- Humidity, wind speed, and precipitation
- Visual weather icons
- Organized in clean grid layout

#### **Weather Alerts**
- Critical weather warnings
- Alert type and severity indicators
- Detailed alert messages
- Color-coded alert cards

#### **2-Day Forecast**
- Tomorrow and day after weather
- Temperature ranges and conditions
- Precipitation chances
- Work impact indicators (Good/Minor/Moderate/Severe)

#### **Work Impact Guidelines**
- Color-coded impact levels
- Clear guidelines for each impact level
- Helps with work planning decisions

## Design Patterns

### 🎨 **Visual Design**
- **Consistent Card Design**: All components use similar card layouts
- **Color Coding**: Consistent color scheme across components
- **Typography Hierarchy**: Clear heading and text size relationships
- **Spacing System**: Uniform padding and margins

### 📱 **Mobile-First Approach**
- **Large Touch Targets**: Minimum 44px touch targets
- **Readable Text**: Appropriate font sizes for mobile screens
- **Thumb-Friendly Navigation**: Easy-to-reach interactive elements
- **Responsive Grids**: Adapt to different screen sizes

### 🔧 **Technical Patterns**
- **TypeScript Interfaces**: Strong typing for all props and data
- **React Hooks**: Modern React patterns with hooks
- **Event Handling**: Proper event handling for user interactions
- **State Management**: Local state management with useState

## Props and Interfaces

### Common Interfaces
```typescript
interface BulkTaskItem {
  id: string;
  category: TaskCategory;
  categoryName: string;
  template: TaskTemplateInfo;
  workDescription: string;
  location: string;
  plannedDate: string;
  plannedTime: string;
  estimatedDuration: number;
  urgency: 'normal' | 'urgent';
  dependencies?: string[];
  notes?: string;
}

interface TaskTemplateInfo {
  id: string;
  name: string;
  description: string;
  estimatedDuration: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
}

interface WeatherInfo {
  current: {
    temperature: number;
    humidity: number;
    windSpeed: number;
    precipitation: number;
    conditions: string;
  };
  forecast: Array<{
    date: string;
    highTemp: number;
    lowTemp: number;
    precipitationChance: number;
    conditions: string;
    workImpact: 'none' | 'minor' | 'moderate' | 'severe';
  }>;
  alerts: Array<{
    type: string;
    severity: string;
    message: string;
  }>;
}
```

## Usage Examples

### TaskCreationTab
```tsx
<TaskCreationTab 
  tasks={tasks}
  setTasks={setTasks}
  siteId={siteId}
/>
```

### TaskReviewTab
```tsx
<TaskReviewTab 
  tasks={tasks}
  setTasks={setTasks}
  onAddMore={() => setActiveTab('create')}
/>
```

### WeatherTab
```tsx
<WeatherTab weather={weatherData} />
```

## Styling Guidelines

### Color Palette
- **Primary Blue**: `blue-600`, `blue-700` for actions
- **Success Green**: `green-600`, `green-50` for positive states
- **Warning Orange**: `orange-600`, `orange-50` for urgent items
- **Danger Red**: `red-600`, `red-50` for alerts and high risk
- **Neutral Gray**: Various gray shades for text and backgrounds

### Component States
- **Default**: Clean, minimal styling
- **Hover**: Subtle shadow and scale effects
- **Active**: Color changes and border highlights
- **Disabled**: Reduced opacity and disabled cursor

## Accessibility

### Features Implemented
- **Semantic HTML**: Proper use of headings, buttons, and form elements
- **ARIA Labels**: Screen reader friendly labels
- **Keyboard Navigation**: Tab-friendly navigation
- **Color Contrast**: Sufficient contrast ratios
- **Focus Indicators**: Clear focus states

## Future Enhancements

### Planned Improvements
- **Animation Library**: Add smooth transitions and animations
- **Drag and Drop**: Reorder tasks in review tab
- **Validation**: Enhanced form validation with error states
- **Accessibility**: Full WCAG 2.1 AA compliance
- **Performance**: Optimize for large task lists

### Component Extensions
- **TaskTemplateManager**: Component for managing custom templates
- **LocationPicker**: Map-based location selection
- **PhotoUpload**: Image attachment functionality
- **VoiceInput**: Voice-to-text for descriptions
