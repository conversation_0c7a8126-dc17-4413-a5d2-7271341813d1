import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import {
  ArrowLeft,
  Bell,
  User
} from 'lucide-react';
import { SiteInfo } from '../../types';

interface SiteEngineerLayoutProps {
  children: React.ReactNode;
  title?: string;
  showBackButton?: boolean;
  site: SiteInfo;
}

const SiteEngineerLayout: React.FC<SiteEngineerLayoutProps> = ({ 
  children, 
  title, 
  showBackButton = false, 
  site 
}) => {
  const navigate = useNavigate();
  const { siteId } = useParams<{ siteId: string }>();

  const handleBackClick = () => {
    navigate(`/sites/${siteId}/engineer`);
  };

  // Truncate site name for better header layout
  const truncatedSiteName = site.name.length > 25 ? `${site.name.substring(0, 25)}...` : site.name;

  return (
    <div className="min-h-screen bg-[#f3f2ee]">
      {/* Floating Card Container */}
      <div className="p-2.5 min-w-0">
        <div className="bg-[#fdfdf9] rounded-[10px] h-[calc(100vh-20px)] flex flex-col overflow-hidden min-w-0">
          
          {/* Dynamic Header */}
          <div className="flex items-center justify-between px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200">
            {/* Left Side - Back Button or Site Name */}
            <div className="flex items-center space-x-3 flex-1 min-w-0">
              {showBackButton && (
                <button
                  onClick={handleBackClick}
                  className="p-2 -ml-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <ArrowLeft className="h-5 w-5" />
                </button>
              )}
              
              <div className="flex-1 min-w-0">
                {showBackButton && title ? (
                  <h1 className="text-lg font-semibold text-gray-900 truncate">
                    {title}
                  </h1>
                ) : (
                  <h1 className="text-lg font-semibold text-gray-900 truncate" title={site.name}>
                    {truncatedSiteName}
                  </h1>
                )}
              </div>
            </div>
            
            {/* Right Side - Notifications and Account */}
            <div className="flex items-center space-x-2 sm:space-x-3 flex-shrink-0">
              {/* Notification Bell */}
              <button
                onClick={() => navigate(`/sites/${siteId}/engineer/notifications`)}
                className="relative p-1.5 sm:p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <Bell className="h-4 w-4 sm:h-5 sm:w-5" />
                {/* Mobile: Simple red dot */}
                <span className="absolute -top-0.5 -right-0.5 bg-red-500 rounded-full h-2 w-2 sm:hidden"></span>
                {/* Desktop: Badge with number */}
                <span className="hidden sm:flex absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 items-center justify-center font-medium">
                  3
                </span>
              </button>

              {/* Account Icon */}
              <button
                onClick={() => navigate(`/sites/${siteId}/engineer/account`)}
                className="p-1.5 sm:p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <User className="h-4 w-4 sm:h-5 sm:w-5" />
              </button>
            </div>
          </div>

          {/* Content Area */}
          <div className="flex-1 overflow-auto">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SiteEngineerLayout;
