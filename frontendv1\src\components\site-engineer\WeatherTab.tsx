import React from 'react';
import {
  Sun,
  AlertTriangle,
  Droplets,
  Wind,
  Eye
} from 'lucide-react';

interface WeatherInfo {
  current: {
    temperature: number;
    humidity: number;
    windSpeed: number;
    precipitation: number;
    conditions: string;
  };
  forecast: {
    date: string;
    highTemp: number;
    lowTemp: number;
    precipitationChance: number;
    conditions: string;
    workImpact: 'none' | 'minor' | 'moderate' | 'severe';
  }[];
  alerts: {
    type: string;
    severity: string;
    message: string;
  }[];
}

interface WeatherTabProps {
  weather: WeatherInfo;
}

const WeatherTab: React.FC<WeatherTabProps> = ({ weather }) => {
  // Weather impact color helper
  const getWeatherImpactColor = (impact: string) => {
    switch (impact) {
      case 'none': return 'text-green-600 bg-green-50 border-green-200';
      case 'minor': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'moderate': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'severe': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getWeatherImpactText = (impact: string) => {
    switch (impact) {
      case 'none': return 'Good for work';
      case 'minor': return 'Minor impact';
      case 'moderate': return 'Moderate impact';
      case 'severe': return 'Severe impact';
      default: return 'Unknown impact';
    }
  };

  const getConditionIcon = (conditions: string) => {
    if (conditions.toLowerCase().includes('rain')) {
      return <Droplets className="h-6 w-6 text-blue-500" />;
    } else if (conditions.toLowerCase().includes('cloud')) {
      return <Sun className="h-6 w-6 text-gray-500" />;
    } else {
      return <Sun className="h-6 w-6 text-yellow-500" />;
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);
    const dayAfter = new Date(today);
    dayAfter.setDate(today.getDate() + 2);

    if (date.toDateString() === tomorrow.toDateString()) {
      return 'Tomorrow';
    } else if (date.toDateString() === dayAfter.toDateString()) {
      return 'Day After';
    } else {
      return date.toLocaleDateString('en-US', { 
        weekday: 'short', 
        month: 'short', 
        day: 'numeric' 
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Current Weather */}
      <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Sun className="h-5 w-5 mr-2 text-yellow-500" />
          Current Conditions
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Temperature and Conditions */}
          <div className="text-center">
            <div className="flex items-center justify-center mb-2">
              {getConditionIcon(weather.current.conditions)}
            </div>
            <div className="text-4xl font-bold text-gray-900 mb-1">
              {weather.current.temperature}°C
            </div>
            <div className="text-lg text-gray-600">{weather.current.conditions}</div>
          </div>

          {/* Weather Details */}
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-2">
                <Droplets className="h-4 w-4 text-blue-500" />
                <span className="text-sm text-gray-600">Humidity</span>
              </div>
              <span className="font-medium text-gray-900">{weather.current.humidity}%</span>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-2">
                <Wind className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-600">Wind Speed</span>
              </div>
              <span className="font-medium text-gray-900">{weather.current.windSpeed} km/h</span>
            </div>
            
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-2">
                <Droplets className="h-4 w-4 text-blue-600" />
                <span className="text-sm text-gray-600">Precipitation</span>
              </div>
              <span className="font-medium text-gray-900">{weather.current.precipitation}mm</span>
            </div>
          </div>
        </div>
      </div>

      {/* Weather Alerts */}
      {weather.alerts.length > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-xl p-5">
          <h4 className="text-lg font-semibold text-red-800 mb-3 flex items-center">
            <AlertTriangle className="h-5 w-5 mr-2" />
            Weather Alerts
          </h4>
          <div className="space-y-3">
            {weather.alerts.map((alert, index) => (
              <div key={index} className="bg-white border border-red-200 rounded-lg p-4">
                <div className="flex items-start space-x-3">
                  <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="text-sm font-medium text-red-800 capitalize">
                        {alert.type.replace('-', ' ')} Alert
                      </span>
                      <span className="px-2 py-1 bg-red-100 text-red-700 text-xs font-medium rounded-full capitalize">
                        {alert.severity}
                      </span>
                    </div>
                    <p className="text-sm text-red-700">{alert.message}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Forecast */}
      <div className="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">2-Day Forecast</h3>
        <div className="space-y-4">
          {weather.forecast.map((day, index) => (
            <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
              <div className="flex items-center space-x-4">
                <div className="flex-shrink-0">
                  {getConditionIcon(day.conditions)}
                </div>
                <div>
                  <div className="font-medium text-gray-900">
                    {formatDate(day.date)}
                  </div>
                  <div className="text-sm text-gray-600">{day.conditions}</div>
                  <div className="text-xs text-gray-500 mt-1">
                    {day.precipitationChance}% chance of rain
                  </div>
                </div>
              </div>
              
              <div className="text-right">
                <div className="font-medium text-gray-900 text-lg">
                  {day.highTemp}° / {day.lowTemp}°
                </div>
                <div className="mt-2">
                  <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getWeatherImpactColor(day.workImpact)}`}>
                    {getWeatherImpactText(day.workImpact)}
                  </span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Work Impact Guidelines */}
      <div className="bg-blue-50 border border-blue-200 rounded-xl p-5">
        <h4 className="text-lg font-semibold text-blue-800 mb-3 flex items-center">
          <Eye className="h-5 w-5 mr-2" />
          Work Impact Guidelines
        </h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              <span className="font-medium text-green-800">Good for work:</span>
            </div>
            <p className="text-green-700 ml-5">All outdoor work can proceed normally</p>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <span className="font-medium text-yellow-800">Minor impact:</span>
            </div>
            <p className="text-yellow-700 ml-5">Some work types may be affected, monitor conditions</p>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
              <span className="font-medium text-orange-800">Moderate impact:</span>
            </div>
            <p className="text-orange-700 ml-5">Outdoor work may be limited, consider alternatives</p>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <span className="font-medium text-red-800">Severe impact:</span>
            </div>
            <p className="text-red-700 ml-5">Most outdoor work should be postponed</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WeatherTab;
