import React, { useState, useEffect } from 'react';
import {
  X,
  Calendar,
  Clock,
  MapPin,
  Plus,
} from 'lucide-react';
import { Task, TaskCategory, TaskPriority } from '../../types/tasks';
// import { Worker } from '../../types';

interface TaskCreationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (taskData: Partial<Task>) => void;
  siteId: string;
  initialData?: Partial<Task>;
}

interface TaskFormData {
  title: string;
  description: string;
  category: TaskCategory;
  priority: TaskPriority;
  location: string;
  plannedStartDate: string;
  plannedEndDate: string;
  estimatedDuration: number;
  assignedSupervisor: string;
  assignedWorkers: string[];
  requiresPermit: boolean;
  permitTypes: string[];
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  safetyRequirements: string[];
  requiredPPE: string[];
  requiredTrainings: string[];
  requiredCertifications: string[];
  tags: string[];
}

// Mock data - replace with actual API calls
const mockSupervisors = [
  { id: 'supervisor-1', name: '<PERSON>', trade: 'General Construction' },
  { id: 'supervisor-2', name: '<PERSON>', trade: 'Electrical' },
  { id: 'supervisor-3', name: '<PERSON>', trade: 'Plumbing' }
];

const mockWorkers = [
  { id: 'worker-1', name: 'David Kamau', trade: 'Carpenter', photo: 'https://randomuser.me/api/portraits/men/1.jpg' },
  { id: 'worker-2', name: 'Mary Wanjiku', trade: 'Electrician', photo: 'https://randomuser.me/api/portraits/women/2.jpg' },
  { id: 'worker-3', name: 'Peter Ochieng', trade: 'Plumber', photo: 'https://randomuser.me/api/portraits/men/3.jpg' },
  { id: 'worker-4', name: 'Grace Muthoni', trade: 'Painter', photo: 'https://randomuser.me/api/portraits/women/4.jpg' }
];

const permitTypes = [
  'Hot Work Permit',
  'Confined Space Entry',
  'Working at Height',
  'Electrical Work',
  'Excavation',
  'Chemical Handling'
];

const ppeOptions = [
  'Hard Hat',
  'Safety Glasses',
  'High-Vis Vest',
  'Steel-Toe Boots',
  'Gloves',
  'Respirator',
  'Fall Protection Harness',
  'Hearing Protection'
];

const trainingOptions = [
  'Site Safety Induction',
  'Working at Height',
  'Confined Space Entry',
  'Hot Work Safety',
  'First Aid',
  'Fire Safety',
  'Manual Handling',
  'COSHH Awareness'
];

const TaskCreationModal: React.FC<TaskCreationModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  siteId,
  initialData
}) => {
  const [formData, setFormData] = useState<TaskFormData>({
    title: '',
    description: '',
    category: 'construction',
    priority: 'medium',
    location: '',
    plannedStartDate: '',
    plannedEndDate: '',
    estimatedDuration: 8,
    assignedSupervisor: '',
    assignedWorkers: [],
    requiresPermit: false,
    permitTypes: [],
    riskLevel: 'low',
    safetyRequirements: [],
    requiredPPE: [],
    requiredTrainings: [],
    requiredCertifications: [],
    tags: []
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const [newTag, setNewTag] = useState('');

  useEffect(() => {
    if (initialData) {
      setFormData(prev => ({
        ...prev,
        ...initialData,
        plannedStartDate: initialData.plannedStartDate?.toISOString().split('T')[0] || '',
        plannedEndDate: initialData.plannedEndDate?.toISOString().split('T')[0] || '',
        assignedWorkers: initialData.assignedWorkers?.map(w => w.workerId) || [],
        permitTypes: initialData.permitTypes || [],
        tags: initialData.tags || []
      }));
    }
  }, [initialData]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Task title is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Task description is required';
    }

    if (!formData.location.trim()) {
      newErrors.location = 'Location is required';
    }

    if (!formData.plannedStartDate) {
      newErrors.plannedStartDate = 'Start date is required';
    }

    if (!formData.plannedEndDate) {
      newErrors.plannedEndDate = 'End date is required';
    }

    if (formData.plannedStartDate && formData.plannedEndDate) {
      const startDate = new Date(formData.plannedStartDate);
      const endDate = new Date(formData.plannedEndDate);
      if (endDate <= startDate) {
        newErrors.plannedEndDate = 'End date must be after start date';
      }
    }

    if (formData.estimatedDuration <= 0) {
      newErrors.estimatedDuration = 'Duration must be greater than 0';
    }

    if (!formData.assignedSupervisor) {
      newErrors.assignedSupervisor = 'Supervisor assignment is required';
    }

    if (formData.requiresPermit && formData.permitTypes.length === 0) {
      newErrors.permitTypes = 'At least one permit type is required when permits are needed';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Convert form data to Task format
      const taskData: Partial<Task> = {
        title: formData.title,
        description: formData.description,
        category: formData.category,
        priority: formData.priority,
        location: formData.location,
        siteId,
        plannedStartDate: new Date(formData.plannedStartDate),
        plannedEndDate: new Date(formData.plannedEndDate),
        estimatedDuration: formData.estimatedDuration,
        assignedSupervisor: formData.assignedSupervisor,
        assignedSupervisorName: mockSupervisors.find(s => s.id === formData.assignedSupervisor)?.name || '',
        assignedWorkers: formData.assignedWorkers.map(workerId => {
          const worker = mockWorkers.find(w => w.id === workerId);
          return {
            workerId,
            workerName: worker?.name || '',
            workerPhoto: worker?.photo,
            primaryTrade: worker?.trade || '',
            role: 'worker' as const,
            assignedAt: new Date(),
            hasRequiredTraining: true,
            hasRequiredCertifications: true,
            estimatedHours: formData.estimatedDuration
          };
        }),
        requiresPermit: formData.requiresPermit,
        permitTypes: formData.permitTypes,
        permitStatus: formData.requiresPermit ? 'not-generated' : 'not-required',
        riskLevel: formData.riskLevel,
        safetyRequirements: formData.safetyRequirements,
        requiredPPE: formData.requiredPPE,
        requiredTrainings: formData.requiredTrainings,
        requiredCertifications: formData.requiredCertifications,
        tags: formData.tags,
        status: 'todo',
        progressPercentage: 0,
        dependencies: [],
        ramsDocuments: [],
        attachments: [],
        qualityChecks: [],
        complianceRequirements: [],
        history: [],
        customFields: {}
      };

      await onSubmit(taskData);
      onClose();
    } catch (error) {
      console.error('Error creating task:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof TaskFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }));
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const toggleArrayItem = (array: string[], item: string, field: keyof TaskFormData) => {
    const newArray = array.includes(item)
      ? array.filter(i => i !== item)
      : [...array, item];
    
    handleInputChange(field, newArray);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {initialData ? 'Edit Task' : 'Create New Task'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Progress Steps */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            {[1, 2, 3].map((step) => (
              <div key={step} className="flex items-center">
                <div
                  className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    currentStep >= step
                      ? 'bg-green-600 text-white'
                      : 'bg-gray-200 text-gray-600'
                  }`}
                >
                  {step}
                </div>
                <span className={`ml-2 text-sm ${
                  currentStep >= step ? 'text-green-600' : 'text-gray-500'
                }`}>
                  {step === 1 ? 'Basic Info' : step === 2 ? 'Assignment' : 'Safety & Permits'}
                </span>
                {step < 3 && <div className="w-8 h-px bg-gray-300 ml-4" />}
              </div>
            ))}
          </div>
        </div>

        <form onSubmit={handleSubmit} className="flex flex-col h-full">
          <div className="flex-1 overflow-y-auto p-6">
            {/* Step 1: Basic Information */}
            {currentStep === 1 && (
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Task Title *
                    </label>
                    <input
                      type="text"
                      value={formData.title}
                      onChange={(e) => handleInputChange('title', e.target.value)}
                      className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                        errors.title ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="Enter task title"
                    />
                    {errors.title && (
                      <p className="mt-1 text-sm text-red-600">{errors.title}</p>
                    )}
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Description *
                    </label>
                    <textarea
                      value={formData.description}
                      onChange={(e) => handleInputChange('description', e.target.value)}
                      rows={4}
                      className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                        errors.description ? 'border-red-500' : 'border-gray-300'
                      }`}
                      placeholder="Describe the task in detail"
                    />
                    {errors.description && (
                      <p className="mt-1 text-sm text-red-600">{errors.description}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Category *
                    </label>
                    <select
                      value={formData.category}
                      onChange={(e) => handleInputChange('category', e.target.value as TaskCategory)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
                    >
                      <option value="construction">Construction</option>
                      <option value="electrical">Electrical</option>
                      <option value="plumbing">Plumbing</option>
                      <option value="hvac">HVAC</option>
                      <option value="safety">Safety</option>
                      <option value="inspection">Inspection</option>
                      <option value="maintenance">Maintenance</option>
                      <option value="other">Other</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Priority *
                    </label>
                    <select
                      value={formData.priority}
                      onChange={(e) => handleInputChange('priority', e.target.value as TaskPriority)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                      <option value="critical">Critical</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Location *
                    </label>
                    <div className="relative">
                      <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <input
                        type="text"
                        value={formData.location}
                        onChange={(e) => handleInputChange('location', e.target.value)}
                        className={`w-full pl-10 pr-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                          errors.location ? 'border-red-500' : 'border-gray-300'
                        }`}
                        placeholder="e.g., Zone A - Level 2"
                      />
                    </div>
                    {errors.location && (
                      <p className="mt-1 text-sm text-red-600">{errors.location}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Estimated Duration (hours) *
                    </label>
                    <div className="relative">
                      <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <input
                        type="number"
                        min="0.5"
                        step="0.5"
                        value={formData.estimatedDuration}
                        onChange={(e) => handleInputChange('estimatedDuration', parseFloat(e.target.value))}
                        className={`w-full pl-10 pr-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                          errors.estimatedDuration ? 'border-red-500' : 'border-gray-300'
                        }`}
                      />
                    </div>
                    {errors.estimatedDuration && (
                      <p className="mt-1 text-sm text-red-600">{errors.estimatedDuration}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Planned Start Date *
                    </label>
                    <div className="relative">
                      <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <input
                        type="date"
                        value={formData.plannedStartDate}
                        onChange={(e) => handleInputChange('plannedStartDate', e.target.value)}
                        className={`w-full pl-10 pr-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                          errors.plannedStartDate ? 'border-red-500' : 'border-gray-300'
                        }`}
                      />
                    </div>
                    {errors.plannedStartDate && (
                      <p className="mt-1 text-sm text-red-600">{errors.plannedStartDate}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Planned End Date *
                    </label>
                    <div className="relative">
                      <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <input
                        type="date"
                        value={formData.plannedEndDate}
                        onChange={(e) => handleInputChange('plannedEndDate', e.target.value)}
                        className={`w-full pl-10 pr-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                          errors.plannedEndDate ? 'border-red-500' : 'border-gray-300'
                        }`}
                      />
                    </div>
                    {errors.plannedEndDate && (
                      <p className="mt-1 text-sm text-red-600">{errors.plannedEndDate}</p>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Step 2: Assignment */}
            {currentStep === 2 && (
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Assigned Supervisor *
                  </label>
                  <select
                    value={formData.assignedSupervisor}
                    onChange={(e) => handleInputChange('assignedSupervisor', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                      errors.assignedSupervisor ? 'border-red-500' : 'border-gray-300'
                    }`}
                  >
                    <option value="">Select a supervisor</option>
                    {mockSupervisors.map((supervisor) => (
                      <option key={supervisor.id} value={supervisor.id}>
                        {supervisor.name} - {supervisor.trade}
                      </option>
                    ))}
                  </select>
                  {errors.assignedSupervisor && (
                    <p className="mt-1 text-sm text-red-600">{errors.assignedSupervisor}</p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Assigned Workers
                  </label>
                  <div className="border border-gray-300 rounded-md p-4 max-h-64 overflow-y-auto">
                    {mockWorkers.map((worker) => (
                      <div key={worker.id} className="flex items-center space-x-3 py-2">
                        <input
                          type="checkbox"
                          id={`worker-${worker.id}`}
                          checked={formData.assignedWorkers.includes(worker.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              handleInputChange('assignedWorkers', [...formData.assignedWorkers, worker.id]);
                            } else {
                              handleInputChange('assignedWorkers', formData.assignedWorkers.filter(id => id !== worker.id));
                            }
                          }}
                          className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                        />
                        <img
                          src={worker.photo}
                          alt={worker.name}
                          className="h-8 w-8 rounded-full object-cover"
                        />
                        <div className="flex-1">
                          <label htmlFor={`worker-${worker.id}`} className="text-sm font-medium text-gray-900 cursor-pointer">
                            {worker.name}
                          </label>
                          <p className="text-xs text-gray-500">{worker.trade}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                  <p className="mt-1 text-sm text-gray-500">
                    {formData.assignedWorkers.length} worker(s) selected
                  </p>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Tags
                  </label>
                  <div className="flex flex-wrap gap-2 mb-2">
                    {formData.tags.map((tag) => (
                      <span
                        key={tag}
                        className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
                      >
                        {tag}
                        <button
                          type="button"
                          onClick={() => removeTag(tag)}
                          className="ml-1 text-green-600 hover:text-green-800"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      value={newTag}
                      onChange={(e) => setNewTag(e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                      className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
                      placeholder="Add a tag"
                    />
                    <button
                      type="button"
                      onClick={addTag}
                      className="px-3 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
                    >
                      <Plus className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Step 3: Safety & Permits */}
            {currentStep === 3 && (
              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Risk Level *
                  </label>
                  <select
                    value={formData.riskLevel}
                    onChange={(e) => handleInputChange('riskLevel', e.target.value as 'low' | 'medium' | 'high' | 'critical')}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  >
                    <option value="low">Low Risk</option>
                    <option value="medium">Medium Risk</option>
                    <option value="high">High Risk</option>
                    <option value="critical">Critical Risk</option>
                  </select>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="requiresPermit"
                    checked={formData.requiresPermit}
                    onChange={(e) => handleInputChange('requiresPermit', e.target.checked)}
                    className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                  />
                  <label htmlFor="requiresPermit" className="text-sm font-medium text-gray-700">
                    This task requires work permits
                  </label>
                </div>

                {formData.requiresPermit && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Required Permit Types *
                    </label>
                    <div className="border border-gray-300 rounded-md p-4 max-h-48 overflow-y-auto">
                      {permitTypes.map((permitType) => (
                        <div key={permitType} className="flex items-center space-x-3 py-2">
                          <input
                            type="checkbox"
                            id={`permit-${permitType}`}
                            checked={formData.permitTypes.includes(permitType)}
                            onChange={() => toggleArrayItem(formData.permitTypes, permitType, 'permitTypes')}
                            className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                          />
                          <label htmlFor={`permit-${permitType}`} className="text-sm text-gray-900 cursor-pointer">
                            {permitType}
                          </label>
                        </div>
                      ))}
                    </div>
                    {errors.permitTypes && (
                      <p className="mt-1 text-sm text-red-600">{errors.permitTypes}</p>
                    )}
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Required PPE
                  </label>
                  <div className="border border-gray-300 rounded-md p-4 max-h-48 overflow-y-auto">
                    {ppeOptions.map((ppe) => (
                      <div key={ppe} className="flex items-center space-x-3 py-2">
                        <input
                          type="checkbox"
                          id={`ppe-${ppe}`}
                          checked={formData.requiredPPE.includes(ppe)}
                          onChange={() => toggleArrayItem(formData.requiredPPE, ppe, 'requiredPPE')}
                          className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                        />
                        <label htmlFor={`ppe-${ppe}`} className="text-sm text-gray-900 cursor-pointer">
                          {ppe}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Required Training
                  </label>
                  <div className="border border-gray-300 rounded-md p-4 max-h-48 overflow-y-auto">
                    {trainingOptions.map((training) => (
                      <div key={training} className="flex items-center space-x-3 py-2">
                        <input
                          type="checkbox"
                          id={`training-${training}`}
                          checked={formData.requiredTrainings.includes(training)}
                          onChange={() => toggleArrayItem(formData.requiredTrainings, training, 'requiredTrainings')}
                          className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                        />
                        <label htmlFor={`training-${training}`} className="text-sm text-gray-900 cursor-pointer">
                          {training}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Safety Requirements
                  </label>
                  <textarea
                    value={formData.safetyRequirements.join('\n')}
                    onChange={(e) => handleInputChange('safetyRequirements', e.target.value.split('\n').filter(req => req.trim()))}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
                    placeholder="Enter safety requirements (one per line)"
                  />
                  <p className="mt-1 text-sm text-gray-500">
                    Enter each safety requirement on a new line
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Footer */}
          <div className="flex justify-between items-center p-6 border-t border-gray-200">
            <div className="flex space-x-3">
              {currentStep > 1 && (
                <button
                  type="button"
                  onClick={() => setCurrentStep(prev => prev - 1)}
                  className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                >
                  Previous
                </button>
              )}
            </div>

            <div className="flex space-x-3">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                Cancel
              </button>
              
              {currentStep < 3 ? (
                <button
                  type="button"
                  onClick={() => setCurrentStep(prev => prev + 1)}
                  className="px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700"
                >
                  Next
                </button>
              ) : (
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? 'Creating...' : 'Create Task'}
                </button>
              )}
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

export default TaskCreationModal;
