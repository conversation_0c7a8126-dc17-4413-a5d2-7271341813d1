import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  CheckCircle,
  XCircle,
  MapPin,
  Clock,
  History,
  Download,
  Calendar
} from 'lucide-react';
import { mockJobs, type MockJob, type JobStatus } from '../../data/mockJobs';
import TaskStatusBadge from './shared/TaskStatusBadge';
import UniversalFilter, { TagOption } from '../shared/UniversalFilter';
import Pagination from '../shared/Pagination';

interface TaskHistoryListProps {
  siteId: string;
}

const TaskHistoryList: React.FC<TaskHistoryListProps> = ({ siteId }) => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [dateRange, setDateRange] = useState({
    start: '',
    end: ''
  });
  const itemsPerPage = 10;

  useEffect(() => {
    // Simulate loading
    setLoading(true);
    setTimeout(() => setLoading(false), 500);
  }, [siteId]);

  // Status filters for historical jobs (all statuses for history view)
  const statusFilters: TagOption[] = [
    { id: 'FINISHED', name: 'Finished' },
    { id: 'DISAPPROVED', name: 'Disapproved' },
    { id: 'BLOCKED', name: 'Blocked' },
    { id: 'REQUESTED', name: 'Requested' },
    { id: 'PENDING_APPROVAL', name: 'Pending Approval' },
    { id: 'APPROVED', name: 'Approved' }
  ];

  // Get status icon for job status
  const getStatusIcon = (status: JobStatus) => {
    switch (status) {
      case 'FINISHED':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'DISAPPROVED':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'BLOCKED':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'APPROVED':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'PENDING_APPROVAL':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'REQUESTED':
        return <Clock className="h-4 w-4 text-blue-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  // Filtered jobs using useMemo for performance
  const filteredJobsAll = useMemo(() => {
    return mockJobs.filter((job) => {
      // Search filter
      const matchesSearch = searchQuery === "" ||
        job.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (job.description && job.description.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (job.location && job.location.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (job.requestedBy?.name && job.requestedBy.name.toLowerCase().includes(searchQuery.toLowerCase()));

      // Status filter
      const matchesStatus = selectedStatus === "all" ||
        job.status === selectedStatus;

      // Date range filter
      const matchesDateRange = (!dateRange.start || new Date(job.createdAt) >= new Date(dateRange.start)) &&
                              (!dateRange.end || new Date(job.createdAt) <= new Date(dateRange.end));

      return matchesSearch && matchesStatus && matchesDateRange;
    }).sort((a, b) => new Date(b.updatedAt || b.createdAt).getTime() - new Date(a.updatedAt || a.createdAt).getTime()); // Sort by most recent first
  }, [searchQuery, selectedStatus, dateRange]);

  // Paginated results
  const filteredJobs = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredJobsAll.slice(startIndex, endIndex);
  }, [filteredJobsAll, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(filteredJobsAll.length / itemsPerPage);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleJobClick = (jobId: number) => {
    navigate(`/sites/${siteId}/tasks/${jobId}`);
  };

  const handleDownloadReport = (job: MockJob) => {
    // Mock download functionality - replace with actual implementation
    console.log('Downloading report for job:', job.id);
    // In a real implementation, this would trigger a PDF download or report generation
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading job history...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Job History</h2>
          <p className="text-sm text-gray-600">Historical job records and completed activities</p>
        </div>
        <div className="flex items-center space-x-4">
          <div className="text-sm text-gray-600">
            Total {filteredJobsAll.length} jobs
          </div>
          <div className="flex items-center space-x-2">
            <Calendar className="h-4 w-4 text-gray-400" />
            <input
              type="date"
              value={dateRange.start}
              onChange={(e) => setDateRange({ ...dateRange, start: e.target.value })}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Start date"
            />
            <span className="text-gray-400">to</span>
            <input
              type="date"
              value={dateRange.end}
              onChange={(e) => setDateRange({ ...dateRange, end: e.target.value })}
              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="End date"
            />
          </div>
        </div>
      </div>

      {/* Search and Filter */}
      <UniversalFilter
        variant="search-tags"
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        searchPlaceholder="Search by job title, location, description, or requester..."
        tags={[{ id: "all", name: "All" }, ...statusFilters]}
        selectedTagId={selectedStatus}
        onTagChange={setSelectedStatus}
      />

      {/* History Jobs Table */}
      <div className="bg-white rounded-lg border-2 border-gray-200 shadow-sm overflow-hidden" style={{ borderRadius: '5px' }}>
        {filteredJobs.length === 0 ? (
          <div className="p-12 text-center">
            <History className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No job history found</h3>
            <p className="text-gray-500">
              {searchQuery || selectedStatus !== 'all' || dateRange.start || dateRange.end
                ? 'Try adjusting your search criteria or date range.'
                : 'No jobs have been completed yet.'
              }
            </p>
          </div>
        ) : (
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Job Details
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Required Permits
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Timeline
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Persons
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredJobs.map((job) => (
                <tr key={job.id} className="hover:bg-gray-50 transition-colors">
                  {/* Job Details */}
                  <td className="px-6 py-4">
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        {getStatusIcon(job.status)}
                        <h4 className="text-sm font-medium text-gray-900">
                          {job.title}
                        </h4>
                      </div>
                      <p className="text-xs text-gray-600 mb-1 line-clamp-2">
                        {job.description || 'No description provided'}
                      </p>
                      <div className="flex items-center text-xs text-gray-500">
                        <MapPin className="h-3 w-3 mr-1" />
                        {job.location || 'Location TBD'}
                      </div>
                    </div>
                  </td>

                  {/* Required Permits */}
                  <td className="px-6 py-4">
                    <div className="flex flex-wrap gap-1">
                      {job.requiredPermits?.map((permit, index) => (
                        <span key={index} className={`px-2 py-1 rounded text-xs ${
                          permit === 'GENERAL_WORK_PERMIT' ? 'bg-orange-100 text-orange-700' :
                          permit === 'HOT_WORK_PERMIT' ? 'bg-red-100 text-red-700' :
                          permit === 'EXCAVATION_PERMIT' ? 'bg-blue-100 text-blue-700' :
                          permit === 'WORK_AT_HEIGHT_PERMIT' ? 'bg-purple-100 text-purple-700' :
                          permit === 'CONFINED_SPACE_ENTRY_PERMIT' ? 'bg-yellow-100 text-yellow-700' :
                          'bg-gray-100 text-gray-700'
                        }`}>
                          {permit.replace('_', ' ').replace('PERMIT', '').trim()}
                        </span>
                      )) || (
                        <span className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">
                          No permits required
                        </span>
                      )}
                    </div>
                  </td>

                  {/* Timeline */}
                  <td className="px-6 py-4">
                    <div>
                      <div className="text-sm text-gray-900 mb-1">
                        Created: {new Date(job.createdAt).toLocaleDateString('en-US', {
                          month: 'short',
                          day: 'numeric',
                          year: 'numeric'
                        })}
                      </div>
                      {job.finishedDate && (
                        <div className="text-xs text-gray-500">
                          Finished: {new Date(job.finishedDate).toLocaleDateString('en-US', {
                            month: 'short',
                            day: 'numeric'
                          })}
                        </div>
                      )}
                      <div className="text-xs text-gray-500">
                        Duration: {job.timeForCompletion || 'TBD'}
                      </div>
                    </div>
                  </td>

                  {/* Persons */}
                  <td className="px-6 py-4">
                    <div className="text-xs">
                      {job.requestedBy && (
                        <div className="text-gray-900 mb-1">
                          Requester: {job.requestedBy.name}
                        </div>
                      )}
                      {job.reviewedBy && (
                        <div className="text-gray-900 mb-1">
                          Reviewer: {job.reviewedBy.name}
                        </div>
                      )}
                      {job.approvedBy && (
                        <div className="text-gray-900 mb-1">
                          Approver: {job.approvedBy.name}
                        </div>
                      )}
                      {job.finishedBy && (
                        <div className="text-gray-900">
                          Finisher: {job.finishedBy.name}
                        </div>
                      )}
                    </div>
                  </td>

                  {/* Status */}
                  <td className="px-6 py-4">
                    <TaskStatusBadge status={job.status.toLowerCase().replace('_', '-') as any} size="sm" />
                  </td>

                  {/* Actions */}
                  <td className="px-6 py-4 text-right text-sm font-medium">
                    <div className="flex justify-end space-x-2">
                      <button
                        onClick={() => handleJobClick(job.id)}
                        className="px-3 py-1 text-xs border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors"
                        style={{ borderRadius: '5px' }}
                        title="View Details"
                      >
                        View
                      </button>
                      <button
                        onClick={() => handleDownloadReport(job)}
                        className="px-3 py-1 text-xs border border-gray-300 text-gray-700 rounded hover:bg-gray-50 transition-colors"
                        style={{ borderRadius: '5px' }}
                        title="Download Report"
                      >
                        <Download className="h-3 w-3" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        )}

        {/* Pagination */}
        {filteredJobsAll.length > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={filteredJobsAll.length}
            itemsPerPage={itemsPerPage}
            onPageChange={handlePageChange}
          />
        )}
      </div>
    </div>
  );
};

export default TaskHistoryList;
