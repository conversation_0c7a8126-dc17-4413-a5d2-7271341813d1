import React, { useState, useEffect } from "react";
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@apollo/client';
import { toast } from 'react-toastify';
import {
  ClipboardList,
  Clock,
  CheckCircle,
  Plus,
  ClipboardCheck,
  History,
  MapPin,
  ChevronLeft,
  ChevronRight
} from 'lucide-react';

import { TaskStats } from '../../types/tasks';
import TaskStatusBadge from './shared/TaskStatusBadge';
import QuickActions, { QuickActionItem } from '../shared/QuickActions';
import { mockJobStats, getRecentJobs } from '../../data/mockJobs';
import { GET_ALL_JOBS } from '../../graphql/queries';

interface TasksDashboardProps {
  siteId: string;
  onNavigateToTab: (tabId: string) => void;
}



// Mock data using centralized job data
const mockTaskStats: TaskStats = {
	totalTasks: mockJobStats.totalJobs,
	todoTasks: mockJobStats.requestedJobs,
	permitPendingTasks: mockJobStats.pendingApprovalJobs,
	permitApprovedTasks: mockJobStats.approvedJobs,
	inProgressTasks: 0, // No direct equivalent in Job schema
	blockedTasks: mockJobStats.blockedJobs,
	completedTasks: mockJobStats.finishedJobs,
	cancelledTasks: mockJobStats.disapprovedJobs,
	overdueTasks: 0, // Would need calculation based on dates
	tasksCompletedToday: mockJobStats.jobsCompletedToday,
	averageCompletionTime: mockJobStats.averageCompletionTime,
	onTimeCompletionRate: mockJobStats.onTimeCompletionRate,
	productivityScore: mockJobStats.productivityScore,
};



const TasksDashboard: React.FC<TasksDashboardProps> = ({ siteId, onNavigateToTab }) => {
  const navigate = useNavigate();
  const [stats] = useState<TaskStats>(mockTaskStats);
  const [currentPage, setCurrentPage] = useState(0);
  const jobsPerPage = 6;

  // Fetch all jobs using GraphQL
  const { data, loading, error } = useQuery(GET_ALL_JOBS, {
    onError: (error) => {
      console.error('Error fetching jobs:', error);
      toast.error('Failed to load recent tasks. Please try again.');
    }
  });

  const allJobs = data?.allJobs || [];

  // Sort jobs by creation date (most recent first) and paginate
  const sortedJobs = [...allJobs].sort((a, b) =>
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );

  const totalPages = Math.ceil(sortedJobs.length / jobsPerPage);
  const startIndex = currentPage * jobsPerPage;
  const endIndex = startIndex + jobsPerPage;
  const currentJobs = sortedJobs.slice(startIndex, endIndex);

  const handlePreviousPage = () => {
    setCurrentPage(prev => Math.max(0, prev - 1));
  };

  const handleNextPage = () => {
    setCurrentPage(prev => Math.min(totalPages - 1, prev + 1));
  };

  useEffect(() => {
    // Reset to first page when jobs data changes
    setCurrentPage(0);
  }, [allJobs.length]);





  return (
    <div className="space-y-6">
      {/* Task Workflow Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm font-medium text-gray-500">Total Tasks</p>
              <p className="mt-2 text-2xl font-semibold text-gray-900">{stats.totalTasks}</p>
            </div>
            <div className="text-blue-600">
              <ClipboardList className="h-6 w-6" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm font-medium text-gray-500">Requested</p>
              <p className="mt-2 text-2xl font-semibold text-gray-900">{stats.permitPendingTasks}</p>
            </div>
            <div className="text-yellow-500">
              <Clock className="h-6 w-6" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm font-medium text-gray-500">Approved</p>
              <p className="mt-2 text-2xl font-semibold text-gray-900">{stats.permitApprovedTasks}</p>
            </div>
            <div className="text-green-600">
              <CheckCircle className="h-6 w-6" />
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
          <div className="flex justify-between items-start">
            <div>
              <p className="text-sm font-medium text-gray-500">In Progress</p>
              <p className="mt-2 text-2xl font-semibold text-gray-900">{stats.inProgressTasks}</p>
            </div>
            <div className="text-blue-500">
              <Clock className="h-6 w-6" />
            </div>
          </div>
        </div>
      </div>



      {/* Quick Actions */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <QuickActions
          actions={[
            {
              title: "New Task",
              description: "Create a new task",
              icon: <Plus className="h-6 w-6 text-indigo-600" />,
              onClick: () => navigate(`/sites/${siteId}/tasks/new`),
            },
            {
              title: "Review Tasks",
              description: "Review requested tasks",
              icon: <ClipboardCheck className="h-6 w-6 text-orange-600" />,
              onClick: () => navigate(`/sites/${siteId}/tasks/review`),
            },
            {
              title: "Active Tasks",
              description: "View active tasks",
              icon: <ClipboardList className="h-6 w-6 text-blue-600" />,
              onClick: () => navigate(`/sites/${siteId}/tasks/active`),
            },
            {
              title: "Task History",
              description: "View completed and archived tasks",
              icon: <History className="h-6 w-6 text-purple-600" />,
              onClick: () => navigate(`/sites/${siteId}/tasks/history`),
            },
          ] as QuickActionItem[]}
          className=""
        />
      </div>

      {/* Recent Tasks - unified list modeled after Recent Permits */}
      <div className="bg-white rounded-lg border-2 border-gray-200 shadow-sm overflow-hidden" style={{ borderRadius: '5px' }}>
        <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Recent Tasks</h3>
            <p className="text-sm text-gray-500 mt-1">
              Showing {startIndex + 1}-{Math.min(endIndex, sortedJobs.length)} of {sortedJobs.length} tasks
            </p>
          </div>
          <div className="flex items-center space-x-3">
            {/* Pagination Controls */}
            <div className="flex items-center space-x-1">
              <button
                onClick={handlePreviousPage}
                disabled={currentPage === 0}
                className="p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                title="Previous page"
              >
                <ChevronLeft className="h-4 w-4" />
              </button>
              <span className="text-sm text-gray-600 px-2">
                {totalPages > 0 ? currentPage + 1 : 0} of {totalPages}
              </span>
              <button
                onClick={handleNextPage}
                disabled={currentPage >= totalPages - 1}
                className="p-1 rounded hover:bg-gray-100 disabled:opacity-50 disabled:cursor-not-allowed"
                title="Next page"
              >
                <ChevronRight className="h-4 w-4" />
              </button>
            </div>
            <button
              onClick={() => onNavigateToTab("active")}
              className="text-sm text-green-600 hover:text-green-800"
            >
              View All
            </button>
          </div>
        </div>
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Job Details</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Required Permits</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Persons</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {loading ? (
              // Loading state
              Array.from({ length: jobsPerPage }).map((_, index) => (
                <tr key={index} className="animate-pulse">
                  <td className="px-6 py-4">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="h-3 bg-gray-200 rounded w-1/3"></div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="h-6 bg-gray-200 rounded w-16"></div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="h-3 bg-gray-200 rounded w-1/4"></div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="h-8 bg-gray-200 rounded w-20"></div>
                  </td>
                </tr>
              ))
            ) : error ? (
              // Error state
              <tr>
                <td colSpan={5} className="px-6 py-8 text-center">
                  <div className="text-red-500 mb-2">Failed to load tasks</div>
                  <button
                    onClick={() => window.location.reload()}
                    className="text-sm text-blue-600 hover:text-blue-800"
                  >
                    Try again
                  </button>
                </td>
              </tr>
            ) : currentJobs.length === 0 ? (
              // Empty state
              <tr>
                <td colSpan={5} className="px-6 py-8 text-center text-gray-500">
                  No tasks found
                </td>
              </tr>
            ) : (
              // Data rows
              currentJobs.map((job: any) => (
              <tr key={job.id} className="hover:bg-gray-50 transition-colors">
                {/* Job Details */}
                <td className="px-6 py-4">
                  <div>
                    <h4 className="text-sm font-medium text-gray-900 mb-1">{job.title}</h4>
                    <p className="text-xs text-gray-600 mb-1 line-clamp-2">{job.description || 'No description provided'}</p>
                    <div className="flex items-center text-xs text-gray-500">
                      <MapPin className="h-3 w-3 mr-1" />
                      {job.location || 'Location TBD'}
                    </div>
                  </div>
                </td>

                {/* Required Permits */}
                <td className="px-6 py-4 align-top">
                  <div className="flex flex-wrap gap-1">
                    {job.permits && job.permits.length > 0 ? (
                      job.permits.map((permit: any, index: number) => (
                        <span key={index} className={`px-2 py-1 rounded text-xs ${
                          permit.type === 'GENERAL_WORK_PERMIT' ? 'bg-orange-100 text-orange-700' :
                          permit.type === 'HOT_WORK_PERMIT' ? 'bg-red-100 text-red-700' :
                          permit.type === 'EXCAVATION_PERMIT' ? 'bg-blue-100 text-blue-700' :
                          permit.type === 'WORK_AT_HEIGHT_PERMIT' ? 'bg-purple-100 text-purple-700' :
                          permit.type === 'CONFINED_SPACE_ENTRY_PERMIT' ? 'bg-yellow-100 text-yellow-700' :
                          'bg-gray-100 text-gray-700'
                        }`}>
                          {permit.type?.replace('_', ' ').replace('PERMIT', '').trim() || 'Unknown'}
                        </span>
                      ))
                    ) : (
                      <span className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">
                        No permits required
                      </span>
                    )}
                  </div>
                </td>

                {/* Status */}
                <td className="px-6 py-4 align-top">
                  <div className="space-y-1">
                    <TaskStatusBadge status={job.status.toLowerCase().replace('_', '-') as any} size="sm" />
                    <div className="text-[11px] text-gray-500">
                      Request Date: {new Date(job.createdAt).toLocaleDateString()}
                    </div>
                  </div>
                </td>

                {/* Persons */}
                <td className="px-6 py-4 align-top">
                  <div className="text-xs">
                    <div className="text-gray-900 mb-1">
                      Chief Engineer: {job.chiefEngineer?.name || '—'}
                    </div>
                    <div className="text-gray-900 mb-1">
                      Reviewer: {job.reviewedBy?.name || '—'}
                    </div>
                    <div className="text-gray-900">
                      Approver: {job.approvedBy?.name || '—'}
                    </div>
                  </div>
                </td>

                {/* Actions */}
                <td className="px-6 py-4 align-top">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => navigate(`/sites/${siteId}/tasks/${job.id}`)}
                      className="px-3 py-1 text-xs border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors"
                      style={{ borderRadius: '5px' }}
                    >
                      View
                    </button>
                  </div>
                </td>
              </tr>
            ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default TasksDashboard;
