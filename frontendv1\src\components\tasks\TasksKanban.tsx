import React, { useState, useEffect } from "react";
import {
	Plus,
	Search,
	Filter,
	Clock,
	Users,
	MapPin,
	Shield,
} from "lucide-react";
import { Task, KanbanColumn, TaskStatus } from "../../types/tasks";

interface TasksKanbanProps {
	siteId: string;
}

// Mock data - replace with actual API calls
const mockTasks: Task[] = [
	{
		id: "task-1",
		taskNumber: "TSK-2024-001",
		title: "Concrete Pouring - Foundation",
		description: "Pour concrete for foundation at Level 1",
		category: "construction",
		location: "Zone A - Level 1",
		siteId: "site-1",
		plannedStartDate: new Date("2024-01-15T08:00:00"),
		plannedEndDate: new Date("2024-01-15T16:00:00"),
		estimatedDuration: 8,
		status: "in-progress",
		priority: "high",
		progressPercentage: 65,
		createdBy: "supervisor-1",
		createdByName: "<PERSON>",
		assignedSupervisor: "supervisor-1",
		assignedSupervisorName: "<PERSON>",
		assignedWorkers: [],
		dependencies: [],
		requiresPermit: false,
		riskLevel: "medium",
		safetyRequirements: [],
		requiredPPE: [],
		requiredTrainings: [],
		requiredCertifications: [],
		ramsDocuments: [],
		attachments: [],
		qualityChecks: [],
		complianceRequirements: [],
		createdAt: new Date("2024-01-14T16:00:00"),
		updatedAt: new Date("2024-01-15T08:15:00"),
		history: [],
		tags: [],
		customFields: {},
	},
	{
		id: "task-2",
		taskNumber: "TSK-2024-002",
		title: "Electrical Wiring - Floor 2",
		description: "Install electrical wiring for Floor 2 offices",
		category: "electrical",
		location: "Zone B - Floor 2",
		siteId: "site-1",
		plannedStartDate: new Date("2024-01-16T09:00:00"),
		plannedEndDate: new Date("2024-01-16T17:00:00"),
		estimatedDuration: 8,
		status: "permit-pending",
		priority: "medium",
		progressPercentage: 0,
		createdBy: "supervisor-2",
		createdByName: "Sarah Johnson",
		assignedSupervisor: "supervisor-2",
		assignedSupervisorName: "Sarah Johnson",
		assignedWorkers: [],
		dependencies: [],
		requiresPermit: true,
		permitStatus: "pending-approval",
		riskLevel: "high",
		safetyRequirements: [],
		requiredPPE: [],
		requiredTrainings: [],
		requiredCertifications: [],
		ramsDocuments: [],
		attachments: [],
		qualityChecks: [],
		complianceRequirements: [],
		createdAt: new Date("2024-01-15T14:00:00"),
		updatedAt: new Date("2024-01-15T14:00:00"),
		history: [],
		tags: [],
		customFields: {},
	},
	{
		id: "task-3",
		taskNumber: "TSK-2024-003",
		title: "HVAC Installation - Basement",
		description: "Install HVAC system in basement level",
		category: "hvac",
		location: "Zone C - Basement",
		siteId: "site-1",
		plannedStartDate: new Date("2024-01-17T08:00:00"),
		plannedEndDate: new Date("2024-01-18T16:00:00"),
		estimatedDuration: 16,
		status: "todo",
		priority: "low",
		progressPercentage: 0,
		createdBy: "supervisor-3",
		createdByName: "Mike Davis",
		assignedSupervisor: "supervisor-3",
		assignedSupervisorName: "Mike Davis",
		assignedWorkers: [],
		dependencies: [],
		requiresPermit: false,
		riskLevel: "low",
		safetyRequirements: [],
		requiredPPE: [],
		requiredTrainings: [],
		requiredCertifications: [],
		ramsDocuments: [],
		attachments: [],
		qualityChecks: [],
		complianceRequirements: [],
		createdAt: new Date("2024-01-15T10:00:00"),
		updatedAt: new Date("2024-01-15T10:00:00"),
		history: [],
		tags: [],
		customFields: {},
	},
];

const TasksKanban: React.FC<TasksKanbanProps> = ({ siteId }) => {
	const [tasks, setTasks] = useState<Task[]>(mockTasks);
	const [searchTerm, setSearchTerm] = useState("");
	const [draggedTask, setDraggedTask] = useState<Task | null>(null);

	useEffect(() => {
		// Fetch tasks for the site
		console.log(`Fetching tasks for site ${siteId}`);
	}, [siteId]);

	const columns: KanbanColumn[] = [
		{
			id: "todo",
			title: "To Do",
			color: "bg-gray-100",
			icon: "clipboard-list",
			tasks: tasks.filter((task) => task.status === "todo"),
			allowDrop: true,
		},
		{
			id: "permit-pending",
			title: "Permit Pending",
			color: "bg-amber-100",
			icon: "clock",
			tasks: tasks.filter((task) => task.status === "permit-pending"),
			allowDrop: true,
		},
		{
			id: "permit-approved",
			title: "Permit Approved",
			color: "bg-green-100",
			icon: "shield-check",
			tasks: tasks.filter((task) => task.status === "permit-approved"),
			allowDrop: true,
		},
		{
			id: "in-progress",
			title: "In Progress",
			color: "bg-blue-100",
			icon: "play",
			tasks: tasks.filter((task) => task.status === "in-progress"),
			allowDrop: true,
		},
		{
			id: "blocked",
			title: "Blocked",
			color: "bg-red-100",
			icon: "x-circle",
			tasks: tasks.filter((task) => task.status === "blocked"),
			allowDrop: true,
		},
		{
			id: "completed",
			title: "Completed",
			color: "bg-green-100",
			icon: "check-circle",
			tasks: tasks.filter((task) => task.status === "completed"),
			allowDrop: true,
		},
	];

	const getPriorityColor = (priority: string) => {
		switch (priority) {
			case "critical":
				return "border-l-red-500";
			case "high":
				return "border-l-orange-500";
			case "medium":
				return "border-l-yellow-500";
			case "low":
				return "border-l-green-500";
			default:
				return "border-l-gray-500";
		}
	};

	const handleDragStart = (e: React.DragEvent, task: Task) => {
		setDraggedTask(task);
		e.dataTransfer.effectAllowed = "move";
	};

	const handleDragOver = (e: React.DragEvent) => {
		e.preventDefault();
		e.dataTransfer.dropEffect = "move";
	};

	const handleDrop = (e: React.DragEvent, newStatus: TaskStatus) => {
		e.preventDefault();

		if (draggedTask && draggedTask.status !== newStatus) {
			// Update task status
			const updatedTasks = tasks.map((task) =>
				task.id === draggedTask.id
					? { ...task, status: newStatus, updatedAt: new Date() }
					: task,
			);
			setTasks(updatedTasks);

			// In a real app, this would make an API call to update the task
			console.log(
				`Moving task ${draggedTask.id} from ${draggedTask.status} to ${newStatus}`,
			);
		}

		setDraggedTask(null);
	};

	const filteredTasks = tasks.filter(
		(task) =>
			task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
			task.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
			task.taskNumber.toLowerCase().includes(searchTerm.toLowerCase()),
	);

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<h2 className="text-2xl font-bold text-gray-900">Task Board</h2>
				<button className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700">
					<Plus className="h-4 w-4 mr-2" />
					Add Task
				</button>
			</div>

			{/* Filters */}
			<div className="bg-white rounded-lg border border-gray-200 p-4">
				<div className="flex items-center space-x-4">
					<div className="relative flex-1">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
						<input
							type="text"
							placeholder="Search tasks..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
						/>
					</div>
					<button className="flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
						<Filter className="h-4 w-4 mr-2" />
						Filters
					</button>
				</div>
			</div>

			{/* Kanban Board */}
			<div className="grid grid-cols-1 lg:grid-cols-6 gap-4 overflow-x-auto">
				{columns.map((column) => (
					<div
						key={column.id}
						className="bg-gray-50 rounded-lg p-4 min-h-[600px]"
						onDragOver={handleDragOver}
						onDrop={(e) => handleDrop(e, column.id)}
					>
						<div className="flex items-center justify-between mb-4">
							<h3 className="font-medium text-gray-900">{column.title}</h3>
							<span className="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded-full">
								{column.tasks.length}
							</span>
						</div>

						<div className="space-y-3">
							{column.tasks
								.filter((task) => filteredTasks.includes(task))
								.map((task) => (
									<div
										key={task.id}
										draggable
										onDragStart={(e) => handleDragStart(e, task)}
										className={`bg-white p-4 rounded-lg border border-gray-200 shadow-sm cursor-move hover:shadow-md transition-shadow border-l-4 ${getPriorityColor(task.priority)}`}
									>
										<div className="space-y-3">
											<div>
												<h4 className="text-sm font-medium text-gray-900 line-clamp-2">
													{task.title}
												</h4>
												<p className="text-xs text-gray-500 mt-1">
													{task.taskNumber}
												</p>
											</div>

											<div className="flex items-center space-x-2 text-xs text-gray-500">
												<MapPin className="h-3 w-3" />
												<span>{task.location}</span>
											</div>

											<div className="flex items-center justify-between">
												<div className="flex items-center space-x-2">
													<Users className="h-3 w-3 text-gray-400" />
													<span className="text-xs text-gray-500">
														{task.assignedWorkers.length} workers
													</span>
												</div>

												{task.requiresPermit && (
													<div className="flex items-center space-x-1">
														<Shield className="h-3 w-3 text-amber-500" />
														<span className="text-xs text-amber-600">
															Permit
														</span>
													</div>
												)}
											</div>

											{task.status === "in-progress" && (
												<div className="space-y-1">
													<div className="flex items-center text-xs text-gray-500">
														<Clock className="h-3 w-3 mr-1" />
														<span>In Progress</span>
													</div>
												</div>
											)}

											<div className="flex items-center justify-between">
												<span
													className={`text-xs px-2 py-1 rounded-full font-medium ${
														task.priority === "critical"
															? "bg-red-100 text-red-800"
															: task.priority === "high"
																? "bg-orange-100 text-orange-800"
																: task.priority === "medium"
																	? "bg-yellow-100 text-yellow-800"
																	: "bg-green-100 text-green-800"
													}`}
												>
													{task.priority.toUpperCase()}
												</span>

												<div className="flex items-center space-x-1 text-xs text-gray-500">
													<Clock className="h-3 w-3" />
													<span>{task.estimatedDuration}h</span>
												</div>
											</div>

											<div className="text-xs text-gray-500">
												Due: {task.plannedEndDate.toLocaleDateString()}
											</div>
										</div>
									</div>
								))}
						</div>

						{column.tasks.filter((task) => filteredTasks.includes(task))
							.length === 0 && (
							<div className="text-center py-8 text-gray-500">
								<div className="text-4xl mb-2">📋</div>
								<p className="text-sm">No tasks in this column</p>
							</div>
						)}
					</div>
				))}
			</div>
		</div>
	);
};

export default TasksKanban;
