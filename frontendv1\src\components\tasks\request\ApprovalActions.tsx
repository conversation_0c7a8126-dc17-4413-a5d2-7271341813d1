import React, { useState } from 'react';
import {
  Check<PERSON><PERSON><PERSON>,
  XCircle,
  ThumbsUp,
  ThumbsDown,
  AlertTriangle,
  FileText,
  Shield,
  Send
} from 'lucide-react';
import { SiteTask } from '../../../types/tasks';

interface ApprovalActionsProps {
  task: SiteTask;
  onTaskUpdate: (task: SiteTask) => void;
}

const ApprovalActions: React.FC<ApprovalActionsProps> = ({ task, onTaskUpdate }) => {
  const [approvalAction, setApprovalAction] = useState<'approve' | 'disapprove' | null>(null);
  const [approvalComment, setApprovalComment] = useState('');
  const [showConfirmation, setShowConfirmation] = useState(false);

  // Check completion status
  const hasHazards = task.hazards.length > 0;
  const hasControlMeasures = task.controlMeasures.length > 0;
  const hasRequiredDocuments = task.attachedDocuments.some(doc => doc.isRequired);
  const isReadyForApproval = hasHazards && hasControlMeasures && hasRequiredDocuments;

  const handleApprovalSubmit = () => {
    if (!approvalAction || (approvalAction === 'disapprove' && !approvalComment.trim())) {
      return;
    }

    // Update task status based on approval action
    const newStatus = approvalAction === 'approve' ? 'approved' : 'rejected';

    const updatedTask = {
      ...task,
      status: newStatus as any,
      updatedAt: new Date()
    };

    onTaskUpdate(updatedTask);

    // Reset form
    setApprovalAction(null);
    setApprovalComment('');
    setShowConfirmation(false);

    // Show success message (in real app, this would be a toast notification)
    alert(`Task ${approvalAction === 'approve' ? 'approved' : 'disapproved'} successfully!`);
  };

  const getCompletionStatus = () => {
    const items = [
      { label: 'Hazard Assessment', completed: hasHazards, icon: <AlertTriangle className="h-4 w-4" /> },
      { label: 'Control Measures', completed: hasControlMeasures, icon: <Shield className="h-4 w-4" /> },
      { label: 'Required Documents', completed: hasRequiredDocuments, icon: <FileText className="h-4 w-4" /> }
    ];

    return items;
  };

  const completionItems = getCompletionStatus();
  const completedCount = completionItems.filter(item => item.completed).length;

  return (
    <div className="p-6">
      {/* Header */}
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Final Approval</h2>
        <p className="text-sm text-gray-600">
          Review all task components and provide final approval or disapproval with comments.
        </p>
      </div>

      {/* Completion Status */}
      <div className="bg-white border border-gray-200 rounded-lg p-6 mb-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Review Checklist</h3>

        <div className="space-y-3 mb-4">
          {completionItems.map((item, index) => (
            <div key={index} className="flex items-center space-x-3">
              <div className={`flex-shrink-0 w-6 h-6 rounded-full flex items-center justify-center ${
                item.completed ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'
              }`}>
                {item.completed ? <CheckCircle className="h-4 w-4" /> : item.icon}
              </div>
              <span className={`text-sm ${item.completed ? 'text-gray-900' : 'text-gray-500'}`}>
                {item.label}
              </span>
              <span className={`text-xs px-2 py-1 rounded-full ${
                item.completed
                  ? 'bg-green-100 text-green-800'
                  : 'bg-yellow-100 text-yellow-800'
              }`}>
                {item.completed ? 'Complete' : 'Pending'}
              </span>
            </div>
          ))}
        </div>

        <div className="bg-gray-50 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium text-gray-700">Overall Progress:</span>
            <div className="flex items-center space-x-2">
              <div className="w-32 bg-gray-200 rounded-full h-2">
                <div
                  className={`h-2 rounded-full transition-all duration-300 ${
                    completedCount === completionItems.length ? 'bg-green-500' : 'bg-yellow-500'
                  }`}
                  style={{ width: `${(completedCount / completionItems.length) * 100}%` }}
                />
              </div>
              <span className="text-sm font-medium text-gray-900">
                {completedCount}/{completionItems.length}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Task Summary */}
      <div className="bg-white border border-gray-200 rounded-lg p-6 mb-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Task Summary</h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-4 bg-orange-50 rounded-lg">
            <AlertTriangle className="h-8 w-8 text-orange-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-orange-900">{task.hazards.length}</div>
            <div className="text-sm text-orange-700">Hazards Identified</div>
          </div>

          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <Shield className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-blue-900">{task.controlMeasures.length}</div>
            <div className="text-sm text-blue-700">Control Measures</div>
          </div>

          <div className="text-center p-4 bg-green-50 rounded-lg">
            <FileText className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <div className="text-2xl font-bold text-green-900">{task.attachedDocuments.length}</div>
            <div className="text-sm text-green-700">Documents Attached</div>
          </div>
        </div>

        <div className="mt-4 p-4 bg-gray-50 rounded-lg">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Risk Level:</span>
              <span className={`ml-2 font-medium ${
                task.riskLevel === 'critical' ? 'text-red-600' :
                task.riskLevel === 'high' ? 'text-orange-600' :
                task.riskLevel === 'medium' ? 'text-yellow-600' :
                'text-green-600'
              }`}>
                {task.riskLevel.toUpperCase()}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Priority:</span>
              <span className="ml-2 font-medium text-gray-900">{task.priority.toUpperCase()}</span>
            </div>
            <div>
              <span className="text-gray-600">Requested By:</span>
              <span className="ml-2 font-medium text-gray-900">{task.createdByName}</span>
            </div>
            <div>
              <span className="text-gray-600">Duration:</span>
              <span className="ml-2 font-medium text-gray-900">{task.estimatedDuration} hours</span>
            </div>
          </div>
        </div>
      </div>

      {/* Approval Actions */}
      {!showConfirmation ? (
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Approval Decision</h3>

          {!isReadyForApproval && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
              <div className="flex items-start space-x-3">
                <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-yellow-900">Task Not Ready for Approval</h4>
                  <p className="text-sm text-yellow-700 mt-1">
                    Please complete all required sections before proceeding with approval.
                  </p>
                </div>
              </div>
            </div>
          )}

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Select Action:
              </label>
              <div className="grid grid-cols-2 gap-4">
                <button
                  onClick={() => setApprovalAction('approve')}
                  disabled={!isReadyForApproval}
                  className={`p-4 border-2 rounded-lg transition-all duration-200 ${
                    approvalAction === 'approve'
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-200 hover:border-green-300'
                  } ${!isReadyForApproval ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                >
                  <div className="flex items-center space-x-3">
                    <ThumbsUp className={`h-6 w-6 ${
                      approvalAction === 'approve' ? 'text-green-600' : 'text-gray-400'
                    }`} />
                    <div className="text-left">
                      <div className={`font-medium ${
                        approvalAction === 'approve' ? 'text-green-900' : 'text-gray-700'
                      }`}>
                        Approve Task
                      </div>
                      <div className="text-sm text-gray-600">
                        Task meets all safety requirements
                      </div>
                    </div>
                  </div>
                </button>

                <button
                  onClick={() => setApprovalAction('disapprove')}
                  className={`p-4 border-2 rounded-lg transition-all duration-200 cursor-pointer ${
                    approvalAction === 'disapprove'
                      ? 'border-red-500 bg-red-50'
                      : 'border-gray-200 hover:border-red-300'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <ThumbsDown className={`h-6 w-6 ${
                      approvalAction === 'disapprove' ? 'text-red-600' : 'text-gray-400'
                    }`} />
                    <div className="text-left">
                      <div className={`font-medium ${
                        approvalAction === 'disapprove' ? 'text-red-900' : 'text-gray-700'
                      }`}>
                        Disapprove Task
                      </div>
                      <div className="text-sm text-gray-600">
                        Task requires modifications
                      </div>
                    </div>
                  </div>
                </button>
              </div>
            </div>

            {approvalAction && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {approvalAction === 'approve' ? 'Comments (Optional)' : 'Reason for Disapproval *'}
                </label>
                <textarea
                  value={approvalComment}
                  onChange={(e) => setApprovalComment(e.target.value)}
                  placeholder={
                    approvalAction === 'approve'
                      ? 'Add any additional comments or conditions...'
                      : 'Explain what needs to be changed or addressed...'
                  }
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  required={approvalAction === 'disapprove'}
                />
              </div>
            )}

            {approvalAction && (
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => {
                    setApprovalAction(null);
                    setApprovalComment('');
                  }}
                  className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={() => setShowConfirmation(true)}
                  disabled={approvalAction === 'disapprove' && !approvalComment.trim()}
                  className={`px-4 py-2 text-white rounded-md transition-colors ${
                    approvalAction === 'approve'
                      ? 'bg-green-600 hover:bg-green-700'
                      : 'bg-red-600 hover:bg-red-700'
                  } disabled:opacity-50 disabled:cursor-not-allowed`}
                >
                  Continue
                </button>
              </div>
            )}
          </div>
        </div>
      ) : (
        /* Confirmation Step */
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Confirm Decision</h3>

          <div className={`p-4 rounded-lg mb-4 ${
            approvalAction === 'approve' ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
          }`}>
            <div className="flex items-start space-x-3">
              {approvalAction === 'approve' ? (
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
              ) : (
                <XCircle className="h-5 w-5 text-red-600 mt-0.5" />
              )}
              <div>
                <h4 className={`text-sm font-medium ${
                  approvalAction === 'approve' ? 'text-green-900' : 'text-red-900'
                }`}>
                  {approvalAction === 'approve' ? 'Approve Task Request' : 'Disapprove Task Request'}
                </h4>
                <p className={`text-sm mt-1 ${
                  approvalAction === 'approve' ? 'text-green-700' : 'text-red-700'
                }`}>
                  {approvalAction === 'approve'
                    ? 'This task will be approved and can proceed to execution.'
                    : 'This task will be returned to the requester for modifications.'
                  }
                </p>
              </div>
            </div>
          </div>

          {approvalComment && (
            <div className="bg-gray-50 rounded-lg p-4 mb-4">
              <h4 className="text-sm font-medium text-gray-900 mb-2">Your Comments:</h4>
              <p className="text-sm text-gray-700">{approvalComment}</p>
            </div>
          )}

          <div className="flex justify-end space-x-3">
            <button
              onClick={() => setShowConfirmation(false)}
              className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              Back
            </button>
            <button
              onClick={handleApprovalSubmit}
              className={`flex items-center space-x-2 px-4 py-2 text-white rounded-md transition-colors ${
                approvalAction === 'approve'
                  ? 'bg-green-600 hover:bg-green-700'
                  : 'bg-red-600 hover:bg-red-700'
              }`}
            >
              <Send className="h-4 w-4" />
              <span>
                {approvalAction === 'approve' ? 'Approve Task' : 'Disapprove Task'}
              </span>
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ApprovalActions;
