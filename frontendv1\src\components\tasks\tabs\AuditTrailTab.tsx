import React from 'react';
import {
  Clock,
  User,
  FileText,
  Edit,
  Play,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Download
} from 'lucide-react';
import { SiteTask } from '../../../types/tasks';

interface AuditTrailTabProps {
  task: SiteTask;
}

interface AuditEntry {
  id: string;
  timestamp: Date;
  action: string;
  description: string;
  performedBy: string;
  performedByName: string;
  oldValue?: any;
  newValue?: any;
  details?: Record<string, any>;
  category: 'status' | 'assignment' | 'document' | 'progress' | 'approval' | 'system';
}

const AuditTrailTab: React.FC<AuditTrailTabProps> = ({ task: _task }) => {

  // Mock audit trail data - replace with actual API data
  const auditEntries: AuditEntry[] = [
    {
      id: '1',
      timestamp: new Date('2024-01-15T09:00:00'),
      action: 'TASK_CREATED',
      description: 'Task created from template',
      performedBy: 'engineer-1',
      performedByName: 'Site Engineer',
      category: 'system',
      details: {
        templateId: 'template-1',
        templateName: 'Excavation - Standard'
      }
    },
    {
      id: '2',
      timestamp: new Date('2024-01-15T09:15:00'),
      action: 'TASK_UPDATED',
      description: 'Work description updated',
      performedBy: 'engineer-1',
      performedByName: 'Site Engineer',
      category: 'status',
      oldValue: 'Basic excavation work',
      newValue: 'Dig 2m deep trench along the north wall for electrical conduit installation'
    },
    {
      id: '3',
      timestamp: new Date('2024-01-16T14:30:00'),
      action: 'DOCUMENT_ATTACHED',
      description: 'Equipment certificate attached',
      performedBy: 'john.smith',
      performedByName: 'John Smith',
      category: 'document',
      details: {
        documentName: 'Equipment Certificate - Excavator-XC200',
        documentType: 'certificate'
      }
    },
    {
      id: '4',
      timestamp: new Date('2024-01-17T10:00:00'),
      action: 'APPROVAL_REQUESTED',
      description: 'Task submitted for HSE approval',
      performedBy: 'engineer-1',
      performedByName: 'Site Engineer',
      category: 'approval'
    },
    {
      id: '5',
      timestamp: new Date('2024-01-18T11:30:00'),
      action: 'HAZARD_UPDATED',
      description: 'Risk assessment updated by HSE Officer',
      performedBy: 'hse-officer-1',
      performedByName: 'HSE Officer',
      category: 'approval',
      details: {
        hazardsAdded: 1,
        controlMeasuresAdded: 2
      }
    },
    {
      id: '6',
      timestamp: new Date('2024-01-18T16:45:00'),
      action: 'TASK_APPROVED',
      description: 'Task approved by HSE Admin',
      performedBy: 'hse-admin-1',
      performedByName: 'HSE Admin',
      category: 'approval',
      oldValue: 'requested',
      newValue: 'approved'
    }
  ];

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'TASK_CREATED':
        return <FileText className="h-4 w-4 text-blue-500" />;
      case 'TASK_UPDATED':
        return <Edit className="h-4 w-4 text-yellow-500" />;
      case 'TASK_STARTED':
        return <Play className="h-4 w-4 text-green-500" />;
      case 'TASK_COMPLETED':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'TASK_BLOCKED':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'TASK_APPROVED':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'APPROVAL_REQUESTED':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'DOCUMENT_ATTACHED':
        return <FileText className="h-4 w-4 text-blue-500" />;
      case 'HAZARD_UPDATED':
        return <AlertTriangle className="h-4 w-4 text-orange-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'status':
        return 'bg-blue-100 text-blue-800';
      case 'assignment':
        return 'bg-purple-100 text-purple-800';
      case 'document':
        return 'bg-green-100 text-green-800';
      case 'progress':
        return 'bg-yellow-100 text-yellow-800';
      case 'approval':
        return 'bg-orange-100 text-orange-800';
      case 'system':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  // Sort entries by timestamp (most recent first)
  const sortedEntries = [...auditEntries].sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

  const exportAuditTrail = () => {
    // Here you would implement the export functionality
    console.log('Exporting audit trail...');
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Audit Trail</h2>
          <p className="text-sm text-gray-600">
            Complete history of all actions performed on this task
          </p>
        </div>
        <button
          onClick={exportAuditTrail}
          className="flex items-center space-x-2 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
        >
          <Download className="h-4 w-4" />
          <span>Export</span>
        </button>
      </div>



      {/* Audit Entries */}
      <div className="space-y-4">
        {sortedEntries.length === 0 ? (
          <div className="text-center py-8">
            <Clock className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500">No audit entries found</p>
          </div>
        ) : (
          sortedEntries.map((entry, index) => (
            <div key={entry.id} className="bg-white border border-gray-200 rounded-lg p-4">
              <div className="flex items-start space-x-4">
                {/* Timeline indicator */}
                <div className="flex flex-col items-center">
                  <div className="flex-shrink-0 w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                    {getActionIcon(entry.action)}
                  </div>
                  {index < sortedEntries.length - 1 && (
                    <div className="w-px h-8 bg-gray-200 mt-2" />
                  )}
                </div>

                {/* Content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <h4 className="text-sm font-medium text-gray-900">
                          {entry.description}
                        </h4>
                        <span className={`px-2 py-0.5 text-xs rounded-full font-medium ${getCategoryColor(entry.category)}`}>
                          {entry.category}
                        </span>
                      </div>

                      <div className="flex items-center space-x-4 text-xs text-gray-500 mb-2">
                        <span className="flex items-center space-x-1">
                          <User className="h-3 w-3" />
                          <span>{entry.performedByName}</span>
                        </span>
                        <span className="flex items-center space-x-1">
                          <Clock className="h-3 w-3" />
                          <span>
                            {entry.timestamp.toLocaleDateString()} at {entry.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                          </span>
                        </span>
                      </div>

                      {/* Value changes */}
                      {(entry.oldValue || entry.newValue) && (
                        <div className="bg-gray-50 rounded p-3 text-sm">
                          {entry.oldValue && (
                            <div className="mb-1">
                              <span className="text-gray-600">From: </span>
                              <span className="text-red-600 line-through">{entry.oldValue}</span>
                            </div>
                          )}
                          {entry.newValue && (
                            <div>
                              <span className="text-gray-600">To: </span>
                              <span className="text-green-600 font-medium">{entry.newValue}</span>
                            </div>
                          )}
                        </div>
                      )}

                      {/* Additional details */}
                      {entry.details && Object.keys(entry.details).length > 0 && (
                        <div className="mt-2">
                          <details className="text-sm">
                            <summary className="text-gray-600 cursor-pointer hover:text-gray-900">
                              View details
                            </summary>
                            <div className="mt-2 bg-gray-50 rounded p-3">
                              {Object.entries(entry.details).map(([key, value]) => (
                                <div key={key} className="flex justify-between py-1">
                                  <span className="text-gray-600 capitalize">
                                    {key.replace(/([A-Z])/g, ' $1').toLowerCase()}:
                                  </span>
                                  <span className="text-gray-900 font-medium">
                                    {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                                  </span>
                                </div>
                              ))}
                            </div>
                          </details>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Summary */}
      <div className="mt-8 bg-gray-50 rounded-lg p-4">
        <h3 className="text-sm font-medium text-gray-900 mb-2">Summary</h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Total Actions:</span>
            <span className="ml-2 font-medium text-gray-900">{auditEntries.length}</span>
          </div>
          <div>
            <span className="text-gray-600">Unique Users:</span>
            <span className="ml-2 font-medium text-gray-900">
              {new Set(auditEntries.map(e => e.performedBy)).size}
            </span>
          </div>
          <div>
            <span className="text-gray-600">First Action:</span>
            <span className="ml-2 font-medium text-gray-900">
              {auditEntries[auditEntries.length - 1]?.timestamp.toLocaleDateString()}
            </span>
          </div>
          <div>
            <span className="text-gray-600">Last Action:</span>
            <span className="ml-2 font-medium text-gray-900">
              {auditEntries[0]?.timestamp.toLocaleDateString()}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuditTrailTab;
