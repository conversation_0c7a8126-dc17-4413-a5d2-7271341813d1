import React from 'react';
import { Shield, X, CheckCircle, Users, Wrench, DollarSign, GraduationCap } from 'lucide-react';
import { ControlMeasure } from '../../../types/tasks';

interface ControlMeasuresTabProps {
  controlMeasures: ControlMeasure[];
}

const ControlMeasuresTab: React.FC<ControlMeasuresTabProps> = ({ controlMeasures }) => {
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'elimination':
        return <X className="h-4 w-4 text-red-600" />;
      case 'substitution':
        return <CheckCircle className="h-4 w-4 text-blue-600" />;
      case 'engineering':
        return <Wrench className="h-4 w-4 text-purple-600" />;
      case 'administrative':
        return <Users className="h-4 w-4 text-orange-600" />;
      case 'ppe':
        return <Shield className="h-4 w-4 text-green-600" />;
      default:
        return <Shield className="h-4 w-4 text-gray-600" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'elimination':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'substitution':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'engineering':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'administrative':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'ppe':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'elimination':
        return 'Elimination';
      case 'substitution':
        return 'Substitution';
      case 'engineering':
        return 'Engineering Controls';
      case 'administrative':
        return 'Administrative Controls';
      case 'ppe':
        return 'Personal Protective Equipment';
      default:
        return type;
    }
  };

  const getCostColor = (cost: string) => {
    switch (cost) {
      case 'low':
        return 'text-green-600 bg-green-100';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100';
      case 'high':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getEffectivenessColor = (effectiveness: number) => {
    if (effectiveness >= 4) return 'text-green-600';
    if (effectiveness >= 3) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (controlMeasures.length === 0) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <Shield className="h-16 w-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No Control Measures</h3>
          <p className="text-gray-500">
            No control measures have been defined for this task yet.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Control Measures</h2>
        <p className="text-sm text-gray-600">
          {controlMeasures.length} control measure{controlMeasures.length !== 1 ? 's' : ''} defined for this task
        </p>
      </div>

      {/* Hierarchy of Controls Guide */}
      <div className="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h3 className="text-sm font-medium text-blue-900 mb-3">Hierarchy of Controls (Most to Least Effective)</h3>
        <div className="grid grid-cols-1 md:grid-cols-5 gap-3 text-xs">
          {[
            { type: 'elimination', label: 'Elimination', desc: 'Remove hazard' },
            { type: 'substitution', label: 'Substitution', desc: 'Replace with safer' },
            { type: 'engineering', label: 'Engineering', desc: 'Physical safeguards' },
            { type: 'administrative', label: 'Administrative', desc: 'Policies & procedures' },
            { type: 'ppe', label: 'PPE', desc: 'Individual protection' }
          ].map((item, index) => (
            <div key={item.type} className="flex items-start space-x-2">
              <div className="flex-shrink-0 w-6 h-6 rounded-full bg-white flex items-center justify-center text-blue-600 font-bold">
                {index + 1}
              </div>
              <div>
                <div className="font-medium text-blue-900">{item.label}</div>
                <div className="text-blue-700">{item.desc}</div>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="space-y-4">
        {controlMeasures.map((measure, index) => (
          <div key={measure.id} className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center space-x-3">
                <div className={`p-2 rounded-lg border ${getTypeColor(measure.type)}`}>
                  {getTypeIcon(measure.type)}
                </div>
                <div>
                  <h3 className="text-lg font-medium text-gray-900">
                    Control Measure #{index + 1}
                  </h3>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getTypeColor(measure.type)}`}>
                    {getTypeLabel(measure.type)}
                  </span>
                </div>
              </div>
              <div className="text-right">
                <div className={`text-2xl font-bold ${getEffectivenessColor(measure.effectiveness)}`}>
                  {measure.effectiveness}/5
                </div>
                <div className="text-xs text-gray-500">Effectiveness</div>
              </div>
            </div>

            <div className="mb-4">
              <h4 className="text-sm font-medium text-gray-700 mb-2">Description</h4>
              <p className="text-gray-900">{measure.description}</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-4">
              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Effectiveness Rating</h4>
                <div className="flex items-center space-x-2">
                  <div className="flex space-x-1">
                    {[1, 2, 3, 4, 5].map((level) => (
                      <div
                        key={level}
                        className={`w-3 h-3 rounded-full ${
                          level <= measure.effectiveness ? 'bg-blue-500' : 'bg-gray-200'
                        }`}
                      />
                    ))}
                  </div>
                  <span className={`text-sm font-medium ${getEffectivenessColor(measure.effectiveness)}`}>
                    {measure.effectiveness}/5
                  </span>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Implementation Cost</h4>
                <div className="flex items-center space-x-2">
                  <DollarSign className="h-4 w-4 text-gray-500" />
                  <span className={`px-2 py-1 text-xs rounded-full font-medium ${getCostColor(measure.implementationCost)}`}>
                    {measure.implementationCost.toUpperCase()}
                  </span>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium text-gray-700 mb-2">Training Required</h4>
                <div className="flex items-center space-x-2">
                  <GraduationCap className="h-4 w-4 text-gray-500" />
                  <span className={`px-2 py-1 text-xs rounded-full font-medium ${
                    measure.trainingRequired ? 'bg-orange-100 text-orange-800' : 'bg-green-100 text-green-800'
                  }`}>
                    {measure.trainingRequired ? 'YES' : 'NO'}
                  </span>
                </div>
              </div>
            </div>

            {measure.equipmentRequired && measure.equipmentRequired.length > 0 && (
              <div className="pt-4 border-t border-gray-200">
                <h4 className="text-sm font-medium text-gray-700 mb-2">Required Equipment</h4>
                <div className="flex flex-wrap gap-2">
                  {measure.equipmentRequired.map((equipment, idx) => (
                    <span
                      key={idx}
                      className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                    >
                      <Wrench className="h-3 w-3 mr-1" />
                      {equipment}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Summary */}
      <div className="mt-6 bg-gray-50 rounded-lg p-4">
        <h3 className="text-sm font-medium text-gray-900 mb-3">Control Measures Summary</h3>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          {['elimination', 'substitution', 'engineering', 'administrative', 'ppe'].map((type) => {
            const count = controlMeasures.filter(m => m.type === type).length;
            return (
              <div key={type} className="text-center">
                <div className="text-2xl font-bold text-gray-900">{count}</div>
                <div className="text-xs text-gray-600 capitalize">{getTypeLabel(type)}</div>
              </div>
            );
          })}
        </div>
        
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="grid grid-cols-3 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Avg. Effectiveness:</span>
              <span className="ml-2 font-medium text-gray-900">
                {controlMeasures.length > 0 
                  ? (controlMeasures.reduce((sum, m) => sum + m.effectiveness, 0) / controlMeasures.length).toFixed(1)
                  : '0'
                }/5
              </span>
            </div>
            <div>
              <span className="text-gray-600">Training Required:</span>
              <span className="ml-2 font-medium text-gray-900">
                {controlMeasures.filter(m => m.trainingRequired).length} measures
              </span>
            </div>
            <div>
              <span className="text-gray-600">Equipment Needed:</span>
              <span className="ml-2 font-medium text-gray-900">
                {controlMeasures.filter(m => m.equipmentRequired && m.equipmentRequired.length > 0).length} measures
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ControlMeasuresTab;
