import React, { useState } from 'react';
import {
  Download,
  Eye,
  FileText,
  Image,
  Video,
  File,
  Calendar,
  User,
  ExternalLink,
  Maximize2,
  ZoomIn,
  ZoomOut,
  RotateCw
} from 'lucide-react';
import { TaskDocument } from '../../../types/tasks';

interface DocumentTabProps {
  document: TaskDocument;
}

const DocumentTab: React.FC<DocumentTabProps> = ({ document: taskDocument }) => {
  const [isPreviewMode, setIsPreviewMode] = useState(true);
  const [zoomLevel, setZoomLevel] = useState(100);

  if (!taskDocument) {
    return (
      <div className="p-6">
        <div className="text-center text-gray-500">
          <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <p>No document selected</p>
        </div>
      </div>
    );
  }

  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return <File className="h-6 w-6 text-red-500" />;
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'gif':
        return <Image className="h-6 w-6 text-blue-500" />;
      case 'mp4':
      case 'avi':
      case 'mov':
        return <Video className="h-6 w-6 text-purple-500" />;
      default:
        return <FileText className="h-6 w-6 text-gray-500" />;
    }
  };

  const getDocumentTypeColor = (type: string) => {
    switch (type) {
      case 'rams':
        return 'bg-red-100 text-red-800';
      case 'method-statement':
        return 'bg-blue-100 text-blue-800';
      case 'drawing':
        return 'bg-green-100 text-green-800';
      case 'specification':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatFileSize = (_url: string): string => {
    // Mock file size calculation - in real app, this would come from the document metadata
    return '2.4 MB';
  };

  const handleDownload = () => {
    // Create a temporary link to download the file
    const link = document.createElement('a');
    link.href = taskDocument.url;
    link.download = taskDocument.name;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleZoomIn = () => {
    setZoomLevel(prev => Math.min(prev + 25, 200));
  };

  const handleZoomOut = () => {
    setZoomLevel(prev => Math.max(prev - 25, 50));
  };

  const handleResetZoom = () => {
    setZoomLevel(100);
  };

  const renderPreview = () => {
    const extension = taskDocument.name.split('.').pop()?.toLowerCase();

    if (['jpg', 'jpeg', 'png', 'gif'].includes(extension || '')) {
      return (
        <div className="flex items-center justify-center bg-gray-100 rounded-lg p-8">
          <img
            src={taskDocument.url}
            alt={taskDocument.name}
            className="max-w-full max-h-96 rounded shadow-lg"
            style={{ transform: `scale(${zoomLevel / 100})` }}
          />
        </div>
      );
    }

    if (extension === 'pdf') {
      return (
        <div className="bg-gray-100 rounded-lg p-8">
          <div className="text-center">
            <File className="h-16 w-16 text-red-500 mx-auto mb-4" />
            <p className="text-gray-600 mb-4">PDF Preview</p>
            <p className="text-sm text-gray-500 mb-4">
              Click "Open External" to view the full document
            </p>
            <button
              onClick={() => window.open(taskDocument.url, '_blank')}
              className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              <ExternalLink className="h-4 w-4" />
              <span>Open External</span>
            </button>
          </div>
        </div>
      );
    }

    if (['mp4', 'avi', 'mov'].includes(extension || '')) {
      return (
        <div className="bg-gray-100 rounded-lg p-8">
          <video
            controls
            className="w-full max-h-96 rounded shadow-lg"
            style={{ transform: `scale(${zoomLevel / 100})` }}
          >
            <source src={taskDocument.url} type={`video/${extension}`} />
            Your browser does not support the video tag.
          </video>
        </div>
      );
    }

    return (
      <div className="bg-gray-100 rounded-lg p-8 text-center">
        {getFileIcon(taskDocument.name)}
        <p className="text-gray-600 mt-4">Preview not available</p>
        <p className="text-sm text-gray-500 mt-2">
          Click download to view this file
        </p>
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-4">
            <div className="flex-shrink-0">
              {getFileIcon(taskDocument.name)}
            </div>
            <div className="flex-1 min-w-0">
              <h2 className="text-xl font-semibold text-gray-900 truncate">
                {taskDocument.name}
              </h2>
              <div className="flex items-center space-x-4 mt-2">
                <span className={`px-2 py-1 text-xs rounded-full font-medium ${getDocumentTypeColor(taskDocument.type)}`}>
                  {taskDocument.type.replace('-', ' ').toUpperCase()}
                </span>
                <span className="text-sm text-gray-500">
                  Version {taskDocument.version}
                </span>
                <span className="text-sm text-gray-500">
                  {formatFileSize(taskDocument.url)}
                </span>
                {taskDocument.isRequired && (
                  <span className="px-2 py-1 text-xs rounded-full bg-orange-100 text-orange-800 font-medium">
                    REQUIRED
                  </span>
                )}
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {/* Zoom Controls */}
            <div className="flex items-center space-x-1 border border-gray-300 rounded-md">
              <button
                onClick={handleZoomOut}
                className="p-2 hover:bg-gray-100 rounded-l-md"
                disabled={zoomLevel <= 50}
              >
                <ZoomOut className="h-4 w-4" />
              </button>
              <span className="px-3 py-2 text-sm border-x border-gray-300">
                {zoomLevel}%
              </span>
              <button
                onClick={handleZoomIn}
                className="p-2 hover:bg-gray-100"
                disabled={zoomLevel >= 200}
              >
                <ZoomIn className="h-4 w-4" />
              </button>
              <button
                onClick={handleResetZoom}
                className="p-2 hover:bg-gray-100 rounded-r-md"
              >
                <RotateCw className="h-4 w-4" />
              </button>
            </div>

            <button
              onClick={() => setIsPreviewMode(!isPreviewMode)}
              className={`p-2 rounded-md transition-colors ${
                isPreviewMode
                  ? 'bg-blue-100 text-blue-600'
                  : 'text-gray-400 hover:text-gray-600 hover:bg-gray-100'
              }`}
              title={isPreviewMode ? 'Hide Preview' : 'Show Preview'}
            >
              <Eye className="h-5 w-5" />
            </button>

            <button
              onClick={handleDownload}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md"
              title="Download"
            >
              <Download className="h-5 w-5" />
            </button>

            <button
              onClick={() => window.open(taskDocument.url, '_blank')}
              className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md"
              title="Open in new tab"
            >
              <Maximize2 className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto">
        {isPreviewMode ? (
          <div className="p-6">
            {renderPreview()}
          </div>
        ) : (
          <div className="p-6">
            {/* Document Metadata */}
            <div className="bg-gray-50 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Document Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Document Type
                    </label>
                    <p className="text-gray-900 capitalize">
                      {taskDocument.type.replace('-', ' ')}
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Version
                    </label>
                    <p className="text-gray-900">{taskDocument.version}</p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Required Document
                    </label>
                    <p className="text-gray-900">
                      {taskDocument.isRequired ? 'Yes' : 'No'}
                    </p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Uploaded By
                    </label>
                    <div className="flex items-center space-x-2">
                      <User className="h-4 w-4 text-gray-500" />
                      <span className="text-gray-900">{taskDocument.uploadedBy}</span>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Upload Date
                    </label>
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <span className="text-gray-900">
                        {taskDocument.uploadedAt.toLocaleDateString()} at {taskDocument.uploadedAt.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </span>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      File Size
                    </label>
                    <p className="text-gray-900">{formatFileSize(taskDocument.url)}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default DocumentTab;
