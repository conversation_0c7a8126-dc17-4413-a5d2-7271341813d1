import React from 'react';
import { MapPin, Building, Navigation, AlertTriangle, Shield, Wrench } from 'lucide-react';
import { SiteTask } from '../../../types/tasks';

interface LocationTabProps {
  task: SiteTask;
}

const LocationTab: React.FC<LocationTabProps> = ({ task }) => {
  // Mock location data - in real app this would come from API
  const locationDetails = {
    zone: task.location.split(' - ')[0] || 'Zone A',
    area: task.location.split(' - ')[1] || 'North Wall',
    building: 'Main Building',
    floor: 'Ground Floor',
    coordinates: '1°17\'S 36°49\'E',
    elevation: '1,795m above sea level',
    accessRoute: 'Main entrance → East corridor → Zone A',
    nearbyFacilities: ['Emergency Exit A', 'Fire Extinguisher Station', 'First Aid Point'],
    restrictions: ['Hard hat required', 'Safety boots mandatory', 'High visibility vest'],
    emergencyContact: 'Site Safety Officer: +254 700 123 456'
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'excavation':
        return <Wrench className="h-5 w-5 text-orange-600" />;
      case 'electrical-installation':
        return <AlertTriangle className="h-5 w-5 text-yellow-600" />;
      case 'construction':
        return <Building className="h-5 w-5 text-blue-600" />;
      default:
        return <Wrench className="h-5 w-5 text-gray-600" />;
    }
  };

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'critical':
        return 'text-red-600 bg-red-100 border-red-200';
      case 'high':
        return 'text-orange-600 bg-orange-100 border-orange-200';
      case 'medium':
        return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'low':
        return 'text-green-600 bg-green-100 border-green-200';
      default:
        return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Location Details</h2>
        <p className="text-sm text-gray-600">
          Detailed information about where this task will be performed
        </p>
      </div>

      {/* Primary Location Info */}
      <div className="bg-white border border-gray-200 rounded-lg p-6 mb-6">
        <div className="flex items-start space-x-4 mb-4">
          <div className="p-3 bg-blue-100 rounded-lg">
            <MapPin className="h-6 w-6 text-blue-600" />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-medium text-gray-900 mb-1">{task.location}</h3>
            <p className="text-sm text-gray-600">Primary work location for this task</p>
            <div className="flex items-center space-x-2 mt-2">
              {getCategoryIcon(task.category)}
              <span className="text-sm font-medium text-gray-700 capitalize">
                {task.category.replace('-', ' ')} Work
              </span>
            </div>
          </div>
          <div className={`px-3 py-1 rounded-full border ${getRiskLevelColor(task.riskLevel)}`}>
            <span className="text-xs font-medium">{task.riskLevel.toUpperCase()} RISK</span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-3">
            <div>
              <div className="text-sm font-medium text-gray-700">Zone</div>
              <div className="text-sm text-gray-900">{locationDetails.zone}</div>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-700">Specific Area</div>
              <div className="text-sm text-gray-900">{locationDetails.area}</div>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-700">Building</div>
              <div className="text-sm text-gray-900">{locationDetails.building}</div>
            </div>
          </div>
          <div className="space-y-3">
            <div>
              <div className="text-sm font-medium text-gray-700">Floor Level</div>
              <div className="text-sm text-gray-900">{locationDetails.floor}</div>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-700">Coordinates</div>
              <div className="text-sm text-gray-900">{locationDetails.coordinates}</div>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-700">Elevation</div>
              <div className="text-sm text-gray-900">{locationDetails.elevation}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Access Information */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Navigation className="h-5 w-5 text-green-600" />
            <h3 className="text-lg font-medium text-gray-900">Access Route</h3>
          </div>
          <p className="text-sm text-gray-900 mb-4">{locationDetails.accessRoute}</p>

          <div className="space-y-2">
            <div className="text-sm font-medium text-gray-700">Nearby Facilities</div>
            <div className="space-y-1">
              {locationDetails.nearbyFacilities.map((facility, index) => (
                <div key={index} className="flex items-center space-x-2">
                  <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-gray-900">{facility}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Shield className="h-5 w-5 text-orange-600" />
            <h3 className="text-lg font-medium text-gray-900">Safety Requirements</h3>
          </div>

          <div className="space-y-3">
            <div>
              <div className="text-sm font-medium text-gray-700 mb-2">Required PPE</div>
              <div className="space-y-1">
                {locationDetails.restrictions.map((restriction, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <AlertTriangle className="h-3 w-3 text-orange-500" />
                    <span className="text-sm text-gray-900">{restriction}</span>
                  </div>
                ))}
              </div>
            </div>

            <div className="pt-3 border-t border-gray-200">
              <div className="text-sm font-medium text-gray-700 mb-1">Emergency Contact</div>
              <div className="text-sm text-gray-900">{locationDetails.emergencyContact}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Environmental Conditions */}
      <div className="bg-white border border-gray-200 rounded-lg p-6 mb-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Environmental Conditions</h3>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-4 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-900">Indoor</div>
            <div className="text-sm text-blue-700">Environment Type</div>
          </div>

          <div className="text-center p-4 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-900">Normal</div>
            <div className="text-sm text-green-700">Ventilation</div>
          </div>

          <div className="text-center p-4 bg-yellow-50 rounded-lg">
            <div className="text-2xl font-bold text-yellow-900">Moderate</div>
            <div className="text-sm text-yellow-700">Noise Level</div>
          </div>
        </div>
      </div>

      {/* Work Area Specifications */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Work Area Specifications</h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-3">Area Details</h4>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Work Area Size:</span>
                <span className="text-sm text-gray-900">10m × 5m</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Ceiling Height:</span>
                <span className="text-sm text-gray-900">3.5m</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Surface Type:</span>
                <span className="text-sm text-gray-900">Concrete</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-gray-600">Lighting:</span>
                <span className="text-sm text-gray-900">Adequate</span>
              </div>
            </div>
          </div>

          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-3">Utilities Available</h4>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm text-gray-900">Electrical Power (220V)</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm text-gray-900">Water Supply</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm text-gray-900">Compressed Air</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                <span className="text-sm text-gray-900">Gas Lines (Isolated)</span>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-6 pt-6 border-t border-gray-200">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Special Considerations</h4>
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div className="flex items-start space-x-2">
              <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5" />
              <div className="text-sm text-yellow-800">
                <p className="font-medium mb-1">Underground Utilities Present</p>
                <p>Call 811 before any excavation work. Utility lines marked on site plan.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LocationTab;
