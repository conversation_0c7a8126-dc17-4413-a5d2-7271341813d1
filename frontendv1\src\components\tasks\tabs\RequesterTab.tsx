import React from 'react';
import { User, Calendar, Clock, Mail, Phone, Building, Badge } from 'lucide-react';
import { SiteTask } from '../../../types/tasks';

interface RequesterTabProps {
  task: SiteTask;
}

const RequesterTab: React.FC<RequesterTabProps> = ({ task }) => {
  const getTimeAgo = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) {
      return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    } else if (diffHours > 0) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="p-6">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Requester Information</h2>
        <p className="text-sm text-gray-600">
          Details about who requested this task and when
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Requester Profile */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <div className="flex items-center space-x-4 mb-6">
            <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
              <User className="h-8 w-8 text-blue-600" />
            </div>
            <div>
              <h3 className="text-lg font-medium text-gray-900">{task.createdByName}</h3>
              <p className="text-sm text-gray-600">Site Engineer</p>
              <div className="flex items-center space-x-2 mt-1">
                <Badge className="h-3 w-3 text-blue-600" />
                <span className="text-xs text-blue-600">ID: {task.createdBy}</span>
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <Mail className="h-4 w-4 text-gray-500" />
              <div>
                <div className="text-sm font-medium text-gray-900">Email</div>
                <div className="text-sm text-gray-600">{task.createdByName.toLowerCase().replace(' ', '.')}@workforce.com</div>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <Phone className="h-4 w-4 text-gray-500" />
              <div>
                <div className="text-sm font-medium text-gray-900">Phone</div>
                <div className="text-sm text-gray-600">+254 700 123 456</div>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <Building className="h-4 w-4 text-gray-500" />
              <div>
                <div className="text-sm font-medium text-gray-900">Department</div>
                <div className="text-sm text-gray-600">Site Engineering</div>
              </div>
            </div>
          </div>
        </div>

        {/* Request Details */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Request Details</h3>
          
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <Calendar className="h-4 w-4 text-gray-500" />
              <div>
                <div className="text-sm font-medium text-gray-900">Request Date</div>
                <div className="text-sm text-gray-600">
                  {task.createdAt.toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </div>
                <div className="text-xs text-gray-500">
                  {task.createdAt.toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <Clock className="h-4 w-4 text-gray-500" />
              <div>
                <div className="text-sm font-medium text-gray-900">Time Since Request</div>
                <div className="text-sm text-gray-600">{getTimeAgo(task.createdAt)}</div>
              </div>
            </div>

            <div className="flex items-center space-x-3">
              <Badge className="h-4 w-4 text-gray-500" />
              <div>
                <div className="text-sm font-medium text-gray-900">Priority Level</div>
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPriorityColor(task.priority)}`}>
                  {task.priority.toUpperCase()}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Task Request Summary */}
      <div className="mt-6 bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Request Summary</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Task Category</h4>
            <p className="text-sm text-gray-900 capitalize">{task.category.replace('-', ' ')}</p>
          </div>

          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Estimated Duration</h4>
            <p className="text-sm text-gray-900">{task.estimatedDuration} hours</p>
          </div>

          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Risk Level</h4>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              task.riskLevel === 'critical' ? 'bg-red-100 text-red-800' :
              task.riskLevel === 'high' ? 'bg-orange-100 text-orange-800' :
              task.riskLevel === 'medium' ? 'bg-yellow-100 text-yellow-800' :
              'bg-green-100 text-green-800'
            }`}>
              {task.riskLevel.toUpperCase()}
            </span>
          </div>

          <div>
            <h4 className="text-sm font-medium text-gray-700 mb-2">Current Status</h4>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
              task.status === 'approved' ? 'bg-green-100 text-green-800' :
              task.status === 'permit-pending' ? 'bg-yellow-100 text-yellow-800' :
              task.status === 'rejected' ? 'bg-red-100 text-red-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {task.status.replace('-', ' ').toUpperCase()}
            </span>
          </div>
        </div>

        <div className="mt-4 pt-4 border-t border-gray-200">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Request Description</h4>
          <p className="text-sm text-gray-900">{task.description}</p>
        </div>

        <div className="mt-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Work Description</h4>
          <p className="text-sm text-gray-900">{task.workDescription}</p>
        </div>
      </div>

      {/* Request Timeline */}
      <div className="mt-6 bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Request Timeline</h3>
        
        <div className="space-y-4">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
            <div>
              <div className="text-sm font-medium text-gray-900">Task Requested</div>
              <div className="text-sm text-gray-600">
                {task.createdAt.toLocaleDateString()} at {task.createdAt.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </div>
              <div className="text-xs text-gray-500">by {task.createdByName}</div>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
            <div>
              <div className="text-sm font-medium text-gray-900">Last Updated</div>
              <div className="text-sm text-gray-600">
                {task.updatedAt.toLocaleDateString()} at {task.updatedAt.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </div>
              <div className="text-xs text-gray-500">Status: {task.status.replace('-', ' ')}</div>
            </div>
          </div>

          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0 w-2 h-2 bg-gray-300 rounded-full mt-2"></div>
            <div>
              <div className="text-sm font-medium text-gray-500">Planned Start</div>
              <div className="text-sm text-gray-600">
                {task.plannedStartDate.toLocaleDateString()} at {task.plannedStartDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </div>
              <div className="text-xs text-gray-500">Pending approval</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RequesterTab;
