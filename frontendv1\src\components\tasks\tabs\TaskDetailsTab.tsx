import React, { useState } from 'react';
import {
  Calendar,
  Clock,
  MapPin,
  User,
  Alert<PERSON>riangle,
  CheckCircle,
  Users,
  Wrench,
  Play,
  Pause,
  XCircle,
  ThumbsUp,
  ThumbsDown
} from 'lucide-react';
import { SiteTask } from '../../../types/tasks';

interface TaskDetailsTabProps {
  task: SiteTask;
}

const TaskDetailsTab: React.FC<TaskDetailsTabProps> = ({ task }) => {
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [approvalAction, setApprovalAction] = useState<'approve' | 'disapprove' | null>(null);
  const [approvalComment, setApprovalComment] = useState('');

  const handleApprovalAction = (action: 'approve' | 'disapprove') => {
    setApprovalAction(action);
    setShowApprovalModal(true);
  };

  const handleSubmitApproval = () => {
    // Here you would call the API to approve/disapprove the task
    console.log('Submitting approval:', {
      action: approvalAction,
      comment: approvalComment,
      taskId: task.id
    });

    setShowApprovalModal(false);
    setApprovalComment('');
    setApprovalAction(null);
  };

  const handleCancelApproval = () => {
    setShowApprovalModal(false);
    setApprovalComment('');
    setApprovalAction(null);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'in-progress':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-gray-100 text-gray-800';
      case 'blocked':
        return 'bg-red-100 text-red-800';
      case 'requested':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-100 text-red-800';
      case 'high':
        return 'bg-orange-100 text-orange-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'in-progress':
        return <Play className="h-4 w-4 text-blue-500" />;
      case 'blocked':
        return <Pause className="h-4 w-4 text-red-500" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'permit-pending':
      case 'requested':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'permit-approved':
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex justify-between items-start mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">{task.name}</h1>
          <div className="flex items-center space-x-4">
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(task.status)}`}>
              {task.status.replace('-', ' ').toUpperCase()}
            </span>
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getPriorityColor(task.priority)}`}>
              {task.priority.toUpperCase()} PRIORITY
            </span>
            <span className="text-sm text-gray-500">
              ID: {task.id}
            </span>
          </div>
        </div>

        {/* Approval Actions - Only show for pending tasks */}
        {(task.status === 'permit-pending' || task.status === 'requested') && (
          <div className="flex space-x-2">
            <button
              onClick={() => handleApprovalAction('approve')}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              <ThumbsUp className="h-4 w-4" />
              <span>Approve</span>
            </button>
            <button
              onClick={() => handleApprovalAction('disapprove')}
              className="flex items-center space-x-2 px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
            >
              <ThumbsDown className="h-4 w-4" />
              <span>Disapprove</span>
            </button>
          </div>
        )}
      </div>

      {/* Task Information Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Basic Information */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <p className="text-gray-900">{task.description}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Work Description</label>
              <p className="text-gray-900">{task.workDescription}</p>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Category</label>
                <div className="flex items-center space-x-2">
                  <Wrench className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-900 capitalize">{task.category.replace('-', ' ')}</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
                <div className="flex items-center space-x-2">
                  <MapPin className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-900">{task.location}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Timing Information */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Timing</h3>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Planned Start</label>
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-900">
                    {task.plannedStartDate.toLocaleDateString()} {task.plannedStartDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Planned End</label>
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-900">
                    {task.plannedEndDate.toLocaleDateString()} {task.plannedEndDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </span>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Estimated Duration</label>
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-900">{task.estimatedDuration} hours</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Current Status</label>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(task.status)}
                  <span className="text-sm text-gray-900 capitalize">
                    {task.status.replace('-', ' ')}
                  </span>
                </div>
              </div>
            </div>

            {task.actualStartDate && (
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Actual Start</label>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-500" />
                    <span className="text-gray-900">
                      {task.actualStartDate.toLocaleDateString()} {task.actualStartDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </span>
                  </div>
                </div>

                {task.actualEndDate && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Actual End</label>
                    <div className="flex items-center space-x-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      <span className="text-gray-900">
                        {task.actualEndDate.toLocaleDateString()} {task.actualEndDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* People & Resources */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">People & Resources</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Created By</label>
              <div className="flex items-center space-x-2">
                <User className="h-4 w-4 text-gray-500" />
                <span className="text-gray-900">{task.createdByName}</span>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Assigned Workers</label>
              <div className="flex items-center space-x-2">
                <Users className="h-4 w-4 text-gray-500" />
                <span className="text-gray-900">
                  {task.assignedWorkers.length > 0
                    ? `${task.assignedWorkers.length} workers assigned`
                    : 'No workers assigned'
                  }
                </span>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Equipment</label>
              <div className="flex items-center space-x-2">
                <Wrench className="h-4 w-4 text-gray-500" />
                <span className="text-gray-900">
                  {task.assignedEquipment.length > 0
                    ? `${task.assignedEquipment.length} equipment assigned`
                    : 'No equipment assigned'
                  }
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Risk Information */}
        <div className="bg-white border border-gray-200 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Risk Assessment</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Risk Level</label>
              <div className="flex items-center space-x-2">
                <AlertTriangle className={`h-4 w-4 ${
                  task.riskLevel === 'critical' ? 'text-red-500' :
                  task.riskLevel === 'high' ? 'text-orange-500' :
                  task.riskLevel === 'medium' ? 'text-yellow-500' :
                  'text-green-500'
                }`} />
                <span className={`font-medium capitalize ${
                  task.riskLevel === 'critical' ? 'text-red-700' :
                  task.riskLevel === 'high' ? 'text-orange-700' :
                  task.riskLevel === 'medium' ? 'text-yellow-700' :
                  'text-green-700'
                }`}>
                  {task.riskLevel}
                </span>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Hazards Identified</label>
              <span className="text-gray-900">{task.hazards.length} hazards</span>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">Control Measures</label>
              <span className="text-gray-900">{task.controlMeasures.length} control measures</span>
            </div>
          </div>
        </div>
      </div>

      {/* Tags */}
      {task.tags.length > 0 && (
        <div className="mt-6">
          <label className="block text-sm font-medium text-gray-700 mb-2">Tags</label>
          <div className="flex flex-wrap gap-2">
            {task.tags.map((tag, index) => (
              <span
                key={index}
                className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full"
              >
                {tag}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Approval Modal */}
      {showApprovalModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
            <div className="flex items-center space-x-2 mb-4">
              {approvalAction === 'approve' ? (
                <ThumbsUp className="h-5 w-5 text-green-600" />
              ) : (
                <ThumbsDown className="h-5 w-5 text-red-600" />
              )}
              <h3 className="text-lg font-semibold text-gray-900">
                {approvalAction === 'approve' ? 'Approve Task' : 'Disapprove Task'}
              </h3>
            </div>

            <p className="text-gray-600 mb-4">
              {approvalAction === 'approve'
                ? 'Are you sure you want to approve this task? This will allow work to begin.'
                : 'Please provide a reason for disapproving this task. This will help the requester understand what needs to be changed.'
              }
            </p>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {approvalAction === 'approve' ? 'Comments (Optional)' : 'Reason for Disapproval *'}
              </label>
              <textarea
                value={approvalComment}
                onChange={(e) => setApprovalComment(e.target.value)}
                placeholder={approvalAction === 'approve'
                  ? 'Add any additional comments...'
                  : 'Explain what needs to be changed or addressed...'
                }
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required={approvalAction === 'disapprove'}
              />
            </div>

            <div className="flex justify-end space-x-3">
              <button
                onClick={handleCancelApproval}
                className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleSubmitApproval}
                disabled={approvalAction === 'disapprove' && !approvalComment.trim()}
                className={`px-4 py-2 text-white rounded-md transition-colors ${
                  approvalAction === 'approve'
                    ? 'bg-green-600 hover:bg-green-700 disabled:bg-green-400'
                    : 'bg-red-600 hover:bg-red-700 disabled:bg-red-400'
                } disabled:cursor-not-allowed`}
              >
                {approvalAction === 'approve' ? 'Approve Task' : 'Disapprove Task'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TaskDetailsTab;
