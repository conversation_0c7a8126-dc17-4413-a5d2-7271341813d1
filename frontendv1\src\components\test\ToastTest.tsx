import React from 'react';
import { toast } from 'react-toastify';

const ToastTest: React.FC = () => {
  const testToasts = () => {
    toast.success('Success toast is working!');
    toast.error('Error toast is working!');
    toast.info('Info toast is working!');
    toast.warning('Warning toast is working!');
  };

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Toast Test</h2>
      <button
        onClick={testToasts}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        Test All Toasts
      </button>
    </div>
  );
};

export default ToastTest;
