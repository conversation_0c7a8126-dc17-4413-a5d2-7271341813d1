import React, { useState, useMemo } from 'react';
import { Users, Clock, AlertTriangle, CheckCircle, Wifi, RefreshCw, Download } from 'lucide-react';
import KPICard from '../dashboard/KPICard';
import TimeLogFilters from './TimeLogFilters';
import TimeLogTable from './TimeLogTable';
import EditTimeLogModal from './EditTimeLogModal';
import { TimeLog } from '../../types';
import { TimeLogFilters as Filters, AttendanceSummary, TerminalStatus, EditTimeLogData } from '../../types/time';
import { getCurrentDate, determineWorkerStatus, calculateTotalHours } from '../../utils/timeUtils';
import { getTerminalsForSite, getTimeLogsForDate } from '../../data/timeMock';

interface AttendanceTabProps {
	siteId: string;
}

// Source terminals and logs from centralized mock data

const AttendanceTab: React.FC<AttendanceTabProps> = ({ siteId }) => {
	const [filters, setFilters] = useState<Filters>({
		search: '',
		status: 'all',
		trade: '',
		date: getCurrentDate()
	});

	// Pull terminals for the site from mock data
	const terminals: TerminalStatus[] = useMemo(() => getTerminalsForSite(siteId), [siteId]);

	// Time logs for selected date from processed attendance
	const [timeLogs, setTimeLogs] = useState<TimeLog[]>(getTimeLogsForDate(siteId, getCurrentDate()));

	// Refresh time logs when date or site changes
	React.useEffect(() => {
		setTimeLogs(getTimeLogsForDate(siteId, filters.date));
	}, [siteId, filters.date]);

	// Calculate terminal statistics
	const terminalStats = useMemo(() => {
		const onlineTerminals = terminals.filter(
			(t) => t.status === "online",
		).length;
		const totalTerminals = terminals.length;
		const totalCheckInsToday = terminals.reduce(
			(sum, t) => sum + t.totalCheckInsToday,
			0,
		);

    return {
      onlineTerminals,
      totalTerminals,
      totalCheckInsToday
    };
  }, [terminals]);
  const [selectedTimeLog, setSelectedTimeLog] = useState<TimeLog | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [loading, _setLoading] = useState(false);
//   const [isSyncing, setIsSyncing] = useState(false);
  // No sync modal; Device navigation replaces sync

	// Get unique trades for filter
	const availableTrades = useMemo(() => {
		const trades = [...new Set(timeLogs.map((log) => log.workerTrade))];
		return trades.sort();
	}, [timeLogs]);

	// Filter time logs
	const filteredTimeLogs = useMemo(() => {
		return timeLogs.filter((log) => {
			// Date filter
			if (log.date !== filters.date) return false;

			// Search filter
			if (filters.search) {
				const searchTerm = filters.search.toLowerCase();
				if (
					!log.workerName.toLowerCase().includes(searchTerm) &&
					!log.workerId.toLowerCase().includes(searchTerm) &&
					!log.workerTrade.toLowerCase().includes(searchTerm)
				) {
					return false;
				}
			}

			// Status filter
			if (filters.status !== "all" && log.status !== filters.status) {
				return false;
			}

			// Trade filter
			if (filters.trade && log.workerTrade !== filters.trade) {
				return false;
			}

			return true;
		});
	}, [timeLogs, filters]);

	// Calculate attendance summary
	const attendanceSummary: AttendanceSummary = useMemo(() => {
		const todayLogs = timeLogs.filter((log) => log.date === filters.date);

    return {
      totalWorkers: todayLogs.length,
      presentToday: todayLogs.filter(log => log.status === 'on-site' || log.status === 'late' || log.status === 'off-site').length,
      late: todayLogs.filter(log => log.status === 'late').length,
      absent: todayLogs.filter(log => log.status === 'absent').length,
      onSite: todayLogs.filter(log => log.status === 'on-site').length,
      verifiedByHikvision: todayLogs.filter(log => log.isVerifiedByHikvision === true).length,
      manualEntries: todayLogs.filter(log => log.isVerifiedByHikvision === false || log.isManuallyEdited === true).length
    };
  }, [timeLogs, filters.date]);

	// Calculate active filters count
	const activeFilterCount = useMemo(() => {
		let count = 0;
		if (filters.search) count++;
		if (filters.status !== 'all') count++;
		if (filters.trade) count++;
		return count;
	}, [filters]);

	const handleEditTimeLog = (timeLog: TimeLog) => {
		setSelectedTimeLog(timeLog);
		setIsEditModalOpen(true);
	};

	const handleSaveTimeLog = (timeLogId: string, data: EditTimeLogData) => {
		setTimeLogs((prev) =>
			prev.map((log) => {
				if (log.id === timeLogId) {
					const updatedLog = {
						...log,
						clockIn: data.clockIn || log.clockIn,
						clockOut: data.clockOut || log.clockOut,
						breakDuration: data.breakDuration,
						isManuallyEdited: true,
						editReason: data.reason,
						editedBy: "Current User",
						editedAt: new Date().toISOString(),
					};

					// Recalculate hours and status
					if (updatedLog.clockIn && updatedLog.clockOut) {
						updatedLog.totalHours = calculateTotalHours(
							updatedLog.clockIn,
							updatedLog.clockOut,
							updatedLog.breakDuration || 0,
						);
					}

					updatedLog.status = determineWorkerStatus(
						updatedLog.clockIn,
						updatedLog.clockOut,
					);

					return updatedLog;
				}
				return log;
			}),
		);
	};

  const handleExport = () => {
    console.log('Exporting time logs...', filteredTimeLogs);
    alert('Export functionality would be implemented here');
  };

  const handleOpenDevices = () => {
    // Navigate to devices view within TimeManagement by setting hash
    window.location.hash = '#devices';
  };

	return (
		<>
			<div className="space-y-8">
				{/* KPI Cards */}
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
					<KPICard
						title="Total Workers"
						value={attendanceSummary.totalWorkers}
						icon={<Users className="h-6 w-6" />}
					/>
					<KPICard
						title="Present Today"
						value={attendanceSummary.presentToday}
						icon={<CheckCircle className="h-6 w-6" />}
					/>
					<KPICard
						title="Late Arrivals"
						value={attendanceSummary.late}
						icon={<AlertTriangle className="h-6 w-6" />}
					/>
					<KPICard
						title="Absent"
						value={attendanceSummary.absent}
						icon={<Clock className="h-6 w-6" />}
					/>
					<KPICard
						title="Terminal Status"
						value={`${terminalStats.onlineTerminals}/${terminalStats.totalTerminals} Online`}
						change={terminalStats.totalCheckInsToday}
						icon={<Wifi className="h-6 w-6" />}
					/>
				</div>

				{/* Time Management Interface */}
				<div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
					{/* Filter and Action Bar */}
					<div className="flex flex-col md:flex-row justify-between mb-6 gap-4">
						{/* Search and Filters */}
						<div className="flex flex-1 items-center space-x-4">
							<TimeLogFilters
								filters={filters}
								onFiltersChange={setFilters}
								trades={availableTrades}
								showActions={false}
							/>
						</div>

						{/* Action Buttons */}
						<div className="flex space-x-3">
							<button
								onClick={handleOpenDevices}
								className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
							>
								<RefreshCw className="h-4 w-4 mr-2" />
								Devices
							</button>
							<button
								onClick={handleExport}
								className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
							>
								<Download className="h-4 w-4 mr-2" />
								Export
							</button>
						</div>
					</div>

					{/* Active Filters Display */}
					{activeFilterCount > 0 && (
						<div className="mb-6 flex flex-wrap gap-2">
							{filters.search && (
								<span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
									Search: "{filters.search}"
									<button
										onClick={() => setFilters(prev => ({ ...prev, search: '' }))}
										className="ml-2 text-blue-600 hover:text-blue-800"
									>
										×
									</button>
								</span>
							)}
							{filters.status !== 'all' && (
								<span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
									Status: {filters.status}
									<button
										onClick={() => setFilters(prev => ({ ...prev, status: 'all' }))}
										className="ml-2 text-green-600 hover:text-green-800"
									>
										×
									</button>
								</span>
							)}
							{filters.trade && (
								<span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
									Trade: {filters.trade}
									<button
										onClick={() => setFilters(prev => ({ ...prev, trade: '' }))}
										className="ml-2 text-purple-600 hover:text-purple-800"
									>
										×
									</button>
								</span>
							)}
						</div>
					)}

					{/* Time Logs Table */}
					<div className="bg-white shadow overflow-hidden border border-gray-200 rounded-lg">
						<TimeLogTable
							timeLogs={filteredTimeLogs}
							onEdit={handleEditTimeLog}
							loading={loading}
						/>
					</div>
				</div>
			</div>

      {/* Edit Modal */}
      <EditTimeLogModal
        timeLog={selectedTimeLog}
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedTimeLog(null);
        }}
        onSave={handleSaveTimeLog}
      />

      {/* Sync modal removed; device management is accessible via Devices button */}
    </>
  );
};

export default AttendanceTab;
