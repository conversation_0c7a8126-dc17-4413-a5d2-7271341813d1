import React, { useState, useEffect } from "react";
import { X, AlertTriangle } from "lucide-react";
import { TimeLog } from "../../types";
import { EditTimeLogData } from "../../types/time";
import {
	isValidTimeFormat,
	isValidTimeRange,
	calculateTotalHours,
	formatHours,
} from "../../utils/timeUtils";

interface EditTimeLogModalProps {
	timeLog: TimeLog | null;
	isOpen: boolean;
	onClose: () => void;
	onSave: (timeLogId: string, data: EditTimeLogData) => void;
}

const EditTimeLogModal: React.FC<EditTimeLogModalProps> = ({
	timeLog,
	isOpen,
	onClose,
	onSave,
}) => {
	const [formData, setFormData] = useState<EditTimeLogData>({
		clockIn: "",
		clockOut: "",
		breakDuration: 0,
		reason: "",
	});
	const [errors, setErrors] = useState<Record<string, string>>({});

	useEffect(() => {
		if (timeLog && isOpen) {
			setFormData({
				clockIn: timeLog.clockIn || "",
				clockOut: timeLog.clockOut || "",
				breakDuration: timeLog.breakDuration || 0,
				reason: "",
			});
			setErrors({});
		}
	}, [timeLog, isOpen]);

	const validateForm = (): boolean => {
		const newErrors: Record<string, string> = {};

		if (!formData.reason.trim()) {
			newErrors.reason = "Reason for edit is required";
		}

		if (formData.clockIn && !isValidTimeFormat(formData.clockIn)) {
			newErrors.clockIn = "Invalid time format (use HH:MM)";
		}

		if (formData.clockOut && !isValidTimeFormat(formData.clockOut)) {
			newErrors.clockOut = "Invalid time format (use HH:MM)";
		}

		if (
			formData.clockIn &&
			formData.clockOut &&
			!isValidTimeRange(formData.clockIn, formData.clockOut)
		) {
			newErrors.clockOut = "Clock out time must be after clock in time";
		}

		if (
			formData.breakDuration &&
			(formData.breakDuration < 0 || formData.breakDuration > 480)
		) {
			newErrors.breakDuration =
				"Break duration must be between 0 and 480 minutes";
		}

		setErrors(newErrors);
		return Object.keys(newErrors).length === 0;
	};

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();

		if (!timeLog || !validateForm()) return;

		onSave(timeLog.id, formData);
		onClose();
	};

	const handleInputChange = (
		field: keyof EditTimeLogData,
		value: string | number,
	) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));

		// Clear error when user starts typing
		if (errors[field]) {
			setErrors((prev) => ({
				...prev,
				[field]: "",
			}));
		}
	};

	const calculatePreviewHours = (): string => {
		if (formData.clockIn && formData.clockOut) {
			const totalHours = calculateTotalHours(
				formData.clockIn,
				formData.clockOut,
				formData.breakDuration || 0,
			);
			return formatHours(totalHours);
		}
		return "-";
	};

	if (!isOpen || !timeLog) return null;

	return (
		<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
			<div className="bg-white rounded-lg max-w-md w-full max-h-[90vh] overflow-y-auto">
				{/* Header */}
				<div className="flex items-center justify-between p-6 border-b border-gray-200">
					<div>
						<h3 className="text-lg font-medium text-gray-900">Edit Time Log</h3>
						<p className="text-sm text-gray-500 mt-1">
							{timeLog.workerName} - {timeLog.date}
						</p>
					</div>
					<button
						onClick={onClose}
						className="text-gray-400 hover:text-gray-600 transition-colors"
					>
						<X className="h-6 w-6" />
					</button>
				</div>

				{/* Form */}
				<form onSubmit={handleSubmit} className="p-6 space-y-4">
					{/* Clock In */}
					<div>
						<label className="block text-sm font-medium text-gray-700 mb-1">
							Clock In Time
						</label>
						<input
							type="time"
							value={formData.clockIn}
							onChange={(e) => handleInputChange("clockIn", e.target.value)}
							className={`w-full border rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500 ${
								errors.clockIn ? "border-red-300" : "border-gray-300"
							}`}
						/>
						{errors.clockIn && (
							<p className="text-red-600 text-xs mt-1">{errors.clockIn}</p>
						)}
					</div>

					{/* Clock Out */}
					<div>
						<label className="block text-sm font-medium text-gray-700 mb-1">
							Clock Out Time
						</label>
						<input
							type="time"
							value={formData.clockOut}
							onChange={(e) => handleInputChange("clockOut", e.target.value)}
							className={`w-full border rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500 ${
								errors.clockOut ? "border-red-300" : "border-gray-300"
							}`}
						/>
						{errors.clockOut && (
							<p className="text-red-600 text-xs mt-1">{errors.clockOut}</p>
						)}
					</div>

					{/* Break Duration */}
					<div>
						<label className="block text-sm font-medium text-gray-700 mb-1">
							Break Duration (minutes)
						</label>
						<input
							type="number"
							min="0"
							max="480"
							value={formData.breakDuration}
							onChange={(e) =>
								handleInputChange(
									"breakDuration",
									parseInt(e.target.value) || 0,
								)
							}
							className={`w-full border rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500 ${
								errors.breakDuration ? "border-red-300" : "border-gray-300"
							}`}
						/>
						{errors.breakDuration && (
							<p className="text-red-600 text-xs mt-1">
								{errors.breakDuration}
							</p>
						)}
					</div>

					{/* Preview Total Hours */}
					<div className="bg-gray-50 rounded-md p-3">
						<div className="flex items-center justify-between">
							<span className="text-sm font-medium text-gray-700">
								Total Hours:
							</span>
							<span className="text-sm font-semibold text-gray-900">
								{calculatePreviewHours()}
							</span>
						</div>
					</div>

					{/* Reason */}
					<div>
						<label className="block text-sm font-medium text-gray-700 mb-1">
							Reason for Edit <span className="text-red-500">*</span>
						</label>
						<textarea
							value={formData.reason}
							onChange={(e) => handleInputChange("reason", e.target.value)}
							placeholder="Please provide a reason for this manual edit..."
							rows={3}
							className={`w-full border rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500 ${
								errors.reason ? "border-red-300" : "border-gray-300"
							}`}
						/>
						{errors.reason && (
							<p className="text-red-600 text-xs mt-1">{errors.reason}</p>
						)}
					</div>

					{/* Warning */}
					<div className="bg-yellow-50 border border-yellow-200 rounded-md p-3">
						<div className="flex">
							<AlertTriangle className="h-5 w-5 text-yellow-400 flex-shrink-0" />
							<div className="ml-3">
								<p className="text-sm text-yellow-800">
									This edit will be logged for audit purposes and marked as
									manually edited.
								</p>
							</div>
						</div>
					</div>

					{/* Actions */}
					<div className="flex justify-end space-x-3 pt-4">
						<button
							type="button"
							onClick={onClose}
							className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors"
						>
							Cancel
						</button>
						<button
							type="submit"
							className="px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700 transition-colors"
						>
							Save Changes
						</button>
					</div>
				</form>
			</div>
		</div>
	);
};

export default EditTimeLogModal;
