import React, { useState, useMemo } from "react";
import {
	Clock,
	CheckCircle,
	XCircle,
	AlertTriangle,
	Plus,
	Search,
	Filter,
} from "lucide-react";
import { OvertimeRequest } from "../../types/time";

interface OvertimeTabProps {
	siteId: string;
}

// Mock data for overtime requests
const mockOvertimeRequests: OvertimeRequest[] = [
	{
		id: "ot1",
		workerId: "W001",
		workerName: "<PERSON>",
		date: "2025-01-15",
		requestedHours: 2,
		reason: "Critical foundation work completion",
		status: "pending",
		requestedBy: "<PERSON>",
		requestedAt: "2025-01-14T16:30:00Z",
	},
	{
		id: "ot2",
		workerId: "W002",
		workerName: "<PERSON>",
		date: "2025-01-14",
		requestedHours: 3,
		reason: "Electrical installation deadline",
		status: "approved",
		requestedBy: "<PERSON>",
		requestedAt: "2025-01-13T14:20:00Z",
		reviewedBy: "Site Supervisor",
		reviewedAt: "2025-01-13T15:45:00Z",
		reviewNotes: "Approved due to project deadline",
	},
	{
		id: "ot3",
		workerId: "W003",
		workerName: "<PERSON>",
		date: "2025-01-12",
		requestedHours: 4,
		reason: "Weekend work for schedule recovery",
		status: "rejected",
		requestedBy: "Peter Ochieng",
		requestedAt: "2025-01-11T10:15:00Z",
		reviewedBy: "Site Supervisor",
		reviewedAt: "2025-01-11T11:30:00Z",
		reviewNotes: "Insufficient justification for 4-hour overtime",
	},
];

const OvertimeTab: React.FC<OvertimeTabProps> = ({ siteId:_siteId }) => {
	const [requests, setRequests] =
		useState<OvertimeRequest[]>(mockOvertimeRequests);
	const [searchTerm, setSearchTerm] = useState("");
	const [statusFilter, setStatusFilter] = useState<
		"all" | "pending" | "approved" | "rejected"
	>("all");

	// Filter requests
	const filteredRequests = useMemo(() => {
		return requests.filter((request) => {
			// Search filter
			if (searchTerm) {
				const searchLower = searchTerm.toLowerCase();
				if (
					!request.workerName.toLowerCase().includes(searchLower) &&
					!request.reason.toLowerCase().includes(searchLower)
				) {
					return false;
				}
			}

			// Status filter
			if (statusFilter !== "all" && request.status !== statusFilter) {
				return false;
			}

			return true;
		});
	}, [requests, searchTerm, statusFilter]);

	const getStatusBadge = (status: OvertimeRequest["status"]) => {
		const statusConfig = {
			pending: {
				icon: Clock,
				className: "bg-yellow-100 text-yellow-800",
				label: "Pending",
			},
			approved: {
				icon: CheckCircle,
				className: "bg-green-100 text-green-800",
				label: "Approved",
			},
			rejected: {
				icon: XCircle,
				className: "bg-red-100 text-red-800",
				label: "Rejected",
			},
		};

		const config = statusConfig[status];
		const Icon = config.icon;

		return (
			<span
				className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.className}`}
			>
				<Icon className="h-3 w-3 mr-1" />
				{config.label}
			</span>
		);
	};

	const handleApprove = (requestId: string) => {
		setRequests((prev) =>
			prev.map((req) =>
				req.id === requestId
					? {
							...req,
							status: "approved",
							reviewedBy: "Current User",
							reviewedAt: new Date().toISOString(),
							reviewNotes: "Approved",
						}
					: req,
			),
		);
	};

	const handleReject = (requestId: string, reason: string) => {
		setRequests((prev) =>
			prev.map((req) =>
				req.id === requestId
					? {
							...req,
							status: "rejected",
							reviewedBy: "Current User",
							reviewedAt: new Date().toISOString(),
							reviewNotes: reason,
						}
					: req,
			),
		);
	};

	const formatDate = (dateStr: string) => {
		return new Date(dateStr).toLocaleDateString("en-GB", {
			day: "2-digit",
			month: "short",
			year: "numeric",
		});
	};

	const formatDateTime = (dateStr: string) => {
		return new Date(dateStr).toLocaleString("en-GB", {
			day: "2-digit",
			month: "short",
			year: "numeric",
			hour: "2-digit",
			minute: "2-digit",
		});
	};

	// Calculate summary stats
	const summaryStats = useMemo(() => {
		return {
			total: requests.length,
			pending: requests.filter((r) => r.status === "pending").length,
			approved: requests.filter((r) => r.status === "approved").length,
			rejected: requests.filter((r) => r.status === "rejected").length,
		};
	}, [requests]);

	return (
		<div className="space-y-6">
			{/* Summary Cards */}
			<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
				<div className="bg-white p-4 rounded-lg border border-gray-200">
					<div className="flex items-center">
						<div className="flex-shrink-0">
							<Clock className="h-8 w-8 text-blue-600" />
						</div>
						<div className="ml-4">
							<p className="text-sm font-medium text-gray-500">
								Total Requests
							</p>
							<p className="text-2xl font-semibold text-gray-900">
								{summaryStats.total}
							</p>
						</div>
					</div>
				</div>

				<div className="bg-white p-4 rounded-lg border border-gray-200">
					<div className="flex items-center">
						<div className="flex-shrink-0">
							<AlertTriangle className="h-8 w-8 text-yellow-600" />
						</div>
						<div className="ml-4">
							<p className="text-sm font-medium text-gray-500">Pending</p>
							<p className="text-2xl font-semibold text-gray-900">
								{summaryStats.pending}
							</p>
						</div>
					</div>
				</div>

				<div className="bg-white p-4 rounded-lg border border-gray-200">
					<div className="flex items-center">
						<div className="flex-shrink-0">
							<CheckCircle className="h-8 w-8 text-green-600" />
						</div>
						<div className="ml-4">
							<p className="text-sm font-medium text-gray-500">Approved</p>
							<p className="text-2xl font-semibold text-gray-900">
								{summaryStats.approved}
							</p>
						</div>
					</div>
				</div>

				<div className="bg-white p-4 rounded-lg border border-gray-200">
					<div className="flex items-center">
						<div className="flex-shrink-0">
							<XCircle className="h-8 w-8 text-red-600" />
						</div>
						<div className="ml-4">
							<p className="text-sm font-medium text-gray-500">Rejected</p>
							<p className="text-2xl font-semibold text-gray-900">
								{summaryStats.rejected}
							</p>
						</div>
					</div>
				</div>
			</div>

			{/* Header with Actions */}
			<div className="flex justify-between items-center">
				<h2 className="text-xl font-semibold text-gray-900">
					Overtime Requests
				</h2>
				<button className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700 transition-colors">
					<Plus className="h-4 w-4 mr-2" />
					New Request
				</button>
			</div>

			{/* Filters */}
			<div className="bg-white rounded-lg border border-gray-200 p-4">
				<div className="flex flex-col sm:flex-row gap-4">
					<div className="flex-1 relative">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
						<input
							type="text"
							placeholder="Search by worker name or reason..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:ring-green-500 focus:border-green-500"
						/>
					</div>
					<div className="flex items-center gap-2">
						<Filter className="h-4 w-4 text-gray-400" />
						<select
							value={statusFilter}
							onChange={(e) => setStatusFilter(e.target.value as any)}
							className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
						>
							<option value="all">All Status</option>
							<option value="pending">Pending</option>
							<option value="approved">Approved</option>
							<option value="rejected">Rejected</option>
						</select>
					</div>
				</div>
			</div>

			{/* Requests Table */}
			<div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
				<div className="overflow-x-auto">
					<table className="min-w-full divide-y divide-gray-200">
						<thead className="bg-gray-50">
							<tr>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Worker
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Date
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Hours
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Reason
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Status
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Requested
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Actions
								</th>
							</tr>
						</thead>
						<tbody className="bg-white divide-y divide-gray-200">
							{filteredRequests.map((request) => (
								<tr key={request.id} className="hover:bg-gray-50">
									<td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
										{request.workerName}
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
										{formatDate(request.date)}
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
										{request.requestedHours}h
									</td>
									<td className="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
										{request.reason}
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										{getStatusBadge(request.status)}
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
										{formatDateTime(request.requestedAt)}
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
										{request.status === "pending" && (
											<div className="flex space-x-2">
												<button
													onClick={() => handleApprove(request.id)}
													className="text-green-600 hover:text-green-900"
												>
													Approve
												</button>
												<button
													onClick={() =>
														handleReject(request.id, "Rejected by supervisor")
													}
													className="text-red-600 hover:text-red-900"
												>
													Reject
												</button>
											</div>
										)}
										{request.status !== "pending" && (
											<span className="text-gray-400">-</span>
										)}
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>
			</div>

			{filteredRequests.length === 0 && (
				<div className="bg-white rounded-lg border border-gray-200 p-12 text-center">
					<Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
					<h3 className="text-lg font-medium text-gray-900 mb-2">
						No Overtime Requests
					</h3>
					<p className="text-gray-500">
						No overtime requests match your current filters.
					</p>
				</div>
			)}
		</div>
	);
};

export default OvertimeTab;
