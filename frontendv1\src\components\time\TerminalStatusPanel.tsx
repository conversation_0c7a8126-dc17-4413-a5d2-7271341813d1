import React from "react";
import {
	Wifi,
	WifiOff,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON>,
	MapPin,
	Activity,
} from "lucide-react";
import { TerminalStatus } from "../../types/time";

interface TerminalStatusPanelProps {
	terminals: TerminalStatus[];
	totalCheckInsToday: number;
}

const TerminalStatusPanel: React.FC<TerminalStatusPanelProps> = ({
	terminals,
	totalCheckInsToday,
}) => {
	const getStatusIcon = (status: TerminalStatus["status"]) => {
		switch (status) {
			case "online":
				return <Wifi className="h-5 w-5 text-green-500" />;
			case "offline":
				return <WifiOff className="h-5 w-5 text-gray-400" />;
			case "error":
				return <AlertTriangle className="h-5 w-5 text-red-500" />;
			default:
				return <WifiOff className="h-5 w-5 text-gray-400" />;
		}
	};

	const getStatusBadge = (status: TerminalStatus["status"]) => {
		const statusConfig = {
			online: {
				className: "bg-green-100 text-green-800",
				label: "Online",
			},
			offline: {
				className: "bg-gray-100 text-gray-800",
				label: "Offline",
			},
			error: {
				className: "bg-red-100 text-red-800",
				label: "Error",
			},
		};

		const config = statusConfig[status];
		return (
			<span
				className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.className}`}
			>
				{config.label}
			</span>
		);
	};

	const formatLastSync = (lastSync?: string) => {
		if (!lastSync) return "Never";

		const syncDate = new Date(lastSync);
		const now = new Date();
		const diffMinutes = Math.floor(
			(now.getTime() - syncDate.getTime()) / (1000 * 60),
		);

		if (diffMinutes < 1) return "Just now";
		if (diffMinutes < 60) return `${diffMinutes}m ago`;
		if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)}h ago`;
		return syncDate.toLocaleDateString();
	};

	const onlineTerminals = terminals.filter((t) => t.status === "online").length;
	const totalTerminals = terminals.length;

	return (
		<div className="bg-white rounded-lg border border-gray-200 p-6">
			<div className="flex items-center justify-between mb-6">
				<h3 className="text-lg font-semibold text-gray-800">Terminal Status</h3>
				<div className="flex items-center space-x-4 text-sm text-gray-600">
					<div className="flex items-center">
						<Activity className="h-4 w-4 mr-1" />
						<span>
							{onlineTerminals}/{totalTerminals} Online
						</span>
					</div>
					<div className="flex items-center">
						<Clock className="h-4 w-4 mr-1" />
						<span>{totalCheckInsToday} Check-ins Today</span>
					</div>
				</div>
			</div>

			{terminals.length === 0 ? (
				<div className="text-center py-8">
					<WifiOff className="h-12 w-12 text-gray-400 mx-auto mb-4" />
					<h4 className="text-lg font-medium text-gray-900 mb-2">
						No Terminals Configured
					</h4>
					<p className="text-gray-500">
						No Hikvision terminals are configured for this site.
					</p>
				</div>
			) : (
				<div className="space-y-4">
					{terminals.map((terminal) => (
						<div
							key={terminal.id}
							className="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
						>
							<div className="flex items-center space-x-4">
								<div className="flex-shrink-0">
									{getStatusIcon(terminal.status)}
								</div>
								<div className="flex-1 min-w-0">
									<div className="flex items-center space-x-2">
										<h4 className="text-sm font-medium text-gray-900 truncate">
											{terminal.name}
										</h4>
										{getStatusBadge(terminal.status)}
									</div>
									<div className="flex items-center mt-1 text-xs text-gray-500">
										<MapPin className="h-3 w-3 mr-1" />
										<span className="truncate">{terminal.location}</span>
										{terminal.ipAddress && (
											<>
												<span className="mx-2">•</span>
												<span>{terminal.ipAddress}</span>
											</>
										)}
									</div>
								</div>
							</div>

							<div className="flex flex-col items-end text-right">
								<div className="text-sm font-medium text-gray-900">
									{terminal.totalCheckInsToday} check-ins
								</div>
								<div className="text-xs text-gray-500">
									Last sync: {formatLastSync(terminal.lastSync)}
								</div>
							</div>
						</div>
					))}
				</div>
			)}

			{/* Summary Footer */}
			<div className="mt-6 pt-4 border-t border-gray-200">
				<div className="grid grid-cols-3 gap-4 text-center">
					<div>
						<div className="text-2xl font-bold text-green-600">
							{onlineTerminals}
						</div>
						<div className="text-xs text-gray-500">Online</div>
					</div>
					<div>
						<div className="text-2xl font-bold text-gray-600">
							{totalTerminals - onlineTerminals}
						</div>
						<div className="text-xs text-gray-500">Offline</div>
					</div>
					<div>
						<div className="text-2xl font-bold text-blue-600">
							{totalCheckInsToday}
						</div>
						<div className="text-xs text-gray-500">Today's Check-ins</div>
					</div>
				</div>
			</div>
		</div>
	);
};

export default TerminalStatusPanel;
