import React, { useState, useRef, useCallback } from 'react';
import { Camera, X, RotateCcw, Download, Trash2 } from 'lucide-react';

interface PhotoCaptureComponentProps {
  onPhotoCapture: (file: File) => void;
  onPhotoRemove?: (index: number) => void;
  capturedPhotos?: File[];
  maxPhotos?: number;
  className?: string;
}

const PhotoCaptureComponent: React.FC<PhotoCaptureComponentProps> = ({
  onPhotoCapture,
  onPhotoRemove,
  capturedPhotos = [],
  maxPhotos = 10,
  className = ''
}) => {
  const [isCapturing, setIsCapturing] = useState(false);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [facingMode, setFacingMode] = useState<'user' | 'environment'>('environment');
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  const startCamera = useCallback(async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: facingMode,
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      });
      
      setStream(mediaStream);
      setIsCapturing(true);
      
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
      }
    } catch (error) {
      console.error('Error accessing camera:', error);
      alert('Unable to access camera. Please check permissions.');
    }
  }, [facingMode]);

  const stopCamera = useCallback(() => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
    setIsCapturing(false);
  }, [stream]);

  const switchCamera = useCallback(() => {
    setFacingMode(prev => prev === 'user' ? 'environment' : 'user');
    if (isCapturing) {
      stopCamera();
      // Restart with new facing mode
      setTimeout(() => {
        startCamera();
      }, 100);
    }
  }, [isCapturing, stopCamera, startCamera]);

  const capturePhoto = useCallback(() => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    if (!context) return;

    // Set canvas dimensions to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw the video frame to canvas
    context.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Convert canvas to blob
    canvas.toBlob((blob) => {
      if (blob) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const file = new File([blob], `toolbox-photo-${timestamp}.jpg`, {
          type: 'image/jpeg'
        });
        onPhotoCapture(file);
      }
    }, 'image/jpeg', 0.8);

    stopCamera();
  }, [onPhotoCapture, stopCamera]);

  const createImageUrl = (file: File) => {
    return URL.createObjectURL(file);
  };

  const downloadPhoto = (file: File) => {
    const url = createImageUrl(file);
    const a = document.createElement('a');
    a.href = url;
    a.download = file.name;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Camera Controls */}
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">
          Attendance Photos ({capturedPhotos.length}/{maxPhotos})
        </h3>
        
        {!isCapturing && capturedPhotos.length < maxPhotos && (
          <button
            onClick={startCamera}
            className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
          >
            <Camera className="h-4 w-4" />
            <span>Take Photo</span>
          </button>
        )}
      </div>

      {/* Camera View */}
      {isCapturing && (
        <div className="relative bg-black rounded-lg overflow-hidden">
          <video
            ref={videoRef}
            autoPlay
            playsInline
            muted
            className="w-full h-64 object-cover"
          />
          
          {/* Camera Controls Overlay */}
          <div className="absolute bottom-4 left-0 right-0 flex items-center justify-center space-x-4">
            <button
              onClick={switchCamera}
              className="bg-white bg-opacity-20 text-white p-3 rounded-full hover:bg-opacity-30"
              title="Switch Camera"
            >
              <RotateCcw className="h-5 w-5" />
            </button>
            
            <button
              onClick={capturePhoto}
              className="bg-white text-gray-900 p-4 rounded-full hover:bg-gray-100"
              title="Capture Photo"
            >
              <Camera className="h-6 w-6" />
            </button>
            
            <button
              onClick={stopCamera}
              className="bg-white bg-opacity-20 text-white p-3 rounded-full hover:bg-opacity-30"
              title="Cancel"
            >
              <X className="h-5 w-5" />
            </button>
          </div>
        </div>
      )}

      {/* Hidden canvas for photo capture */}
      <canvas ref={canvasRef} className="hidden" />

      {/* Captured Photos Grid */}
      {capturedPhotos.length > 0 && (
        <div className="space-y-3">
          <h4 className="text-md font-medium text-gray-700">Captured Photos</h4>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {capturedPhotos.map((photo, index) => (
              <div key={index} className="relative group">
                <img
                  src={createImageUrl(photo)}
                  alt={`Captured photo ${index + 1}`}
                  className="w-full h-32 object-cover rounded-lg border border-gray-200"
                />
                
                {/* Photo Actions Overlay */}
                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-200 rounded-lg flex items-center justify-center space-x-2 opacity-0 group-hover:opacity-100">
                  <button
                    onClick={() => downloadPhoto(photo)}
                    className="bg-white text-gray-900 p-2 rounded-full hover:bg-gray-100"
                    title="Download"
                  >
                    <Download className="h-4 w-4" />
                  </button>
                  
                  {onPhotoRemove && (
                    <button
                      onClick={() => onPhotoRemove(index)}
                      className="bg-red-600 text-white p-2 rounded-full hover:bg-red-700"
                      title="Remove"
                    >
                      <Trash2 className="h-4 w-4" />
                    </button>
                  )}
                </div>
                
                {/* Photo Info */}
                <div className="absolute bottom-2 left-2 right-2">
                  <div className="bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">
                    {photo.name}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Instructions */}
      {capturedPhotos.length === 0 && !isCapturing && (
        <div className="text-center py-8 bg-gray-50 rounded-lg">
          <Camera className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p className="text-gray-600">No photos captured yet</p>
          <p className="text-gray-500 text-sm mt-1">
            Take photos of attendees for the toolbox meeting
          </p>
        </div>
      )}

      {/* Max Photos Warning */}
      {capturedPhotos.length >= maxPhotos && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <p className="text-yellow-800 text-sm">
            Maximum number of photos ({maxPhotos}) reached. Remove some photos to take more.
          </p>
        </div>
      )}
    </div>
  );
};

export default PhotoCaptureComponent;
