import React, { useState } from 'react';
import { Users, Search, CheckCircle, AlertTriangle } from 'lucide-react';
import { mockInductions, mockWorkers } from '../../mock/siteData';

interface ToolboxAttendanceProps {
  siteId: string;
  selectedInduction: string | null;
}

const ToolboxAttendance: React.FC<ToolboxAttendanceProps> = ({ selectedInduction }) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [attendanceMap, setAttendanceMap] = useState<Record<string, boolean>>(
    mockWorkers.reduce((acc, worker) => ({
      ...acc,
      [worker.id]: worker.attended
    }), {})
  );

  const induction = selectedInduction 
    ? mockInductions.find(ind => ind.id === selectedInduction) 
    : null;

  if (!selectedInduction || !induction) {
    return (
      <div className="text-center py-12">
        <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No Induction Selected</h3>
        <p className="text-gray-500">Please select an induction from the Inductions tab to manage attendance.</p>
      </div>
    );
  }

  const handleAttendanceChange = (workerId: string, value: boolean) => {
    setAttendanceMap(prev => ({
      ...prev,
      [workerId]: value
    }));
  };

  const handleMarkAll = (value: boolean) => {
    const newMap = { ...attendanceMap };
    mockWorkers.forEach(worker => {
      newMap[worker.id] = value;
    });
    setAttendanceMap(newMap);
  };

  const handleSaveAttendance = () => {
    // In a real app, this would send data to the backend
    console.log('Saving attendance for induction:', selectedInduction);
    console.log('Attendance data:', attendanceMap);
    alert('Attendance saved successfully!');
  };

  const filteredWorkers = mockWorkers.filter(worker => 
    worker.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    worker.trade.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const presentCount = Object.values(attendanceMap).filter(Boolean).length;
  const totalCount = mockWorkers.length;
  const attendancePercentage = totalCount > 0 ? Math.round((presentCount / totalCount) * 100) : 0;

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
        <div>
          <h2 className="text-2xl font-bold">{induction.title}</h2>
          <p className="text-gray-500">Manage worker attendance for this induction</p>
        </div>
        <div className="flex flex-col sm:flex-row gap-2">
          <button 
            onClick={() => handleMarkAll(true)}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 flex items-center justify-center"
          >
            <CheckCircle className="h-4 w-4 mr-2" />
            Mark All Present
          </button>
          <button 
            onClick={handleSaveAttendance}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Save Attendance
          </button>
        </div>
      </div>
      
      {/* Attendance Summary */}
      <div className="bg-white p-4 rounded-lg border border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-blue-500 mr-3" />
            <div>
              <p className="text-sm text-gray-500">Total Workers</p>
              <p className="text-xl font-semibold">{totalCount}</p>
            </div>
          </div>
          <div className="flex items-center">
            <CheckCircle className="h-8 w-8 text-green-500 mr-3" />
            <div>
              <p className="text-sm text-gray-500">Present</p>
              <p className="text-xl font-semibold">{presentCount}</p>
            </div>
          </div>
          <div className="flex items-center">
            <AlertTriangle className="h-8 w-8 text-orange-500 mr-3" />
            <div>
              <p className="text-sm text-gray-500">Absent</p>
              <p className="text-xl font-semibold">{totalCount - presentCount}</p>
            </div>
          </div>
        </div>
        
        <div className="mt-4">
          <div className="flex justify-between mb-1">
            <span className="text-sm">Attendance Rate</span>
            <span className="text-sm font-medium">{attendancePercentage}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2.5">
            <div 
              className={`h-2.5 rounded-full ${
                attendancePercentage >= 80 ? 'bg-green-600' : 
                attendancePercentage >= 50 ? 'bg-yellow-500' : 
                'bg-red-500'
              }`} 
              style={{ width: `${attendancePercentage}%` }}
            ></div>
          </div>
        </div>
      </div>
      
      {/* Worker List */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center">
            <Search className="h-5 w-5 text-gray-400 mr-2" />
            <input
              type="text"
              placeholder="Search workers by name or trade..."
              className="w-full border-none focus:ring-0 text-sm"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
        
        {filteredWorkers.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            <Users className="h-8 w-8 mx-auto mb-2" />
            <p>No workers match your search criteria</p>
          </div>
        ) : (
          <ul className="divide-y divide-gray-200">
            {filteredWorkers.map(worker => (
              <li key={worker.id} className="p-4 hover:bg-gray-50">
                <div className="flex justify-between items-center">
                  <div>
                    <span className="font-medium">{worker.name}</span>
                    <p className="text-sm text-gray-500">{worker.trade}</p>
                  </div>
                  <div className="flex items-center space-x-4">
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      attendanceMap[worker.id] ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {attendanceMap[worker.id] ? 'Present' : 'Absent'}
                    </span>
                    <label className="inline-flex items-center">
                      <input 
                        type="checkbox" 
                        checked={attendanceMap[worker.id] || false}
                        onChange={(e) => handleAttendanceChange(worker.id, e.target.checked)}
                        className="h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                    </label>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>
      
      {/* Action Buttons - Bottom */}
      <div className="flex justify-end space-x-2">
        <button 
          onClick={() => handleMarkAll(false)}
          className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
        >
          Mark All Absent
        </button>
        <button 
          onClick={handleSaveAttendance}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
        >
          Save Attendance
        </button>
      </div>
    </div>
  );
};

export default ToolboxAttendance;