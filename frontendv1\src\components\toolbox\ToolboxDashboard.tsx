import React from 'react';
import { mockInductions, mockTasks } from '../../mock/siteData';

interface ToolboxDashboardProps {
  siteId: string;
  onNavigateToTab: (tabId: string) => void;
}

const ToolboxDashboard: React.FC<ToolboxDashboardProps> = ({ onNavigateToTab }) => {
  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Toolbox Talks Dashboard</h2>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
          <h3 className="font-medium mb-2">Recent Inductions</h3>
          <div className="space-y-2">
            {mockInductions.slice(0, 3).map(induction => (
              <div key={induction.id} className="flex justify-between items-center p-2 hover:bg-gray-50 rounded">
                <span>{induction.title}</span>
                <span className={`text-xs px-2 py-1 rounded-full ${induction.completed ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}`}>
                  {induction.completed ? 'Completed' : 'Pending'}
                </span>
              </div>
            ))}
          </div>
          <button 
            className="mt-4 text-sm text-blue-600 hover:text-blue-800"
            onClick={() => onNavigateToTab('inductions')}
          >
            View all inductions →
          </button>
        </div>
        
        <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
          <h3 className="font-medium mb-2">Upcoming Tasks</h3>
          <div className="space-y-2">
            {mockTasks.slice(0, 3).map(task => (
              <div key={task.id} className="flex justify-between items-center p-2 hover:bg-gray-50 rounded">
                <span>{task.title}</span>
                <span className={`text-xs px-2 py-1 rounded-full ${
                  task.status === 'completed' ? 'bg-green-100 text-green-800' : 
                  task.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :
                  task.status === 'blocked' ? 'bg-red-100 text-red-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {task.status.replace('-', ' ')}
                </span>
              </div>
            ))}
          </div>
          <button 
            className="mt-4 text-sm text-blue-600 hover:text-blue-800"
            onClick={() => onNavigateToTab('tasks')}
          >
            View all tasks →
          </button>
        </div>
        
        <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm">
          <h3 className="font-medium mb-2">Attendance Stats</h3>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm">Today's Attendance</span>
                <span className="text-sm font-medium">75%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-blue-600 h-2 rounded-full" style={{ width: '75%' }}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm">Weekly Average</span>
                <span className="text-sm font-medium">82%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-green-600 h-2 rounded-full" style={{ width: '82%' }}></div>
              </div>
            </div>
            <div>
              <div className="flex justify-between mb-1">
                <span className="text-sm">Monthly Average</span>
                <span className="text-sm font-medium">88%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div className="bg-green-600 h-2 rounded-full" style={{ width: '88%' }}></div>
              </div>
            </div>
          </div>
          <button 
            className="mt-4 text-sm text-blue-600 hover:text-blue-800"
            onClick={() => onNavigateToTab('attendance')}
          >
            View attendance records →
          </button>
        </div>
      </div>
    </div>
  );
};

export default ToolboxDashboard;