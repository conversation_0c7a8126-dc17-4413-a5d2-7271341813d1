import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@apollo/client';
import {
  FileText,
  Users,
  BarChart3,
  Plus,
  Clock,
  CheckCircle,
  AlertTriangle,
  Calendar
} from 'lucide-react';
import { GET_TODAYS_TOOLBOX, GET_ALL_TOOLBOXES } from '../../graphql/queries';
import { Toolbox } from '../../types/toolbox';

interface ToolboxManagementProps {
  siteId: string;
}

const ToolboxManagement: React.FC<ToolboxManagementProps> = ({ siteId }) => {
  const navigate = useNavigate();
  const { data: todaysToolboxData, loading: todaysLoading } = useQuery(GET_TODAYS_TOOLBOX);
  const { data: allToolboxesData, loading: allLoading } = useQuery(GET_ALL_TOOLBOXES, {
    variables: {
      where: {
        // Add any filters if needed
      }
    }
  });

  const todaysToolbox: Toolbox | null = todaysToolboxData?.todaysToolbox;
  const allToolboxes: Toolbox[] = allToolboxesData?.allToolboxes || [];

  const handleStartNewToolbox = () => {
    if (siteId) {
      navigate(`/sites/${siteId}/toolbox/fill`);
    } else {
      navigate('/toolbox/fill');
    }
  };

  const handleContinueToolbox = (toolboxId: number) => {
    const basePath = siteId ? `/sites/${siteId}` : '';
    navigate(`${basePath}/toolbox/attendance?toolboxId=${toolboxId}`);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'OPEN':
        return 'text-green-600 bg-green-100';
      case 'CLOSED':
        return 'text-gray-600 bg-gray-100';
      case 'DRAFT':
        return 'text-yellow-600 bg-yellow-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'OPEN':
        return <CheckCircle className="h-4 w-4" />;
      case 'CLOSED':
        return <CheckCircle className="h-4 w-4" />;
      case 'DRAFT':
        return <Clock className="h-4 w-4" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  if (todaysLoading || allLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Today's Toolbox Section */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold mb-4 flex items-center">
          <Calendar className="h-5 w-5 mr-2 text-blue-600" />
          Today's Toolbox
        </h3>

        {todaysToolbox ? (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center mb-2">
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(todaysToolbox.status)}`}>
                    {getStatusIcon(todaysToolbox.status)}
                    <span className="ml-1">{todaysToolbox.status}</span>
                  </span>
                  <span className="ml-3 text-sm text-gray-600">
                    {new Date(todaysToolbox.date).toLocaleDateString()}
                  </span>
                </div>
                <p className="text-sm text-gray-700">
                  Conductor: {todaysToolbox.conductor?.name || 'Not assigned'}
                </p>
                <p className="text-sm text-gray-700">
                  Attendees: {todaysToolbox.attendees?.length || 0} workers
                </p>
                <p className="text-sm text-gray-700">
                  Jobs: {todaysToolbox.jobs?.length || 0}
                </p>
              </div>
              <div className="flex space-x-2">
                {todaysToolbox.status === 'DRAFT' && (
                  <button
                    onClick={() => handleContinueToolbox(todaysToolbox.id)}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
                  >
                    Continue
                  </button>
                )}
                {todaysToolbox.status === 'OPEN' && (
                  <button
                    onClick={() => {
                      const basePath = siteId ? `/sites/${siteId}` : '';
                      navigate(`${basePath}/toolbox/summarize?toolboxId=${todaysToolbox.id}`);
                    }}
                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm"
                  >
                    Summarize
                  </button>
                )}
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-8 bg-gray-50 rounded-lg">
            <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p className="text-gray-600 mb-4">No toolbox meeting scheduled for today</p>
            <button
              onClick={handleStartNewToolbox}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              <Plus className="h-4 w-4 mr-2" />
              Start New Toolbox
            </button>
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <button
          onClick={handleStartNewToolbox}
          className="p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 text-left"
        >
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <FileText className="h-6 w-6 text-blue-600" />
            </div>
            <div className="ml-3">
              <p className="font-medium text-gray-900">Fill Toolbox</p>
              <p className="text-sm text-gray-500">Create new toolbox meeting</p>
            </div>
          </div>
        </button>

        <button
          onClick={() => {
            const basePath = siteId ? `/sites/${siteId}` : '';
            navigate(`${basePath}/toolbox/attendance`);
          }}
          className="p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 text-left"
        >
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <Users className="h-6 w-6 text-green-600" />
            </div>
            <div className="ml-3">
              <p className="font-medium text-gray-900">Attendance</p>
              <p className="text-sm text-gray-500">Manage worker attendance</p>
            </div>
          </div>
        </button>

        <button
          onClick={() => {
            const basePath = siteId ? `/sites/${siteId}` : '';
            navigate(`${basePath}/toolbox/summarize`);
          }}
          className="p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 text-left"
        >
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <BarChart3 className="h-6 w-6 text-purple-600" />
            </div>
            <div className="ml-3">
              <p className="font-medium text-gray-900">Summarize</p>
              <p className="text-sm text-gray-500">Complete toolbox meeting</p>
            </div>
          </div>
        </button>
      </div>

      {/* Recent Toolboxes */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold mb-4 flex items-center">
          <BarChart3 className="h-5 w-5 mr-2 text-gray-600" />
          Recent Toolboxes
        </h3>

        {allToolboxes.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p>No toolbox meetings found</p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Date</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Conductor</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Attendees</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Jobs</th>
                  <th className="text-left py-3 px-4 font-medium text-gray-700">Status</th>
                </tr>
              </thead>
              <tbody>
                {allToolboxes.slice(0, 10).map((toolbox) => (
                  <tr key={toolbox.id} className="border-b border-gray-100 hover:bg-gray-50">
                    <td className="py-3 px-4 text-sm text-gray-900">
                      {new Date(toolbox.date).toLocaleDateString()}
                    </td>
                    <td className="py-3 px-4 text-sm text-gray-900">
                      {toolbox.conductor?.name || 'N/A'}
                    </td>
                    <td className="py-3 px-4 text-sm text-gray-900">
                      {toolbox.attendees?.length || 0}
                    </td>
                    <td className="py-3 px-4 text-sm text-gray-900">
                      {toolbox.jobs?.length || 0}
                    </td>
                    <td className="py-3 px-4">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(toolbox.status)}`}>
                        {getStatusIcon(toolbox.status)}
                        <span className="ml-1">{toolbox.status}</span>
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
};

export default ToolboxManagement;
