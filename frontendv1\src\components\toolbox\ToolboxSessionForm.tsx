import React, { useState } from 'react';
import { Calendar, User, FileText, Camera, Save, X, Users, Clock } from 'lucide-react';
import { Worker, ToolboxSession } from '../../types';

interface ToolboxSessionFormProps {
  workers: Worker[];
  onSave: (sessionData: {
    sessionTime: string;
    topic: string;
    conductor: string;
    notes?: string;
    photoFile?: File;
    attendeeIds: number[];
  }) => Promise<void>;
  onCancel: () => void;
  existingSession?: ToolboxSession;
}

export const ToolboxSessionForm: React.FC<ToolboxSessionFormProps> = ({
  workers,
  onSave,
  onCancel,
  existingSession,
}) => {
  const [sessionTime, setSessionTime] = useState(
    existingSession?.sessionTime.split('T')[0] || new Date().toISOString().split('T')[0]
  );
  const [sessionTimeHour, setSessionTimeHour] = useState(
    existingSession?.sessionTime ? new Date(existingSession.sessionTime).toTimeString().slice(0, 5) : '08:00'
  );
  const [topic, setTopic] = useState(existingSession?.topic || '');
  const [conductor, setConductor] = useState(existingSession?.conductor || '');
  const [notes, setNotes] = useState(existingSession?.notes || '');
  const [photoFile, setPhotoFile] = useState<File | null>(null);
  const [selectedAttendees, setSelectedAttendees] = useState<Set<number>>(
    new Set(existingSession?.attendances?.map(a => a.workerId) || [])
  );
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleAttendeeToggle = (workerId: number) => {
    const newSelected = new Set(selectedAttendees);
    if (newSelected.has(workerId)) {
      newSelected.delete(workerId);
    } else {
      newSelected.add(workerId);
    }
    setSelectedAttendees(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedAttendees.size === workers.length) {
      setSelectedAttendees(new Set());
    } else {
      setSelectedAttendees(new Set(workers.map(w => w.id)));
    }
  };

  const handlePhotoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        alert('Please select an image file');
        return;
      }
      // Validate file size (max 10MB)
      if (file.size > 10 * 1024 * 1024) {
        alert('File size must be less than 10MB');
        return;
      }
      setPhotoFile(file);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!topic.trim()) {
      alert('Please enter a topic');
      return;
    }

    if (!conductor.trim()) {
      alert('Please enter the conductor name');
      return;
    }

    if (selectedAttendees.size === 0) {
      alert('Please select at least one attendee');
      return;
    }

    setIsSubmitting(true);
    try {
      const sessionDateTime = `${sessionTime}T${sessionTimeHour}:00.000Z`;
      
      await onSave({
        sessionTime: sessionDateTime,
        topic: topic.trim(),
        conductor: conductor.trim(),
        notes: notes.trim() || undefined,
        photoFile: photoFile || undefined,
        attendeeIds: Array.from(selectedAttendees),
      });
    } catch (error) {
      console.error('Toolbox session save failed:', error);
      alert('Failed to save toolbox session. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-10 mx-auto p-5 border w-full max-w-4xl shadow-lg rounded-md bg-white">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">
            {existingSession ? 'Edit Toolbox Talk Session' : 'New Toolbox Talk Session'}
          </h3>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Session Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Date */}
            <div>
              <label htmlFor="sessionDate" className="block text-sm font-medium text-gray-700 mb-1">
                Session Date *
              </label>
              <div className="relative">
                <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="date"
                  id="sessionDate"
                  value={sessionTime}
                  onChange={(e) => setSessionTime(e.target.value)}
                  required
                  className="pl-10 w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
            </div>

            {/* Time */}
            <div>
              <label htmlFor="sessionTime" className="block text-sm font-medium text-gray-700 mb-1">
                Session Time *
              </label>
              <div className="relative">
                <Clock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="time"
                  id="sessionTime"
                  value={sessionTimeHour}
                  onChange={(e) => setSessionTimeHour(e.target.value)}
                  required
                  className="pl-10 w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>

          {/* Topic */}
          <div>
            <label htmlFor="topic" className="block text-sm font-medium text-gray-700 mb-1">
              Topic *
            </label>
            <input
              type="text"
              id="topic"
              value={topic}
              onChange={(e) => setTopic(e.target.value)}
              placeholder="e.g., Working at Heights Safety, PPE Requirements"
              required
              className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>

          {/* Conductor */}
          <div>
            <label htmlFor="conductor" className="block text-sm font-medium text-gray-700 mb-1">
              Conductor *
            </label>
            <div className="relative">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                id="conductor"
                value={conductor}
                onChange={(e) => setConductor(e.target.value)}
                placeholder="Name of the person conducting the session"
                required
                className="pl-10 w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Photo Upload */}
          <div>
            <label htmlFor="photo" className="block text-sm font-medium text-gray-700 mb-1">
              Session Photo
            </label>
            <div className="flex items-center space-x-4">
              <label className="cursor-pointer inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                <Camera className="h-4 w-4 mr-2" />
                {photoFile ? 'Change Photo' : 'Upload Photo'}
                <input
                  type="file"
                  id="photo"
                  accept="image/*"
                  onChange={handlePhotoChange}
                  className="sr-only"
                />
              </label>
              {photoFile && (
                <span className="text-sm text-gray-600">
                  {photoFile.name}
                </span>
              )}
            </div>
            <p className="text-xs text-gray-500 mt-1">
              Upload a photo documenting the toolbox talk session
            </p>
          </div>

          {/* Notes */}
          <div>
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <div className="relative">
              <FileText className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
                placeholder="Additional notes about the session..."
                className="pl-10 w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Attendee Selection */}
          <div>
            <div className="flex items-center justify-between mb-3">
              <label className="block text-sm font-medium text-gray-700">
                Attendees * ({selectedAttendees.size} selected)
              </label>
              <button
                type="button"
                onClick={handleSelectAll}
                className="text-sm text-green-600 hover:text-green-700 font-medium"
              >
                {selectedAttendees.size === workers.length ? 'Deselect All' : 'Select All'}
              </button>
            </div>

            <div className="border border-gray-300 rounded-md max-h-64 overflow-y-auto">
              {workers.length === 0 ? (
                <div className="p-4 text-center text-gray-500">
                  <Users className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p>No workers available</p>
                </div>
              ) : (
                <div className="divide-y divide-gray-200">
                  {workers.map(worker => (
                    <label
                      key={worker.id}
                      className="flex items-center p-3 hover:bg-gray-50 cursor-pointer"
                    >
                      <input
                        type="checkbox"
                        checked={selectedAttendees.has(worker.id)}
                        onChange={() => handleAttendeeToggle(worker.id)}
                        className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                      />
                      <div className="ml-3 flex items-center flex-1">
                        <div className="flex-shrink-0 h-8 w-8">
                          {worker.photoUrl ? (
                            <img
                              className="h-8 w-8 rounded-full object-cover"
                              src={worker.photoUrl}
                              alt={worker.name}
                            />
                          ) : (
                            <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                              <span className="text-xs text-gray-500 font-medium">
                                {worker.name.charAt(0)}
                              </span>
                            </div>
                          )}
                        </div>
                        <div className="ml-3 flex-1">
                          <div className="text-sm font-medium text-gray-900">
                            {worker.name}
                          </div>
                          <div className="text-xs text-gray-500">
                            {worker.trades.map(trade => trade.name).join(', ')} • {worker.company}
                          </div>
                        </div>
                      </div>
                    </label>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
            >
              <Save className="h-4 w-4 mr-2" />
              {isSubmitting ? 'Saving...' : existingSession ? 'Update Session' : 'Create Session'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
