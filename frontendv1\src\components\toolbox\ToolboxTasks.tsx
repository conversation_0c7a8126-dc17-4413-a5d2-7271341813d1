import React from 'react';
import { mockTasks } from '../../mock/siteData';

interface ToolboxTasksProps {
  siteId: string;
}

const ToolboxTasks: React.FC<ToolboxTasksProps> = ({ }) => {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Tasks</h2>
        <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
          Add New Task
        </button>
      </div>
      
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="p-4 border-b border-gray-200">
          <h3 className="font-medium">All Tasks</h3>
        </div>
        <ul className="divide-y divide-gray-200">
          {mockTasks.map(task => (
            <li key={task.id} className="p-4 hover:bg-gray-50 cursor-pointer">
              <div className="flex justify-between items-center">
                <span className="font-medium">{task.title}</span>
                <span className={`text-xs px-2 py-1 rounded-full ${
                  task.status === 'completed' ? 'bg-green-100 text-green-800' : 
                  task.status === 'in-progress' ? 'bg-blue-100 text-blue-800' :
                  task.status === 'blocked' ? 'bg-red-100 text-red-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {task.status.replace('-', ' ')}
                </span>
              </div>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default ToolboxTasks;