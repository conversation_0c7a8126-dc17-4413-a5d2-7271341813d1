import React from "react";
import { useNavigate } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import FloatingCard from "../layout/FloatingCard";
import TrainingForm from "./TrainingForm";
import { useSiteContext } from "../../hooks/useSiteContext";

const NewTrainingForm: React.FC = () => {
	const navigate = useNavigate();
	// const { siteId } = useParams();
	const { siteId } = useSiteContext();
	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		// Handle form submission logic here
		// In a real app, you would collect form data and send to API
		alert("Training saved successfully!");
		navigate(`/sites/${siteId}/training`);
	};

	const handleCancel = () => {
		navigate(`/sites/${siteId}/training`);
	};

	return (
		<div className="p-6">
			<FloatingCard title="New Training">
				<div className="flex items-center mb-6">
					<button
						onClick={handleCancel}
						className="mr-4 text-gray-500 hover:text-gray-700"
					>
						<ArrowLeft className="h-5 w-5" />
					</button>
					<h1 className="text-xl font-semibold">Add New Training</h1>
				</div>

				<TrainingForm onSubmit={handleSubmit} onCancel={handleCancel} />
			</FloatingCard>
		</div>
	);
};

export default NewTrainingForm;
