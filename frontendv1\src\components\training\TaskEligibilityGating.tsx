import React, { useState, useEffect } from 'react';
import {
  Shield,
  CheckCircle,
  XCircle,
  AlertTriangle,
  User,
  Clock,
  Award,
  Search
} from 'lucide-react';
import { Worker, Trade } from '../../types';
import { useTenantContext } from '../../hooks/useTenantContext.tsx';

interface TaskRequirement {
  id: number;
  type: 'training' | 'certification' | 'skill' | 'trade';
  name: string;
  description: string;
  isMandatory: boolean;
  validityPeriodMonths?: number;
}

interface Task {
  id: number;
  name: string;
  description: string;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  requirements: TaskRequirement[];
  siteId: string;
  createdAt: string;
}

interface WorkerEligibility {
  workerId: number;
  workerName: string;
  workerPhoto?: string;
  tradeName: string;
  isEligible: boolean;
  eligibilityScore: number; // 0-100
  missingRequirements: TaskRequirement[];
  expiringRequirements: TaskRequirement[];
  status: 'eligible' | 'partially-eligible' | 'not-eligible' | 'expired';
}

interface TaskEligibilityGatingProps {
  taskId?: number;
  siteId: string;
  onWorkerSelect?: (workerId: number, isEligible: boolean) => void;
  mode?: 'selection' | 'validation' | 'report';
}

const TaskEligibilityGating: React.FC<TaskEligibilityGatingProps> = ({
  taskId,
  siteId,
  onWorkerSelect,
  mode = 'selection'
}) => {
  const { tenantId } = useTenantContext();
  const [task, setTask] = useState<Task | null>(null);
  const [_workers, setWorkers] = useState<Worker[]>([]);
  const [eligibility, setEligibility] = useState<WorkerEligibility[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [tradeFilter, setTradeFilter] = useState<string>('all');

  // Mock task data
  const mockTask: Task = {
    id: 1,
    name: 'High Voltage Electrical Work',
    description: 'Installation and maintenance of high voltage electrical systems',
    riskLevel: 'critical',
    requirements: [
      {
        id: 1,
        type: 'training',
        name: 'Electrical Safety Training',
        description: 'Comprehensive electrical safety procedures',
        isMandatory: true,
        validityPeriodMonths: 12
      },
      {
        id: 2,
        type: 'certification',
        name: 'High Voltage License',
        description: 'Licensed to work with high voltage systems',
        isMandatory: true,
        validityPeriodMonths: 24
      },
      {
        id: 3,
        type: 'training',
        name: 'Working at Heights',
        description: 'Safety training for elevated work',
        isMandatory: true,
        validityPeriodMonths: 12
      },
      {
        id: 4,
        type: 'skill',
        name: 'Arc Flash Protection',
        description: 'Knowledge of arc flash protection procedures',
        isMandatory: false
      }
    ],
    siteId: siteId,
    createdAt: '2025-01-15T00:00:00Z'
  };

  // Mock workers data
  const mockWorkers: Worker[] = [
    {
      id: 1,
      tenantId: tenantId || 'tenant-1',
      name: 'David Kamau',
      company: 'ABC Construction',
      nationalId: '12345678',
      phoneNumber: '+*********** 678',
      gender: 'Male',
      manHours: 2080,
      rating: 4.5,
      hireDate: '2024-12-01T00:00:00Z',
      status: 'active',
      trades: [{ id: 2, name: 'Electrician', tenantId: tenantId || 'tenant-1' } as Trade],
      skills: [],
      trainings: [],
      trainingHistory: [],
      siteAssignments: [],
      certifications: [],
      trainingsCompleted: 3,
      createdAt: '2025-01-01T00:00:00Z',
      createdBy: 'System'
    },
    {
      id: 2,
      tenantId: tenantId || 'tenant-1',
      name: 'Mary Wanjiku',
      company: 'XYZ Contractors',
      nationalId: '87654321',
      phoneNumber: '+*********** 654',
      gender: 'Female',
      manHours: 3200,
      rating: 4.8,
      hireDate: '2024-11-01T00:00:00Z',
      status: 'active',
      trades: [{ id: 2, name: 'Electrician', tenantId: tenantId || 'tenant-1' } as Trade],
      skills: [],
      trainings: [],
      trainingHistory: [],
      siteAssignments: [],
      certifications: [],
      trainingsCompleted: 4,
      createdAt: '2024-12-01T00:00:00Z',
      createdBy: 'System'
    }
  ];

  useEffect(() => {
    fetchTaskAndWorkers();
  }, [taskId, siteId, tenantId]);

  const fetchTaskAndWorkers = async () => {
    setLoading(true);
    try {
      // In real implementation:
      // const [taskResult, workersResult] = await Promise.all([
      //   getTask(taskId),
      //   getWorkers({ tenantId, siteId })
      // ]);

      // Mock implementation
      setTimeout(() => {
        setTask(mockTask);
        setWorkers(mockWorkers);
        
        // Calculate eligibility for each worker
        const eligibilityResults = mockWorkers.map(worker => 
          calculateWorkerEligibility(worker, mockTask)
        );
        setEligibility(eligibilityResults);
        setLoading(false);
      }, 500);
    } catch (error) {
      console.error('Error fetching task and workers:', error);
      setLoading(false);
    }
  };

  const calculateWorkerEligibility = (worker: Worker, task: Task): WorkerEligibility => {
    const missingRequirements: TaskRequirement[] = [];
    const expiringRequirements: TaskRequirement[] = [];
    let metRequirements = 0;

    // Check each requirement
    task.requirements.forEach(requirement => {
      const hasRequirement = checkWorkerHasRequirement(worker, requirement);
      const isExpiring = checkRequirementExpiring(worker, requirement);

      if (!hasRequirement) {
        missingRequirements.push(requirement);
      } else {
        metRequirements++;
        if (isExpiring) {
          expiringRequirements.push(requirement);
        }
      }
    });

    const mandatoryRequirements = task.requirements.filter(r => r.isMandatory);
    const metMandatoryRequirements = mandatoryRequirements.filter(r => 
      checkWorkerHasRequirement(worker, r)
    ).length;

    const eligibilityScore = Math.round((metRequirements / task.requirements.length) * 100);
    const hasAllMandatory = metMandatoryRequirements === mandatoryRequirements.length;
    
    let status: WorkerEligibility['status'];
    if (hasAllMandatory && missingRequirements.length === 0) {
      status = expiringRequirements.length > 0 ? 'expired' : 'eligible';
    } else if (hasAllMandatory) {
      status = 'partially-eligible';
    } else {
      status = 'not-eligible';
    }

    return {
      workerId: worker.id,
      workerName: worker.name,
      workerPhoto: worker.photoUrl,
      tradeName: worker.trades[0]?.name || 'No Trade',
      isEligible: status === 'eligible',
      eligibilityScore,
      missingRequirements,
      expiringRequirements,
      status
    };
  };

  const checkWorkerHasRequirement = (worker: Worker, requirement: TaskRequirement): boolean => {
    // Mock implementation - in real app this would check actual training/certification records
    switch (requirement.type) {
      case 'training':
        return worker.trainingsCompleted >= 2; // Mock logic
      case 'certification':
        return worker.trainingsCompleted >= 3; // Mock logic
      case 'skill':
        return worker.skills.some(skill => skill.name.includes(requirement.name));
      case 'trade':
        return worker.trades.some(trade => trade.name === requirement.name);
      default:
        return false;
    }
  };

  const checkRequirementExpiring = (_worker: Worker, _requirement: TaskRequirement): boolean => {
    // Mock implementation - in real app this would check expiry dates
    return Math.random() > 0.8; // 20% chance of expiring
  };

  const getStatusColor = (status: WorkerEligibility['status']) => {
    switch (status) {
      case 'eligible': return 'text-green-600 bg-green-100';
      case 'partially-eligible': return 'text-yellow-600 bg-yellow-100';
      case 'not-eligible': return 'text-red-600 bg-red-100';
      case 'expired': return 'text-orange-600 bg-orange-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: WorkerEligibility['status']) => {
    switch (status) {
      case 'eligible': return <CheckCircle className="h-4 w-4" />;
      case 'partially-eligible': return <AlertTriangle className="h-4 w-4" />;
      case 'not-eligible': return <XCircle className="h-4 w-4" />;
      case 'expired': return <Clock className="h-4 w-4" />;
      default: return <Shield className="h-4 w-4" />;
    }
  };

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'critical': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const handleWorkerSelection = (workerId: number, isEligible: boolean) => {
    if (onWorkerSelect) {
      onWorkerSelect(workerId, isEligible);
    }
  };

  const filteredEligibility = eligibility.filter(worker => {
    const matchesSearch = worker.workerName.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || worker.status === statusFilter;
    const matchesTrade = tradeFilter === 'all' || worker.tradeName === tradeFilter;
    return matchesSearch && matchesStatus && matchesTrade;
  });

  const eligibilityCounts = {
    eligible: eligibility.filter(w => w.status === 'eligible').length,
    partiallyEligible: eligibility.filter(w => w.status === 'partially-eligible').length,
    notEligible: eligibility.filter(w => w.status === 'not-eligible').length,
    expired: eligibility.filter(w => w.status === 'expired').length
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    );
  }

  if (!task) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Task not found</h3>
        <p className="text-gray-500">The requested task could not be loaded.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Task Information */}
      <div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
        <div className="flex justify-between items-start mb-4">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">{task.name}</h2>
            <p className="text-sm text-gray-600 mt-1">{task.description}</p>
          </div>
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRiskLevelColor(task.riskLevel)}`}>
            <Shield className="h-3 w-3 mr-1" />
            {task.riskLevel.toUpperCase()} RISK
          </span>
        </div>

        <div>
          <h3 className="text-sm font-medium text-gray-900 mb-3">Required Qualifications</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {task.requirements.map(requirement => (
              <div key={requirement.id} className="flex items-center p-3 bg-gray-50 rounded-md">
                <Award className="h-4 w-4 text-gray-400 mr-2" />
                <div className="flex-1">
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-gray-900">{requirement.name}</span>
                    {requirement.isMandatory && (
                      <span className="ml-2 text-xs text-red-600 font-medium">REQUIRED</span>
                    )}
                  </div>
                  <p className="text-xs text-gray-500">{requirement.description}</p>
                  {requirement.validityPeriodMonths && (
                    <p className="text-xs text-gray-400">
                      Valid for {requirement.validityPeriodMonths} months
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Eligibility Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <div className="flex items-center">
            <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
            <div>
              <p className="text-sm font-medium text-green-900">Eligible</p>
              <p className="text-2xl font-bold text-green-600">{eligibilityCounts.eligible}</p>
            </div>
          </div>
        </div>
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-yellow-600 mr-2" />
            <div>
              <p className="text-sm font-medium text-yellow-900">Partially Eligible</p>
              <p className="text-2xl font-bold text-yellow-600">{eligibilityCounts.partiallyEligible}</p>
            </div>
          </div>
        </div>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center">
            <XCircle className="h-5 w-5 text-red-600 mr-2" />
            <div>
              <p className="text-sm font-medium text-red-900">Not Eligible</p>
              <p className="text-2xl font-bold text-red-600">{eligibilityCounts.notEligible}</p>
            </div>
          </div>
        </div>
        <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
          <div className="flex items-center">
            <Clock className="h-5 w-5 text-orange-600 mr-2" />
            <div>
              <p className="text-sm font-medium text-orange-900">Expiring Soon</p>
              <p className="text-2xl font-bold text-orange-600">{eligibilityCounts.expired}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search workers..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
            />
          </div>
        </div>
        <div className="flex gap-2">
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
          >
            <option value="all">All Status</option>
            <option value="eligible">Eligible</option>
            <option value="partially-eligible">Partially Eligible</option>
            <option value="not-eligible">Not Eligible</option>
            <option value="expired">Expiring Soon</option>
          </select>
          <select
            value={tradeFilter}
            onChange={(e) => setTradeFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
          >
            <option value="all">All Trades</option>
            <option value="Electrician">Electrician</option>
            <option value="Carpenter">Carpenter</option>
            <option value="Plumber">Plumber</option>
          </select>
        </div>
      </div>

      {/* Workers Eligibility List */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Worker
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Score
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Missing Requirements
                </th>
                {mode === 'selection' && (
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                )}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredEligibility.map((worker) => (
                <tr key={worker.workerId} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-8 w-8">
                        <div className="h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center">
                          <User className="h-4 w-4 text-gray-600" />
                        </div>
                      </div>
                      <div className="ml-3">
                        <div className="text-sm font-medium text-gray-900">
                          {worker.workerName}
                        </div>
                        <div className="text-sm text-gray-500">
                          {worker.tradeName}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(worker.status)}`}>
                      {getStatusIcon(worker.status)}
                      <span className="ml-1 capitalize">{worker.status.replace('-', ' ')}</span>
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-16 bg-gray-200 rounded-full h-2 mr-2">
                        <div
                          className={`h-2 rounded-full ${
                            worker.eligibilityScore >= 80 ? 'bg-green-500' :
                            worker.eligibilityScore >= 60 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${worker.eligibilityScore}%` }}
                        />
                      </div>
                      <span className="text-sm font-medium text-gray-900">
                        {worker.eligibilityScore}%
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-gray-900">
                      {worker.missingRequirements.length === 0 ? (
                        <span className="text-green-600">All requirements met</span>
                      ) : (
                        <div className="space-y-1">
                          {worker.missingRequirements.slice(0, 2).map(req => (
                            <div key={req.id} className="text-xs text-red-600">
                              • {req.name}
                            </div>
                          ))}
                          {worker.missingRequirements.length > 2 && (
                            <div className="text-xs text-gray-500">
                              +{worker.missingRequirements.length - 2} more
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </td>
                  {mode === 'selection' && (
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => handleWorkerSelection(worker.workerId, worker.isEligible)}
                        disabled={!worker.isEligible}
                        className={`px-3 py-1 rounded-md text-sm font-medium ${
                          worker.isEligible
                            ? 'bg-green-600 text-white hover:bg-green-700'
                            : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                        }`}
                      >
                        {worker.isEligible ? 'Select' : 'Not Eligible'}
                      </button>
                    </td>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredEligibility.length === 0 && (
          <div className="text-center py-12">
            <User className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No workers found</h3>
            <p className="text-gray-500">
              {searchTerm || statusFilter !== 'all' || tradeFilter !== 'all'
                ? 'Try adjusting your search criteria.'
                : 'No workers are available for this task.'}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TaskEligibilityGating;
