import React, { useState, useEffect } from 'react';
import {
  X,
  AlertTriangle,
  Search,
} from 'lucide-react';
import { Worker, Training, Trade } from '../../types';
import { useTenantContext } from '../../hooks/useTenantContext.tsx';

interface TrainingAssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: 'assign' | 'bulk-assign' | 'auto-assign';
  trainingId?: number;
  siteId?: string;
  selectedWorkers?: number[];
  onAssign: (assignment: TrainingAssignment) => void;
}

interface TrainingAssignment {
  trainingId: number;
  workerIds: number[];
  targetCompletionDate?: string;
  notes?: string;
  assignmentType: 'manual' | 'auto' | 'bulk';
}

interface WorkerSelectionItem extends Worker {
  isSelected: boolean;
  isEligible: boolean;
  ineligibilityReason?: string;
}

const TrainingAssignmentModal: React.FC<TrainingAssignmentModalProps> = ({
  isOpen,
  onClose,
  mode,
  trainingId,
  siteId,
  selectedWorkers = [],
  onAssign
}) => {
  const { tenantId } = useTenantContext();
  const [workers, setWorkers] = useState<WorkerSelectionItem[]>([]);
  const [trainings, setTrainings] = useState<Training[]>([]);
  const [trades, setTrades] = useState<Trade[]>([]);
  const [selectedTrainingId, setSelectedTrainingId] = useState<number>(trainingId || 0);
  const [selectedWorkerIds, setSelectedWorkerIds] = useState<number[]>(selectedWorkers);
  const [targetDate, setTargetDate] = useState<string>('');
  const [notes, setNotes] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState('');
  const [tradeFilter, setTradeFilter] = useState<number>(0);
  const [loading, setLoading] = useState(false);

  // Mock data
  const mockWorkers: Worker[] = [
    {
      id: 1,
      tenantId: tenantId || 'tenant-1',
      name: 'David Kamau',
      company: 'ABC Construction',
      nationalId: '12345678',
      phoneNumber: '+*********** 678',
      email: '<EMAIL>',
      gender: 'Male',
      manHours: 2080,
      rating: 4.5,
      hireDate: '2024-12-01T00:00:00Z',
      status: 'active',
      trades: [],
      skills: [],
      trainings: [],
      trainingHistory: [],
      siteAssignments: [],
      certifications: [],
      trainingsCompleted: 1,
      createdAt: '2025-01-01T00:00:00Z',
      createdBy: 'System'
    }
  ];

  const mockTrainings: Training[] = [
    {
      id: 1,
      tenantId: tenantId || 'tenant-1',
      name: 'Working at Heights Safety',
      description: 'Safety training for elevated work',
      validityPeriodMonths: 12,
      trainingType: 'Safety',
      status: 'Scheduled' as any,
      workers: [],
      trainingHistory: [],
      createdAt: '2025-01-01T00:00:00Z',
      createdBy: 'System'
    }
  ];

  useEffect(() => {
    if (isOpen && tenantId) {
      fetchData();
    }
  }, [isOpen, tenantId, siteId]);

  const fetchData = async () => {
    setLoading(true);
    try {
      // In real implementation:
      // const [workersResult, trainingsResult, tradesResult] = await Promise.all([
      //   getWorkers({ tenantId, siteId }),
      //   getTrainings(tenantId),
      //   getTrades(tenantId)
      // ]);

      // Mock implementation
      setTimeout(() => {
        const workersWithSelection = mockWorkers.map(worker => ({
          ...worker,
          isSelected: selectedWorkers.includes(worker.id),
          isEligible: checkWorkerEligibility(worker, selectedTrainingId),
          ineligibilityReason: getIneligibilityReason(worker, selectedTrainingId)
        }));

        setWorkers(workersWithSelection);
        setTrainings(mockTrainings);
        setTrades([]);
        setLoading(false);
      }, 500);
    } catch (error) {
      console.error('Error fetching data:', error);
      setLoading(false);
    }
  };

  const checkWorkerEligibility = (worker: Worker, trainingId: number): boolean => {
    // Check if worker already has this training
    const hasTraining = worker.trainings.some(t => t.id === trainingId);
    if (hasTraining) return false;

    // Check if worker has required prerequisites
    // This would be more complex in real implementation
    return true;
  };

  const getIneligibilityReason = (worker: Worker, trainingId: number): string | undefined => {
    const hasTraining = worker.trainings.some(t => t.id === trainingId);
    if (hasTraining) return 'Already assigned to this training';

    // Add more eligibility checks here
    return undefined;
  };

  const handleWorkerToggle = (workerId: number) => {
    setSelectedWorkerIds(prev => {
      if (prev.includes(workerId)) {
        return prev.filter(id => id !== workerId);
      } else {
        return [...prev, workerId];
      }
    });
  };

  const handleSelectAll = () => {
    const eligibleWorkerIds = workers
      .filter(w => w.isEligible)
      .map(w => w.id);
    
    setSelectedWorkerIds(eligibleWorkerIds);
  };

  const handleDeselectAll = () => {
    setSelectedWorkerIds([]);
  };

  const handleAutoAssignByTrade = async (tradeId: number) => {
    try {
      setLoading(true);

      // Get training requirements for the selected training
      const selectedTraining = trainings.find(t => t.id === selectedTrainingId);
      if (!selectedTraining) return;

      // Find workers with the specified trade who need this training
      const eligibleWorkers = workers.filter(worker => {
        // Check if worker has the required trade
        const hasRequiredTrade = worker.trades.some(t => t.id === tradeId);
        if (!hasRequiredTrade) return false;

        // Check if worker already has this training (and it's not expired)
        const hasCurrentTraining = worker.trainingHistory.some(history =>
          history.trainingId === selectedTrainingId &&
          history.status === 'Completed' &&
          (!history.expiryDate || new Date(history.expiryDate) > new Date())
        );

        return !hasCurrentTraining && worker.isEligible;
      });

      const eligibleWorkerIds = eligibleWorkers.map(w => w.id);

      // Auto-assign the training
      if (eligibleWorkerIds.length > 0) {
        const assignment = {
          trainingId: selectedTrainingId,
          workerIds: eligibleWorkerIds,
          targetCompletionDate: targetDate || undefined,
          notes: `Auto-assigned to ${eligibleWorkers.length} workers with ${trades.find(t => t.id === tradeId)?.name || 'selected'} trade`,
          assignmentType: 'auto' as const
        };

        // In real implementation, this would call the auto-assignment API
        console.log('Auto-assigning training by trade:', assignment);

        // Update selected workers
        setSelectedWorkerIds(prev => [...new Set([...prev, ...eligibleWorkerIds])]);

        // Show success message
        alert(`Successfully auto-assigned training to ${eligibleWorkerIds.length} workers with the selected trade.`);
      } else {
        alert('No eligible workers found with the selected trade who need this training.');
      }
    } catch (error) {
      console.error('Auto-assignment failed:', error);
      alert('Auto-assignment failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = () => {
    if (!selectedTrainingId || selectedWorkerIds.length === 0) {
      alert('Please select a training and at least one worker');
      return;
    }

    const assignment: TrainingAssignment = {
      trainingId: selectedTrainingId,
      workerIds: selectedWorkerIds,
      targetCompletionDate: targetDate || undefined,
      notes: notes || undefined,
      assignmentType: mode === 'auto-assign' ? 'auto' : mode === 'bulk-assign' ? 'bulk' : 'manual'
    };

    onAssign(assignment);
    onClose();
  };

  const filteredWorkers = workers.filter(worker => {
    const matchesSearch = worker.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         worker.nationalId.includes(searchTerm);
    const matchesTrade = tradeFilter === 0 || worker.trades.some(t => t.id === tradeFilter);
    return matchesSearch && matchesTrade;
  });

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">
              {mode === 'assign' && 'Assign Training'}
              {mode === 'bulk-assign' && 'Bulk Assign Training'}
              {mode === 'auto-assign' && 'Auto-Assign Training by Trade'}
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              Select workers and assign training programs
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {/* Training Selection */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Training Program
            </label>
            <select
              value={selectedTrainingId}
              onChange={(e) => setSelectedTrainingId(Number(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
              disabled={!!trainingId}
            >
              <option value={0}>Select a training program...</option>
              {trainings.map(training => (
                <option key={training.id} value={training.id}>
                  {training.name} - {training.trainingType}
                </option>
              ))}
            </select>
          </div>

          {/* Worker Selection */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-4">
              <label className="block text-sm font-medium text-gray-700">
                Select Workers ({selectedWorkerIds.length} selected)
              </label>
              <div className="flex space-x-2">
                <button
                  onClick={handleSelectAll}
                  className="text-sm text-green-600 hover:text-green-800"
                >
                  Select All Eligible
                </button>
                <button
                  onClick={handleDeselectAll}
                  className="text-sm text-gray-600 hover:text-gray-800"
                >
                  Deselect All
                </button>
              </div>
            </div>

            {/* Auto-Assignment by Trade */}
            {mode === 'auto-assign' && trades.length > 0 && (
              <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
                <h4 className="text-sm font-medium text-blue-900 mb-3">Auto-Assign by Trade</h4>
                <p className="text-sm text-blue-700 mb-3">
                  Automatically assign this training to all eligible workers in selected trades who don't already have current certification.
                </p>
                <div className="flex flex-wrap gap-2">
                  {trades.map(trade => (
                    <button
                      key={trade.id}
                      onClick={() => handleAutoAssignByTrade(trade.id)}
                      disabled={loading || !selectedTrainingId}
                      className="inline-flex items-center px-3 py-1 border border-blue-300 rounded-md text-sm text-blue-700 hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Auto-assign to {trade.name}s
                    </button>
                  ))}
                </div>
              </div>
            )}

            {/* Filters */}
            <div className="flex gap-4 mb-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search workers..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
                  />
                </div>
              </div>
              <select
                value={tradeFilter}
                onChange={(e) => setTradeFilter(Number(e.target.value))}
                className="px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
              >
                <option value={0}>All Trades</option>
                {trades.map(trade => (
                  <option key={trade.id} value={trade.id}>
                    {trade.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Worker List */}
            <div className="border border-gray-200 rounded-md max-h-64 overflow-y-auto">
              {loading ? (
                <div className="p-4 text-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600 mx-auto"></div>
                </div>
              ) : (
                filteredWorkers.map(worker => (
                  <div
                    key={worker.id}
                    className={`flex items-center p-3 border-b border-gray-100 last:border-b-0 ${
                      !worker.isEligible ? 'bg-gray-50 opacity-60' : 'hover:bg-gray-50'
                    }`}
                  >
                    <input
                      type="checkbox"
                      checked={selectedWorkerIds.includes(worker.id)}
                      onChange={() => handleWorkerToggle(worker.id)}
                      disabled={!worker.isEligible}
                      className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                    />
                    <div className="ml-3 flex-1">
                      <div className="flex items-center">
                        <span className="text-sm font-medium text-gray-900">
                          {worker.name}
                        </span>
                        {!worker.isEligible && (
                          <AlertTriangle className="h-4 w-4 text-amber-500 ml-2" />
                        )}
                      </div>
                      <div className="text-xs text-gray-500">
                        {worker.nationalId} • {worker.trades.map(t => t.name).join(', ') || 'No trades assigned'}
                      </div>
                      {worker.ineligibilityReason && (
                        <div className="text-xs text-amber-600 mt-1">
                          {worker.ineligibilityReason}
                        </div>
                      )}
                    </div>
                  </div>
                ))
              )}
            </div>
          </div>

          {/* Additional Options */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Target Completion Date (Optional)
              </label>
              <input
                type="date"
                value={targetDate}
                onChange={(e) => setTargetDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
              />
            </div>
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Notes (Optional)
            </label>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
              placeholder="Add any additional notes about this training assignment..."
            />
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-600">
            {selectedWorkerIds.length} worker(s) selected for assignment
          </div>
          <div className="flex space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleSubmit}
              disabled={!selectedTrainingId || selectedWorkerIds.length === 0}
              className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed"
            >
              Assign Training
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrainingAssignmentModal;
