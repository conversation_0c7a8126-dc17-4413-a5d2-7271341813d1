import React, { useState } from 'react';
import { Calendar, User, Award, FileText, Save, X } from 'lucide-react';
import { Training, Worker } from '../../types';

interface TrainingCompletionProps {
  training: Training;
  worker: Worker;
  onComplete: (completionData: {
    completionDate: string;
    score?: number;
    notes?: string;
    expiryDate?: string;
  }) => Promise<void>;
  onCancel: () => void;
}

export const TrainingCompletion: React.FC<TrainingCompletionProps> = ({
  training,
  worker,
  onComplete,
  onCancel,
}) => {
  const [completionDate, setCompletionDate] = useState(
    new Date().toISOString().split('T')[0]
  );
  const [score, setScore] = useState<number | undefined>();
  const [notes, setNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Calculate expiry date based on training validity period
  const calculateExpiryDate = (completionDate: string): string | undefined => {
    if (!training.validityPeriodMonths) return undefined;
    
    const completion = new Date(completionDate);
    completion.setMonth(completion.getMonth() + training.validityPeriodMonths);
    return completion.toISOString().split('T')[0];
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!completionDate) {
      alert('Please select a completion date');
      return;
    }

    if (score !== undefined && (score < 0 || score > 100)) {
      alert('Score must be between 0 and 100');
      return;
    }

    setIsSubmitting(true);
    try {
      await onComplete({
        completionDate,
        score,
        notes: notes.trim() || undefined,
        expiryDate: calculateExpiryDate(completionDate),
      });
    } catch (error) {
      console.error('Training completion failed:', error);
      alert('Failed to record training completion. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div className="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">
            Record Training Completion
          </h3>
          <button
            onClick={onCancel}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Training and Worker Info */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Training
                </label>
                <div className="flex items-center">
                  <Award className="h-4 w-4 text-gray-400 mr-2" />
                  <span className="text-sm text-gray-900">{training.name}</span>
                </div>
                {training.description && (
                  <p className="text-xs text-gray-500 mt-1">{training.description}</p>
                )}
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Worker
                </label>
                <div className="flex items-center">
                  <User className="h-4 w-4 text-gray-400 mr-2" />
                  <span className="text-sm text-gray-900">{worker.name}</span>
                </div>
                <p className="text-xs text-gray-500 mt-1">ID: {worker.nationalId}</p>
              </div>
            </div>
          </div>

          {/* Completion Date */}
          <div>
            <label htmlFor="completionDate" className="block text-sm font-medium text-gray-700 mb-1">
              Completion Date *
            </label>
            <div className="relative">
              <Calendar className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="date"
                id="completionDate"
                value={completionDate}
                onChange={(e) => setCompletionDate(e.target.value)}
                max={new Date().toISOString().split('T')[0]}
                required
                className="pl-10 w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Score */}
          <div>
            <label htmlFor="score" className="block text-sm font-medium text-gray-700 mb-1">
              Score (0-100)
            </label>
            <input
              type="number"
              id="score"
              value={score || ''}
              onChange={(e) => setScore(e.target.value ? Number(e.target.value) : undefined)}
              min="0"
              max="100"
              placeholder="Enter score (optional)"
              className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
            />
          </div>

          {/* Notes */}
          <div>
            <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
              Notes
            </label>
            <div className="relative">
              <FileText className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <textarea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
                placeholder="Additional notes about the training completion..."
                className="pl-10 w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Validity Information */}
          {training.validityPeriodMonths && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="text-sm font-medium text-blue-900 mb-2">Validity Information</h4>
              <div className="text-sm text-blue-700">
                <p>• Training valid for: {training.validityPeriodMonths} months</p>
                {completionDate && (
                  <p>• Expiry date: {calculateExpiryDate(completionDate)}</p>
                )}
                {training.frequency && (
                  <p>• Renewal frequency: {training.frequency}</p>
                )}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <button
              type="button"
              onClick={onCancel}
              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
            >
              <Save className="h-4 w-4 mr-2" />
              {isSubmitting ? 'Recording...' : 'Record Completion'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
