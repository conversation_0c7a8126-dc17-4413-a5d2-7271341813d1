import React, { useState, useEffect } from 'react';
import {
  X,
  Upload,
  FileText,
  Award,
} from 'lucide-react';
import { Worker, Training, TrainingStatus } from '../../types';

interface TrainingCompletionModalProps {
  isOpen: boolean;
  onClose: () => void;
  workerId?: number;
  trainingId?: number;
  siteId?: string;
  onComplete: (completion: TrainingCompletion) => void;
}

interface TrainingCompletion {
  workerId: number;
  trainingId: number;
  completionDate: string;
  expiryDate?: string;
  score?: number;
  notes?: string;
  certificateFile?: File;
  trainerName?: string;
  location?: string;
}

const TrainingCompletionModal: React.FC<TrainingCompletionModalProps> = ({
  isOpen,
  onClose,
  workerId,
  trainingId,
  siteId,
  onComplete
}) => {
  const [selectedWorkerId, setSelectedWorkerId] = useState<number>(workerId || 0);
  const [selectedTrainingId, setSelectedTrainingId] = useState<number>(trainingId || 0);
  const [completionDate, setCompletionDate] = useState<string>(
    new Date().toISOString().split('T')[0]
  );
  const [expiryDate, setExpiryDate] = useState<string>('');
  const [score, setScore] = useState<string>('');
  const [notes, setNotes] = useState<string>('');
  const [trainerName, setTrainerName] = useState<string>('');
  const [location, setLocation] = useState<string>('');
  const [certificateFile, setCertificateFile] = useState<File | null>(null);
  const [workers, setWorkers] = useState<Worker[]>([]);
  const [trainings, setTrainings] = useState<Training[]>([]);
  const [selectedTraining, setSelectedTraining] = useState<Training | null>(null);
  const [_loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Mock data
  const mockWorkers: Worker[] = [
    {
      id: 1,
      tenantId: 'tenant-1',
      name: 'David Kamau',
      company: 'ABC Construction',
      nationalId: '12345678',
      phoneNumber: '+*********** 678',
      gender: 'Male',
      manHours: 2080,
      rating: 4.5,
      hireDate: '2024-12-01T00:00:00Z',
      status: 'active',
      trades: [],
      skills: [],
      trainings: [],
      trainingHistory: [],
      siteAssignments: [],
      certifications: [],
      trainingsCompleted: 1,
      createdAt: '2025-01-01T00:00:00Z',
      createdBy: 'System'
    }
  ];

  const mockTrainings: Training[] = [
    {
      id: 1,
      tenantId: 'tenant-1',
      name: 'Working at Heights Safety',
      description: 'Safety training for elevated work',
      validityPeriodMonths: 12,
      trainingType: 'Safety',
      status: TrainingStatus.InProgress,
      workers: [],
      trainingHistory: [],
      createdAt: '2025-01-01T00:00:00Z',
      createdBy: 'System'
    }
  ];

  useEffect(() => {
    if (isOpen) {
      fetchData();
    }
  }, [isOpen, siteId]);

  useEffect(() => {
    if (selectedTrainingId) {
      const training = trainings.find(t => t.id === selectedTrainingId);
      setSelectedTraining(training || null);
      
      // Auto-calculate expiry date based on training validity period
      if (training?.validityPeriodMonths && completionDate) {
        const completion = new Date(completionDate);
        completion.setMonth(completion.getMonth() + training.validityPeriodMonths);
        setExpiryDate(completion.toISOString().split('T')[0]);
      }
    }
  }, [selectedTrainingId, trainings, completionDate]);

  const fetchData = async () => {
    setLoading(true);
    try {
      // In real implementation:
      // const [workersResult, trainingsResult] = await Promise.all([
      //   getWorkers({ tenantId, siteId }),
      //   getTrainings(tenantId)
      // ]);

      // Mock implementation
      setTimeout(() => {
        setWorkers(mockWorkers);
        setTrainings(mockTrainings);
        setLoading(false);
      }, 500);
    } catch (error) {
      console.error('Error fetching data:', error);
      setLoading(false);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type and size
      const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png', 'image/jpg'];
      const maxSize = 5 * 1024 * 1024; // 5MB

      if (!allowedTypes.includes(file.type)) {
        setErrors(prev => ({
          ...prev,
          certificate: 'Please upload a PDF, JPEG, or PNG file'
        }));
        return;
      }

      if (file.size > maxSize) {
        setErrors(prev => ({
          ...prev,
          certificate: 'File size must be less than 5MB'
        }));
        return;
      }

      setCertificateFile(file);
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.certificate;
        return newErrors;
      });
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!selectedWorkerId) {
      newErrors.worker = 'Please select a worker';
    }

    if (!selectedTrainingId) {
      newErrors.training = 'Please select a training';
    }

    if (!completionDate) {
      newErrors.completionDate = 'Please enter completion date';
    }

    if (score && (isNaN(Number(score)) || Number(score) < 0 || Number(score) > 100)) {
      newErrors.score = 'Score must be a number between 0 and 100';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (!validateForm()) {
      return;
    }

    const completion: TrainingCompletion = {
      workerId: selectedWorkerId,
      trainingId: selectedTrainingId,
      completionDate,
      expiryDate: expiryDate || undefined,
      score: score ? Number(score) : undefined,
      notes: notes || undefined,
      certificateFile: certificateFile || undefined,
      trainerName: trainerName || undefined,
      location: location || undefined
    };

    onComplete(completion);
    onClose();
  };

  const handleClose = () => {
    // Reset form
    setSelectedWorkerId(workerId || 0);
    setSelectedTrainingId(trainingId || 0);
    setCompletionDate(new Date().toISOString().split('T')[0]);
    setExpiryDate('');
    setScore('');
    setNotes('');
    setTrainerName('');
    setLocation('');
    setCertificateFile(null);
    setErrors({});
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Record Training Completion</h2>
            <p className="text-sm text-gray-600 mt-1">
              Record completion details and upload certificate
            </p>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {/* Worker and Training Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Worker *
              </label>
              <select
                value={selectedWorkerId}
                onChange={(e) => setSelectedWorkerId(Number(e.target.value))}
                className={`w-full px-3 py-2 border rounded-md focus:ring-green-500 focus:border-green-500 ${
                  errors.worker ? 'border-red-300' : 'border-gray-300'
                }`}
                disabled={!!workerId}
              >
                <option value={0}>Select a worker...</option>
                {workers.map(worker => (
                  <option key={worker.id} value={worker.id}>
                    {worker.name} - {worker.nationalId}
                  </option>
                ))}
              </select>
              {errors.worker && (
                <p className="mt-1 text-sm text-red-600">{errors.worker}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Training Program *
              </label>
              <select
                value={selectedTrainingId}
                onChange={(e) => setSelectedTrainingId(Number(e.target.value))}
                className={`w-full px-3 py-2 border rounded-md focus:ring-green-500 focus:border-green-500 ${
                  errors.training ? 'border-red-300' : 'border-gray-300'
                }`}
                disabled={!!trainingId}
              >
                <option value={0}>Select a training...</option>
                {trainings.map(training => (
                  <option key={training.id} value={training.id}>
                    {training.name}
                  </option>
                ))}
              </select>
              {errors.training && (
                <p className="mt-1 text-sm text-red-600">{errors.training}</p>
              )}
            </div>
          </div>

          {/* Training Details */}
          {selectedTraining && (
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
              <div className="flex items-start">
                <Award className="h-5 w-5 text-blue-500 mt-0.5 mr-3" />
                <div>
                  <h4 className="text-sm font-medium text-blue-900">
                    {selectedTraining.name}
                  </h4>
                  <p className="text-sm text-blue-700 mt-1">
                    {selectedTraining.description}
                  </p>
                  <div className="mt-2 text-xs text-blue-600">
                    Type: {selectedTraining.trainingType} • 
                    Validity: {selectedTraining.validityPeriodMonths ? 
                      `${selectedTraining.validityPeriodMonths} months` : 'N/A'}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Completion Details */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Completion Date *
              </label>
              <input
                type="date"
                value={completionDate}
                onChange={(e) => setCompletionDate(e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-green-500 focus:border-green-500 ${
                  errors.completionDate ? 'border-red-300' : 'border-gray-300'
                }`}
              />
              {errors.completionDate && (
                <p className="mt-1 text-sm text-red-600">{errors.completionDate}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Expiry Date
                {selectedTraining?.validityPeriodMonths && (
                  <span className="text-xs text-gray-500 ml-1">(Auto-calculated)</span>
                )}
              </label>
              <input
                type="date"
                value={expiryDate}
                onChange={(e) => setExpiryDate(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Score (0-100)
              </label>
              <input
                type="number"
                min="0"
                max="100"
                value={score}
                onChange={(e) => setScore(e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-green-500 focus:border-green-500 ${
                  errors.score ? 'border-red-300' : 'border-gray-300'
                }`}
                placeholder="Enter score..."
              />
              {errors.score && (
                <p className="mt-1 text-sm text-red-600">{errors.score}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Trainer Name
              </label>
              <input
                type="text"
                value={trainerName}
                onChange={(e) => setTrainerName(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
                placeholder="Enter trainer name..."
              />
            </div>
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Training Location
            </label>
            <input
              type="text"
              value={location}
              onChange={(e) => setLocation(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
              placeholder="Enter training location..."
            />
          </div>

          {/* Certificate Upload */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Training Certificate
            </label>
            <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
              <div className="space-y-1 text-center">
                {certificateFile ? (
                  <div className="flex items-center justify-center">
                    <FileText className="h-8 w-8 text-green-500" />
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-900">
                        {certificateFile.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {(certificateFile.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                  </div>
                ) : (
                  <>
                    <Upload className="mx-auto h-12 w-12 text-gray-400" />
                    <div className="flex text-sm text-gray-600">
                      <label className="relative cursor-pointer bg-white rounded-md font-medium text-green-600 hover:text-green-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-green-500">
                        <span>Upload a file</span>
                        <input
                          type="file"
                          className="sr-only"
                          accept=".pdf,.jpg,.jpeg,.png"
                          onChange={handleFileUpload}
                        />
                      </label>
                      <p className="pl-1">or drag and drop</p>
                    </div>
                    <p className="text-xs text-gray-500">
                      PDF, PNG, JPG up to 5MB
                    </p>
                  </>
                )}
              </div>
            </div>
            {errors.certificate && (
              <p className="mt-1 text-sm text-red-600">{errors.certificate}</p>
            )}
          </div>

          {/* Notes */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Notes
            </label>
            <textarea
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
              placeholder="Add any additional notes about the training completion..."
            />
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-50">
          <button
            onClick={handleClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700"
          >
            Record Completion
          </button>
        </div>
      </div>
    </div>
  );
};

export default TrainingCompletionModal;
