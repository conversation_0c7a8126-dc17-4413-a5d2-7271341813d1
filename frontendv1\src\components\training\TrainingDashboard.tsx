﻿import React, { useState, useEffect } from "react";
import {
  Users,
  Calendar,
  Clock,
  AlertTriangle,
  CalendarPlus,
  BookOpen,
  FileText,
} from 'lucide-react';
import KPICard from "../permits/shared/KPICard";
import QuickActions, { QuickActionItem } from '../shared/QuickActions';

interface TrainingDashboardProps {
  siteId: string;
  onNavigateToTab: (tabId: string) => void;
}

// Mock data for the new training structure
const mockTrainingStats = {
  fullyTrainedWorkers: 42,
  scheduledTrainings: 8,
  expiringSoon: 6,
  expired: 3,
};

const mockTrainingAlerts = [
  {
    id: 1,
    workerName: "<PERSON>",
    photoUrl: "https://randomuser.me/api/portraits/men/1.jpg",
    trainings: "First Aid, Fire Safety",
    expiresDate: "2024-09-15",
    daysLeft: 17,
  },
  {
    id: 2,
    workerName: "<PERSON>",
    photoUrl: "https://randomuser.me/api/portraits/women/2.jpg",
    trainings: "Confined Space",
    expiresDate: "2024-09-10",
    daysLeft: 12,
  },
  {
    id: 3,
    workerName: "<PERSON>",
    photoUrl: "https://randomuser.me/api/portraits/men/3.jpg",
    trainings: "Working at Height",
    expiresDate: "2024-09-05",
    daysLeft: 7,
  },
  {
    id: 4,
    workerName: "Emma Davis",
    photoUrl: "https://randomuser.me/api/portraits/women/4.jpg",
    trainings: "Manual Handling",
    expiresDate: "2024-09-01",
    daysLeft: 3,
  },
];

const TrainingDashboard: React.FC<TrainingDashboardProps> = ({ siteId, onNavigateToTab }) => {
  const [stats, setStats] = useState(mockTrainingStats);
  const [trainingAlerts, setTrainingAlerts] = useState(mockTrainingAlerts);

  useEffect(() => {
    // TODO: Replace with actual API calls
    // fetchTrainingStats(siteId);
    // fetchTrainingAlerts(siteId);
  }, [siteId]);

  const quickActions: QuickActionItem[] = [
    {
      title: "Schedule Training",
      description: "Schedule new training sessions for workers",
      icon: <CalendarPlus className="h-6 w-6 text-blue-600" />,
      onClick: () => {
        // TODO: Open schedule training modal
        console.log("Schedule training clicked");
      },
    },
    {
      title: "Track Expiring",
      description: "View and manage expiring certifications",
      icon: <Clock className="h-6 w-6 text-blue-600" />,
      onClick: () => onNavigateToTab('alerts'),
    },
    {
      title: "View Programmes",
      description: "Browse available training programmes",
      icon: <BookOpen className="h-6 w-6 text-blue-600" />,
      onClick: () => onNavigateToTab('library'),
    },
    {
      title: "View Reports",
      description: "Access training compliance reports",
      icon: <FileText className="h-6 w-6 text-blue-600" />,
      onClick: () => onNavigateToTab('reports'),
    },
  ];

  const handleScheduleTraining = (workerId: number) => {
    // TODO: Open schedule training modal for specific worker
    console.log("Schedule training for worker:", workerId);
  };

  const getDaysLeftColor = (daysLeft: number) => {
    if (daysLeft <= 7) return "text-red-600";
    if (daysLeft <= 14) return "text-orange-600";
    return "text-green-600";
  };

  return (
    <div className="space-y-6">
      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <KPICard
          title="Fully Trained Workers"
          value={stats.fullyTrainedWorkers}
          icon={<Users className="h-6 w-6 text-green-500" />}
        />
        <KPICard
          title="Scheduled Trainings"
          value={stats.scheduledTrainings}
          icon={<Calendar className="h-6 w-6 text-blue-500" />}
        />
        <KPICard
          title="Expiring Soon"
          value={stats.expiringSoon}
          icon={<Clock className="h-6 w-6 text-orange-500" />}
        />
        <KPICard
          title="Expired"
          value={stats.expired}
          icon={<AlertTriangle className="h-6 w-6 text-red-500" />}
        />
      </div>

      {/* Quick Actions */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <QuickActions actions={quickActions} />
      </div>

      {/* Training Alerts List */}
      <div className="bg-white rounded-lg border-2 border-gray-200 shadow-sm overflow-hidden" style={{ borderRadius: '5px' }}>
        <div className="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Training Alerts</h3>
          </div>
          <button
            className="text-sm text-green-600 hover:text-green-800"
            onClick={() => onNavigateToTab('alerts')}
          >
            View All
          </button>
        </div>
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Worker</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trainings</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expires</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Days Left</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {trainingAlerts.map((alert) => (
              <tr key={alert.id} className="hover:bg-gray-50 transition-colors">
                <td className="px-6 py-4">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-8 w-8">
                      <img
                        className="h-8 w-8 rounded-full object-cover border-2 border-gray-200"
                        src={alert.photoUrl || `https://ui-avatars.com/api/?name=${encodeURIComponent(alert.workerName)}&background=10B981&color=fff`}
                        alt={alert.workerName}
                      />
                    </div>
                    <div className="ml-3">
                      <div className="text-sm font-medium text-gray-900">{alert.workerName}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm text-gray-600">{alert.trainings}</div>
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm text-gray-600">{alert.expiresDate}</div>
                </td>
                <td className="px-6 py-4">
                  <div className={`text-sm font-medium ${getDaysLeftColor(alert.daysLeft)}`}>
                    {alert.daysLeft} days
                  </div>
                </td>
                <td className="px-6 py-4">
                  <button
                    onClick={() => handleScheduleTraining(alert.id)}
                    className="px-3 py-1 text-xs border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors"
                    style={{ borderRadius: '5px' }}
                  >
                    Schedule
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default TrainingDashboard;
