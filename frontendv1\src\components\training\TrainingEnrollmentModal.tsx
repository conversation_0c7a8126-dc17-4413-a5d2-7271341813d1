import React, { useState, useEffect } from 'react';
import {
  X,
  Calendar,
  Clock,
  MapPin,
  Users,
  User,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react';
import { 
  mockTrainingSessions, 
  mockSessionEnrollments, 
  mockWorkers,
  type TrainingSession,
  type SessionEnrollment,
  type Worker
} from '../../data/trainingData';

interface TrainingEnrollmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  siteId: string;
}

const TrainingEnrollmentModal: React.FC<TrainingEnrollmentModalProps> = ({
  isOpen,
  onClose,
  siteId
}) => {
  const [selectedSession, setSelectedSession] = useState<string>('');
  const [selectedWorkers, setSelectedWorkers] = useState<string[]>([]);
  const [availableSessions, setAvailableSessions] = useState<TrainingSession[]>([]);
  const [availableWorkers, setAvailableWorkers] = useState<Worker[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (isOpen) {
      // Fetch only company-scheduled training sessions that are open for enrollment
      const upcomingSessions = mockTrainingSessions.filter(session => {
        const sessionDate = new Date(session.startDate);
        const now = new Date();
        return sessionDate > now && session.status === 'scheduled' && session.currentEnrollment < session.maxParticipants;
      });
      setAvailableSessions(upcomingSessions);

      // Fetch site workers (mock data - would normally filter by siteId)
      setAvailableWorkers(mockWorkers);
    }
  }, [isOpen, siteId]);

  const handleWorkerToggle = (workerId: string) => {
    setSelectedWorkers(prev => 
      prev.includes(workerId) 
        ? prev.filter(id => id !== workerId)
        : [...prev, workerId]
    );
  };

  const handleEnrollment = async () => {
    if (!selectedSession || selectedWorkers.length === 0) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Simulate API call for enrollment
      await new Promise(resolve => setTimeout(resolve, 1500));

      const enrollments = selectedWorkers.map(workerId => {
        const worker = availableWorkers.find(w => w.id === workerId);
        return {
          id: `en-${Date.now()}-${workerId}`,
          workerId,
          workerName: worker?.name || '',
          workerDepartment: worker?.department || '',
          scheduledSessionId: selectedSession,
          enrollmentStatus: 'registered' as const,
          registrationDate: new Date(),
          completionStatus: 'not-started' as const
        };
      });

      console.log('Workers enrolled:', enrollments);
      
      // Reset form and close modal
      setSelectedSession('');
      setSelectedWorkers([]);
      onClose();
    } catch (error) {
      console.error('Enrollment failed:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const selectedSessionDetails = availableSessions.find(s => s.id === selectedSession);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Enroll Workers in Training</h2>
            <p className="text-sm text-gray-600 mt-1">
              Select from company-scheduled training programs
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="flex-1 overflow-y-auto p-6 space-y-6">
          {/* Available Training Sessions */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-3">
              Available Training Sessions
            </label>
            
            {availableSessions.length === 0 ? (
              <div className="text-center py-8 bg-gray-50 rounded-lg">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Available Sessions</h3>
                <p className="text-gray-500">
                  There are no company-scheduled training sessions available for enrollment at this time.
                </p>
              </div>
            ) : (
              <div className="space-y-3">
                {availableSessions.map(session => (
                  <div
                    key={session.id}
                    className={`border rounded-lg p-4 cursor-pointer transition-all ${
                      selectedSession === session.id
                        ? 'border-green-500 bg-green-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedSession(session.id)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h3 className="text-lg font-medium text-gray-900">{session.programName}</h3>
                        
                        <div className="mt-2 space-y-1 text-sm text-gray-600">
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-2" />
                            {new Date(session.startDate).toLocaleDateString()} - {new Date(session.endDate).toLocaleDateString()}
                          </div>
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-2" />
                            {new Date(session.startDate).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} - {new Date(session.endDate).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                          </div>
                          <div className="flex items-center">
                            <MapPin className="h-4 w-4 mr-2" />
                            {session.provider.trainingLocation}
                          </div>
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-2" />
                            Provider: {session.provider.name}
                          </div>
                        </div>
                      </div>
                      
                      <div className="text-right">
                        <div className="flex items-center text-sm text-gray-600 mb-2">
                          <Users className="h-4 w-4 mr-1" />
                          {session.currentEnrollment}/{session.maxParticipants} enrolled
                        </div>
                        <div className="w-24 bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-green-500 h-2 rounded-full"
                            style={{ width: `${(session.currentEnrollment / session.maxParticipants) * 100}%` }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Worker Selection */}
          {selectedSession && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                Select Workers to Enroll
              </label>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 max-h-60 overflow-y-auto">
                {availableWorkers.map(worker => (
                  <div
                    key={worker.id}
                    className={`border rounded-lg p-3 cursor-pointer transition-all ${
                      selectedWorkers.includes(worker.id)
                        ? 'border-green-500 bg-green-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => handleWorkerToggle(worker.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium text-gray-900">{worker.name}</h4>
                        <p className="text-sm text-gray-600">{worker.department} • {worker.position}</p>
                        <p className="text-xs text-gray-500">{worker.email}</p>
                      </div>
                      {selectedWorkers.includes(worker.id) && (
                        <CheckCircle className="h-5 w-5 text-green-600" />
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {selectedWorkers.length > 0 && selectedSessionDetails && (
                <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-start space-x-3">
                    <Info className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <h4 className="text-sm font-medium text-blue-800">Enrollment Summary</h4>
                      <div className="mt-2 text-sm text-blue-700 space-y-1">
                        <p>• {selectedWorkers.length} workers selected for enrollment</p>
                        <p>• Training: {selectedSessionDetails.programName}</p>
                        <p>• Available spots: {selectedSessionDetails.maxParticipants - selectedSessionDetails.currentEnrollment}</p>
                        {selectedWorkers.length > (selectedSessionDetails.maxParticipants - selectedSessionDetails.currentEnrollment) && (
                          <p className="text-red-600 font-medium">
                            ⚠️ Not enough spots available for all selected workers
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={handleEnrollment}
            disabled={!selectedSession || selectedWorkers.length === 0 || isSubmitting}
            className="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2 inline-block"></div>
                Enrolling...
              </>
            ) : (
              <>Enroll Workers</>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default TrainingEnrollmentModal;
