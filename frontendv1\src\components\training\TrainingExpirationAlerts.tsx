import React, { useState, useEffect, useMemo } from 'react';
import {
  Bell,
  AlertTriangle,
  Clock,
  User,
  Calendar,
  CheckCircle,
  X,
  Settings,
  Filter,
  Download
} from 'lucide-react';
import { useTenantContext } from '../../hooks/useTenantContext.tsx';
import UniversalFilter from '../shared/UniversalFilter';

interface ExpiringTraining {
  id: number;
  workerId: number;
  workerName: string;
  workerPhoto?: string;
  trainingId: number;
  trainingName: string;
  completionDate: string;
  expiryDate: string;
  daysUntilExpiry: number;
  priority: 'critical' | 'high' | 'medium' | 'low';
  status: 'active' | 'expiring' | 'expired';
  tradeId?: number;
  tradeName?: string;
  siteId?: string;
  siteName?: string;
}

interface AlertSettings {
  criticalDays: number; // Red alerts
  warningDays: number;  // Yellow alerts
  infoDays: number;     // Blue alerts
  enableEmailAlerts: boolean;
  enableSMSAlerts: boolean;
  enablePushNotifications: boolean;
}

interface TrainingExpirationAlertsProps {
  siteId?: string;
  showSettings?: boolean;
}

const TrainingExpirationAlerts: React.FC<TrainingExpirationAlertsProps> = ({
  siteId,
  showSettings = false
}) => {
  const { tenantId } = useTenantContext();
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10;

  useEffect(() => {
    // Simulate loading
    setLoading(true);
    setTimeout(() => setLoading(false), 500);
  }, [siteId]);

  // Status filters for training alerts
  const statusFilters = [
    { id: 'expiring', name: 'Expiring Soon' },
    { id: 'expired', name: 'Expired' },
    { id: 'critical', name: 'Critical' },
    { id: 'high', name: 'High Priority' }
  ];

  // Mock data for demonstration
  const mockExpiringTrainings: ExpiringTraining[] = [
    {
      id: 1,
      workerId: 1,
      workerName: 'David Kamau',
      workerPhoto: 'https://randomuser.me/api/portraits/men/1.jpg',
      trainingId: 1,
      trainingName: 'Working at Heights Safety',
      completionDate: '2024-01-15T00:00:00Z',
      expiryDate: '2024-02-15T00:00:00Z',
      daysUntilExpiry: 5,
      priority: 'critical',
      status: 'expiring',
      tradeId: 1,
      tradeName: 'Carpenter',
      siteId: 'site1',
      siteName: 'Main Construction Site'
    },
    {
      id: 2,
      workerId: 2,
      workerName: 'Mary Wanjiku',
      workerPhoto: 'https://randomuser.me/api/portraits/women/2.jpg',
      trainingId: 2,
      trainingName: 'First Aid Level 1',
      completionDate: '2023-12-01T00:00:00Z',
      expiryDate: '2024-03-01T00:00:00Z',
      daysUntilExpiry: 20,
      priority: 'high',
      status: 'expiring',
      tradeId: 2,
      tradeName: 'Electrician',
      siteId: 'site1',
      siteName: 'Main Construction Site'
    },
    {
      id: 3,
      workerId: 3,
      workerName: 'Peter Ochieng',
      workerPhoto: 'https://randomuser.me/api/portraits/men/3.jpg',
      trainingId: 3,
      trainingName: 'Equipment Operation',
      completionDate: '2023-11-15T00:00:00Z',
      expiryDate: '2024-03-15T00:00:00Z',
      daysUntilExpiry: 35,
      priority: 'medium',
      status: 'expiring',
      tradeId: 3,
      tradeName: 'Plumber',
      siteId: 'site1',
      siteName: 'Main Construction Site'
    },
    {
      id: 4,
      workerId: 4,
      workerName: 'Jane Doe',
      workerPhoto: 'https://randomuser.me/api/portraits/women/4.jpg',
      trainingId: 1,
      trainingName: 'Working at Heights Safety',
      completionDate: '2023-12-01T00:00:00Z',
      expiryDate: '2024-01-20T00:00:00Z',
      daysUntilExpiry: -10,
      priority: 'critical',
      status: 'expired',
      tradeId: 1,
      tradeName: 'Carpenter',
      siteId: 'site1',
      siteName: 'Main Construction Site'
    },
    {
      id: 5,
      workerId: 5,
      workerName: 'Sarah Johnson',
      workerPhoto: 'https://randomuser.me/api/portraits/women/5.jpg',
      trainingId: 2,
      trainingName: 'Confined Space Entry',
      completionDate: '2023-11-01T00:00:00Z',
      expiryDate: '2024-09-10T00:00:00Z',
      daysUntilExpiry: 12,
      priority: 'high',
      status: 'expiring',
      tradeId: 2,
      tradeName: 'Electrician',
      siteId: 'site1',
      siteName: 'Main Construction Site'
    },
    {
      id: 6,
      workerId: 6,
      workerName: 'Mike Wilson',
      workerPhoto: 'https://randomuser.me/api/portraits/men/6.jpg',
      trainingId: 3,
      trainingName: 'Manual Handling',
      completionDate: '2023-12-15T00:00:00Z',
      expiryDate: '2024-09-01T00:00:00Z',
      daysUntilExpiry: 3,
      priority: 'critical',
      status: 'expiring',
      tradeId: 3,
      tradeName: 'Plumber',
      siteId: 'site1',
      siteName: 'Main Construction Site'
    }
  ];

  useEffect(() => {
    // Simulate loading
    setLoading(true);
    setTimeout(() => setLoading(false), 500);
  }, [siteId]);

  // Filtered trainings using useMemo for performance
  const filteredTrainingsAll = useMemo(() => {
    return mockExpiringTrainings.filter((training) => {
      // Search filter
      const matchesSearch = searchQuery === "" ||
        training.workerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        training.trainingName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (training.tradeName && training.tradeName.toLowerCase().includes(searchQuery.toLowerCase()));

      // Status filter
      const matchesStatus = selectedStatus === "all" ||
        training.status === selectedStatus ||
        training.priority === selectedStatus;

      return matchesSearch && matchesStatus;
    });
  }, [searchQuery, selectedStatus]);

  // Paginated trainings
  const totalPages = Math.ceil(filteredTrainingsAll.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const filteredTrainings = filteredTrainingsAll.slice(startIndex, startIndex + itemsPerPage);

  const handleScheduleTraining = (trainingId: number) => {
    // TODO: Open schedule training modal for specific training
    console.log("Schedule training for training:", trainingId);
  };

  const getDaysLeftColor = (daysLeft: number) => {
    if (daysLeft <= 0) return "text-red-600";
    if (daysLeft <= 7) return "text-red-600";
    if (daysLeft <= 14) return "text-orange-600";
    return "text-green-600";
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Training Alerts</h2>
          <p className="text-sm text-gray-600">Monitor expiring and expired training certifications</p>
        </div>
        <div className="text-sm text-gray-600">
          Total {filteredTrainingsAll.length} alerts
        </div>
      </div>

      {/* Search and Filter */}
      <UniversalFilter
        variant="search-tags"
        searchQuery={searchQuery}
        onSearchChange={setSearchQuery}
        searchPlaceholder="Search by worker name, training name, or trade..."
        tags={[{ id: "all", name: "All" }, ...statusFilters]}
        selectedTagId={selectedStatus}
        onTagChange={setSelectedStatus}
      />

      {/* Training Alerts Table */}
      <div className="bg-white rounded-lg border-2 border-gray-200 shadow-sm overflow-hidden" style={{ borderRadius: '5px' }}>
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Worker</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trainings</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expires</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Days Left</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredTrainings.map((training) => (
              <tr key={training.id} className="hover:bg-gray-50 transition-colors">
                <td className="px-6 py-4">
                  <div className="flex items-center">
                    <div className="flex-shrink-0 h-8 w-8">
                      <img
                        className="h-8 w-8 rounded-full object-cover border-2 border-gray-200"
                        src={training.workerPhoto || `https://ui-avatars.com/api/?name=${encodeURIComponent(training.workerName)}&background=10B981&color=fff`}
                        alt={training.workerName}
                      />
                    </div>
                    <div className="ml-3">
                      <div className="text-sm font-medium text-gray-900">{training.workerName}</div>
                      <div className="text-xs text-gray-500">{training.tradeName}</div>
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm text-gray-600">{training.trainingName}</div>
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm text-gray-600">{new Date(training.expiryDate).toLocaleDateString()}</div>
                </td>
                <td className="px-6 py-4">
                  <div className={`text-sm font-medium ${getDaysLeftColor(training.daysUntilExpiry)}`}>
                    {training.daysUntilExpiry <= 0
                      ? `${Math.abs(training.daysUntilExpiry)} days overdue`
                      : `${training.daysUntilExpiry} days`
                    }
                  </div>
                </td>
                <td className="px-6 py-4">
                  <button
                    onClick={() => handleScheduleTraining(training.id)}
                    className="px-3 py-1 text-xs border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors"
                    style={{ borderRadius: '5px' }}
                  >
                    Schedule
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Showing {startIndex + 1} to {Math.min(startIndex + itemsPerPage, filteredTrainingsAll.length)} of {filteredTrainingsAll.length} results
          </div>
          <div className="flex space-x-2">
            <button
              onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
              className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <span className="px-3 py-1 text-sm text-gray-700">
              Page {currentPage} of {totalPages}
            </span>
            <button
              onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
              className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default TrainingExpirationAlerts;
