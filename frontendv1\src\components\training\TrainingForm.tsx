import React from "react";

interface TrainingFormProps {
	onSubmit: (e: React.FormEvent) => void;
	onCancel: () => void;
	initialData?: any;
}

const TrainingForm: React.FC<TrainingFormProps> = ({
	onSubmit,
	onCancel,
	initialData = {},
}) => {
	return (
		<form onSubmit={onSubmit} className="space-y-6">
			<div className="space-y-4">
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-1">
						Training Name
					</label>
					<input
						type="text"
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
						placeholder="Name of the training"
						defaultValue={initialData.name || ""}
						required
					/>
				</div>

				<div>
					<label className="block text-sm font-medium text-gray-700 mb-1">
						Description
					</label>
					<textarea
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
						rows={3}
						placeholder="Description of training"
						defaultValue={initialData.description || ""}
					/>
				</div>

				<div>
					<label className="block text-sm font-medium text-gray-700 mb-1">
						Training Type
					</label>
					<input
						type="text"
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
						placeholder="Type of the training"
						defaultValue={initialData.type || ""}
					/>
				</div>

				<div>
					<label className="block text-sm font-medium text-gray-700 mb-1">
						Trainer
					</label>
					<input
						type="text"
						className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
						placeholder="Trainer of the training"
						defaultValue={initialData.trainer || ""}
					/>
				</div>

				<h2 className="text-lg font-medium text-gray-800 pt-4">
					Schedule Information
				</h2>

				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div>
						<label className="block text-sm font-medium text-gray-700 mb-1">
							Start Date
						</label>
						<input
							type="datetime-local"
							className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
							defaultValue={initialData.startDate || ""}
						/>
					</div>

					<div>
						<label className="block text-sm font-medium text-gray-700 mb-1">
							End Date
						</label>
						<input
							type="datetime-local"
							className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
							defaultValue={initialData.endDate || ""}
						/>
					</div>
				</div>

				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div>
						<label className="block text-sm font-medium text-gray-700 mb-1">
							Duration
						</label>
						<input
							type="text"
							className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
							placeholder="2 hours, 1 day"
							defaultValue={initialData.duration || ""}
						/>
					</div>

					<div>
						<label className="block text-sm font-medium text-gray-700 mb-1">
							Frequency
						</label>
						<input
							type="text"
							className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
							placeholder="Annual, Monthly, One-time"
							defaultValue={initialData.frequency || ""}
						/>
					</div>
				</div>

				<h2 className="text-lg font-medium text-gray-800 pt-4">
					Certification
				</h2>

				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div>
						<label className="block text-sm font-medium text-gray-700 mb-1">
							Validity Period (Months)
						</label>
						<input
							type="number"
							className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
							min="1"
							defaultValue={initialData.validityPeriod || ""}
						/>
					</div>

					<div>
						<label className="block text-sm font-medium text-gray-700 mb-1">
							Status
						</label>
						<select
							className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
							defaultValue={initialData.status || "Scheduled"}
						>
							<option value="Scheduled">Scheduled</option>
							<option value="Active">Active</option>
							<option value="Completed">Completed</option>
							<option value="Cancelled">Cancelled</option>
						</select>
					</div>
				</div>
			</div>

			<div className="flex justify-end space-x-3 pt-4">
				<button
					type="button"
					onClick={onCancel}
					className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
				>
					Cancel
				</button>
				<button
					type="submit"
					className="px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700"
				>
					Save Training
				</button>
			</div>
		</form>
	);
};

export default TrainingForm;
