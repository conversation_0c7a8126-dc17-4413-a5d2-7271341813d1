import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { 
  BookOpen, 
  Clock, 
  Users, 
  Search, 
  Filter,
  Calendar,
  Shield,
  AlertTriangle,
  HardHat,
  Zap,
  Flame,
  Droplets,
  Plus
} from 'lucide-react';

interface TrainingLibraryProps {
  siteId: string;
}

interface TrainingProgram {
  id: string;
  name: string;
  description: string;
  validityPeriod: string;
  duration: string;
  providers: string[];
  category: 'safety' | 'technical' | 'compliance' | 'emergency';
  frequency: string;
  targetAudience: string;
  regulatoryBasis?: string;
}

// Mock training programs data
const mockTrainingPrograms: TrainingProgram[] = [
  {
    id: 'tp-001',
    name: 'Safety Induction Training',
    description: 'Comprehensive safety orientation covering HSE policies, legal requirements, fire safety, and site-specific hazards for all new workers.',
    validityPeriod: 'Valid for 1 year',
    duration: '4 hours',
    providers: ['Internal HSE Team', 'Terrace Ventures & Safety Solutions'],
    category: 'safety',
    frequency: 'On hire and annually',
    targetAudience: 'All new workers and visitors',
    regulatoryBasis: 'OSHA, 2007'
  },
  {
    id: 'tp-002',
    name: 'First Aid Training',
    description: 'Emergency first aid and CPR training covering basic life support, injury management, and emergency response procedures.',
    validityPeriod: 'Valid for 2 years',
    duration: '16 hours',
    providers: ['Terrace Ventures & Safety Solutions Limited', 'Red Cross Kenya'],
    category: 'emergency',
    frequency: 'Biennially',
    targetAudience: 'Designated first aiders',
    regulatoryBasis: 'First Aid Rules, 1977'
  },
  {
    id: 'tp-003',
    name: 'Fire Marshal Training',
    description: 'Fire safety and emergency evacuation procedures training for designated fire marshals and emergency response team members.',
    validityPeriod: 'Valid for 1 year',
    duration: '8 hours',
    providers: ['Strategic SHE Limited', 'Fire Safety Kenya'],
    category: 'emergency',
    frequency: 'Annually',
    targetAudience: 'Fire marshals and emergency team',
    regulatoryBasis: 'Fire Reduction Rules, 2007'
  },
  {
    id: 'tp-004',
    name: 'Working at Heights',
    description: 'Safety training for working at elevated positions, including fall protection, scaffold safety, and ladder safety procedures.',
    validityPeriod: 'Valid for 1 year',
    duration: '6 hours',
    providers: ['Height Safety Solutions', 'Internal HSE Team'],
    category: 'safety',
    frequency: 'Annually',
    targetAudience: 'Workers performing elevated work',
    regulatoryBasis: 'OSHA, 2007'
  },
  {
    id: 'tp-005',
    name: 'Electrical Safety Training',
    description: 'Electrical hazard awareness, lockout/tagout procedures, and safe electrical work practices for qualified electrical workers.',
    validityPeriod: 'Valid for 1 year',
    duration: '8 hours',
    providers: ['Electrical Safety Institute', 'Internal Electrical Team'],
    category: 'technical',
    frequency: 'Annually',
    targetAudience: 'Electrical workers and supervisors',
    regulatoryBasis: 'Electrical Safety Regulations'
  },
  {
    id: 'tp-006',
    name: 'Confined Space Entry',
    description: 'Safety procedures for entering and working in confined spaces, including gas testing, ventilation, and emergency rescue.',
    validityPeriod: 'Valid for 1 year',
    duration: '12 hours',
    providers: ['Confined Space Specialists', 'Internal HSE Team'],
    category: 'safety',
    frequency: 'Annually',
    targetAudience: 'Workers entering confined spaces',
    regulatoryBasis: 'Confined Space Regulations'
  },
  {
    id: 'tp-007',
    name: 'OSH Committee Training',
    description: 'Training for OSH committee members on their roles, responsibilities, and effective committee operation procedures.',
    validityPeriod: 'Valid for 1 year',
    duration: '4 hours',
    providers: ['Essenspark Limited', 'Internal HSE Team'],
    category: 'compliance',
    frequency: 'Annually',
    targetAudience: 'OSH Committee members',
    regulatoryBasis: 'Health and Safety Committee Rules, 2004'
  },
  {
    id: 'tp-008',
    name: 'Hazard Communication',
    description: 'Training on chemical safety, SDS understanding, and proper handling of hazardous materials in the workplace.',
    validityPeriod: 'Valid for 1 year',
    duration: '3 hours',
    providers: ['Chemical Safety Consultants', 'Internal HSE Team'],
    category: 'safety',
    frequency: 'Annually',
    targetAudience: 'Workers handling chemicals',
    regulatoryBasis: 'Hazard Communication Standard'
  },
  {
    id: 'tp-009',
    name: 'Heavy Equipment Operation',
    description: 'Safety training for operators of heavy machinery including cranes, excavators, and other construction equipment.',
    validityPeriod: 'Valid for 2 years',
    duration: '20 hours',
    providers: ['Heavy Equipment Training Institute', 'Internal Equipment Team'],
    category: 'technical',
    frequency: 'Biennially',
    targetAudience: 'Heavy equipment operators',
    regulatoryBasis: 'Equipment Safety Regulations'
  },
  {
    id: 'tp-010',
    name: 'Environmental Awareness',
    description: 'Environmental protection training covering waste management, pollution prevention, and environmental compliance requirements.',
    validityPeriod: 'Valid for 1 year',
    duration: '4 hours',
    providers: ['Environmental Consultants Ltd', 'Internal HSE Team'],
    category: 'compliance',
    frequency: 'Annually',
    targetAudience: 'All workers',
    regulatoryBasis: 'Environmental Management Act'
  },
  {
    id: 'tp-011',
    name: 'Incident Investigation',
    description: 'Training on proper incident investigation techniques, root cause analysis, and corrective action implementation.',
    validityPeriod: 'Valid for 1 year',
    duration: '6 hours',
    providers: ['Incident Investigation Specialists', 'Internal HSE Team'],
    category: 'compliance',
    frequency: 'Annually',
    targetAudience: 'Supervisors and HSE personnel',
    regulatoryBasis: 'OSHA, 2007'
  },
  {
    id: 'tp-012',
    name: 'Personal Protective Equipment',
    description: 'Training on proper selection, use, maintenance, and limitations of various types of personal protective equipment.',
    validityPeriod: 'Valid for 1 year',
    duration: '2 hours',
    providers: ['PPE Safety Consultants', 'Internal HSE Team'],
    category: 'safety',
    frequency: 'Annually',
    targetAudience: 'All workers',
    regulatoryBasis: 'PPE Regulations'
  }
];

const TrainingLibrary: React.FC<TrainingLibraryProps> = ({ siteId }) => {
  const navigate = useNavigate();
  const [trainingPrograms, setTrainingPrograms] = useState<TrainingProgram[]>(mockTrainingPrograms);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // TODO: Fetch actual training programs from API
    setLoading(true);
    setTimeout(() => setLoading(false), 500);
  }, [siteId]);

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'safety':
        return <Shield className="h-5 w-5" />;
      case 'technical':
        return <HardHat className="h-5 w-5" />;
      case 'compliance':
        return <AlertTriangle className="h-5 w-5" />;
      case 'emergency':
        return <Zap className="h-5 w-5" />;
      default:
        return <BookOpen className="h-5 w-5" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'safety':
        return 'bg-blue-100 text-blue-600 border-blue-200';
      case 'technical':
        return 'bg-orange-100 text-orange-600 border-orange-200';
      case 'compliance':
        return 'bg-purple-100 text-purple-600 border-purple-200';
      case 'emergency':
        return 'bg-red-100 text-red-600 border-red-200';
      default:
        return 'bg-gray-100 text-gray-600 border-gray-200';
    }
  };

  const filteredPrograms = trainingPrograms.filter(program => {
    const matchesSearch = program.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         program.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         program.targetAudience.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || program.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const categories = [
    { id: 'all', name: 'All Programs' },
    { id: 'safety', name: 'Safety Training' },
    { id: 'technical', name: 'Technical Training' },
    { id: 'compliance', name: 'Compliance Training' },
    { id: 'emergency', name: 'Emergency Response' }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Training Programs Library</h2>
          <p className="text-gray-600 mt-1">
            Comprehensive catalog of mandatory training programs for company members
          </p>
        </div>
        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-500">
            {filteredPrograms.length} of {trainingPrograms.length} programs
          </span>
          <div className="text-sm text-gray-500 italic">
            Training programs can only be scheduled at company level
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search */}
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search training programs..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
          />
        </div>

        {/* Category Filter */}
        <div className="relative">
          <Filter className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent appearance-none bg-white"
          >
            {categories.map(category => (
              <option key={category.id} value={category.id}>
                {category.name}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Training Programs Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredPrograms.map(program => (
          <div
            key={program.id}
            className="bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer hover:border-gray-300"
          >
            {/* Card Header */}
            <div className="p-6 border-b border-gray-100">
              <div className="flex items-start justify-between mb-3">
                <div className={`p-2 rounded-lg border ${getCategoryColor(program.category)}`}>
                  {getCategoryIcon(program.category)}
                </div>
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${getCategoryColor(program.category)}`}>
                  {program.category.charAt(0).toUpperCase() + program.category.slice(1)}
                </span>
              </div>
              
              <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2">
                {program.name}
              </h3>
              
              <p className="text-sm text-gray-600 line-clamp-3">
                {program.description}
              </p>
            </div>

            {/* Card Content */}
            <div className="p-6 space-y-4">
              {/* Duration and Validity */}
              <div className="flex items-center justify-between text-sm">
                <div className="flex items-center text-gray-600">
                  <Clock className="h-4 w-4 mr-2" />
                  <span>{program.duration}</span>
                </div>
                <div className="text-gray-500">
                  {program.validityPeriod}
                </div>
              </div>

              {/* Target Audience */}
              <div className="flex items-start text-sm">
                <Users className="h-4 w-4 mr-2 mt-0.5 text-gray-600 flex-shrink-0" />
                <div>
                  <span className="font-medium text-gray-700">Target:</span>
                  <span className="text-gray-600 ml-1">{program.targetAudience}</span>
                </div>
              </div>

              {/* Frequency */}
              <div className="flex items-center text-sm">
                <Calendar className="h-4 w-4 mr-2 text-gray-600" />
                <span className="text-gray-600">{program.frequency}</span>
              </div>

              {/* Training Providers */}
              <div className="space-y-2">
                <div className="text-sm font-medium text-gray-700">Training Providers:</div>
                <div className="space-y-1">
                  {program.providers.map((provider, index) => (
                    <div key={index} className="text-sm text-gray-600 bg-gray-50 px-2 py-1 rounded">
                      {provider}
                    </div>
                  ))}
                </div>
              </div>

              {/* Regulatory Basis */}
              {program.regulatoryBasis && (
                <div className="pt-2 border-t border-gray-100">
                  <div className="text-xs text-gray-500">
                    <span className="font-medium">Regulatory Basis:</span> {program.regulatoryBasis}
                  </div>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Empty State */}
      {filteredPrograms.length === 0 && (
        <div className="text-center py-12">
          <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No training programs found</h3>
          <p className="text-gray-500">
            Try adjusting your search or filter criteria to find what you're looking for.
          </p>
        </div>
      )}
    </div>
  );
};

export default TrainingLibrary;
