import React, { useState, useEffect } from "react";
import { toast } from "react-toastify";

interface TrainingMatrixProps {
	siteId: string;
}

interface TrainingRow {
	id: number;
	training: string;
	targetGroup: string;
	sectionsOfLaw: string;
	frequency: string;
	trainingProvider: string;
	months: Record<string, "completed" | "scheduled" | "none">;
}

const initialTrainingData: TrainingRow[] = [
	{
		id: 1,
		training: "First Aid Training",
		targetGroup: "First Aiders",
		sectionsOfLaw: "First Aid Rules, 1977",
		frequency: "Annually",
		trainingProvider: "Terrace Ventures & Safety Solutions Limited",
		months: {
			"Dec-23": "none",
			"Jan-24": "none",
			"Feb-24": "none",
			"Mar-24": "none",
			"Apr-24": "none",
			"May-24": "completed",
			"Jun-24": "completed",
			"Jul-24": "none",
			"Aug-24": "none",
			"Sep-24": "none",
			"Oct-24": "none",
			"Nov-24": "none",
			"Dec-24": "none",
		},
	},
	{
		id: 2,
		training: "Fire Marshals Training",
		targetGroup: "Firefighting team",
		sectionsOfLaw: "Fire Reduction Rules, 2007",
		frequency: "Annually",
		trainingProvider: "Strategic SHE Limited",
		months: {
			"Dec-23": "none",
			"Jan-24": "none",
			"Feb-24": "none",
			"Mar-24": "none",
			"Apr-24": "none",
			"May-24": "none",
			"Jun-24": "completed",
			"Jul-24": "none",
			"Aug-24": "none",
			"Sep-24": "none",
			"Oct-24": "none",
			"Nov-24": "none",
			"Dec-24": "none",
		},
	},
	{
		id: 3,
		training: "OSH committee",
		targetGroup: "OSH Committee Members",
		sectionsOfLaw: "Health and Safety Committee Rules, 2004",
		frequency: "Annually",
		trainingProvider: "Essenspark Limited",
		months: {
			"Dec-23": "none",
			"Jan-24": "none",
			"Feb-24": "none",
			"Mar-24": "none",
			"Apr-24": "none",
			"May-24": "none",
			"Jun-24": "none",
			"Jul-24": "none",
			"Aug-24": "none",
			"Sep-24": "none",
			"Oct-24": "none",
			"Nov-24": "none",
			"Dec-24": "none",
		},
	},
	{
		id: 4,
		training:
			"Safety Induction - HSE Policy, legal requirements and safety culture, fire safety",
		targetGroup: "All new workers recruited to site",
		sectionsOfLaw: "OSHA, 2007",
		frequency: "To all new employees/Visitors",
		trainingProvider: "Internal",
		months: {
			"Dec-23": "none",
			"Jan-24": "completed",
			"Feb-24": "completed",
			"Mar-24": "scheduled",
			"Apr-24": "scheduled",
			"May-24": "scheduled",
			"Jun-24": "scheduled",
			"Jul-24": "scheduled",
			"Aug-24": "scheduled",
			"Sep-24": "scheduled",
			"Oct-24": "scheduled",
			"Nov-24": "scheduled",
			"Dec-24": "scheduled",
		},
	},
	{
		id: 9,
		training:
			"General risks at customers sites •Risk Assessment Method •Statement •JSA •Preventive measures",
		targetGroup: "All workers",
		sectionsOfLaw: "OSHA, 2007",
		frequency: "When they visit site",
		trainingProvider: "Internal",
		months: {
			"Dec-23": "completed",
			"Jan-24": "completed",
			"Feb-24": "completed",
			"Mar-24": "completed",
			"Apr-24": "completed",
			"May-24": "completed",
			"Jun-24": "completed",
			"Jul-24": "completed",
			"Aug-24": "completed",
			"Sep-24": "completed",
			"Oct-24": "completed",
			"Nov-24": "completed",
			"Dec-24": "completed",
		},
	},
];

const months = [
	"Dec-23",
	"Jan-24",
	"Feb-24",
	"Mar-24",
	"Apr-24",
	"May-24",
	"Jun-24",
	"Jul-24",
	"Aug-24",
	"Sep-24",
	"Oct-24",
	"Nov-24",
	"Dec-24",
];

const TrainingMatrix: React.FC<TrainingMatrixProps> = ({/*siteId*/}) => {
	const [trainingData, setTrainingData] =
		useState<TrainingRow[]>(initialTrainingData);
	const [lastUpdate, setLastUpdate] = useState<{
		rowId: number;
		month: string;
		status: string;
	} | null>(null);

	// Use useEffect to handle toast notifications
	useEffect(() => {
		if (lastUpdate) {
			const { rowId, month, status } = lastUpdate;
			const row = trainingData.find((r) => r.id === rowId);

			if (row) {
				toast.info(`${row.training} for ${month} marked as ${status}`);
			}
		}
	}, [lastUpdate, trainingData]);

	const toggleCellStatus = (rowId: number, month: string) => {
		setTrainingData((prevData) =>
			prevData.map((row) => {
				if (row.id === rowId) {
					const currentStatus = row.months[month];
					let newStatus: "completed" | "scheduled" | "none";

					// Cycle through states: none -> scheduled -> completed -> none
					if (currentStatus === "none") {
						newStatus = "scheduled";
					} else if (currentStatus === "scheduled") {
						newStatus = "completed";
					} else {
						newStatus = "none";
					}

					// Set the last update to trigger the toast in useEffect
					const statusText =
						newStatus === "completed"
							? "completed"
							: newStatus === "scheduled"
								? "scheduled"
								: "cleared";

					setLastUpdate({ rowId, month, status: statusText });

					return {
						...row,
						months: {
							...row.months,
							[month]: newStatus,
						},
					};
				}
				return row;
			}),
		);
	};

	const getCellColor = (status: "completed" | "scheduled" | "none") => {
		switch (status) {
			case "completed":
				return "bg-green-500";
			case "scheduled":
				return "bg-yellow-400";
			default:
				return "bg-white";
		}
	};

	return (
		<div className="space-y-6">
			<div className="flex justify-between items-center">
				<h2 className="text-xl font-semibold">Training Matrix</h2>
				<div className="flex space-x-2">
					<div className="flex items-center">
						<div className="w-3 h-3 bg-green-500 mr-1"></div>
						<span className="text-xs">Done</span>
					</div>
					<div className="flex items-center">
						<div className="w-3 h-3 bg-yellow-400 mr-1"></div>
						<span className="text-xs">Scheduled to be done</span>
					</div>
				</div>
			</div>

			<div className="overflow-x-auto">
				<table className="min-w-full divide-y divide-gray-200 border text-xs">
					<thead className="bg-gray-50">
						<tr>
							<th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border">
								No
							</th>
							<th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border">
								Training
							</th>
							<th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border">
								Target Group
							</th>
							<th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border">
								Sections of the Law
							</th>
							<th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border">
								Frequency
							</th>
							<th className="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border">
								Training Provider
							</th>
							{months.map((month) => (
								<th
									key={month}
									className="px-1 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider border"
									style={{ minWidth: "50px" }}
								>
									{month}
								</th>
							))}
						</tr>
					</thead>
					<tbody className="bg-white divide-y divide-gray-200">
						{trainingData.map((row, id) => (
							<tr key={row.id}>
								<td className="px-2 py-2 whitespace-nowrap text-xs text-gray-900 border">
									{id + 1}
								</td>
								<td className="px-2 py-2 text-xs text-gray-900 border">
									{row.training}
								</td>
								<td className="px-2 py-2 text-xs text-gray-900 border">
									{row.targetGroup}
								</td>
								<td className="px-2 py-2 text-xs text-gray-900 border">
									{row.sectionsOfLaw}
								</td>
								<td className="px-2 py-2 text-xs text-gray-900 border">
									{row.frequency}
								</td>
								<td className="px-2 py-2 text-xs text-gray-900 border">
									{row.trainingProvider}
								</td>
								{months.map((month) => (
									<td
										key={`${row.id}-${month}`}
										className={`border text-center cursor-pointer ${getCellColor(row.months[month])}`}
										onClick={() => toggleCellStatus(row.id, month)}
										style={{ width: "30px", height: "24px" }}
									>
										&nbsp;
									</td>
								))}
							</tr>
						))}
					</tbody>
				</table>
			</div>
		</div>
	);
};

export default TrainingMatrix;
