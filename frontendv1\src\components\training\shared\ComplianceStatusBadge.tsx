import React from "react";

interface ComplianceStatusBadgeProps {
	status: "compliant" | "expiring" | "expired" | "incomplete";
}

const ComplianceStatusBadge: React.FC<ComplianceStatusBadgeProps> = ({
	status,
}) => {
	const baseClasses = "px-2 py-1 text-xs font-medium rounded-full";

	switch (status) {
		case "compliant":
			return (
				<span className={`${baseClasses} bg-green-100 text-green-800`}>
					Compliant
				</span>
			);
		case "expiring":
			return (
				<span className={`${baseClasses} bg-amber-100 text-amber-800`}>
					Expiring Soon
				</span>
			);
		case "expired":
			return (
				<span className={`${baseClasses} bg-red-100 text-red-800`}>
					Expired
				</span>
			);
		case "incomplete":
			return (
				<span className={`${baseClasses} bg-gray-100 text-gray-800`}>
					Incomplete
				</span>
			);
		default:
			return (
				<span className={`${baseClasses} bg-gray-100 text-gray-800`}>
					{status}
				</span>
			);
	}
};

export default ComplianceStatusBadge;
