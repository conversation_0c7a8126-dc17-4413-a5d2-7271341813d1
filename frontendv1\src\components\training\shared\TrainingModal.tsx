import React, { useState } from "react";
import { X } from "lucide-react";

interface TrainingModalProps {
	isOpen: boolean;
	onClose: () => void;
	mode: "assign" | "record" | "schedule";
	workerId?: string;
	siteId: string;
}

interface AssignTrainingFormProps {
	workerId?: string;
	siteId: string;
	onSubmit: (data: any) => void;
	onCancel: () => void;
}

interface RecordCompletionFormProps {
	workerId?: string;
	siteId: string;
	onSubmit: (data: any) => void;
	onCancel: () => void;
}

export interface ScheduleTrainingFormProps {
	siteId: string;
	onSubmit: (data: any) => void;
	onCancel: () => void;
}

const AssignTrainingForm: React.FC<AssignTrainingFormProps> = ({
	workerId,
	siteId:_siteId,
	onSubmit,
	onCancel,
}) => {
	const [selectedProgram, setSelectedProgram] = useState("");
	const [selectedWorkers, _setSelectedWorkers] = useState<string[]>(
		workerId ? [workerId] : [],
	);
	const [dueDate, setDueDate] = useState("");
	const [priority, setPriority] = useState("medium");
	const [notes, setNotes] = useState("");

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		onSubmit({
			trainingProgramId: selectedProgram,
			workerIds: selectedWorkers,
			dueDate: dueDate ? new Date(dueDate) : undefined,
			priority,
			notes,
		});
	};

	return (
		<form onSubmit={handleSubmit} className="space-y-4">
			<div>
				<label className="block text-sm font-medium text-gray-700 mb-1">
					Training Program
				</label>
				<select
					className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
					value={selectedProgram}
					onChange={(e) => setSelectedProgram(e.target.value)}
					required
				>
					<option value="">Select a training program</option>
					<option value="safety-orientation">Safety Orientation</option>
					<option value="equipment-operation">Equipment Operation</option>
					<option value="first-aid">First Aid Certification</option>
				</select>
			</div>

			{!workerId && (
				<div>
					<label className="block text-sm font-medium text-gray-700 mb-1">
						Workers
					</label>
					<div className="border border-gray-300 rounded-md p-3 max-h-32 overflow-y-auto">
						{/* TODO: Replace with actual worker list */}
						<div className="space-y-2">
							<label className="flex items-center">
								<input type="checkbox" className="mr-2" />
								<span className="text-sm">John Kamau</span>
							</label>
							<label className="flex items-center">
								<input type="checkbox" className="mr-2" />
								<span className="text-sm">Mary Wanjiku</span>
							</label>
						</div>
					</div>
				</div>
			)}

			<div>
				<label className="block text-sm font-medium text-gray-700 mb-1">
					Due Date (Optional)
				</label>
				<input
					type="date"
					className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
					value={dueDate}
					onChange={(e) => setDueDate(e.target.value)}
				/>
			</div>

			<div>
				<label className="block text-sm font-medium text-gray-700 mb-1">
					Priority
				</label>
				<select
					className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
					value={priority}
					onChange={(e) => setPriority(e.target.value)}
				>
					<option value="low">Low</option>
					<option value="medium">Medium</option>
					<option value="high">High</option>
				</select>
			</div>

			<div>
				<label className="block text-sm font-medium text-gray-700 mb-1">
					Notes (Optional)
				</label>
				<textarea
					className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
					rows={3}
					value={notes}
					onChange={(e) => setNotes(e.target.value)}
					placeholder="Additional notes or requirements..."
				/>
			</div>

			<div className="flex justify-end space-x-3 pt-4">
				<button
					type="button"
					onClick={onCancel}
					className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
				>
					Cancel
				</button>
				<button
					type="submit"
					className="px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700"
				>
					Assign Training
				</button>
			</div>
		</form>
	);
};

const RecordCompletionForm: React.FC<RecordCompletionFormProps> = ({
	workerId: _workerId,
	siteId: _siteId,
	onSubmit,
	onCancel,
}) => {
	const [selectedProgram, setSelectedProgram] = useState("");
	const [completionDate, setCompletionDate] = useState("");
	const [instructor, setInstructor] = useState("");
	const [score, setScore] = useState("");
	const [certificateNumber, setCertificateNumber] = useState("");
	const [notes, setNotes] = useState("");

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		onSubmit({
			trainingProgramId: selectedProgram,
			completionDate: new Date(completionDate),
			instructor,
			score: score ? parseInt(score) : undefined,
			certificateNumber,
			notes,
		});
	};

	return (
		<form onSubmit={handleSubmit} className="space-y-4">
			<div>
				<label className="block text-sm font-medium text-gray-700 mb-1">
					Training Program
				</label>
				<select
					className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
					value={selectedProgram}
					onChange={(e) => setSelectedProgram(e.target.value)}
					required
				>
					<option value="">Select a training program</option>
					<option value="safety-orientation">Safety Orientation</option>
					<option value="equipment-operation">Equipment Operation</option>
					<option value="first-aid">First Aid Certification</option>
				</select>
			</div>

			<div>
				<label className="block text-sm font-medium text-gray-700 mb-1">
					Completion Date
				</label>
				<input
					type="date"
					className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
					value={completionDate}
					onChange={(e) => setCompletionDate(e.target.value)}
					required
				/>
			</div>

			<div>
				<label className="block text-sm font-medium text-gray-700 mb-1">
					Instructor
				</label>
				<input
					type="text"
					className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
					value={instructor}
					onChange={(e) => setInstructor(e.target.value)}
					required
					placeholder="Instructor name"
				/>
			</div>

			<div>
				<label className="block text-sm font-medium text-gray-700 mb-1">
					Score (Optional)
				</label>
				<input
					type="number"
					min="0"
					max="100"
					className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
					value={score}
					onChange={(e) => setScore(e.target.value)}
					placeholder="Score out of 100"
				/>
			</div>

			<div>
				<label className="block text-sm font-medium text-gray-700 mb-1">
					Certificate Number (Optional)
				</label>
				<input
					type="text"
					className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
					value={certificateNumber}
					onChange={(e) => setCertificateNumber(e.target.value)}
					placeholder="Certificate or license number"
				/>
			</div>

			<div>
				<label className="block text-sm font-medium text-gray-700 mb-1">
					Notes (Optional)
				</label>
				<textarea
					className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
					rows={3}
					value={notes}
					onChange={(e) => setNotes(e.target.value)}
					placeholder="Additional notes about the training completion..."
				/>
			</div>

			<div className="flex justify-end space-x-3 pt-4">
				<button
					type="button"
					onClick={onCancel}
					className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
				>
					Cancel
				</button>
				<button
					type="submit"
					className="px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700"
				>
					Record Completion
				</button>
			</div>
		</form>
	);
};

const TrainingModal: React.FC<TrainingModalProps> = ({
	isOpen,
	onClose,
	mode,
	workerId,
	siteId,
}) => {
	if (!isOpen) return null;

	const handleAssign = (data: any) => {
		console.log("Assign training:", data);
		onClose();
	};

	const handleRecord = (data: any) => {
		console.log("Record completion:", data);
		onClose();
	};

	// const handleSchedule = (data: any) => {
	// 	console.log("Schedule training:", data);
	// 	onClose();
	// };

	const getTitle = () => {
		switch (mode) {
			case "assign":
				return "Assign Training";
			case "record":
				return "Record Training Completion";
			case "schedule":
				return "Schedule Training Session";
			default:
				return "Training";
		}
	};

	return (
		<div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
			<div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
				<div className="flex justify-between items-center p-6 border-b border-gray-200">
					<h2 className="text-xl font-semibold text-gray-900">{getTitle()}</h2>
					<button
						onClick={onClose}
						className="text-gray-400 hover:text-gray-600 transition-colors"
					>
						<X className="h-6 w-6" />
					</button>
				</div>

				<div className="p-6">
					{mode === "assign" && (
						<AssignTrainingForm
							workerId={workerId}
							siteId={siteId}
							onSubmit={handleAssign}
							onCancel={onClose}
						/>
					)}
					{mode === "record" && (
						<RecordCompletionForm
							workerId={workerId}
							siteId={siteId}
							onSubmit={handleRecord}
							onCancel={onClose}
						/>
					)}
					{mode === "schedule" && (
						<div className="text-center py-8">
							<p className="text-gray-500">
								Schedule training form will be implemented here
							</p>
						</div>
					)}
				</div>
			</div>
		</div>
	);
};

export default TrainingModal;
