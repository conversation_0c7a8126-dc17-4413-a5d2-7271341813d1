import React, { useState, useRef, useCallback } from 'react';
import { createPortal } from 'react-dom';
import { Card, CardContent } from './card';
import { Button } from './button';
import { Camera, Upload, User, RotateCcw, Check, X } from 'lucide-react';
import { cn } from '../../lib/utils';

interface PhotoCaptureProps {
  currentPhoto?: File | string | null;
  onPhotoChange: (photo: File) => void;
  className?: string;
  photoSize?: 'sm' | 'md' | 'lg';
}

const PhotoCapture: React.FC<PhotoCaptureProps> = ({
  currentPhoto,
  onPhotoChange,
  className,
  photoSize = 'md',
}) => {
  const [isCapturing, setIsCapturing] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [capturedPhoto, setCapturedPhoto] = useState<string | null>(null);
  const [stream, setStream] = useState<MediaStream | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const startCamera = useCallback(async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'user', width: 640, height: 640 }
      });
      setStream(mediaStream);
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
      }
      setIsCapturing(true);
      setIsModalOpen(true);
    } catch (error) {
      console.error('Error accessing camera:', error);
      alert('Unable to access camera. Please check permissions.');
    }
  }, []);

  const stopCamera = useCallback(() => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
    setIsCapturing(false);
    setCapturedPhoto(null);
    setIsModalOpen(false);
  }, [stream]);

  const capturePhoto = useCallback(() => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');

    if (!context) return;

    // Set canvas size to square
    canvas.width = 400;
    canvas.height = 400;

    // Calculate dimensions to crop to square
    const videoWidth = video.videoWidth;
    const videoHeight = video.videoHeight;
    const minDimension = Math.min(videoWidth, videoHeight);
    const x = (videoWidth - minDimension) / 2;
    const y = (videoHeight - minDimension) / 2;

    // Draw cropped square image
    context.drawImage(video, x, y, minDimension, minDimension, 0, 0, 400, 400);

    const dataUrl = canvas.toDataURL('image/jpeg', 0.8);
    setCapturedPhoto(dataUrl);
  }, []);

  const confirmPhoto = useCallback(() => {
    if (!capturedPhoto || !canvasRef.current) return;

    canvasRef.current.toBlob((blob) => {
      if (blob) {
        const file = new File([blob], 'captured-photo.jpg', { type: 'image/jpeg' });
        onPhotoChange(file);
        stopCamera();
      }
    }, 'image/jpeg', 0.8);
  }, [capturedPhoto, onPhotoChange, stopCamera]);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      onPhotoChange(file);
    }
  };

  const getPhotoUrl = () => {
    if (typeof currentPhoto === 'string') return currentPhoto;
    if (currentPhoto instanceof File) return URL.createObjectURL(currentPhoto);
    return null;
  };

  const sizeClasses = {
    sm: { avatar: 'w-24 h-24', icon: 'w-12 h-12' },
    md: { avatar: 'w-32 h-32', icon: 'w-16 h-16' },
    lg: { avatar: 'w-40 h-40', icon: 'w-20 h-20' },
  } as const;

  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardContent className="p-6">
        <div className="space-y-4">
          {/* Photo Display */}
          <div className="flex justify-center">
            {getPhotoUrl() ? (
              <img
                src={getPhotoUrl()!}
                alt="Worker photo"
                className={`${sizeClasses[photoSize].avatar} rounded-full object-cover border-4 border-gray-200`}
              />
            ) : (
              <div className={`${sizeClasses[photoSize].avatar} rounded-full bg-gray-100 flex items-center justify-center border-4 border-gray-200`}> 
                <User className={`${sizeClasses[photoSize].icon} text-gray-400`} />
              </div>
            )}
          </div>

          {/* Centered Camera Modal via portal to avoid clipping and ensure full overlay */}
          {isModalOpen && createPortal(
            <div className="fixed inset-0 z-[1000]">
              {/* Glass overlay */}
              <div className="absolute inset-0 bg-gray-900/30 backdrop-blur-md" />
              {/* Modal content */}
              <div className="absolute inset-0 flex items-center justify-center p-4">
                <div className="relative bg-white text-gray-900 rounded-2xl shadow-2xl w-[380px] max-w-[92vw]">
                  {/* Top bar with close */}
                  <div className="flex items-center justify-between px-4 py-3 border-b border-gray-200 rounded-t-2xl">
                    <div className="text-xs uppercase tracking-widest text-gray-500">Camera</div>
                    <button
                      onClick={stopCamera}
                      className="text-gray-500 hover:text-gray-700"
                      aria-label="Close camera"
                    >
                      <X className="w-5 h-5" />
                    </button>
                  </div>

                  {/* Viewfinder */}
                  <div className="px-4 pb-4 pt-4">
                    <div className="relative mx-auto w-[320px] h-[320px] rounded-2xl overflow-hidden bg-black">
                      {!capturedPhoto ? (
                        <>
                          <video
                            ref={videoRef}
                            autoPlay
                            playsInline
                            className="w-full h-full object-cover"
                          />
                          {/* Framing overlay */}
                          <div className="pointer-events-none absolute inset-0 rounded-2xl border-2 border-green-500/70" />
                        </>
                      ) : (
                        <img src={capturedPhoto} alt="Captured" className="w-full h-full object-cover" />
                      )}
                    </div>
                  </div>

                  {/* Bottom controls */}
                  <div className="px-6 pb-6">
                    {!capturedPhoto ? (
                      <div className="flex items-center justify-between">
                        {/* Upload while in camera */}
                        <Button
                          variant="outline"
                          onClick={() => fileInputRef.current?.click()}
                          className="border-gray-300"
                        >
                          <Upload className="w-4 h-4 mr-2" /> Upload
                        </Button>
                        {/* Shutter */}
                        <button
                          onClick={capturePhoto}
                          className="w-16 h-16 rounded-full bg-white shadow-inner ring-2 ring-gray-300 active:scale-95 transition"
                          aria-label="Capture photo"
                        />
                        {/* Cancel */}
                        <Button
                          variant="outline"
                          onClick={stopCamera}
                          className="border-gray-300"
                        >
                          Cancel
                        </Button>
                      </div>
                    ) : (
                      <div className="flex items-center justify-between">
                        <Button
                          variant="outline"
                          onClick={() => setCapturedPhoto(null)}
                          className="border-gray-300"
                        >
                          <RotateCcw className="w-4 h-4 mr-2" /> Retake
                        </Button>
                        <Button onClick={confirmPhoto} className="bg-green-600 hover:bg-green-700 text-white">
                          <Check className="w-4 h-4 mr-2" /> Use Photo
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => fileInputRef.current?.click()}
                          className="border-gray-300"
                        >
                          <Upload className="w-4 h-4 mr-2" /> Upload
                        </Button>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Hidden file input for modal upload */}
              <input
                ref={fileInputRef}
                type="file"
                accept="image/*"
                onChange={(e) => {
                  const f = e.target.files?.[0];
                  if (f) {
                    onPhotoChange(f);
                    stopCamera();
                  }
                }}
                className="hidden"
              />
            </div>,
            document.body
          )}

          {/* Upload Controls */}
          {!isCapturing && !isModalOpen && (
            <div className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                className="flex-1"
              >
                <Upload className="w-4 h-4 mr-2" />
                Upload
              </Button>
              <Button
                variant="outline"
                onClick={startCamera}
                className="flex-1"
              >
                <Camera className="w-4 h-4 mr-2" />
                Camera
              </Button>
            </div>
          )}

          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            className="hidden"
          />
          <canvas ref={canvasRef} className="hidden" />
        </div>
      </CardContent>
    </Card>
  );
};

export { PhotoCapture };
