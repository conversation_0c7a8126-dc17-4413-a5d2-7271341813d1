import React from 'react';
import { Card, CardContent } from './card';
import { But<PERSON> } from './button';
import {
  Check<PERSON><PERSON><PERSON>,
  Clock,
  XCircle,
  AlertTriangle,
  MapPin,
  Eye
} from 'lucide-react';
import { cn } from '../../lib/utils';

interface WorkerCardProps {
  worker: {
    id: number;
    name: string;
    employeeNumber: string;
    email: string;
    profilePictureUrl?: string;
    trades: Array<{ id: number; name: string }>;
    skills?: Array<{ id: number; name: string }>;
    currentSiteId?: string;
    complianceStatus: 'compliant' | 'pending_training' | 'non_compliant' | 'expired';
  };
  isSelected: boolean;
  onSelect: (workerId: number) => void;
  onView: (worker: any) => void;
  className?: string;
}

const WorkerCard: React.FC<WorkerCardProps> = ({
  worker,
  isSelected,
  onSelect,
  onView,
  className,
}) => {
  const getComplianceStatusIcon = (status: string) => {
    switch (status) {
      case 'compliant':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'pending_training':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'non_compliant':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'expired':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      default:
        return <AlertTriangle className="w-4 h-4 text-gray-400" />;
    }
  };

  const getComplianceStatusText = (status: string) => {
    switch (status) {
      case 'compliant':
        return 'Compliant';
      case 'pending_training':
        return 'Pending Training';
      case 'non_compliant':
        return 'Non-Compliant';
      case 'expired':
        return 'Expired';
      default:
        return 'Unknown';
    }
  };

  const getComplianceStatusColor = (status: string) => {
    switch (status) {
      case 'compliant':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending_training':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'non_compliant':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'expired':
        return 'bg-red-100 text-red-900 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <Card 
      className={cn(
        "transition-all duration-200 cursor-pointer hover:shadow-md",
        isSelected ? "ring-2 ring-green-500 bg-green-50" : "hover:bg-gray-50",
        className
      )}
      onClick={() => onSelect(worker.id)}
    >
      <CardContent className="p-4">
        <div className="flex items-start space-x-4">
          {/* Checkbox */}
          <div className="flex-shrink-0 pt-1">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={() => onSelect(worker.id)}
              className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
              onClick={(e) => e.stopPropagation()}
            />
          </div>

          {/* Profile Picture */}
          <div className="flex-shrink-0">
            <img
              className="h-12 w-12 rounded-full object-cover border-2 border-gray-200"
              src={worker.profilePictureUrl || `https://ui-avatars.com/api/?name=${encodeURIComponent(worker.name)}&background=10B981&color=fff`}
              alt={worker.name}
            />
          </div>

          {/* Worker Info */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h3 className="text-sm font-semibold text-gray-900 truncate">
                  {worker.name}
                </h3>
                <p className="text-xs text-gray-500 mb-1">
                  {worker.employeeNumber}
                </p>
                <p className="text-xs text-gray-600 truncate">
                  {worker.email}
                </p>
              </div>

              {/* Action Button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onView(worker);
                }}
                className="ml-2"
              >
                <Eye className="w-4 h-4" />
              </Button>
            </div>

            {/* Trades */}
            <div className="mt-3">
              <div className="flex flex-wrap gap-1">
                {worker.trades.slice(0, 3).map((trade) => (
                  <span
                    key={trade.id}
                    className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                  >
                    {trade.name}
                  </span>
                ))}
                {worker.trades.length > 3 && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                    +{worker.trades.length - 3} more
                  </span>
                )}
              </div>
            </div>

            {/* Status and Assignment */}
            <div className="mt-3 flex items-center justify-between">
              <div className="flex items-center space-x-3">
                {/* Compliance Status */}
                <span className={cn(
                  "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border",
                  getComplianceStatusColor(worker.complianceStatus)
                )}>
                  {getComplianceStatusIcon(worker.complianceStatus)}
                  <span className="ml-1">{getComplianceStatusText(worker.complianceStatus)}</span>
                </span>
              </div>

              {/* Current Assignment */}
              <div className="text-right">
                {worker.currentSiteId ? (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    <MapPin className="w-3 h-3 mr-1" />
                    {worker.currentSiteId}
                  </span>
                ) : (
                  <span className="text-xs text-gray-400">Available</span>
                )}
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export { WorkerCard };
