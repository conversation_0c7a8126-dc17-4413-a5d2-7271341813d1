import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader } from './card';
import { Button } from './button';
import {
  CheckCircle,
  Clock,
  XCircle,
  AlertTriangle,
  MapPin,
  Eye,
  Plus,
  Users,
  Download,
  Check
} from 'lucide-react';
import { cn } from '../../lib/utils';

interface Worker {
  id: number;
  name: string;
  employeeNumber: string;
  email?: string;
  photoUrl?: string;
  trades: Array<{ id: number; name: string }>;
  skills?: Array<{ id: number; name: string }>;
  currentSiteId?: string;
  complianceStatus: 'compliant' | 'pending_training' | 'non_compliant' | 'expired';
}

interface WorkerTableProps {
  workers: Worker[];
  selectedWorkers: number[];
  onWorkerSelect: (workerId: number) => void;
  onSelectAll: () => void;
  onClearSelection: () => void;
  onViewWorker: (worker: any) => void;
  onImportWorkers: (workerIds: number[]) => void;
  isImporting?: boolean;
  className?: string;
}

const WorkerTable: React.FC<WorkerTableProps> = ({
  workers,
  selectedWorkers,
  onWorkerSelect,
  onSelectAll,
  onClearSelection,
  onViewWorker,
  onImportWorkers,
  isImporting = false,
  className,
}) => {
  const [showImportConfirm, setShowImportConfirm] = useState(false);

  const getComplianceStatusIcon = (status: string) => {
    switch (status) {
      case 'compliant':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'pending_training':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'non_compliant':
        return <XCircle className="w-4 h-4 text-red-500" />;
      case 'expired':
        return <AlertTriangle className="w-4 h-4 text-red-600" />;
      default:
        return <AlertTriangle className="w-4 h-4 text-gray-400" />;
    }
  };

  const getComplianceStatusText = (status: string) => {
    switch (status) {
      case 'compliant':
        return 'Compliant';
      case 'pending_training':
        return 'Pending Training';
      case 'non_compliant':
        return 'Non-Compliant';
      case 'expired':
        return 'Expired';
      default:
        return 'Unknown';
    }
  };

  const getComplianceStatusColor = (status: string) => {
    switch (status) {
      case 'compliant':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending_training':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'non_compliant':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'expired':
        return 'bg-red-100 text-red-900 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const allSelected = workers.length > 0 && workers.every(w => selectedWorkers.includes(w.id));
  const someSelected = selectedWorkers.length > 0 && !allSelected;

  const handleImportSelected = () => {
    if (selectedWorkers.length > 0) {
      setShowImportConfirm(true);
    }
  };

  const confirmImport = () => {
    onImportWorkers(selectedWorkers);
    setShowImportConfirm(false);
  };

  return (
    <Card className={className}>
      {/* Enhanced Header with Selection Controls */}
      <CardHeader className="pb-4">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
            <h3 className="text-lg font-semibold text-gray-900">Available Workers</h3>
            {selectedWorkers.length > 0 && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600 bg-blue-100 px-3 py-1 rounded-full font-medium">
                  {selectedWorkers.length} of {workers.length} selected
                </span>
              </div>
            )}
          </div>

          <div className="flex flex-col sm:flex-row items-stretch sm:items-center space-y-2 sm:space-y-0 sm:space-x-2">
            {/* Selection Controls */}
            {workers.length > 0 && (
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onSelectAll}
                  className="text-xs flex-1 sm:flex-none"
                >
                  {allSelected ? 'Deselect All' : 'Select All'}
                </Button>
                {selectedWorkers.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={onClearSelection}
                    className="text-xs flex-1 sm:flex-none"
                  >
                    Clear Selection
                  </Button>
                )}
              </div>
            )}

            {/* Import Actions */}
            {selectedWorkers.length > 0 && (
              <Button
                onClick={handleImportSelected}
                disabled={isImporting}
                className="w-full sm:w-auto"
              >
                {isImporting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Importing...
                  </>
                ) : (
                  <>
                    <Download className="w-4 h-4 mr-2" />
                    <span className="hidden sm:inline">Import Selected</span>
                    <span className="sm:hidden">Import</span>
                    <span className="ml-1">({selectedWorkers.length})</span>
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        {workers.length === 0 ? (
          <div className="px-6 py-12 text-center">
            <Users className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No workers found</h3>
            <p className="mt-1 text-sm text-gray-500">
              Try adjusting your search criteria or filters.
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                    <input
                      type="checkbox"
                      checked={allSelected}
                      ref={(input) => {
                        if (input) input.indeterminate = someSelected;
                      }}
                      onChange={onSelectAll}
                      className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Worker
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">
                    Trades
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell">
                    Assignment
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">
                    Compliance
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {workers.map((worker) => {
                  const isSelected = selectedWorkers.includes(worker.id);
                  return (
                    <tr
                      key={worker.id}
                      className={cn(
                        "hover:bg-gray-50 transition-colors",
                        isSelected && "bg-green-50 border-l-4 border-green-500"
                      )}
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="checkbox"
                          checked={isSelected}
                          onChange={() => onWorkerSelect(worker.id)}
                          className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                        />
                      </td>

                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <img
                              className="h-10 w-10 rounded-full object-cover border-2 border-gray-200"
                              src={worker.photoUrl || `https://ui-avatars.com/api/?name=${encodeURIComponent(worker.name)}&background=10B981&color=fff`}
                              alt={worker.name}
                            />
                          </div>
                          <div className="ml-4 min-w-0 flex-1">
                            <div className="text-sm font-medium text-gray-900 truncate">
                              {worker.name}
                            </div>
                            <div className="text-xs text-gray-500">
                              {worker.employeeNumber}
                            </div>
                            <div className="text-xs text-gray-600 truncate sm:hidden">
                              {worker.email || 'No email'}
                            </div>
                            <div className="text-xs text-gray-600 truncate hidden sm:block lg:max-w-48">
                              {worker.email || 'No email'}
                            </div>
                            {/* Mobile-only compliance and trades */}
                            <div className="mt-1 sm:hidden">
                              <div className="flex items-center space-x-2">
                                <span className={cn(
                                  "inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium border",
                                  getComplianceStatusColor(worker.complianceStatus)
                                )}>
                                  {getComplianceStatusIcon(worker.complianceStatus)}
                                  <span className="ml-1">{getComplianceStatusText(worker.complianceStatus)}</span>
                                </span>
                              </div>
                              <div className="flex flex-wrap gap-1 mt-1">
                                {worker.trades.slice(0, 2).map((trade) => (
                                  <span
                                    key={trade.id}
                                    className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                                  >
                                    {trade.name}
                                  </span>
                                ))}
                                {worker.trades.length > 2 && (
                                  <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                                    +{worker.trades.length - 2}
                                  </span>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      </td>

                      <td className="px-6 py-4 whitespace-nowrap hidden md:table-cell">
                        <div className="flex flex-wrap gap-1 max-w-48">
                          {worker.trades.slice(0, 2).map((trade) => (
                            <span
                              key={trade.id}
                              className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                            >
                              {trade.name}
                            </span>
                          ))}
                          {worker.trades.length > 2 && (
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-600">
                              +{worker.trades.length - 2}
                            </span>
                          )}
                        </div>
                      </td>

                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 hidden lg:table-cell">
                        {worker.currentSiteId ? (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            <MapPin className="w-3 h-3 mr-1" />
                            {worker.currentSiteId}
                          </span>
                        ) : (
                          <span className="text-gray-400 text-xs">Available</span>
                        )}
                      </td>

                      <td className="px-6 py-4 whitespace-nowrap hidden sm:table-cell">
                        <span className={cn(
                          "inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium border",
                          getComplianceStatusColor(worker.complianceStatus)
                        )}>
                          {getComplianceStatusIcon(worker.complianceStatus)}
                          <span className="ml-1">{getComplianceStatusText(worker.complianceStatus)}</span>
                        </span>
                      </td>

                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-1 sm:space-x-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onViewWorker(worker)}
                            className="p-2"
                          >
                            <Eye className="w-4 h-4" />
                            <span className="sr-only">View Details</span>
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => onImportWorkers([worker.id])}
                            disabled={isImporting}
                            className="text-xs"
                          >
                            <Plus className="w-4 h-4 sm:mr-1" />
                            <span className="hidden sm:inline">Import</span>
                          </Button>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </CardContent>

      {/* Import Confirmation Modal */}
      {showImportConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md mx-4">
            <CardHeader>
              <h3 className="text-lg font-semibold">Confirm Import</h3>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-gray-600">
                Are you sure you want to import {selectedWorkers.length} worker{selectedWorkers.length !== 1 ? 's' : ''}?
              </p>
              <div className="flex space-x-2">
                <Button
                  onClick={confirmImport}
                  disabled={isImporting}
                  className="flex-1"
                >
                  {isImporting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Importing...
                    </>
                  ) : (
                    <>
                      <Check className="w-4 h-4 mr-2" />
                      Confirm Import
                    </>
                  )}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setShowImportConfirm(false)}
                  disabled={isImporting}
                  className="flex-1"
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </Card>
  );
};

export { WorkerTable };
