import { useState, useEffect } from "react";
import {
	Search,
	Filter,
	Plus,
	Calendar,
	User,
	Thermometer,
	Droplets,
	Wind,
	Cloud,
  Edit,
	Trash2 } from "lucide-react";

interface LoggedWeatherEntry {
	id: string;
	timestamp: Date;
	loggedBy: string;
	loggedByName: string;
	temperature?: number;
	humidity?: number;
	windSpeed?: number;
	windDirection?: string;
	visibility?: string;
	rainGauge?: number;
	generalObservations: string;
	weatherCondition: "clear" | "cloudy" | "rainy" | "stormy" | "foggy" | "snowy";
	notes?: string;
}

interface LoggedWeatherDataProps {
	siteId: string;
}

const LoggedWeatherData = ({ siteId }: LoggedWeatherDataProps) => {
	const [entries, setEntries] = useState<LoggedWeatherEntry[]>([]);
	const [filteredEntries, setFilteredEntries] = useState<LoggedWeatherEntry[]>([]);
	const [searchTerm, setSearchTerm] = useState("");
	const [dateFilter, setDateFilter] = useState("");
	const [conditionFilter, setConditionFilter] = useState("");
	const [isLoading, setIsLoading] = useState(true);


	useEffect(() => {
		// Mock data for logged weather entries
		const mockEntries: LoggedWeatherEntry[] = [
			{
				id: "log-001",
				timestamp: new Date("2024-12-18T08:30:00"),
				loggedBy: "user-001",
				loggedByName: "John Mwangi",
				temperature: 22,
				humidity: 65,
				windSpeed: 8,
				windDirection: "NW",
				visibility: "Good (>10km)",
				rainGauge: 0,
				generalObservations: "Clear morning, good visibility for construction work",
				weatherCondition: "clear",
				notes: "Perfect conditions for crane operations" },
			{
				id: "log-002",
				timestamp: new Date("2024-12-18T14:15:00"),
				loggedBy: "user-002",
				loggedByName: "Sarah Ochieng",
				temperature: 28,
				humidity: 45,
				windSpeed: 12,
				windDirection: "SW",
				visibility: "Moderate (5-10km)",
				rainGauge: 0,
				generalObservations: "Hot afternoon, strong winds affecting crane operations",
				weatherCondition: "clear",
				notes: "Suspended crane work due to wind speed exceeding safety limits" },
			{
				id: "log-003",
				timestamp: new Date("2024-12-17T16:45:00"),
				loggedBy: "user-003",
				loggedByName: "David Kimani",
				temperature: 19,
				humidity: 85,
				windSpeed: 5,
				windDirection: "E",
				visibility: "Poor (<5km)",
				rainGauge: 15.5,
				generalObservations: "Heavy rain in the afternoon, reduced visibility",
				weatherCondition: "rainy",
				notes: "All outdoor work suspended, workers moved to covered areas" },
			{
				id: "log-004",
				timestamp: new Date("2024-12-17T07:00:00"),
				loggedBy: "user-004",
				loggedByName: "Grace Wanjiku",
				temperature: 16,
				humidity: 90,
				windSpeed: 3,
				windDirection: "N",
				visibility: "Very Poor (<1km)",
				rainGauge: 0,
				generalObservations: "Dense fog in early morning, clearing by 9 AM",
				weatherCondition: "foggy",
				notes: "Delayed start of work until fog cleared" },
		];

		setTimeout(() => {
			setEntries(mockEntries);
			setFilteredEntries(mockEntries);
			setIsLoading(false);
		}, 500);
	}, [siteId]);

	// Filter logic
	useEffect(() => {
		let filtered = entries.filter((entry) => {
			const matchesSearch =
				entry.loggedByName.toLowerCase().includes(searchTerm.toLowerCase()) ||
				entry.generalObservations.toLowerCase().includes(searchTerm.toLowerCase()) ||
				entry.notes?.toLowerCase().includes(searchTerm.toLowerCase());

			const matchesDate = !dateFilter || entry.timestamp.toDateString() === new Date(dateFilter).toDateString();
			const matchesCondition = !conditionFilter || entry.weatherCondition === conditionFilter;

			return matchesSearch && matchesDate && matchesCondition;
		});

		// Sort by timestamp (newest first)
		filtered.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
		setFilteredEntries(filtered);
	}, [entries, searchTerm, dateFilter, conditionFilter]);

	const getConditionBadge = (condition: string) => {
		const badges = {
			clear: "bg-green-100 text-green-800",
			cloudy: "bg-gray-100 text-gray-800",
			rainy: "bg-blue-100 text-blue-800",
			stormy: "bg-purple-100 text-purple-800",
			foggy: "bg-yellow-100 text-yellow-800",
			snowy: "bg-blue-100 text-blue-800" };
		return badges[condition as keyof typeof badges] || "bg-gray-100 text-gray-800";
	};

	const getConditionIcon = (condition: string) => {
		switch (condition) {
			case "clear":
				return "☀️";
			case "cloudy":
				return "☁️";
			case "rainy":
				return "🌧️";
			case "stormy":
				return "⛈️";
			case "foggy":
				return "🌫️";
			case "snowy":
				return "❄️";
			default:
				return "🌤️";
		}
	};

	if (isLoading) {
		return (
			<div className="space-y-6">
				<div className="animate-pulse">
					<div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
					<div className="h-32 bg-gray-200 rounded mb-4"></div>
					<div className="space-y-3">
						{[...Array(5)].map((_, i) => (
							<div key={i} className="h-16 bg-gray-200 rounded"></div>
						))}
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<h2 className="text-2xl font-bold">Logged Weather Data</h2>
				<button
					onClick={() => {/* TODO: Implement weather logging form */}}
					className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700"
				>
					<Plus className="h-4 w-4 mr-2" />
					Log Weather Observation
				</button>
			</div>

			{/* Summary Cards */}
			<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Total Entries</p>
							<p className="text-2xl font-bold">{entries.length}</p>
						</div>
						<Calendar className="h-8 w-8 text-blue-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Today's Entries</p>
							<p className="text-2xl font-bold text-green-600">
								{entries.filter(e => e.timestamp.toDateString() === new Date().toDateString()).length}
							</p>
						</div>
						<User className="h-8 w-8 text-green-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Rain Events</p>
							<p className="text-2xl font-bold text-blue-600">
								{entries.filter(e => e.weatherCondition === "rainy" || (e.rainGauge && e.rainGauge > 0)).length}
							</p>
						</div>
						<Droplets className="h-8 w-8 text-blue-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Weather Alerts</p>
							<p className="text-2xl font-bold text-orange-600">
								{entries.filter(e => e.weatherCondition === "stormy" || e.weatherCondition === "foggy").length}
							</p>
						</div>
						<Cloud className="h-8 w-8 text-orange-500" />
					</div>
				</div>
			</div>

			{/* Filters */}
			<div className="bg-white rounded-lg border border-gray-200 p-4">
				<div className="flex flex-col sm:flex-row gap-4 items-center">
					<div className="flex-1 relative">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
						<input
							type="text"
							placeholder="Search by observer, observations, or notes..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
						/>
					</div>
					<div className="flex items-center gap-2">
						<Filter className="h-4 w-4 text-gray-400" />
						<input
							type="date"
							value={dateFilter}
							onChange={(e) => setDateFilter(e.target.value)}
							className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
						/>
						<select
							value={conditionFilter}
							onChange={(e) => setConditionFilter(e.target.value)}
							className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
						>
							<option value="">All Conditions</option>
							<option value="clear">Clear</option>
							<option value="cloudy">Cloudy</option>
							<option value="rainy">Rainy</option>
							<option value="stormy">Stormy</option>
							<option value="foggy">Foggy</option>
							<option value="snowy">Snowy</option>
						</select>
					</div>
				</div>
			</div>

			{/* Weather Entries Table */}
			<div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
				<div className="overflow-x-auto">
					<table className="min-w-full divide-y divide-gray-200">
						<thead className="bg-gray-50">
							<tr>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Date & Time
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Observer
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Condition
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Measurements
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Observations
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Actions
								</th>
							</tr>
						</thead>
						<tbody className="bg-white divide-y divide-gray-200">
							{filteredEntries.map((entry) => (
								<tr key={entry.id} className="hover:bg-gray-50">
									<td className="px-6 py-4 whitespace-nowrap">
										<div className="text-sm text-gray-900">
											<div className="font-medium">
												{entry.timestamp.toLocaleDateString()}
											</div>
											<div className="text-gray-500">
												{entry.timestamp.toLocaleTimeString()}
											</div>
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<div className="flex items-center">
											<div className="flex-shrink-0 h-8 w-8">
												<div className="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center text-white text-xs font-medium">
													{entry.loggedByName.split(' ').map(n => n[0]).join('')}
												</div>
											</div>
											<div className="ml-3">
												<div className="text-sm font-medium text-gray-900">
													{entry.loggedByName}
												</div>
											</div>
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<div className="flex items-center space-x-2">
											<span className="text-lg">{getConditionIcon(entry.weatherCondition)}</span>
											<span className={`px-2 py-1 text-xs font-medium rounded-full ${getConditionBadge(entry.weatherCondition)}`}>
												{entry.weatherCondition.charAt(0).toUpperCase() + entry.weatherCondition.slice(1)}
											</span>
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<div className="text-sm text-gray-900 space-y-1">
											{entry.temperature && (
												<div className="flex items-center space-x-1">
													<Thermometer className="h-3 w-3 text-red-400" />
													<span>{entry.temperature}°C</span>
												</div>
											)}
											{entry.humidity && (
												<div className="flex items-center space-x-1">
													<Droplets className="h-3 w-3 text-blue-400" />
													<span>{entry.humidity}%</span>
												</div>
											)}
											{entry.windSpeed && (
												<div className="flex items-center space-x-1">
													<Wind className="h-3 w-3 text-green-400" />
													<span>{entry.windSpeed} m/s {entry.windDirection}</span>
												</div>
											)}
											{entry.rainGauge !== undefined && entry.rainGauge > 0 && (
												<div className="flex items-center space-x-1">
													<Droplets className="h-3 w-3 text-blue-600" />
													<span>{entry.rainGauge}mm rain</span>
												</div>
											)}
										</div>
									</td>
									<td className="px-6 py-4">
										<div className="text-sm text-gray-900">
											<div className="mb-1">{entry.generalObservations}</div>
											{entry.notes && (
												<div className="text-xs text-gray-500 italic">
													Note: {entry.notes}
												</div>
											)}
											{entry.visibility && (
												<div className="text-xs text-gray-500 mt-1">
													Visibility: {entry.visibility}
												</div>
											)}
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
										<div className="flex space-x-2">
											<button
												className="text-blue-600 hover:text-blue-900"
												title="Edit Entry"
											>
												<Edit className="h-4 w-4" />
											</button>
											<button
												className="text-red-600 hover:text-red-900"
												title="Delete Entry"
											>
												<Trash2 className="h-4 w-4" />
											</button>
										</div>
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>
			</div>

			{filteredEntries.length === 0 && !isLoading && (
				<div className="text-center py-12">
					<Cloud className="h-16 w-16 text-gray-400 mx-auto mb-4" />
					<h3 className="text-lg font-medium text-gray-900 mb-2">No Weather Entries Found</h3>
					<p className="text-gray-500 mb-4">
						{searchTerm || dateFilter || conditionFilter 
							? "No entries match your current filters." 
							: "No weather observations have been logged yet."
						}
					</p>
					<button
						onClick={() => {/* TODO: Implement weather logging form */}}
						className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
					>
						<Plus className="h-4 w-4 mr-2" />
						Log First Observation
					</button>
				</div>
			)}
		</div>
	);
};

export default LoggedWeatherData;
