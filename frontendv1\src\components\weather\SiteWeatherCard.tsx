import React, { useState } from 'react';
import { 
  Cloud, 
  CloudRain, 
  Sun, 
  CloudSnow, 
  Zap, 
  Wind, 
  Droplets, 
  Eye, 
  AlertTriangle,
  ChevronDown,
  ChevronUp,
  MapPin,
  Calendar
} from 'lucide-react';
import { WeatherData } from '../../types/weather';
import { weatherService } from '../../services/weatherService';

interface SiteWeatherCardProps {
  weather: WeatherData;
  isLast?: boolean;
  compact?: boolean;
}

export const SiteWeatherCard: React.FC<SiteWeatherCardProps> = ({ 
  weather, 
  isLast = false, 
  compact = false 
}) => {
  const [showForecast, setShowForecast] = useState(false);
  const [showDetails, setShowDetails] = useState(!compact);

  const getWeatherIcon = (condition: string, size: string = 'h-6 w-6') => {
    const iconClass = size;
    
    switch (condition.toLowerCase()) {
      case 'clear':
        return <Sun className={`${iconClass} text-yellow-500`} />;
      case 'clouds':
        return <Cloud className={`${iconClass} text-gray-500`} />;
      case 'rain':
        return <CloudRain className={`${iconClass} text-blue-500`} />;
      case 'snow':
        return <CloudSnow className={`${iconClass} text-blue-300`} />;
      case 'thunderstorm':
        return <Zap className={`${iconClass} text-purple-500`} />;
      default:
        return <Cloud className={`${iconClass} text-gray-500`} />;
    }
  };

  const getTemperatureColor = (temp: number) => {
    if (temp < 10) return 'text-blue-600';
    if (temp < 25) return 'text-green-600';
    if (temp < 35) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getWindDirection = (degrees: number) => {
    const directions = ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW'];
    const index = Math.round(degrees / 45) % 8;
    return directions[index];
  };

  const safetyCheck = weatherService.isWeatherSafeForWork(weather);

  return (
    <div className={`p-4 ${!isLast ? 'border-b border-gray-100' : ''}`}>
      {/* Site Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <h4 className="font-medium text-gray-900">{weather.siteName}</h4>
          <div className="flex items-center space-x-1 text-xs text-gray-500">
            <MapPin className="h-3 w-3" />
            <span className="truncate max-w-32">{weather.location}</span>
          </div>
        </div>
        {compact && (
          <button
            onClick={() => setShowDetails(!showDetails)}
            className="p-1 hover:bg-gray-100 rounded"
          >
            {showDetails ? (
              <ChevronUp className="h-4 w-4 text-gray-400" />
            ) : (
              <ChevronDown className="h-4 w-4 text-gray-400" />
            )}
          </button>
        )}
      </div>

      {/* Current Weather */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-3">
          {getWeatherIcon(weather.current.conditions.main)}
          <div>
            <div className={`text-2xl font-bold ${getTemperatureColor(weather.current.temperature)}`}>
              {Math.round(weather.current.temperature)}°C
            </div>
            <div className="text-sm text-gray-500 capitalize">
              {weather.current.conditions.description}
            </div>
          </div>
        </div>
        <div className="text-right">
          <div className="text-sm text-gray-600">
            Feels like {Math.round(weather.current.feelsLike)}°C
          </div>
          {weather.alerts && weather.alerts.length > 0 && (
            <div className="flex items-center space-x-1 text-red-600 text-xs mt-1">
              <AlertTriangle className="h-3 w-3" />
              <span>{weather.alerts.length} Alert{weather.alerts.length > 1 ? 's' : ''}</span>
            </div>
          )}
        </div>
      </div>

      {/* Safety Warnings */}
      {!safetyCheck.safe && (
        <div className="mb-3 p-2 bg-yellow-50 border border-yellow-200 rounded-md">
          <div className="flex items-center space-x-2 mb-1">
            <AlertTriangle className="h-4 w-4 text-yellow-600" />
            <span className="text-sm font-medium text-yellow-800">Safety Warnings</span>
          </div>
          <ul className="text-xs text-yellow-700 space-y-1">
            {safetyCheck.warnings.map((warning, index) => (
              <li key={index}>• {warning}</li>
            ))}
          </ul>
        </div>
      )}

      {/* Detailed Weather Info */}
      {showDetails && (
        <div className="space-y-3">
          <div className="grid grid-cols-2 gap-3 text-sm">
            <div className="flex items-center space-x-2">
              <Droplets className="h-4 w-4 text-blue-500" />
              <span className="text-gray-600">Humidity:</span>
              <span className="font-medium">{weather.current.humidity}%</span>
            </div>
            <div className="flex items-center space-x-2">
              <Wind className="h-4 w-4 text-gray-500" />
              <span className="text-gray-600">Wind:</span>
              <span className="font-medium">
                {Math.round(weather.current.windSpeed)} m/s {getWindDirection(weather.current.windDirection)}
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Eye className="h-4 w-4 text-gray-500" />
              <span className="text-gray-600">Visibility:</span>
              <span className="font-medium">{Math.round(weather.current.visibility / 1000)} km</span>
            </div>
            <div className="flex items-center space-x-2">
              <Sun className="h-4 w-4 text-orange-500" />
              <span className="text-gray-600">UV Index:</span>
              <span className="font-medium">{weather.current.uvIndex}</span>
            </div>
          </div>

          {/* Weather Alerts */}
          {weather.alerts && weather.alerts.length > 0 && (
            <div className="space-y-2">
              <h5 className="text-sm font-medium text-gray-900 flex items-center space-x-2">
                <AlertTriangle className="h-4 w-4 text-red-500" />
                <span>Active Alerts</span>
              </h5>
              {weather.alerts.map((alert) => (
                <div key={alert.id} className="p-2 bg-red-50 border border-red-200 rounded-md">
                  <div className="text-sm font-medium text-red-800">{alert.title}</div>
                  <div className="text-xs text-red-600 mt-1">{alert.description}</div>
                  <div className="text-xs text-red-500 mt-1">
                    Until: {new Date(alert.endTime).toLocaleString('en-GB')}
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* 7-Day Forecast Toggle */}
          <div className="pt-2 border-t border-gray-100">
            <button
              onClick={() => setShowForecast(!showForecast)}
              className="flex items-center space-x-2 text-sm text-gray-600 hover:text-gray-900 transition-colors"
            >
              <Calendar className="h-4 w-4" />
              <span>7-Day Forecast</span>
              {showForecast ? (
                <ChevronUp className="h-4 w-4" />
              ) : (
                <ChevronDown className="h-4 w-4" />
              )}
            </button>

            {/* Forecast */}
            {showForecast && (
              <div className="mt-3 space-y-2">
                {weather.forecast.slice(0, 7).map((day) => (
                  <div key={day.date} className="flex items-center justify-between text-sm">
                    <div className="flex items-center space-x-3 flex-1">
                      <span className="w-12 text-gray-600">{day.day.slice(0, 3)}</span>
                      {getWeatherIcon(day.conditions.main, 'h-4 w-4')}
                      <span className="text-gray-600 capitalize text-xs">
                        {day.conditions.description}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-gray-500">{day.temperature.min}°</span>
                      <span className="font-medium">{day.temperature.max}°</span>
                      {day.precipitationChance > 30 && (
                        <span className="text-blue-500 text-xs">{day.precipitationChance}%</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};
