import { useState} from "react";
import {
	Refresh<PERSON>w,
	Alert<PERSON><PERSON>gle,
	Wind,
	Eye,
	Droplets,
	Sun,
	Cloud,
	CloudRain,
	CloudSnow,
	Zap,
	MapPin,
	Clock,
  FileText } from "lucide-react";
import { useWeather } from "../../hooks/useWeather";

interface WeatherDashboardProps {
	siteId: string;
	onNavigateToTab?: (tabId: string) => void;
}

const WeatherDashboard = ({ siteId, onNavigateToTab }: WeatherDashboardProps) => {
	const { loading, refetch, getWeatherForSite } = useWeather();

	const [isRefreshing, setIsRefreshing] = useState(false);

	const weather = getWeatherForSite(siteId);
	const isLoading = loading;

	const handleRefresh = async () => {
		setIsRefreshing(true);
		try {
			await refetch();
		} finally {
			setIsRefreshing(false);
		}
	};

	const getWeatherIcon = (condition: string, size: string = "h-8 w-8") => {
		const iconClass = `${size} text-blue-500`;
		switch (condition.toLowerCase()) {
			case "clear":
				return <Sun className={`${size} text-yellow-500`} />;
			case "clouds":
				return <Cloud className={iconClass} />;
			case "rain":
			case "drizzle":
				return <CloudRain className={iconClass} />;
			case "snow":
				return <CloudSnow className={iconClass} />;
			case "thunderstorm":
				return <Zap className={`${size} text-purple-500`} />;
			default:
				return <Cloud className={iconClass} />;
		}
	};

	const getTemperatureColor = (temp: number) => {
		if (temp < 0) return "text-blue-600";
		if (temp < 10) return "text-blue-500";
		if (temp < 20) return "text-green-500";
		if (temp < 30) return "text-yellow-500";
		return "text-red-500";
	};

	const getUVIndexColor = (uvIndex: number) => {
		if (uvIndex <= 2) return "text-green-500";
		if (uvIndex <= 5) return "text-yellow-500";
		if (uvIndex <= 7) return "text-orange-500";
		if (uvIndex <= 10) return "text-red-500";
		return "text-purple-500";
	};

	const getUVIndexLabel = (uvIndex: number) => {
		if (uvIndex <= 2) return "Low";
		if (uvIndex <= 5) return "Moderate";
		if (uvIndex <= 7) return "High";
		if (uvIndex <= 10) return "Very High";
		return "Extreme";
	};

	if (isLoading) {
		return (
			<div className="space-y-6">
				<div className="animate-pulse">
					<div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
					<div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
						{[...Array(4)].map((_, i) => (
							<div key={i} className="h-24 bg-gray-200 rounded"></div>
						))}
					</div>
					<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
						<div className="h-64 bg-gray-200 rounded"></div>
						<div className="h-64 bg-gray-200 rounded"></div>
					</div>
				</div>
			</div>
		);
	}

	if (!weather) {
		return (
			<div className="text-center py-12">
				<Cloud className="h-16 w-16 text-gray-400 mx-auto mb-4" />
				<h3 className="text-lg font-medium text-gray-900 mb-2">No Weather Data Available</h3>
				<p className="text-gray-500 mb-4">Unable to load weather information for this site.</p>
				<button
					onClick={handleRefresh}
					className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
				>
					<RefreshCw className="h-4 w-4 mr-2" />
					Retry
				</button>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<div>
					<h2 className="text-2xl font-bold">Weather Dashboard</h2>
					<div className="flex items-center text-sm text-gray-500 mt-1">
						<MapPin className="h-4 w-4 mr-1" />
						<span>{weather.location}</span>
						<span className="mx-2">•</span>
						<Clock className="h-4 w-4 mr-1" />
						<span>Last updated: {new Date(weather.lastUpdated).toLocaleTimeString()}</span>
					</div>
				</div>
				<div className="flex gap-2">
					<button
						onClick={() => onNavigateToTab?.("logged-data")}
						className="flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
					>
						<FileText className="h-4 w-4 mr-2" />
						View Logged Data
					</button>
					<button
						onClick={handleRefresh}
						disabled={isRefreshing}
						className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700 disabled:opacity-50"
					>
						<RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
						Refresh
					</button>
				</div>
			</div>

			{/* Weather Alerts */}
			{weather.alerts && weather.alerts.length > 0 && (
				<div className="bg-red-50 border border-red-200 rounded-lg p-4">
					<div className="flex items-center mb-2">
						<AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
						<h3 className="text-lg font-medium text-red-800">Weather Alerts</h3>
					</div>
					<div className="space-y-2">
						{weather.alerts.map((alert) => (
							<div key={alert.id} className="bg-white rounded p-3 border border-red-200">
								<div className="flex justify-between items-start mb-1">
									<h4 className="font-medium text-red-800">{alert.title}</h4>
									<span className={`px-2 py-1 text-xs font-medium rounded ${
										alert.severity === 'extreme' ? 'bg-red-100 text-red-800' :
										alert.severity === 'severe' ? 'bg-orange-100 text-orange-800' :
										'bg-yellow-100 text-yellow-800'
									}`}>
										{alert.severity.toUpperCase()}
									</span>
								</div>
								<p className="text-sm text-red-700 mb-2">{alert.description}</p>
								<div className="text-xs text-red-600">
									{new Date(alert.startTime).toLocaleString()} - {new Date(alert.endTime).toLocaleString()}
								</div>
							</div>
						))}
					</div>
				</div>
			)}

			{/* Current Weather Overview */}
			<div className="bg-white rounded-lg border border-gray-200 p-6">
				<h3 className="text-lg font-semibold mb-4">Current Conditions</h3>
				<div className="flex items-center justify-between">
					<div className="flex items-center space-x-4">
						{getWeatherIcon(weather.current.conditions.main, "h-16 w-16")}
						<div>
							<div className={`text-4xl font-bold ${getTemperatureColor(weather.current.temperature)}`}>
								{Math.round(weather.current.temperature)}°C
							</div>
							<div className="text-lg text-gray-600 capitalize">
								{weather.current.conditions.description}
							</div>
							<div className="text-sm text-gray-500">
								Feels like {Math.round(weather.current.feelsLike)}°C
							</div>
						</div>
					</div>
					<div className="text-right">
						<div className="text-sm text-gray-600 mb-1">
							{new Date(weather.current.timestamp).toLocaleString()}
						</div>
					</div>
				</div>
			</div>

			{/* Weather Metrics */}
			<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Humidity</p>
							<p className="text-2xl font-bold text-blue-600">{weather.current.humidity}%</p>
						</div>
						<Droplets className="h-8 w-8 text-blue-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Wind Speed</p>
							<p className="text-2xl font-bold text-green-600">{Math.round(weather.current.windSpeed)} m/s</p>
						</div>
						<Wind className="h-8 w-8 text-green-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">Visibility</p>
							<p className="text-2xl font-bold text-purple-600">{Math.round(weather.current.visibility / 1000)} km</p>
						</div>
						<Eye className="h-8 w-8 text-purple-500" />
					</div>
				</div>
				<div className="bg-white rounded-lg border border-gray-200 p-4">
					<div className="flex items-center justify-between">
						<div>
							<p className="text-sm text-gray-600">UV Index</p>
							<p className={`text-2xl font-bold ${getUVIndexColor(weather.current.uvIndex)}`}>
								{weather.current.uvIndex} ({getUVIndexLabel(weather.current.uvIndex)})
							</p>
						</div>
						<Sun className="h-8 w-8 text-yellow-500" />
					</div>
				</div>
			</div>

			{/* 7-Day Forecast */}
			<div className="bg-white rounded-lg border border-gray-200 p-6">
				<h3 className="text-lg font-semibold mb-4">7-Day Forecast</h3>
				<div className="space-y-3">
					{weather.forecast.slice(0, 7).map((day) => (
						<div key={day.date} className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50">
							<div className="flex items-center space-x-4 flex-1">
								<div className="w-16 text-sm font-medium text-gray-700">
									{day.day}
								</div>
								{getWeatherIcon(day.conditions.main, "h-6 w-6")}
								<div className="flex-1">
									<div className="text-sm font-medium text-gray-900 capitalize">
										{day.conditions.description}
									</div>
									<div className="text-xs text-gray-500">
										{day.precipitationChance > 0 && `${day.precipitationChance}% chance of rain`}
									</div>
								</div>
							</div>
							<div className="flex items-center space-x-4 text-sm">
								<div className="flex items-center space-x-1">
									<Droplets className="h-3 w-3 text-blue-400" />
									<span className="text-gray-600">{day.humidity}%</span>
								</div>
								<div className="flex items-center space-x-1">
									<Wind className="h-3 w-3 text-green-400" />
									<span className="text-gray-600">{Math.round(day.windSpeed)} m/s</span>
								</div>
								<div className="text-right">
									<div className="font-medium">{day.temperature.max}°</div>
									<div className="text-gray-500 text-xs">{day.temperature.min}°</div>
								</div>
							</div>
						</div>
					))}
				</div>
			</div>
		</div>
	);
};

export default WeatherDashboard;
