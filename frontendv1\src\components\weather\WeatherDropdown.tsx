import React from 'react';
import { 
  RefreshCw, 
  X, 
  AlertTriangle, 
  Wind, 
  Eye, 
  Thermometer,
  Calendar
} from 'lucide-react';
import { WeatherData } from '../../types/weather';
import { SiteWeatherCard } from './SiteWeatherCard';

interface WeatherDropdownProps {
  weatherData: WeatherData[];
  onClose: () => void;
  onRefresh: () => Promise<void>;
  loading: boolean;
}

export const WeatherDropdown: React.FC<WeatherDropdownProps> = ({
  weatherData,
  onClose,
  onRefresh,
  loading
}) => {
  const handleRefresh = async () => {
    await onRefresh();
  };

  const getOverallWeatherSummary = () => {
    if (weatherData.length === 0) return null;

    const totalAlerts = weatherData.reduce((sum, weather) => 
      sum + (weather.alerts?.length || 0), 0
    );

    const avgTemp = weatherData.reduce((sum, weather) => 
      sum + weather.current.temperature, 0
    ) / weatherData.length;

    const maxWindSpeed = Math.max(...weatherData.map(weather => weather.current.windSpeed));
    const minVisibility = Math.min(...weatherData.map(weather => weather.current.visibility));

    return {
      totalAlerts,
      avgTemp: Math.round(avgTemp),
      maxWindSpeed: Math.round(maxWindSpeed),
      minVisibility: Math.round(minVisibility / 1000) // Convert to km
    };
  };

  const summary = getOverallWeatherSummary();

  return (
    <div className="bg-white rounded-lg shadow-lg border border-gray-200 w-96 max-h-96 overflow-hidden">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-100">
        <div className="flex items-center space-x-2">
          <h3 className="text-lg font-semibold text-gray-900">
            {weatherData.length === 1 ? 'Site Weather' : 'Weather Overview'}
          </h3>
          {summary && summary.totalAlerts > 0 && (
            <div className="flex items-center space-x-1">
              <AlertTriangle className="h-4 w-4 text-red-500" />
              <span className="text-xs text-red-600 font-medium">
                {summary.totalAlerts} Alert{summary.totalAlerts > 1 ? 's' : ''}
              </span>
            </div>
          )}
        </div>
        <div className="flex items-center space-x-2">
          <button
            onClick={handleRefresh}
            disabled={loading}
            className="p-1 hover:bg-gray-100 rounded transition-colors disabled:opacity-50"
            title="Refresh weather data"
          >
            <RefreshCw className={`h-4 w-4 text-gray-500 ${loading ? 'animate-spin' : ''}`} />
          </button>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded transition-colors"
            title="Close"
          >
            <X className="h-4 w-4 text-gray-500" />
          </button>
        </div>
      </div>

      {/* Summary for multiple sites */}
      {weatherData.length > 1 && summary && (
        <div className="p-4 bg-gray-50 border-b border-gray-100">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="flex items-center space-x-2">
              <Thermometer className="h-4 w-4 text-orange-500" />
              <span className="text-gray-600">Avg Temp:</span>
              <span className="font-medium">{summary.avgTemp}°C</span>
            </div>
            <div className="flex items-center space-x-2">
              <Wind className="h-4 w-4 text-blue-500" />
              <span className="text-gray-600">Max Wind:</span>
              <span className="font-medium">{summary.maxWindSpeed} m/s</span>
            </div>
            <div className="flex items-center space-x-2">
              <Eye className="h-4 w-4 text-gray-500" />
              <span className="text-gray-600">Min Visibility:</span>
              <span className="font-medium">{summary.minVisibility} km</span>
            </div>
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4 text-green-500" />
              <span className="text-gray-600">Sites:</span>
              <span className="font-medium">{weatherData.length}</span>
            </div>
          </div>
        </div>
      )}

      {/* Weather Data */}
      <div className="max-h-64 overflow-y-auto">
        {weatherData.length === 0 ? (
          <div className="p-6 text-center">
            <AlertTriangle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-500">No weather data available</p>
          </div>
        ) : (
          <div className="space-y-0">
            {weatherData.map((weather, index) => (
              <SiteWeatherCard
                key={weather.siteId}
                weather={weather}
                isLast={index === weatherData.length - 1}
                compact={weatherData.length > 1}
              />
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="p-3 bg-gray-50 border-t border-gray-100">
        <div className="flex items-center justify-between text-xs text-gray-500">
          <span>
            Last updated: {weatherData[0] ? 
              new Date(weatherData[0].lastUpdated).toLocaleTimeString('en-GB', {
                hour: '2-digit',
                minute: '2-digit'
              }) : 'Never'
            }
          </span>
          <span>Auto-refresh: 30min</span>
        </div>
      </div>
    </div>
  );
};
