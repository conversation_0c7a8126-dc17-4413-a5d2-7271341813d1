import React, { useState, useRef, useEffect } from 'react';
import { 
  Cloud, 
  CloudRain, 
  Sun, 
  CloudSnow, 
  Zap, 
  Wind, 
  AlertTriangle,
  RefreshCw,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { useWeather, useSiteWeather } from '../../hooks/useWeather';
import { WeatherDropdown } from './WeatherDropdown';
import { extractSiteId } from '../../utils/routeUtils';
import { useLocation } from 'react-router-dom';

interface WeatherIndicatorProps {
  className?: string;
}

export const WeatherIndicator: React.FC<WeatherIndicatorProps> = ({ className = '' }) => {
  const location = useLocation();
  const currentSiteId = extractSiteId(location.pathname);
  
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  
  // Use site-specific weather if on a site page, otherwise use general weather hook
  const { weather: siteWeather, loading: siteLoading } = useSiteWeather(currentSiteId || '');
  const { weatherData, loading: allLoading, error, refetch } = useWeather();
  
  const loading = currentSiteId ? siteLoading : allLoading;
  const displayWeather = currentSiteId ? siteWeather : weatherData[0]; // Show first site if not on specific site

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        buttonRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    if (isDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isDropdownOpen]);

  const getWeatherIcon = (condition: string, size: string = 'h-5 w-5') => {
    const iconClass = `${size} text-gray-600`;
    
    switch (condition.toLowerCase()) {
      case 'clear':
        return <Sun className={`${iconClass} text-yellow-500`} />;
      case 'clouds':
        return <Cloud className={iconClass} />;
      case 'rain':
        return <CloudRain className={`${iconClass} text-blue-500`} />;
      case 'snow':
        return <CloudSnow className={`${iconClass} text-blue-300`} />;
      case 'thunderstorm':
        return <Zap className={`${iconClass} text-purple-500`} />;
      case 'wind':
        return <Wind className={iconClass} />;
      default:
        return <Cloud className={iconClass} />;
    }
  };

  const getTemperatureColor = (temp: number) => {
    if (temp < 10) return 'text-blue-600';
    if (temp < 25) return 'text-green-600';
    if (temp < 35) return 'text-yellow-600';
    return 'text-red-600';
  };

  const hasWeatherAlerts = () => {
    if (currentSiteId && siteWeather) {
      return siteWeather.alerts && siteWeather.alerts.length > 0;
    }
    return weatherData.some(weather => weather.alerts && weather.alerts.length > 0);
  };

  const handleRefresh = async (e: React.MouseEvent) => {
    e.stopPropagation();
    await refetch();
  };

  if (loading && !displayWeather) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className="animate-spin">
          <RefreshCw className="h-5 w-5 text-gray-400" />
        </div>
        <span className="text-sm text-gray-500">Loading weather...</span>
      </div>
    );
  }

  if (error || !displayWeather) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <AlertTriangle className="h-5 w-5 text-red-500" />
        <span className="text-sm text-red-600">Weather unavailable</span>
        <button
          onClick={handleRefresh}
          className="p-1 hover:bg-gray-100 rounded"
          title="Retry"
        >
          <RefreshCw className="h-4 w-4 text-gray-500" />
        </button>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      <button
        ref={buttonRef}
        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
        className="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
        title={`Weather for ${displayWeather.siteName || 'All Sites'}`}
      >
        {/* Weather Icon */}
        <div className="relative">
          {getWeatherIcon(displayWeather.current.conditions.main)}
          {hasWeatherAlerts() && (
            <div className="absolute -top-1 -right-1 w-2 h-2 bg-red-500 rounded-full"></div>
          )}
        </div>

        {/* Temperature */}
        <span className={`text-sm font-medium ${getTemperatureColor(displayWeather.current.temperature)}`}>
          {Math.round(displayWeather.current.temperature)}°C
        </span>



        {/* Dropdown indicator */}
        {isDropdownOpen ? (
          <ChevronUp className="h-4 w-4 text-gray-400" />
        ) : (
          <ChevronDown className="h-4 w-4 text-gray-400" />
        )}

        {/* Loading indicator */}
        {loading && (
          <RefreshCw className="h-4 w-4 text-gray-400 animate-spin" />
        )}
      </button>

      {/* Weather Dropdown */}
      {isDropdownOpen && (
        <div
          ref={dropdownRef}
          className="absolute right-0 top-full mt-2 z-50"
        >
          <WeatherDropdown
            weatherData={currentSiteId ? [displayWeather] : weatherData}
            onClose={() => setIsDropdownOpen(false)}
            onRefresh={refetch}
            loading={loading}
          />
        </div>
      )}
    </div>
  );
};
