import React, { useState } from "react";
import { toast } from "react-toastify";
import {
  User,
  Building,
  Phone,
  Mail,
  Calendar,
  AlertCircle,
  IdCard,
} from "lucide-react";
import { mockTrades, mockSkills } from "../../data/mockData";
import { Gender } from "../../types/graphql";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Button } from "../ui/button";
import { Select } from "../ui/select";

// Photo capture is now handled by the parent page's left panel
// Removed Additional Documents upload from registration; dedicated tab handles documents

interface CreateWorkerFormProps {
  onSuccess?: (worker: any) => void;
  onCancel?: () => void;
  useDummyData?: boolean;
  // External controls from parent wizard shell
  submitSignal?: number; // when incremented, triggers submit
  onBasicInfoChange?: (info: { name: string; photoUrl?: string }) => void;
  // Wizard navigation controls (non-submitting)
  onPrev?: () => void;
  onNext?: () => void;
  // External photo provided by parent (left panel capture/upload)
  externalPhoto?: File | null;
}

interface FormData {
  name: string;
  company: string;
  nationalId: string;
  gender: Gender;
  phoneNumber: string;
  dateOfBirth: string;
  tradeId?: number;
  skillIds: number[];
  mpesaNumber: string;
  email: string;
  inductionDate: string;
  medicalCheckDate: string;
  address?: string;
  emergencyContactName?: string;
  emergencyContactPhone?: string;
  emergencyContactRelationship?: string;
  emergencyContactEmail?: string;
  idFront?: File | null;
  idBack?: File | null;
  signature?: File | null;
}

interface FormErrors {
  [key: string]: string;
}

const CreateWorkerForm: React.FC<CreateWorkerFormProps> = ({
  onSuccess,
  submitSignal,
  onBasicInfoChange,
  onPrev,
  onNext,
  externalPhoto,
}) => {
  const [formData, setFormData] = useState<FormData>({
    name: "",
    company: "",
    nationalId: "",
    gender: "MALE",
    phoneNumber: "",
    dateOfBirth: "",
    tradeId: undefined,
    skillIds: [],
    mpesaNumber: "",
    email: "",
    inductionDate: "",
    medicalCheckDate: "",
    address: "",
    emergencyContactName: "",
    emergencyContactPhone: "",
    emergencyContactRelationship: "",
    emergencyContactEmail: "",
    idFront: null,
    idBack: null,
    signature: null,
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [touched, setTouched] = useState<Partial<Record<keyof FormData, boolean>>>({});
  const [submitAttempted, setSubmitAttempted] = useState(false);
  const [, setIsSubmitting] = useState(false);
  // --- Training/Docs moved to dedicated tabs; no extra state here ---

  // Use mock data instead of GraphQL
  const trades = mockTrades;
  const skills = mockSkills;

  // Notify parent about basic info so it can render the left profile preview
  React.useEffect(() => {
    if (onBasicInfoChange) {
      const url = externalPhoto ? URL.createObjectURL(externalPhoto) : undefined;
      onBasicInfoChange({ name: formData.name || "", photoUrl: url });
    }
    // We intentionally depend on name and externalPhoto
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formData.name, externalPhoto]);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};
    // Required field validations
    !formData.name.trim()
      ? (newErrors.name = "Name is required")
      : delete newErrors.name;
    !formData.company.trim()
      ? (newErrors.company = "Company is required")
      : delete newErrors.company;
    !formData.nationalId.trim()
      ? (newErrors.nationalId = "National ID is required")
      : delete newErrors.nationalId;
    !formData.gender
      ? (newErrors.gender = "Gender is required")
      : delete newErrors.gender;
    !formData.phoneNumber.trim()
      ? (newErrors.phoneNumber = "Phone number is required")
      : delete newErrors.phoneNumber;
    !formData.mpesaNumber.trim()
      ? (newErrors.mpesaNumber = "Mpesa number is required")
      : delete newErrors.mpesaNumber;
    !formData.email.trim()
      ? (newErrors.email = "Email is required")
      : delete newErrors.email;
    // Registration step doesn't require induction/medical at first save
    // Make trade mandatory at registration
    formData.tradeId == null
      ? (newErrors.tradeId = "Please select a trade")
      : delete newErrors.tradeId;
    !formData.dateOfBirth.trim()
      ? (newErrors.dateOfBirth = "Date of birth is required")
      : delete newErrors.dateOfBirth;

    // Format validations
    if (formData.nationalId && !/^\d{8,12}$/.test(formData.nationalId)) {
      newErrors.nationalId = "National ID must be 8-12 digits";
    } else if (formData.nationalId.trim()) {
      delete newErrors.nationalId;
    }

    if (
      formData.phoneNumber &&
      !/^[+]?[\d\s-()]{10,15}$/.test(formData.phoneNumber)
    ) {
      newErrors.phoneNumber = "Please enter a valid phone number";
    } else if (formData.phoneNumber.trim()) {
      delete newErrors.phoneNumber;
    }
    // Validate mpesa number
    if (
      formData.mpesaNumber &&
      !/^[+]?[\d\s-()]{10,15}$/.test(formData.mpesaNumber)
    ) {
      newErrors.mpesaNumber = "Please enter a valid mpesa number";
    } else if (formData.mpesaNumber.trim()) {
      delete newErrors.mpesaNumber;
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = "Please enter a valid email address";
    } else if (formData.email.trim()) {
      delete newErrors.email;
    }

    // Date validations
    if (formData.dateOfBirth) {
      const birthDate = new Date(formData.dateOfBirth);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();

      if (age < 18 || age > 80) {
        newErrors.dateOfBirth = "Age must be between 18 and 80 years";
      } else if (formData.dateOfBirth.trim()) {
        delete newErrors.dateOfBirth;
      }
    }

    if (
      formData.inductionDate &&
      new Date(formData.inductionDate) > new Date()
    ) {
      newErrors.inductionDate = "Induction date cannot be in the future";
    } else if (formData.inductionDate.trim()) {
      delete newErrors.inductionDate;
    }

    if (
      formData.medicalCheckDate &&
      new Date(formData.medicalCheckDate) > new Date()
    ) {
      newErrors.medicalCheckDate = "Medical check date cannot be in the future";
    } else if (formData.medicalCheckDate.trim()) {
      delete newErrors.medicalCheckDate;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Allow parent to trigger submit via submitSignal (only when value changes)
  const lastSubmitSignalRef = React.useRef<number | undefined>(submitSignal);
  React.useEffect(() => {
    if (submitSignal === undefined) return;
    // Skip initial mount
    if (lastSubmitSignalRef.current === undefined) {
      lastSubmitSignalRef.current = submitSignal;
      return;
    }
    if (submitSignal !== lastSubmitSignalRef.current) {
      const evt = new Event('submit', { bubbles: true, cancelable: true });
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      handleSubmit(evt as any);
      lastSubmitSignalRef.current = submitSignal;
    }
  }, [submitSignal]);

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }));
    }
  };

  // Helper function to clear the form
  function clearForm() {
    setFormData({
      name: "",
      company: "",
      nationalId: "",
      gender: "MALE",
      phoneNumber: "",
      dateOfBirth: "",
      tradeId: undefined,
      skillIds: [],
      mpesaNumber: "",
      email: "",
      inductionDate: "",
      medicalCheckDate: "",
      address: "",
      emergencyContactName: "",
      emergencyContactPhone: "",
      emergencyContactRelationship: "",
      emergencyContactEmail: "",
      idFront: null,
      idBack: null,
      signature: null,
    });
    setErrors({});
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitAttempted(true);

    if (!validateForm()) return;
    if (!externalPhoto) {
      toast.error("Please select a photo");
      return;
    }

    setIsSubmitting(true);

    // Simulate API call with mock data
    try {
      // Create mock worker object
      const newWorker = {
        id: Date.now(), // Simple ID generation
        name: formData.name,
        company: formData.company,
        nationalId: formData.nationalId,
        gender: formData.gender,
        phoneNumber: formData.phoneNumber,
        dateOfBirth: formData.dateOfBirth,
        email: formData.email,
        address: formData.address,
        emergencyContactName: formData.emergencyContactName,
        emergencyContactPhone: formData.emergencyContactPhone,
        inductionDate: formData.inductionDate,
        medicalCheckDate: formData.medicalCheckDate,
        trade: trades.find(t => t.id === formData.tradeId) || null,
        skills: formData.skillIds.map(id => skills.find(s => s.id === id)).filter(Boolean),
        mpesaNumber: formData.mpesaNumber,
        profilePictureUrl: URL.createObjectURL(externalPhoto),
        status: 'active',
        createdAt: new Date().toISOString(),
      };

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Show success toast
      toast.success(`Worker "${newWorker.name}" created successfully!`);
      clearForm();
      onSuccess?.(newWorker);
    } catch (error) {
      console.error('Error creating worker:', error);
      toast.error('Failed to create worker. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Additional documents are handled in the Documents tab

  return (
    <div className="w-full">
      <form onSubmit={handleSubmit} className="space-y-8 max-w-6xl mx-auto">
        {/* Photo upload/capture is handled in the left panel */}

        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Personal Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name" className="flex items-center gap-1">
                  <User className="h-4 w-4" />
                  Full Name *
                </Label>
                <Input
                  id="name"
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  onBlur={() => setTouched((p) => ({ ...p, name: true }))}
                  className={errors.name ? "border-red-500" : ""}
                  placeholder="Enter full name"
                />
                {errors.name && (touched.name || submitAttempted) && (
                  <p className="text-red-500 text-sm flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.name}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="company" className="flex items-center gap-1">
                  <Building className="h-4 w-4" />
                  Company *
                </Label>
                <Input
                  id="company"
                  type="text"
                  value={formData.company}
                  onChange={(e) => handleInputChange("company", e.target.value)}
                  onBlur={() => setTouched((p) => ({ ...p, company: true }))}
                  className={errors.company ? "border-red-500" : ""}
                  placeholder="Enter company name"
                />
                {errors.company && (touched.company || submitAttempted) && (
                  <p className="text-red-500 text-sm flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.company}
                  </p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="nationalId" className="flex items-center gap-1">
                  <IdCard className="h-4 w-4" />
                  National ID *
                </Label>
                <Input
                  id="nationalId"
                  type="text"
                  value={formData.nationalId}
                  onChange={(e) => handleInputChange("nationalId", e.target.value)}
                  onBlur={() => setTouched((p) => ({ ...p, nationalId: true }))}
                  className={errors.nationalId ? "border-red-500" : ""}
                  placeholder="Enter national ID"
                />
                {errors.nationalId && (touched.nationalId || submitAttempted) && (
                  <p className="text-red-500 text-sm flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.nationalId}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="gender">Gender *</Label>
                <Select
                  value={formData.gender}
                  onChange={(e) => handleInputChange("gender", e.target.value)}
                  onBlur={() => setTouched((p) => ({ ...p, gender: true }))}
                  className={errors.gender ? "border-red-500" : ""}
                >
                  <option value="MALE">Male</option>
                  <option value="FEMALE">Female</option>
                </Select>
                {errors.gender && (touched.gender || submitAttempted) && (
                  <p className="text-red-500 text-sm flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.gender}
                  </p>
                )}
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="address">Home Address</Label>
                <Input
                  id="address"
                  type="text"
                  value={formData.address}
                  onChange={(e) => handleInputChange("address", e.target.value)}
                  placeholder="Street, City"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="trade">Trade *</Label>
                <Select
                  value={formData.tradeId?.toString() ?? ''}
                  onChange={(e) => handleInputChange("tradeId", Number(e.target.value))}
                  onBlur={() => setTouched((p) => ({ ...p, tradeId: true }))}
                  className={errors.tradeId ? "border-red-500" : ""}
                >
                  <option value="" disabled>Select trade</option>
                  {trades.map(t => (
                    <option key={t.id} value={t.id}>{t.name}</option>
                  ))}
                </Select>
                {errors.tradeId && (touched.tradeId || submitAttempted) && (
                  <p className="text-red-500 text-sm flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.tradeId}
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Phone className="h-5 w-5" />
              Contact Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="phoneNumber" className="flex items-center gap-1">
                  <Phone className="h-4 w-4" />
                  Phone Number *
                </Label>
                <Input
                  id="phoneNumber"
                  type="tel"
                  value={formData.phoneNumber}
                  onChange={(e) => handleInputChange("phoneNumber", e.target.value)}
                  onBlur={() => setTouched((p) => ({ ...p, phoneNumber: true }))}
                  className={errors.phoneNumber ? "border-red-500" : ""}
                  placeholder="+254 712 345 678"
                />
                {errors.phoneNumber && (touched.phoneNumber || submitAttempted) && (
                  <p className="text-red-500 text-sm flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.phoneNumber}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="email" className="flex items-center gap-1">
                  <Mail className="h-4 w-4" />
                  Email Address
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                  onBlur={() => setTouched((p) => ({ ...p, email: true }))}
                  className={errors.email ? "border-red-500" : ""}
                  placeholder="<EMAIL>"
                />
                {errors.email && (touched.email || submitAttempted) && (
                  <p className="text-red-500 text-sm flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.email}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="mpesaNumber" className="flex items-center gap-1">
                  <Phone className="h-4 w-4" />
                  M-Pesa Number
                </Label>
                <Input
                  id="mpesaNumber"
                  type="tel"
                  value={formData.mpesaNumber}
                  onChange={(e) => handleInputChange("mpesaNumber", e.target.value)}
                  onBlur={() => setTouched((p) => ({ ...p, mpesaNumber: true }))}
                  className={errors.mpesaNumber ? "border-red-500" : ""}
                  placeholder="+254 712 345 678"
                />
                {errors.mpesaNumber && (touched.mpesaNumber || submitAttempted) && (
                  <p className="text-red-500 text-sm flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.mpesaNumber}
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Date Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Important Dates
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label htmlFor="dateOfBirth" className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  Date of Birth
                </Label>
                <Input
                  id="dateOfBirth"
                  type="date"
                  value={formData.dateOfBirth}
                  onChange={(e) => handleInputChange("dateOfBirth", e.target.value)}
                  onBlur={() => setTouched((p) => ({ ...p, dateOfBirth: true }))}
                  className={errors.dateOfBirth ? "border-red-500" : ""}
                />
                {errors.dateOfBirth && (touched.dateOfBirth || submitAttempted) && (
                  <p className="text-red-500 text-sm flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.dateOfBirth}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="inductionDate">Induction Date</Label>
                <Input
                  id="inductionDate"
                  type="date"
                  value={formData.inductionDate}
                  onChange={(e) => handleInputChange("inductionDate", e.target.value)}
                  onBlur={() => setTouched((p) => ({ ...p, inductionDate: true }))}
                  className={errors.inductionDate ? "border-red-500" : ""}
                />
                {errors.inductionDate && (touched.inductionDate || submitAttempted) && (
                  <p className="text-red-500 text-sm flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.inductionDate}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="medicalCheckDate">Medical Check Date</Label>
                <Input
                  id="medicalCheckDate"
                  type="date"
                  value={formData.medicalCheckDate}
                  onChange={(e) => handleInputChange("medicalCheckDate", e.target.value)}
                  onBlur={() => setTouched((p) => ({ ...p, medicalCheckDate: true }))}
                  className={errors.medicalCheckDate ? "border-red-500" : ""}
                />
                {errors.medicalCheckDate && (touched.medicalCheckDate || submitAttempted) && (
                  <p className="text-red-500 text-sm flex items-center gap-1">
                    <AlertCircle className="h-4 w-4" />
                    {errors.medicalCheckDate}
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        

        {/* Emergency Contact */}
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Emergency Contact</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="space-y-2">
                <Label htmlFor="emergencyContactName">Name</Label>
                <Input
                  id="emergencyContactName"
                  type="text"
                  value={formData.emergencyContactName}
                  onChange={(e) => handleInputChange("emergencyContactName", e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="emergencyContactRelationship">Relationship</Label>
                <Input
                  id="emergencyContactRelationship"
                  type="text"
                  value={formData.emergencyContactRelationship}
                  onChange={(e) => handleInputChange("emergencyContactRelationship", e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="emergencyContactPhone">Phone</Label>
                <Input
                  id="emergencyContactPhone"
                  type="tel"
                  value={formData.emergencyContactPhone}
                  onChange={(e) => handleInputChange("emergencyContactPhone", e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="emergencyContactEmail">Email</Label>
                <Input
                  id="emergencyContactEmail"
                  type="email"
                  value={formData.emergencyContactEmail}
                  onChange={(e) => handleInputChange("emergencyContactEmail", e.target.value)}
                />
              </div>
            </div>
          </CardContent>
        </Card>

        

        {/* Wizard Navigation (non-sticky, non-submitting) */}
        <div className="flex items-center pt-6 border-t border-gray-200">
          <Button
            type="button"
            variant="outline"
            onClick={onPrev}
          >
            ← Previous
          </Button>
          <div className="flex-1" />
          <Button
            type="button"
            onClick={onNext}
            className="min-w-[120px]"
          >
            Next →
          </Button>
        </div>
      </form>
    </div>
  );
};

export default CreateWorkerForm;

// --- COMPONENTS for uploading docs ---
// Re-introduced lightweight uploaders for compatibility with other pages
export const TrainingDocUploader: React.FC<{
  trainingId: number;
  onAdd: (trainingId: number, file: File, name: string) => void;
}> = ({ trainingId, onAdd }) => {
  const [file, setFile] = React.useState<File | null>(null);
  const [name, setName] = React.useState<string>("");

  const handleSubmit = () => {
    if (!file || !name.trim()) return;
    onAdd(trainingId, file, name.trim());
    setFile(null);
    setName("");
  };

  return (
    <div className="flex flex-col sm:flex-row items-stretch sm:items-end gap-2">
      <div className="flex-1 min-w-0">
        <Label htmlFor={`doc-name-${trainingId}`}>Document Name</Label>
        <Input
          id={`doc-name-${trainingId}`}
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="e.g. Safety Certificate"
        />
      </div>
      <div className="flex-1 min-w-0">
        <Label htmlFor={`doc-file-${trainingId}`}>Upload File</Label>
        <Input
          id={`doc-file-${trainingId}`}
          type="file"
          accept="image/*,application/pdf"
          onChange={(e) => setFile(e.target.files?.[0] || null)}
        />
      </div>
      <div className="sm:pb-0 pb-1">
        <Button type="button" onClick={handleSubmit} disabled={!file || !name.trim()}>
          Add
        </Button>
      </div>
    </div>
  );
};

export const GeneralDocUploader: React.FC<{
  onAdd: (file: File, name: string) => void;
}> = ({ onAdd }) => {
  const [file, setFile] = React.useState<File | null>(null);
  const [name, setName] = React.useState<string>("");

  const handleSubmit = () => {
    if (!file || !name.trim()) return;
    onAdd(file, name.trim());
    setFile(null);
    setName("");
  };

  return (
    <div className="flex flex-col sm:flex-row items-stretch sm:items-end gap-2">
      <div className="flex-1 min-w-0">
        <Label htmlFor={`gen-doc-name`}>Document Name</Label>
        <Input
          id={`gen-doc-name`}
          type="text"
          value={name}
          onChange={(e) => setName(e.target.value)}
          placeholder="e.g. ID Copy, Medical Report"
        />
      </div>
      <div className="flex-1 min-w-0">
        <Label htmlFor={`gen-doc-file`}>Upload File</Label>
        <Input
          id={`gen-doc-file`}
          type="file"
          accept="image/*,application/pdf"
          onChange={(e) => setFile(e.target.files?.[0] || null)}
        />
      </div>
      <div className="sm:pb-0 pb-1">
        <Button type="button" onClick={handleSubmit} disabled={!file || !name.trim()}>
          Add
        </Button>
      </div>
    </div>
  );
};
