# Worker Management Components

This directory contains components for managing workers in the workforce management system.

## Components

### CreateWorkerForm

A comprehensive form component for creating new workers with validation, photo upload, and multi-select dropdowns.

#### Features

- **Form Validation**: Comprehensive validation for all required fields with real-time feedback
- **Photo Upload**: File upload with validation (image types, max 10MB) and preview
- **Camera Integration**: Placeholder for taking photos (ready for implementation)
- **Multi-select Dropdowns**: Checkbox-based selection for Skills, Training, and Trades
- **GraphQL Integration**: Supports both dummy data and real GraphQL queries
- **Responsive Design**: Works on mobile and desktop devices
- **Loading States**: Shows loading indicators during form submission and data fetching
- **Error Handling**: Displays validation errors with clear messaging

#### Props

```typescript
interface CreateWorkerFormProps {
  onSuccess?: (worker: any) => void;     // Callback when worker is created successfully
  onCancel?: () => void;                 // Callback when form is cancelled
  useDummyData?: boolean;                // Flag to use dummy data instead of GraphQL queries
}
```

#### Usage

```tsx
import CreateWorkerForm from './components/workers/CreateWorkerForm';

const MyComponent = () => {
  const handleWorkerCreated = (worker) => {
    console.log('Worker created:', worker);
    // Handle success (show toast, refresh list, etc.)
  };

  const handleCancel = () => {
    // Handle form cancellation
  };

  return (
    <CreateWorkerForm
      onSuccess={handleWorkerCreated}
      onCancel={handleCancel}
      useDummyData={true} // Set to false for real GraphQL queries
    />
  );
};
```

#### Form Fields

**Required Fields:**
- Name (string)
- Company (string)
- National ID (8-12 digits)
- Gender (dropdown: Male, Female, Other)
- Phone Number (validated format)

**Optional Fields:**
- Email (validated format)
- Date of Birth (age validation: 16-80 years)
- Induction Date (cannot be future)
- Medical Check Date (cannot be future)
- Man Hours (number, defaults to 0)
- Skills (multi-select checkboxes)
- Training (multi-select checkboxes)
- Trades (multi-select checkboxes)
- Photo (file upload with preview)

#### Validation Rules

- **Name**: Required, non-empty string
- **Company**: Required, non-empty string
- **National ID**: Required, 8-12 digits only
- **Gender**: Required, must select from dropdown
- **Phone Number**: Required, valid phone format (+*********** 678)
- **Email**: Optional, valid email format when provided
- **Date of Birth**: Optional, age must be 16-80 years
- **Induction Date**: Optional, cannot be in the future
- **Medical Check Date**: Optional, cannot be in the future
- **Photo**: Optional, must be image file under 10MB

#### GraphQL Integration

The component supports both dummy data and real GraphQL queries:

**Queries Used:**
- `GET_ALL_TRAININGS`: Fetches available training programs
- `GET_ALL_TRADES`: Fetches available trades
- `GET_ALL_SKILLS`: Fetches available skills

**Mutation Used:**
- `CREATE_WORKER`: Creates a new worker with the provided data

**Schema Requirements:**

```graphql
# Queries
query GetAllTrainings {
  allTrainings {
    id
    name
    description
  }
}

query GetAllTrades {
  allTrades {
    id
    name
    description
  }
}

query GetAllSkills {
  allSkills {
    id
    name
    description
  }
}

# Mutation
mutation CreateWorker(
  $name: String!
  $company: String!
  $nationalId: String!
  $gender: String!
  $phoneNumber: String!
  $dateOfBirth: Date
  $trainingIds: [Int!]
  $tradeIds: [Int!]
  $skillIds: [Int!]
  $manHours: Int! = 0
  $email: String
  $inductionDate: DateTime
  $medicalCheckDate: DateTime
) {
  createWorker(
    name: $name
    company: $company
    nationalId: $nationalId
    gender: $gender
    phoneNumber: $phoneNumber
    dateOfBirth: $dateOfBirth
    trainingIds: $trainingIds
    tradeIds: $tradeIds
    skillIds: $skillIds
    manHours: $manHours
    email: $email
    inductionDate: $inductionDate
    medicalCheckDate: $medicalCheckDate
  ) {
    id
    name
    company
    nationalId
    phoneNumber
    email
    # ... other fields
  }
}
```

### CreateWorkerDemo

A demo component showing the CreateWorkerForm in action with a simple interface.

### WorkerFormIntegrationExample

An example component demonstrating how to integrate the CreateWorkerForm into existing components like DataDashboard.

## File Structure

```
workers/
├── CreateWorkerForm.tsx          # Main form component
├── CreateWorkerDemo.tsx           # Demo component
├── WorkerFormIntegrationExample.tsx # Integration example
├── PhotoUpload.tsx               # Existing photo upload component
└── README.md                     # This file
```

## Dependencies

- React 19.0.0
- @apollo/client ^3.13.8
- lucide-react ^0.510.0
- Tailwind CSS for styling

## Future Enhancements

- Camera integration for taking photos
- Drag-and-drop photo upload
- Bulk worker import
- Worker photo management
- Integration with face recognition systems
- Advanced validation rules
- Custom field configurations
