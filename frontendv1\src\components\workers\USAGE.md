# Worker Creation Form - Usage Guide

This guide explains how to access and use the CreateWorkerForm component in the workforce management system.

## Available Routes

### 1. De<PERSON> Page (Recommended for Testing)
**URL:** `/demo/worker-form`

This is a standalone demo page that showcases the CreateWorkerForm with:
- Interactive demo interface
- Feature explanations
- Technical implementation details
- Success feedback when workers are created
- Easy access without needing site context

**How to access:**
1. Start the development server: `npm run dev`
2. Navigate to: `http://localhost:5173/demo/worker-form`
3. Click "Try the Form" to test the worker creation form

### 2. Site-Specific Worker Creation
**URL:** `/sites/{siteId}/workers/create`

This is the production route for creating workers within a specific site context.

**How to access:**
1. Navigate to any site's worker directory: `/sites/{siteId}/workers`
2. Click the "Add New Worker" button
3. This will take you to `/sites/{siteId}/workers/create`

**Example URLs:**
- `/sites/site-1/workers/create`
- `/sites/westlands-site/workers/create`

### 3. General Worker Creation
**URL:** `/workers/create`

This is a general worker creation route not tied to a specific site.

## Form Features

### Required Fields
- **Name**: Full name of the worker
- **Company**: Company the worker belongs to
- **National ID**: 8-12 digit national identification number
- **Gender**: Male, Female, or Other
- **Phone Number**: Valid phone number format

### Optional Fields
- **Email**: Valid email address
- **Date of Birth**: Must result in age between 16-80 years
- **Induction Date**: Cannot be in the future
- **Medical Check Date**: Cannot be in the future
- **Man Hours**: Defaults to 0

### Multi-Select Options
- **Skills**: Checkbox selection from available skills
- **Training**: Checkbox selection from available training programs
- **Trades**: Checkbox selection from available trades

### Photo Upload
- **File Upload**: Supports image files up to 10MB
- **Take Photo**: Placeholder button for camera functionality
- **Preview**: Shows uploaded image preview
- **Independent Validation**: Photo upload doesn't affect form validation

## Integration Examples

### Adding to Existing Components

```tsx
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import CreateWorkerForm from '../components/workers/CreateWorkerForm';

const MyComponent = () => {
  const navigate = useNavigate();
  const [showForm, setShowForm] = useState(false);

  const handleWorkerCreated = (worker) => {
    console.log('Worker created:', worker);
    // Show success message
    alert(`Worker "${worker.name}" created successfully!`);
    // Navigate or update UI
    navigate('/workers');
  };

  const handleCancel = () => {
    setShowForm(false);
  };

  return (
    <div>
      {showForm ? (
        <CreateWorkerForm
          onSuccess={handleWorkerCreated}
          onCancel={handleCancel}
          useDummyData={true} // or false for real GraphQL
        />
      ) : (
        <button onClick={() => setShowForm(true)}>
          Create Worker
        </button>
      )}
    </div>
  );
};
```

### Adding to Quick Actions

```tsx
import { Link } from 'react-router-dom';
import { Users } from 'lucide-react';
import QuickActionCard from '../components/data/shared/QuickActionCard';

// In your component's render method
<QuickActionCard
  title="Create New Worker"
  description="Add a new worker to the system"
  icon={<Users className="h-5 w-5" />}
  onClick={() => navigate('/workers/create')}
/>

// Or as a Link
<Link 
  to="/workers/create"
  className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
>
  <Users className="h-4 w-4 mr-2" />
  Create Worker
</Link>
```

## Configuration Options

### Using Dummy Data (Default)
```tsx
<CreateWorkerForm
  onSuccess={handleSuccess}
  onCancel={handleCancel}
  useDummyData={true} // Uses predefined dummy data
/>
```

### Using Real GraphQL Queries
```tsx
<CreateWorkerForm
  onSuccess={handleSuccess}
  onCancel={handleCancel}
  useDummyData={false} // Fetches data from GraphQL endpoints
/>
```

## GraphQL Requirements

When `useDummyData={false}`, the form expects these GraphQL operations to be available:

### Queries
- `GET_ALL_TRAININGS`: Returns list of training programs
- `GET_ALL_TRADES`: Returns list of trades
- `GET_ALL_SKILLS`: Returns list of skills

### Mutations
- `CREATE_WORKER`: Creates a new worker with the provided data

## Validation Rules

- **Name**: Required, non-empty string
- **Company**: Required, non-empty string
- **National ID**: Required, 8-12 digits only
- **Gender**: Required, must select from dropdown
- **Phone Number**: Required, valid phone format
- **Email**: Optional, valid email format when provided
- **Date of Birth**: Optional, age must be 16-80 years
- **Dates**: Induction and Medical Check dates cannot be in the future
- **Photo**: Optional, must be image file under 10MB

## Success Handling

The form calls the `onSuccess` callback with the created worker data:

```tsx
const handleWorkerCreated = (worker) => {
  // worker object contains:
  // - id, name, company, nationalId, phoneNumber
  // - email, dateOfBirth, gender, manHours
  // - trainingIds, tradeIds, skillIds
  // - inductionDate, medicalCheckDate
  // - createdAt, createdBy, etc.
  
  console.log('New worker:', worker);
  // Handle success (show toast, refresh list, navigate, etc.)
};
```

## Error Handling

The form handles validation errors automatically and displays them inline. For GraphQL errors, the form will show appropriate error messages and allow retry.

## Styling

The form uses Tailwind CSS classes and follows the existing design system:
- Green color scheme for primary actions
- Consistent spacing and typography
- Responsive design for mobile and desktop
- Loading states and error styling
