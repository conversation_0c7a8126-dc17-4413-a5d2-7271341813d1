import React, { useState, useEffect } from 'react';
import { useMutation, useQuery } from '@apollo/client';
import { toast } from 'react-toastify';
import { AlertCircle, Building, Calendar, Mail, Phone, User, Trash2 } from 'lucide-react';
import { DELETE_WORKER, UPDATE_WORKER_WITH_TRAINING } from '../../graphql/mutations';
import { GET_ALL_TRAININGS, GET_ALL_TRADES, GET_ALL_SKILLS, GET_WORKER_BY_ID } from '../../graphql/queries';
import { TrainingDocUploader, GeneralDocUploader } from './CreateWorkerForm';
import { UpdateWorkerWithTrainingInput } from '../../types/graphql';

interface UpdateWorkerFormProps {
  workerId: number;
  onSuccess?: (worker: any) => void;
  onCancel?: () => void;
  onDelete?: () => void;
}

interface FormData {
  name: string;
  company: string;
  nationalId: string;
  gender: string;
  phoneNumber: string;
  dateOfBirth: string;
  trainingIds: number[];
  tradeIds: number[];
  skillIds: number[];
  mpesaNumber: string;
  email: string;
  inductionDate: string;
  medicalCheckDate: string;
}

interface FormErrors {
  [key: string]: string;
}

const UpdateWorkerForm: React.FC<UpdateWorkerFormProps> = ({
  workerId,
  onSuccess,
  onCancel,
  onDelete
}) => {
  const [formData, setFormData] = useState<FormData>({
    name: '',
    company: '',
    nationalId: '',
    gender: '',
    phoneNumber: '',
    dateOfBirth: '',
    trainingIds: [],
    tradeIds: [],
    skillIds: [],
    mpesaNumber: '',
    email: '',
    inductionDate: '',
    medicalCheckDate: '',
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  // Add state for assigned and selectable trainings
  const [assignedTrainings, setAssignedTrainings] = useState<number[]>([]); // trainings worker already has
  const [selectableTrainings, setSelectableTrainings] = useState<number[]>([]); // trainings user can select
  const [trainingDocuments, setTrainingDocuments] = useState<Record<number, any[]>>({}); // {trainingId: [docs]}
  const [generalDocuments, setGeneralDocuments] = useState<any[]>([]);

  // Fetch worker data
  const { data: workerData, loading: workerLoading } = useQuery(GET_WORKER_BY_ID, {
    variables: { id: workerId }
  });

  // GraphQL mutations
  const [updateWorker] = useMutation(UPDATE_WORKER_WITH_TRAINING, {
    onCompleted: (data) => {
      toast.success(`Worker "${data.updateWorkerWithTraining.name}" updated successfully!`);
      onSuccess?.(data.updateWorkerWithTraining);
    },
    onError: (error) => {
      console.error('Error updating worker:', error);
      toast.error(`Failed to update worker: ${error.message}`);
    }
  });

  const [deleteWorker] = useMutation(DELETE_WORKER, {
    onCompleted: () => {
      toast.success('Worker deleted successfully!');
      onDelete?.();
    },
    onError: (error) => {
      console.error('Error deleting worker:', error);
      toast.error(`Failed to delete worker: ${error.message}`);
    }
  });

  // GraphQL queries for dropdown data
  const { data: trainingsData, loading: trainingsLoading, error: trainingsError } = useQuery(GET_ALL_TRAININGS, {
    onError: (error) => {
      console.error('Error loading trainings:', error);
      toast.error('Failed to load trainings data');
    }
  });
  const { data: tradesData, loading: tradesLoading, error: tradesError } = useQuery(GET_ALL_TRADES, {
    onError: (error) => {
      console.error('Error loading trades:', error);
      toast.error('Failed to load trades data');
    }
  });
  const { data: skillsData, loading: skillsLoading, error: skillsError } = useQuery(GET_ALL_SKILLS, {
    onError: (error) => {
      console.error('Error loading skills:', error);
      toast.error('Failed to load skills data');
    }
  });

  // Get data from GraphQL
  const trainings = trainingsData?.allTrainings || [];
  const trades = tradesData?.allTrades || [];
  const skills = skillsData?.allSkills || [];

  const isLoadingDropdownData = trainingsLoading || tradesLoading || skillsLoading;
  const hasDropdownErrors = trainingsError || tradesError || skillsError;

  // Helper function to convert DateTime string to date string for form inputs
  const formatDateForInput = (dateTimeString: string): string => {
    if (!dateTimeString) return '';
    // Convert ISO DateTime string to YYYY-MM-DD format for date inputs
    const date = new Date(dateTimeString);
    return date.toISOString().split('T')[0];
  };

  // Populate form with worker data when loaded
  useEffect(() => {
    if (workerData?.workerById?.[0]) {
      const worker = workerData.workerById[0];
      const assigned = worker.trainings?.map((t: any) => t.id) || [];
      setAssignedTrainings(assigned);
      setSelectableTrainings([]); // start with none selected
      setFormData({
        name: worker.name || '',
        company: worker.company || '',
        nationalId: worker.nationalId || '',
        gender: worker.gender || '',
        phoneNumber: worker.phoneNumber || '',
        dateOfBirth: formatDateForInput(worker.dateOfBirth) || '',
        trainingIds: assigned,
        tradeIds: worker.trades?.map((t: any) => t.id) || [],
        skillIds: worker.skills?.map((s: any) => s.id) || [],
        mpesaNumber: worker.mpesaNumber || '',
        email: worker.email || '',
        inductionDate: formatDateForInput(worker.inductionDate) || '',
        medicalCheckDate: formatDateForInput(worker.medicalCheckDate) || '',
      });
      // Initialize trainingDocuments from worker's trainings if available
      const docsMap: Record<number, any[]> = {};
      if (worker.trainings) {
        worker.trainings.forEach((t: any) => {
          docsMap[t.id] = t.documents || [];
        });
      }
      setTrainingDocuments(docsMap);
      setGeneralDocuments(worker.documents || []);
    }
  }, [workerData]);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Required fields validation
    if (!formData.name.trim()) newErrors.name = 'Name is required';
    if (!formData.company.trim()) newErrors.company = 'Company is required';
    if (!formData.nationalId.trim()) newErrors.nationalId = 'National ID is required';
    if (!formData.gender) newErrors.gender = 'Gender is required';
    if (!formData.phoneNumber.trim()) newErrors.phoneNumber = 'Phone number is required';

    // Email validation
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Phone number validation
    if (formData.phoneNumber && !/^\+?[\d\s-()]+$/.test(formData.phoneNumber)) {
      newErrors.phoneNumber = 'Please enter a valid phone number';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Helper function to convert date string to DateTime format
  const formatDateTimeForGraphQL = (dateString: string): string | null => {
    if (!dateString) return null;
    // Convert date string (YYYY-MM-DD) to ISO DateTime string
    const date = new Date(dateString + 'T00:00:00.000Z');
    return date.toISOString();
  };

  // Handlers for training selection and document upload
  const handleTrainingCheckbox = (trainingId: number) => {
    setSelectableTrainings((prev) =>
      prev.includes(trainingId)
        ? prev.filter((id) => id !== trainingId)
        : [...prev, trainingId]
    );
  };
  const handleTrainingDocumentChange = (
    trainingId: number,
    file: File,
    name: string
  ) => {
    if (!file || !name) return;
    setTrainingDocuments((prev) => {
      const docs = prev[trainingId] || [];
      return {
        ...prev,
        [trainingId]: [...docs, { file, name, isPublic: true }],
      };
    });
  };
  const handleRemoveTrainingDocument = (trainingId: number, idx: number) => {
    setTrainingDocuments((prev) => {
      const docs = prev[trainingId] || [];
      return {
        ...prev,
        [trainingId]: docs.filter((_, i) => i !== idx),
      };
    });
  };

  const handleGeneralDocumentChange = (file: File, name: string) => {
    if (!file || !name) return;
    setGeneralDocuments((prev) => [...prev, { file, name, isPublic: true }]);
  };
  const handleRemoveGeneralDocument = (idx: number) => {
    setGeneralDocuments((prev) => prev.filter((_, i) => i !== idx));
  };

  // On submit, combine assigned and newly selected trainings
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      const allTrainingIds = [...assignedTrainings, ...selectableTrainings];
      const variables: UpdateWorkerWithTrainingInput = {
        id: workerId,
        name: formData.name,
        company: formData.company,
        nationalId: formData.nationalId,
        gender: formData.gender,
        phoneNumber: formData.phoneNumber,
        dateOfBirth: formData.dateOfBirth || undefined,
        tradeIds: formData.tradeIds,
        skillIds: formData.skillIds,
        mpesaNumber: formData.mpesaNumber || undefined,
        email: formData.email || undefined,
        inductionDate: formatDateTimeForGraphQL(formData.inductionDate) || undefined,
        medicalCheckDate: formatDateTimeForGraphQL(formData.medicalCheckDate) || undefined,
        documents: generalDocuments.length > 0 ? generalDocuments : undefined,
        trainings: allTrainingIds.map((trainingId) => ({
          trainingId,
          documents: trainingDocuments[trainingId] || []
        }))
      }
      // console.log(variables)
      await updateWorker({
        variables: {
          input: variables
        }
      })
      // await updateWorker({
      //   variables: {
      //     id: workerId,
      //     name: formData.name,
      //     company: formData.company,
      //     nationalId: formData.nationalId,
      //     gender: formData.gender,
      //     phoneNumber: formData.phoneNumber,
      //     dateOfBirth: formData.dateOfBirth || null,
      //     trainingIds: allTrainingIds.length > 0 ? allTrainingIds : null,
      //     tradeIds: formData.tradeIds.length > 0 ? formData.tradeIds : null,
      //     skillIds: formData.skillIds.length > 0 ? formData.skillIds : null,
      //     mpesaNumber: formData.mpesaNumber || null,
      //     email: formData.email || null,
      //     inductionDate: formatDateTimeForGraphQL(formData.inductionDate),
      //     medicalCheckDate: formatDateTimeForGraphQL(formData.medicalCheckDate),
      //     documents: generalDocuments.length > 0 ? generalDocuments : null,
      //   }
      // });
    } catch (error) {
      console.error('Error updating worker:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async () => {
    try {
      await deleteWorker({
        variables: { id: workerId }
      });
    } catch (error) {
      console.error('Error deleting worker:', error);
    }
    setShowDeleteConfirm(false);
  };

  if (workerLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto">
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Error message for dropdown loading failures */}
        {hasDropdownErrors && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">
                  Failed to load dropdown data
                </h3>
                <div className="mt-2 text-sm text-red-700">
                  <p>Unable to load skills, trades, or trainings. Please refresh the page and try again.</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Basic Information */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <User className="h-4 w-4 inline mr-1" />
                Full Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleInputChange('name', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.name ? 'border-red-500' : 'border-gray-300'
                  }`}
                placeholder="Enter full name"
              />
              {errors.name && (
                <p className="text-red-500 text-sm mt-1 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  {errors.name}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Building className="h-4 w-4 inline mr-1" />
                Company *
              </label>
              <input
                type="text"
                value={formData.company}
                onChange={(e) => handleInputChange('company', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.company ? 'border-red-500' : 'border-gray-300'
                  }`}
                placeholder="Enter company name"
              />
              {errors.company && (
                <p className="text-red-500 text-sm mt-1 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  {errors.company}
                </p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                National ID *
              </label>
              <input
                type="text"
                value={formData.nationalId}
                onChange={(e) => handleInputChange('nationalId', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.nationalId ? 'border-red-500' : 'border-gray-300'
                  }`}
                placeholder="Enter national ID"
              />
              {errors.nationalId && (
                <p className="text-red-500 text-sm mt-1 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  {errors.nationalId}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Gender *
              </label>
              <select
                value={formData.gender}
                onChange={(e) => handleInputChange('gender', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.gender ? 'border-red-500' : 'border-gray-300'
                  }`}
              >
                <option value="">Select gender</option>
                <option value="Male">Male</option>
                <option value="Female">Female</option>
              </select>
              {errors.gender && (
                <p className="text-red-500 text-sm mt-1 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  {errors.gender}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Phone className="h-4 w-4 inline mr-1" />
                Phone Number *
              </label>
              <input
                type="tel"
                value={formData.phoneNumber}
                onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.phoneNumber ? 'border-red-500' : 'border-gray-300'
                  }`}
                placeholder="+254 712 345 678"
              />
              {errors.phoneNumber && (
                <p className="text-red-500 text-sm mt-1 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  {errors.phoneNumber}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Mail className="h-4 w-4 inline mr-1" />
                Email Address
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.email ? 'border-red-500' : 'border-gray-300'
                  }`}
                placeholder="<EMAIL>"
              />
              {errors.email && (
                <p className="text-red-500 text-sm mt-1 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  {errors.email}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Phone className="h-4 w-4 inline mr-1" />
                M-Pesa Number
              </label>
              <input
                type="tel"
                value={formData.mpesaNumber}
                onChange={(e) => handleInputChange('mpesaNumber', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.mpesaNumber ? 'border-red-500' : 'border-gray-300'
                  }`}
                placeholder="+254 712 345 678"
              />
              {errors.mpesaNumber && (
                <p className="text-red-500 text-sm mt-1 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  {errors.mpesaNumber}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Date Information */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Date Information</h3>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <Calendar className="h-4 w-4 inline mr-1" />
                Date of Birth
              </label>
              <input
                type="date"
                value={formData.dateOfBirth}
                onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.dateOfBirth ? 'border-red-500' : 'border-gray-300'
                  }`}
              />
              {errors.dateOfBirth && (
                <p className="text-red-500 text-sm mt-1 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  {errors.dateOfBirth}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Induction Date
              </label>
              <input
                type="date"
                value={formData.inductionDate}
                onChange={(e) => handleInputChange('inductionDate', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.inductionDate ? 'border-red-500' : 'border-gray-300'
                  }`}
              />
              {errors.inductionDate && (
                <p className="text-red-500 text-sm mt-1 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  {errors.inductionDate}
                </p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Medical Check Date
              </label>
              <input
                type="date"
                value={formData.medicalCheckDate}
                onChange={(e) => handleInputChange('medicalCheckDate', e.target.value)}
                className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${errors.medicalCheckDate ? 'border-red-500' : 'border-gray-300'
                  }`}
              />
              {errors.medicalCheckDate && (
                <p className="text-red-500 text-sm mt-1 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-1" />
                  {errors.medicalCheckDate}
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Trainings Section (full width) */}
        <div className="bg-white shadow rounded-lg p-6 mt-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Trainings</h3>
          <div className="border border-gray-300 rounded-md p-3 max-h-96 overflow-y-auto">
            {isLoadingDropdownData ? (
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
                <span className="ml-2 text-sm text-gray-500">Loading trainings...</span>
              </div>
            ) : trainingsError ? (
              <div className="text-red-500 text-sm">Failed to load trainings</div>
            ) : trainings.length === 0 ? (
              <div className="text-gray-500 text-sm">No trainings available</div>
            ) : (
              trainings.map((training: { id: number; name: string }) => {
                const isAssigned = assignedTrainings.includes(training.id);
                const isSelected = selectableTrainings.includes(training.id);
                return (
                  <div key={training.id} className="mb-3">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={isAssigned || isSelected}
                        disabled={isAssigned}
                        onChange={() => handleTrainingCheckbox(training.id)}
                        className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                      />
                      <span className={`text-sm${isAssigned ? ' opacity-60' : ''}`}>{training.name}</span>
                    </label>
                    {(isAssigned || isSelected) && (
                      <div className="ml-6 mt-2 space-y-2">
                        <p className="text-sm text-gray-700">
                          Upload documents for {training.name}, if any. E.g certificates
                        </p>
                        {/* List uploaded docs for this training */}
                        {(trainingDocuments[training.id] || []).map((doc, idx) => (
                          <div
                            key={idx}
                            className="flex items-center space-x-2 text-md border border-gray-300 rounded-md p-2 w-fit"
                          >
                            <span className="text-xs text-gray-700 m-2 flex items-center">
                              {doc.file?.name || doc.name}
                            </span>
                            <span className="text-xs text-gray-700 mr-2">
                              {doc.name}
                            </span>
                            <button
                              type="button"
                              onClick={() => handleRemoveTrainingDocument(training.id, idx)}
                              className="text-xs text-red-500"
                            >
                              Remove
                            </button>
                          </div>
                        ))}
                        {/* Upload new doc for this training */}
                        <TrainingDocUploader
                          trainingId={training.id}
                          onAdd={handleTrainingDocumentChange}
                        />
                      </div>
                    )}
                  </div>
                );
              })
            )}
          </div>
        </div>


        {/* Skills Section */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Skills</h3>
          <div className="border border-gray-300 rounded-md p-3 max-h-40 overflow-y-auto">
            {isLoadingDropdownData ? (
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
                <span className="ml-2 text-sm text-gray-500">Loading skills...</span>
              </div>
            ) : skillsError ? (
              <div className="text-red-500 text-sm">Failed to load skills</div>
            ) : skills.length === 0 ? (
              <div className="text-gray-500 text-sm">No skills available</div>
            ) : (
              skills.map((skill: { id: number, name: string }) => (
                <label key={skill.id} className="flex items-center space-x-2 py-1">
                  <input
                    type="checkbox"
                    checked={formData.skillIds.includes(skill.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        handleInputChange('skillIds', [...formData.skillIds, skill.id]);
                      } else {
                        handleInputChange('skillIds', formData.skillIds.filter(id => id !== skill.id));
                      }
                    }}
                    className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                  />
                  <span className="text-sm text-gray-700">{skill.name}</span>
                </label>
              ))
            )}
          </div>
        </div>



        {/* Trades Section */}
        <div className="bg-white shadow rounded-lg p-6 mt-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Trades</h3>
          <div className="border border-gray-300 rounded-md p-3 max-h-40 overflow-y-auto">
            {isLoadingDropdownData ? (
              <div className="flex items-center justify-center py-4">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
                <span className="ml-2 text-sm text-gray-500">Loading trades...</span>
              </div>
            ) : tradesError ? (
              <div className="text-red-500 text-sm">Failed to load trades</div>
            ) : trades.length === 0 ? (
              <div className="text-gray-500 text-sm">No trades available</div>
            ) : (
              trades.map((trade: { id: number, name: string }) => (
                <label key={trade.id} className="flex items-center space-x-2 py-1">
                  <input
                    type="checkbox"
                    checked={formData.tradeIds.includes(trade.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        handleInputChange('tradeIds', [...formData.tradeIds, trade.id]);
                      } else {
                        handleInputChange('tradeIds', formData.tradeIds.filter(id => id !== trade.id));
                      }
                    }}
                    className="rounded border-gray-300 text-green-600 focus:ring-green-500"
                  />
                  <span className="text-sm text-gray-700">{trade.name}</span>
                </label>
              ))
            )}
          </div>
        </div>

        {/* Documents Section (full width) */}
        <div className="bg-white shadow rounded-lg p-6 mt-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Documents</h3>
          <div className="border border-gray-300 rounded-md p-3 max-h-60 overflow-y-auto">
            {/* List uploaded docs */}
            {generalDocuments.map((doc, idx) => (
              <div key={idx} className="flex items-center space-x-2 mb-2 text-md border border-gray-300 rounded-md p-2 w-fit">
                <span className="text-xs text-gray-700 m-2 flex items-center">
                  {doc.file?.name || doc.name}
                </span>
                <span className="text-xs text-gray-700">{doc.name}</span>
                <button
                  type="button"
                  onClick={() => handleRemoveGeneralDocument(idx)}
                  className="text-xs text-red-500"
                >
                  Remove
                </button>
              </div>
            ))}
            {/* Upload new doc */}
            <GeneralDocUploader onAdd={handleGeneralDocumentChange} />
          </div>
        </div>

        {/* Form Actions */}
        <div className="bg-white shadow rounded-lg p-6">
          <div className="flex justify-between items-center">
            <button
              type="button"
              onClick={() => setShowDeleteConfirm(true)}
              className="inline-flex items-center px-4 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Delete Worker
            </button>

            <div className="flex space-x-3">
              <button
                type="button"
                onClick={onCancel}
                className="px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting || isLoadingDropdownData}
                className="px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? 'Updating...' : 'Update Worker'}
              </button>
            </div>
          </div>
        </div>
      </form>

      {/* Delete Confirmation Modal */}
      {showDeleteConfirm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <Trash2 className="h-6 w-6 text-red-600" />
              </div>
              <h3 className="text-lg font-medium text-gray-900 mt-4">Delete Worker</h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500">
                  Are you sure you want to delete this worker? This action cannot be undone.
                </p>
              </div>
              <div className="items-center px-4 py-3">
                <div className="flex space-x-3">
                  <button
                    onClick={() => setShowDeleteConfirm(false)}
                    className="px-4 py-2 bg-gray-500 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleDelete}
                    className="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-full shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300"
                  >
                    Delete
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UpdateWorkerForm;
