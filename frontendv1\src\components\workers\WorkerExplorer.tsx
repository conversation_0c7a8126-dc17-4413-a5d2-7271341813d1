import {
  Award,
  FileText,
  Image,
  Shield,
  File,
  Camera,
  IdCard,
  Briefcase,
  Star,
  Upload,
} from 'lucide-react';
import { Worker } from '../../types';
import { CompanyWorker } from '../../data/workers';
import { ExplorerItem } from '../shared/VSCodeInterface';

// interface WorkerExplorerProps {
//   worker: Worker | CompanyWorker;
// }

const createWorkerExplorerItems = (worker: Worker | CompanyWorker): ExplorerItem[] => {
  // Helper functions for document management
  const getFileIcon = (fileName: string, fileType?: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    const type = fileType?.toLowerCase();

    if (extension === 'pdf' || type?.includes('pdf')) {
      return <FileText className="h-4 w-4 text-red-500" />;
    }
    if (extension === 'doc' || extension === 'docx' || type?.includes('word')) {
      return <FileText className="h-4 w-4 text-blue-500" />;
    }
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(extension || '') || type?.includes('image')) {
      return <Image className="h-4 w-4 text-green-500" />;
    }
    return <File className="h-4 w-4 text-gray-500" />;
  };

  // const getDocumentStatusIcon = (status: string) => {
  //   switch (status) {
  //     case 'valid':
  //       return <CheckCircle className="h-3 w-3 text-green-500" />;
  //     case 'expiring':
  //       return <Clock className="h-3 w-3 text-yellow-500" />;
  //     case 'expired':
  //       return <XCircle className="h-3 w-3 text-red-500" />;
  //     default:
  //       return <AlertTriangle className="h-3 w-3 text-orange-500" />;
  //   }
  // };

  const getDocumentCount = (documents: any[]) => {
    return documents?.length || 0;
  };

  const getCertificateStatusBadge = (certifications: any[]) => {
    const valid = certifications?.filter(c => c.status === 'valid').length || 0;
    const total = certifications?.length || 0;
    return `${valid}/${total}`;
  };

  // Build the document-focused explorer tree structure
  const explorerItems: ExplorerItem[] = [
    // About - Worker Metadata (First item for easy access)
    {
      id: 'worker-about',
      name: 'About',
      type: 'file',
      icon: <IdCard className="h-4 w-4 text-blue-600" />,
      data: { type: 'about', worker }
    },

    // Certificates Folder
    {
      id: 'certificates-folder',
      name: 'Certificates',
      type: 'folder',
      icon: <Award className="h-4 w-4 text-yellow-600" />,
      badge: getCertificateStatusBadge(worker.certifications || []),
      badgeColor: 'bg-yellow-100 text-yellow-800',
      children: [
        // Training Certificates Subfolder
        {
          id: 'training-certificates',
          name: 'Training Certificates',
          type: 'folder',
          icon: <Shield className="h-4 w-4 text-blue-600" />,
          badge: getDocumentCount(worker.certifications?.filter(c => c.name.toLowerCase().includes('training') || c.name.toLowerCase().includes('safety')) || []).toString(),
          badgeColor: 'bg-blue-100 text-blue-800',
          children: [
            // Individual training certificates
            ...(worker.certifications?.filter(cert =>
              cert.name.toLowerCase().includes('training') ||
              cert.name.toLowerCase().includes('safety') ||
              cert.name.toLowerCase().includes('induction')
            ).map(cert => ({
              id: `training-cert-${cert.id}`,
              name: `${cert.name}.pdf`,
              type: 'file' as const,
              icon: getFileIcon(`${cert.name}.pdf`, 'application/pdf'),
              badge: cert.status,
              badgeColor: cert.status === 'valid'
                ? 'bg-green-100 text-green-800'
                : cert.status === 'expiring'
                ? 'bg-yellow-100 text-yellow-800'
                : 'bg-red-100 text-red-800',
              data: {
                type: 'certificate-document',
                certificate: cert,
                fileUrl: `/certificates/${cert.name.toLowerCase().replace(/\s+/g, '-')}.pdf`,
                fileName: `${cert.name}.pdf`
              }
            })) || []),
            // Add upload option for training certificates
            {
              id: 'upload-training-cert',
              name: '+ Upload Training Certificate',
              type: 'file',
              icon: <Upload className="h-4 w-4 text-gray-500" />,
              data: { type: 'upload-document', category: 'training-certificate' }
            }
          ]
        },

        // Academic Qualifications Subfolder
        {
          id: 'academic-qualifications',
          name: 'Academic Qualifications',
          type: 'folder',
          icon: <Award className="h-4 w-4 text-purple-600" />,
          badge: getDocumentCount(worker.certifications?.filter(c => c.name.toLowerCase().includes('diploma') || c.name.toLowerCase().includes('degree') || c.name.toLowerCase().includes('certificate')) || []).toString(),
          badgeColor: 'bg-purple-100 text-purple-800',
          children: [
            // Mock academic qualifications based on worker data
            ...(worker.skills?.slice(0, 2).map((skill) => ({
              id: `academic-${skill.id}`,
              name: `${skill.name} Certificate.pdf`,
              type: 'file' as const,
              icon: getFileIcon(`${skill.name} Certificate.pdf`, 'application/pdf'),
              badge: 'valid',
              badgeColor: 'bg-green-100 text-green-800',
              data: {
                type: 'academic-document',
                skill: skill,
                fileUrl: `/certificates/academic-${skill.name.toLowerCase().replace(/\s+/g, '-')}.pdf`,
                fileName: `${skill.name} Certificate.pdf`
              }
            })) || []),
            {
              id: 'upload-academic-cert',
              name: '+ Upload Academic Certificate',
              type: 'file',
              icon: <Upload className="h-4 w-4 text-gray-500" />,
              data: { type: 'upload-document', category: 'academic-certificate' }
            }
          ]
        },

        // Medical Certificates Subfolder
        {
          id: 'medical-certificates',
          name: 'Medical Certificates',
          type: 'folder',
          icon: <Shield className="h-4 w-4 text-red-600" />,
          badge: worker.medicalCheckDate ? '1' : '0',
          badgeColor: worker.medicalCheckDate ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800',
          children: [
            ...(worker.medicalCheckDate ? [{
              id: 'medical-fitness-cert',
              name: 'Medical Fitness Certificate.pdf',
              type: 'file' as const,
              icon: getFileIcon('Medical Fitness Certificate.pdf', 'application/pdf'),
              badge: 'valid',
              badgeColor: 'bg-green-100 text-green-800',
              data: {
                type: 'medical-document',
                fileUrl: `/certificates/medical-fitness-${worker.id}.pdf`,
                fileName: 'Medical Fitness Certificate.pdf',
                issueDate: worker.medicalCheckDate
              }
            }] : []),
            {
              id: 'upload-medical-cert',
              name: '+ Upload Medical Certificate',
              type: 'file',
              icon: <Upload className="h-4 w-4 text-gray-500" />,
              data: { type: 'upload-document', category: 'medical-certificate' }
            }
          ]
        },

        // Safety Certifications Subfolder
        {
          id: 'safety-certifications',
          name: 'Safety Certifications',
          type: 'folder',
          icon: <Shield className="h-4 w-4 text-orange-600" />,
          badge: getDocumentCount(worker.certifications?.filter(c => c.name.toLowerCase().includes('safety') || c.name.toLowerCase().includes('first aid')) || []).toString(),
          badgeColor: 'bg-orange-100 text-orange-800',
          children: [
            ...(worker.certifications?.filter(cert =>
              cert.name.toLowerCase().includes('safety') ||
              cert.name.toLowerCase().includes('first aid') ||
              cert.name.toLowerCase().includes('heights')
            ).map(cert => ({
              id: `safety-cert-${cert.id}`,
              name: `${cert.name}.pdf`,
              type: 'file' as const,
              icon: getFileIcon(`${cert.name}.pdf`, 'application/pdf'),
              badge: cert.status,
              badgeColor: cert.status === 'valid'
                ? 'bg-green-100 text-green-800'
                : cert.status === 'expiring'
                ? 'bg-yellow-100 text-yellow-800'
                : 'bg-red-100 text-red-800',
              data: {
                type: 'safety-document',
                certificate: cert,
                fileUrl: `/certificates/${cert.name.toLowerCase().replace(/\s+/g, '-')}.pdf`,
                fileName: `${cert.name}.pdf`
              }
            })) || []),
            {
              id: 'upload-safety-cert',
              name: '+ Upload Safety Certificate',
              type: 'file',
              icon: <Upload className="h-4 w-4 text-gray-500" />,
              data: { type: 'upload-document', category: 'safety-certificate' }
            }
          ]
        }
      ]
    },

    // Documents Folder
    {
      id: 'documents-folder',
      name: 'Documents',
      type: 'folder',
      icon: <FileText className="h-4 w-4 text-blue-600" />,
      badge: '3', // Mock count of documents
      badgeColor: 'bg-blue-100 text-blue-800',
      children: [
        // ID Documents Subfolder
        {
          id: 'id-documents',
          name: 'ID Documents',
          type: 'folder',
          icon: <IdCard className="h-4 w-4 text-green-600" />,
          badge: '2',
          badgeColor: 'bg-green-100 text-green-800',
          children: [
            {
              id: 'national-id',
              name: 'National ID.pdf',
              type: 'file',
              icon: getFileIcon('National ID.pdf', 'application/pdf'),
              data: {
                type: 'id-document',
                fileUrl: `/documents/national-id-${worker.id}.pdf`,
                fileName: 'National ID.pdf',
                documentType: 'national_id',
                idNumber: worker.nationalId
              }
            },
            {
              id: 'passport',
              name: 'Passport.pdf',
              type: 'file',
              icon: getFileIcon('Passport.pdf', 'application/pdf'),
              data: {
                type: 'id-document',
                fileUrl: `/documents/passport-${worker.id}.pdf`,
                fileName: 'Passport.pdf',
                documentType: 'passport'
              }
            },
            {
              id: 'upload-id-doc',
              name: '+ Upload ID Document',
              type: 'file',
              icon: <Upload className="h-4 w-4 text-gray-500" />,
              data: { type: 'upload-document', category: 'id-document' }
            }
          ]
        },

        // Employment Contracts Subfolder
        {
          id: 'employment-contracts',
          name: 'Employment Contracts',
          type: 'folder',
          icon: <Briefcase className="h-4 w-4 text-purple-600" />,
          badge: '1',
          badgeColor: 'bg-purple-100 text-purple-800',
          children: [
            {
              id: 'employment-contract',
              name: 'Employment Contract.pdf',
              type: 'file',
              icon: getFileIcon('Employment Contract.pdf', 'application/pdf'),
              data: {
                type: 'contract-document',
                fileUrl: `/documents/contract-${worker.id}.pdf`,
                fileName: 'Employment Contract.pdf',
                contractType: 'employment',
                startDate: (worker as CompanyWorker).hireDate || worker.createdAt
              }
            },
            {
              id: 'upload-contract',
              name: '+ Upload Contract',
              type: 'file',
              icon: <Upload className="h-4 w-4 text-gray-500" />,
              data: { type: 'upload-document', category: 'employment-contract' }
            }
          ]
        },

        // Performance Reviews Subfolder
        {
          id: 'performance-reviews',
          name: 'Performance Reviews',
          type: 'folder',
          icon: <Star className="h-4 w-4 text-orange-600" />,
          badge: worker.rating ? '1' : '0',
          badgeColor: worker.rating ? 'bg-orange-100 text-orange-800' : 'bg-gray-100 text-gray-800',
          children: [
            ...(worker.rating ? [{
              id: 'latest-performance-review',
              name: 'Performance Review 2024.pdf',
              type: 'file' as const,
              icon: getFileIcon('Performance Review 2024.pdf', 'application/pdf'),
              badge: `${worker.rating}/5`,
              badgeColor: worker.rating >= 4 ? 'bg-green-100 text-green-800' :
                         worker.rating >= 3 ? 'bg-yellow-100 text-yellow-800' :
                         'bg-red-100 text-red-800',
              data: {
                type: 'performance-document',
                fileUrl: `/documents/performance-review-${worker.id}-2024.pdf`,
                fileName: 'Performance Review 2024.pdf',
                rating: worker.rating,
                reviewDate: worker.updatedAt || worker.createdAt
              }
            }] : []),
            {
              id: 'upload-performance-review',
              name: '+ Upload Performance Review',
              type: 'file',
              icon: <Upload className="h-4 w-4 text-gray-500" />,
              data: { type: 'upload-document', category: 'performance-review' }
            }
          ]
        }
      ]
    },

    // Photos Folder
    {
      id: 'photos-folder',
      name: 'Photos',
      type: 'folder',
      icon: <Image className="h-4 w-4 text-green-600" />,
      badge: worker.photoUrl ? '3' : '0', // Mock count including profile and site photos
      badgeColor: worker.photoUrl ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800',
      children: [
        // Profile Photos Subfolder
        {
          id: 'profile-photos',
          name: 'Profile Photos',
          type: 'folder',
          icon: <Camera className="h-4 w-4 text-blue-600" />,
          badge: worker.photoUrl ? '1' : '0',
          badgeColor: worker.photoUrl ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800',
          children: [
            ...(worker.photoUrl ? [{
              id: 'current-profile-photo',
              name: 'Profile Photo.jpg',
              type: 'file' as const,
              icon: getFileIcon('Profile Photo.jpg', 'image/jpeg'),
              data: {
                type: 'profile-photo',
                fileUrl: worker.photoUrl,
                fileName: 'Profile Photo.jpg',
                uploadDate: worker.updatedAt || worker.createdAt
              }
            }] : []),
            {
              id: 'upload-profile-photo',
              name: '+ Upload Profile Photo',
              type: 'file',
              icon: <Upload className="h-4 w-4 text-gray-500" />,
              data: { type: 'upload-document', category: 'profile-photo' }
            }
          ]
        },

        // Site Photos Subfolder
        {
          id: 'site-photos',
          name: 'Site Photos',
          type: 'folder',
          icon: <Camera className="h-4 w-4 text-orange-600" />,
          badge: '2', // Mock count of site photos
          badgeColor: 'bg-orange-100 text-orange-800',
          children: [
            {
              id: 'site-photo-1',
              name: 'Site Work Photo 1.jpg',
              type: 'file',
              icon: getFileIcon('Site Work Photo 1.jpg', 'image/jpeg'),
              data: {
                type: 'site-photo',
                fileUrl: `/photos/site-work-${worker.id}-1.jpg`,
                fileName: 'Site Work Photo 1.jpg',
                siteId: (worker as CompanyWorker).currentSiteId || 'site1',
                captureDate: worker.updatedAt || worker.createdAt
              }
            },
            {
              id: 'site-photo-2',
              name: 'Site Work Photo 2.jpg',
              type: 'file',
              icon: getFileIcon('Site Work Photo 2.jpg', 'image/jpeg'),
              data: {
                type: 'site-photo',
                fileUrl: `/photos/site-work-${worker.id}-2.jpg`,
                fileName: 'Site Work Photo 2.jpg',
                siteId: (worker as CompanyWorker).currentSiteId || 'site1',
                captureDate: worker.updatedAt || worker.createdAt
              }
            },
            {
              id: 'upload-site-photo',
              name: '+ Upload Site Photo',
              type: 'file',
              icon: <Upload className="h-4 w-4 text-gray-500" />,
              data: { type: 'upload-document', category: 'site-photo' }
            }
          ]
        },

        // Equipment Photos Subfolder
        {
          id: 'equipment-photos',
          name: 'Equipment Photos',
          type: 'folder',
          icon: <Camera className="h-4 w-4 text-purple-600" />,
          badge: '0',
          badgeColor: 'bg-gray-100 text-gray-800',
          children: [
            {
              id: 'upload-equipment-photo',
              name: '+ Upload Equipment Photo',
              type: 'file',
              icon: <Upload className="h-4 w-4 text-gray-500" />,
              data: { type: 'upload-document', category: 'equipment-photo' }
            }
          ]
        }
      ]
    }
  ];

  return explorerItems;
};

export default createWorkerExplorerItems;
