import React from "react";
import { <PERSON><PERSON>ircle, AlertTriangle, XCircle, MapPin, Clock } from "lucide-react";
import { CompanyWorker, TrainingComplianceStatus } from "../../data/workers";

type Props = {
  worker: CompanyWorker;
  onEdit?: () => void;
  onDelete?: () => void;
};

const complianceColor = (status?: TrainingComplianceStatus) => {
  switch (status) {
    case "compliant":
      return "bg-green-100 text-green-800";
    case "pending_training":
      return "bg-yellow-100 text-yellow-800";
    case "non_compliant":
      return "bg-red-100 text-red-800";
    case "expired":
      return "bg-red-100 text-red-900";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const complianceIcon = (status?: TrainingComplianceStatus) => {
  switch (status) {
    case "compliant":
      return <CheckCircle className="w-4 h-4 text-green-600" />;
    case "pending_training":
      return <Clock className="w-4 h-4 text-yellow-600" />;
    case "non_compliant":
      return <AlertTriangle className="w-4 h-4 text-red-600" />;
    case "expired":
      return <XCircle className="w-4 h-4 text-red-700" />;
    default:
      return <AlertTriangle className="w-4 h-4 text-gray-400" />;
  }
};

const WorkerHeader: React.FC<Props> = ({ worker, onEdit, onDelete }) => {
  const primaryTrade = worker.trades && worker.trades.length > 0 ? worker.trades[0].name : undefined;
  const currentRole = worker.siteAssignments && worker.siteAssignments.length > 0 ? worker.siteAssignments[0].role : undefined;

  return (
    <div className="flex items-center justify-between gap-4">
      <div className="flex items-center gap-4">
        <div className="h-16 w-16 rounded-full bg-blue-50 border border-blue-200 flex items-center justify-center overflow-hidden">
        {worker.photoUrl ? (
          <img src={worker.photoUrl} alt={worker.name} className="h-full w-full object-cover" />
        ) : (
          <div className="text-blue-700 text-sm font-semibold flex items-center justify-center h-full w-full">
            {worker.name?.slice(0, 2).toUpperCase() || "WK"}
          </div>
        )}
        </div>
        <div className="flex-1">
        <div className="flex flex-wrap items-center gap-2">
          <h1 className="text-xl font-semibold text-gray-900 mr-2">{worker.name}</h1>
          <span className="px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
            {worker.employeeNumber}
          </span>
          {primaryTrade && (
            <span className="px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              {primaryTrade}
            </span>
          )}
          {currentRole && (
            <span className="px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
              {currentRole}
            </span>
          )}
          <span className={`inline-flex items-center gap-1 px-2.5 py-0.5 rounded-full text-xs font-medium ${complianceColor(
            worker.complianceStatus
          )}`}>
            {complianceIcon(worker.complianceStatus)}
            <span>
              {worker.complianceStatus
                ? worker.complianceStatus.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
                : "Unknown"}
            </span>
          </span>
        </div>
        <div className="text-sm text-gray-500 mt-1 flex items-center gap-2">
          {worker.currentSiteId && (
            <span className="inline-flex items-center gap-1"><MapPin className="w-3 h-3" /> {worker.currentSiteId}</span>
          )}
          {worker.company && <span>• {worker.company}</span>}
        </div>
        </div>
      </div>
      <div className="flex items-center gap-2">
        {onEdit && (
          <button
            onClick={onEdit}
            className="inline-flex items-center px-4 py-2 border border-blue-600 rounded-md shadow-sm text-sm font-medium text-blue-600 bg-white hover:bg-blue-50"
          >
            Edit
          </button>
        )}
        {onDelete && (
          <button
            onClick={onDelete}
            className="inline-flex items-center px-4 py-2 border border-red-600 rounded-md shadow-sm text-sm font-medium text-red-600 bg-white hover:bg-red-50"
          >
            Remove
          </button>
        )}
      </div>
    </div>
  );
};

export default WorkerHeader;
