import React, { useEffect, useState } from 'react';
import { User, Building, Phone, Briefcase } from 'lucide-react';
import { EnhancedWorkerCreationInput } from '../../../types/credentials';

interface BasicInfoStepProps {
  formData: EnhancedWorkerCreationInput;
  updateFormData: (updates: Partial<EnhancedWorkerCreationInput>) => void;
  onNext: () => void;
  onPrevious: () => void;
  isValid: boolean;
  onValidationChange: (isValid: boolean) => void;
}

// Mock data for trades and skills (replace with actual data)
const mockTrades = [
  { id: 1, name: 'Electrician' },
  { id: 2, name: 'Plumber' },
  { id: 3, name: '<PERSON>' },
  { id: 4, name: 'Welder' },
  { id: 5, name: '<PERSON>' },
  { id: 6, name: 'Painter' },
];

const mockSkills = [
  { id: 1, name: 'Blueprint Reading' },
  { id: 2, name: 'Safety Protocols' },
  { id: 3, name: 'Equipment Operation' },
  { id: 4, name: 'Quality Control' },
  { id: 5, name: 'Team Leadership' },
  { id: 6, name: 'Problem Solving' },
];

export const BasicInfoStep: React.FC<BasicInfoStepProps> = ({
  formData,
  updateFormData,
  onValidationChange
}) => {
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Validation logic
  useEffect(() => {
    const newErrors: Record<string, string> = {};

    if (!formData.name?.trim()) {
      newErrors.name = 'Full name is required';
    }

    if (!formData.company?.trim()) {
      newErrors.company = 'Company name is required';
    }

    if (!formData.nationalId?.trim()) {
      newErrors.nationalId = 'National ID is required';
    } else if (formData.nationalId.length < 8) {
      newErrors.nationalId = 'National ID must be at least 8 characters';
    }

    if (!formData.phoneNumber?.trim()) {
      newErrors.phoneNumber = 'Phone number is required';
    } else if (!/^\+?[\d\s-()]+$/.test(formData.phoneNumber)) {
      newErrors.phoneNumber = 'Please enter a valid phone number';
    }

    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    if (formData.dateOfBirth) {
      const birthDate = new Date(formData.dateOfBirth);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      
      if (age < 16 || age > 80) {
        newErrors.dateOfBirth = 'Worker must be between 16 and 80 years old';
      }
    }

    setErrors(newErrors);
    onValidationChange(Object.keys(newErrors).length === 0);
  }, [formData, onValidationChange]);

  const handleInputChange = (field: keyof EnhancedWorkerCreationInput, value: any) => {
    updateFormData({ [field]: value });
  };

  const handleTradeChange = (tradeId: number, checked: boolean) => {
    const currentTrades = formData.tradeIds || [];
    const newTrades = checked
      ? [...currentTrades, tradeId]
      : currentTrades.filter(id => id !== tradeId);
    
    updateFormData({ tradeIds: newTrades });
  };

  const handleSkillChange = (skillId: number, checked: boolean) => {
    const currentSkills = formData.skillIds || [];
    const newSkills = checked
      ? [...currentSkills, skillId]
      : currentSkills.filter(id => id !== skillId);
    
    updateFormData({ skillIds: newSkills });
  };

  return (
    <div className="space-y-8">
      {/* Step header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Basic Information</h2>
        <p className="text-gray-600">
          Enter the worker's personal and employment details. Fields marked with * are required.
        </p>
      </div>

      {/* Personal Information */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <User className="h-5 w-5 mr-2" />
          Personal Information
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Full Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                errors.name ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Enter full name"
            />
            {errors.name && (
              <p className="text-red-500 text-sm mt-1">{errors.name}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              National ID *
            </label>
            <input
              type="text"
              value={formData.nationalId}
              onChange={(e) => handleInputChange('nationalId', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                errors.nationalId ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Enter national ID"
            />
            {errors.nationalId && (
              <p className="text-red-500 text-sm mt-1">{errors.nationalId}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Gender *
            </label>
            <select
              value={formData.gender}
              onChange={(e) => handleInputChange('gender', e.target.value as 'MALE' | 'FEMALE')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
            >
              <option value="MALE">Male</option>
              <option value="FEMALE">Female</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Date of Birth
            </label>
            <input
              type="date"
              value={formData.dateOfBirth || ''}
              onChange={(e) => handleInputChange('dateOfBirth', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                errors.dateOfBirth ? 'border-red-500' : 'border-gray-300'
              }`}
            />
            {errors.dateOfBirth && (
              <p className="text-red-500 text-sm mt-1">{errors.dateOfBirth}</p>
            )}
          </div>
        </div>
      </div>

      {/* Contact Information */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Phone className="h-5 w-5 mr-2" />
          Contact Information
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Phone Number *
            </label>
            <input
              type="tel"
              value={formData.phoneNumber}
              onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                errors.phoneNumber ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="+254 712 345 678"
            />
            {errors.phoneNumber && (
              <p className="text-red-500 text-sm mt-1">{errors.phoneNumber}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Email Address
            </label>
            <input
              type="email"
              value={formData.email || ''}
              onChange={(e) => handleInputChange('email', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                errors.email ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="<EMAIL>"
            />
            {errors.email && (
              <p className="text-red-500 text-sm mt-1">{errors.email}</p>
            )}
          </div>
        </div>
      </div>

      {/* Employment Information */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Building className="h-5 w-5 mr-2" />
          Employment Information
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Company *
            </label>
            <input
              type="text"
              value={formData.company}
              onChange={(e) => handleInputChange('company', e.target.value)}
              className={`w-full px-3 py-2 border rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500 ${
                errors.company ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Enter company name"
            />
            {errors.company && (
              <p className="text-red-500 text-sm mt-1">{errors.company}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Employee Number
            </label>
            <input
              type="text"
              value={formData.employeeNumber || ''}
              onChange={(e) => handleInputChange('employeeNumber', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
              placeholder="EMP001"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Hire Date
            </label>
            <input
              type="date"
              value={formData.hireDate || ''}
              onChange={(e) => handleInputChange('hireDate', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
            />
          </div>
        </div>
      </div>

      {/* Trades and Skills */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Briefcase className="h-5 w-5 mr-2" />
          Trades and Skills
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Trades */}
          <div>
            <h4 className="text-md font-medium text-gray-800 mb-3">Primary Trades</h4>
            <div className="space-y-2">
              {mockTrades.map((trade) => (
                <label key={trade.id} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.tradeIds?.includes(trade.id) || false}
                    onChange={(e) => handleTradeChange(trade.id, e.target.checked)}
                    className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">{trade.name}</span>
                </label>
              ))}
            </div>
          </div>

          {/* Skills */}
          <div>
            <h4 className="text-md font-medium text-gray-800 mb-3">Skills</h4>
            <div className="space-y-2">
              {mockSkills.map((skill) => (
                <label key={skill.id} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.skillIds?.includes(skill.id) || false}
                    onChange={(e) => handleSkillChange(skill.id, e.target.checked)}
                    className="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                  />
                  <span className="ml-2 text-sm text-gray-700">{skill.name}</span>
                </label>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
