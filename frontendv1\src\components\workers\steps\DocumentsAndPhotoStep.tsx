import React, { useState, useRef } from 'react';
import { Camera, Upload, FileText, Trash2, AlertCircle, User } from 'lucide-react';
import { EnhancedWorkerCreationInput } from '../../../types/credentials';
import { toast } from 'react-toastify';

interface DocumentsAndPhotoStepProps {
  formData: EnhancedWorkerCreationInput;
  updateFormData: (updates: Partial<EnhancedWorkerCreationInput>) => void;
  onNext: () => void;
  onPrevious: () => void;
  isValid: boolean;
  onValidationChange: (isValid: boolean) => void;
}

interface AdditionalDocument {
  id: string;
  name: string;
  file: File;
  description?: string;
}

export const DocumentsAndPhotoStep: React.FC<DocumentsAndPhotoStepProps> = ({
  formData,
  updateFormData,
  onValidationChange
}) => {
  const [photoPreview, setPhotoPreview] = useState<string | null>(null);
  const [signaturePreview, setSignaturePreview] = useState<string | null>(null);
  const [additionalDocuments, setAdditionalDocuments] = useState<AdditionalDocument[]>([]);
  const [showAddDocumentForm, setShowAddDocumentForm] = useState(false);
  const [newDocumentName, setNewDocumentName] = useState('');
  const [newDocumentDescription, setNewDocumentDescription] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});

  const photoInputRef = useRef<HTMLInputElement>(null);
  const signatureInputRef = useRef<HTMLInputElement>(null);
  const documentInputRef = useRef<HTMLInputElement>(null);

  // Validate step
  React.useEffect(() => {
    const isStepValid = !!formData.profilePicture;
    onValidationChange(isStepValid);

    if (!isStepValid) {
      setErrors(prev => ({ ...prev, profilePicture: 'Profile picture is required' }));
    } else {
      setErrors(prev => ({ ...prev, profilePicture: '' }));
    }
  }, [formData.profilePicture, onValidationChange]);

  // Handle profile picture upload
  const handlePhotoUpload = (file: File) => {
    if (!file.type.startsWith('image/')) {
      toast.error('Please upload an image file');
      return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      toast.error('Image size must be less than 5MB');
      return;
    }

    updateFormData({ profilePicture: file });

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setPhotoPreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    // Clear error
    setErrors(prev => ({ ...prev, profilePicture: '' }));
    toast.success('Profile picture uploaded successfully');
  };

  // Handle signature upload
  const handleSignatureUpload = (file: File) => {
    if (!file.type.startsWith('image/')) {
      toast.error('Please upload an image file');
      return;
    }

    if (file.size > 2 * 1024 * 1024) { // 2MB limit
      toast.error('Signature size must be less than 2MB');
      return;
    }

    updateFormData({ signature: file });

    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => {
      setSignaturePreview(e.target?.result as string);
    };
    reader.readAsDataURL(file);

    toast.success('Signature uploaded successfully');
  };

  // Handle additional document upload
  const handleDocumentUpload = (file: File) => {
    if (!newDocumentName.trim()) {
      toast.error('Please enter a document name');
      return;
    }

    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      toast.error('Document size must be less than 10MB');
      return;
    }

    const newDocument: AdditionalDocument = {
      id: `doc_${Date.now()}`,
      name: newDocumentName.trim(),
      file,
      description: newDocumentDescription.trim() || undefined
    };

    setAdditionalDocuments(prev => [...prev, newDocument]);

    // Reset form
    setNewDocumentName('');
    setNewDocumentDescription('');
    setShowAddDocumentForm(false);

    if (documentInputRef.current) {
      documentInputRef.current.value = '';
    }

    toast.success('Document added successfully');
  };

  // Remove additional document
  const removeDocument = (documentId: string) => {
    setAdditionalDocuments(prev => prev.filter(doc => doc.id !== documentId));
    toast.success('Document removed');
  };

  // Remove profile picture
  const removePhoto = () => {
    updateFormData({ profilePicture: undefined });
    setPhotoPreview(null);
    if (photoInputRef.current) {
      photoInputRef.current.value = '';
    }
  };

  // Remove signature
  const removeSignature = () => {
    updateFormData({ signature: undefined });
    setSignaturePreview(null);
    if (signatureInputRef.current) {
      signatureInputRef.current.value = '';
    }
  };

  return (
    <div className="space-y-8">
      {/* Step header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Documents & Photo</h2>
        <p className="text-gray-600">
          Upload the worker's profile picture and any additional documents. The profile picture is required.
        </p>
      </div>

      {/* Profile Picture */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <Camera className="h-5 w-5 mr-2" />
          Profile Picture *
        </h3>

        <div className="flex flex-col md:flex-row gap-6">
          {/* Upload area */}
          <div className="flex-1">
            <div className={`border-2 border-dashed rounded-lg p-6 text-center ${errors.profilePicture ? 'border-red-300 bg-red-50' : 'border-gray-300'
              }`}>
              <input
                ref={photoInputRef}
                type="file"
                accept="image/*"
                onChange={(e) => e.target.files?.[0] && handlePhotoUpload(e.target.files[0])}
                className="hidden"
                id="profile-picture-upload"
              />
              <label
                htmlFor="profile-picture-upload"
                className="cursor-pointer flex flex-col items-center"
              >
                <Camera className="h-12 w-12 text-gray-400 mb-4" />
                <span className="text-lg font-medium text-gray-700 mb-2">
                  Upload Profile Picture
                </span>
                <span className="text-sm text-gray-500">
                  Click to browse or drag and drop
                </span>
                <span className="text-xs text-gray-400 mt-1">
                  JPEG, PNG, WebP up to 5MB
                </span>
              </label>
            </div>
            {errors.profilePicture && (
              <p className="text-red-500 text-sm mt-2 flex items-center">
                <AlertCircle className="h-4 w-4 mr-1" />
                {errors.profilePicture}
              </p>
            )}
          </div>

          {/* Preview */}
          {(photoPreview || formData.profilePicture) && (
            <div className="flex-shrink-0">
              <div className="relative">
                <div className="w-32 h-32 bg-gray-200 rounded-lg overflow-hidden">
                  {photoPreview ? (
                    <img
                      src={photoPreview}
                      alt="Profile preview"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <User className="h-12 w-12 text-gray-400" />
                    </div>
                  )}
                </div>
                <button
                  onClick={removePhoto}
                  className="absolute -top-2 -right-2 p-1 bg-red-600 text-white rounded-full hover:bg-red-700"
                  title="Remove photo"
                >
                  <Trash2 className="h-3 w-3" />
                </button>
              </div>
              <p className="text-xs text-gray-500 mt-2 text-center">
                {formData.profilePicture?.name}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Digital Signature */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <FileText className="h-5 w-5 mr-2" />
          Digital Signature (Optional)
        </h3>

        <div className="flex flex-col md:flex-row gap-6">
          {/* Upload area */}
          <div className="flex-1">
            <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
              <input
                ref={signatureInputRef}
                type="file"
                accept="image/*"
                onChange={(e) => e.target.files?.[0] && handleSignatureUpload(e.target.files[0])}
                className="hidden"
                id="signature-upload"
              />
              <label
                htmlFor="signature-upload"
                className="cursor-pointer flex flex-col items-center"
              >
                <Upload className="h-8 w-8 text-gray-400 mb-2" />
                <span className="text-sm font-medium text-gray-700 mb-1">
                  Upload Digital Signature
                </span>
                <span className="text-xs text-gray-500">
                  JPEG, PNG, WebP up to 2MB
                </span>
              </label>
            </div>
          </div>

          {/* Preview */}
          {(signaturePreview || formData.signature) && (
            <div className="flex-shrink-0">
              <div className="relative">
                <div className="w-32 h-16 bg-white border border-gray-200 rounded-lg overflow-hidden">
                  {signaturePreview ? (
                    <img
                      src={signaturePreview}
                      alt="Signature preview"
                      className="w-full h-full object-contain"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <FileText className="h-6 w-6 text-gray-400" />
                    </div>
                  )}
                </div>
                <button
                  onClick={removeSignature}
                  className="absolute -top-2 -right-2 p-1 bg-red-600 text-white rounded-full hover:bg-red-700"
                  title="Remove signature"
                >
                  <Trash2 className="h-3 w-3" />
                </button>
              </div>
              <p className="text-xs text-gray-500 mt-2 text-center">
                {formData.signature?.name}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Additional Documents */}
      <div className="bg-gray-50 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
          <FileText className="h-5 w-5 mr-2" />
          Additional Documents (Optional)
        </h3>

        {/* Existing documents */}
        {additionalDocuments.length > 0 && (
          <div className="mb-6 space-y-3">
            {additionalDocuments.map((doc) => (
              <div key={doc.id} className="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-md">
                <div className="flex items-center space-x-3">
                  <FileText className="h-5 w-5 text-gray-400" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">{doc.name}</p>
                    {doc.description && (
                      <p className="text-xs text-gray-500">{doc.description}</p>
                    )}
                    <p className="text-xs text-gray-400">{doc.file.name}</p>
                  </div>
                </div>
                <button
                  onClick={() => removeDocument(doc.id)}
                  className="p-1 text-red-600 hover:bg-red-50 rounded"
                  title="Remove document"
                >
                  <Trash2 className="h-4 w-4" />
                </button>
              </div>
            ))}
          </div>
        )}

        {/* Add document form */}
        {!showAddDocumentForm ? (
          <button
            onClick={() => setShowAddDocumentForm(true)}
            className="w-full flex items-center justify-center px-4 py-3 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-green-500 hover:text-green-600 transition-colors"
          >
            <Upload className="h-5 w-5 mr-2" />
            Add Additional Document
          </button>
        ) : (
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-md font-medium text-gray-900">Add Document</h4>
              <button
                onClick={() => {
                  setShowAddDocumentForm(false);
                  setNewDocumentName('');
                  setNewDocumentDescription('');
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                ×
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Document Name *
                </label>
                <input
                  type="text"
                  value={newDocumentName}
                  onChange={(e) => setNewDocumentName(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="e.g., Medical Certificate"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description (Optional)
                </label>
                <input
                  type="text"
                  value={newDocumentDescription}
                  onChange={(e) => setNewDocumentDescription(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
                  placeholder="Brief description of the document"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Select File *
                </label>
                <input
                  ref={documentInputRef}
                  type="file"
                  accept=".pdf,.doc,.docx,.jpg,.jpeg,.png,.webp"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) {
                      handleDocumentUpload(file);
                    }
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
                />
                <p className="text-xs text-gray-500 mt-1">
                  PDF, DOC, DOCX, JPEG, PNG, WebP up to 10MB
                </p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Info box */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex">
          <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 mr-3" />
          <div className="text-sm text-blue-700">
            <p className="font-medium mb-1">Document Guidelines</p>
            <ul className="space-y-1">
              <li>• Profile picture is required for worker identification</li>
              <li>• Digital signature can be used for document signing workflows</li>
              <li>• Additional documents can include medical certificates, ID copies, etc.</li>
              <li>• All documents are securely stored and encrypted</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};
