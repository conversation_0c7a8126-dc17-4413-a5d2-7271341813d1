import React from 'react';
import {
  CheckCircle,
  User,
  GraduationCap,
  Shield,
  Camera,
  AlertTriangle
} from 'lucide-react';
import { EnhancedWorkerCreationInput } from '../../../types/credentials';
import { calculateDaysUntilExpiry, getCredentialSummary } from '../../../utils/credentialUtils';

interface ReviewAndSubmitStepProps {
  formData: EnhancedWorkerCreationInput;
  updateFormData: (updates: Partial<EnhancedWorkerCreationInput>) => void;
  onNext: () => void;
  onPrevious: () => void;
  isValid: boolean;
  onValidationChange: (isValid: boolean) => void;
}

export const ReviewAndSubmitStep: React.FC<ReviewAndSubmitStepProps> = ({
  formData
}) => {
  // Mock data for trades and skills (should match BasicInfoStep)
  const mockTrades = [
    { id: 1, name: 'Electrician' },
    { id: 2, name: '<PERSON>lumber' },
    { id: 3, name: '<PERSON>' },
    { id: 4, name: 'Welder' },
    { id: 5, name: '<PERSON>' },
    { id: 6, name: '<PERSON>' },
  ];

  const mockSkills = [
    { id: 1, name: 'Blueprint Reading' },
    { id: 2, name: 'Safety Protocols' },
    { id: 3, name: 'Equipment Operation' },
    { id: 4, name: 'Quality Control' },
    { id: 5, name: 'Team Leadership' },
    { id: 6, name: 'Problem Solving' },
  ];

  // Get selected trades and skills
  const selectedTrades = mockTrades.filter(trade => formData.tradeIds?.includes(trade.id));
  const selectedSkills = mockSkills.filter(skill => formData.skillIds?.includes(skill.id));

  // Get credential summaries
  const allCredentials = [...(formData.permanentCredentials || []), ...(formData.temporaryCredentials || [])];
  const credentialSummary = getCredentialSummary(allCredentials);

  // Validation removed as onValidationChange parameter not available

  // Get status badge for temporary credentials
  const getCredentialStatusBadge = (expiryDate: string) => {
    const daysUntilExpiry = calculateDaysUntilExpiry(expiryDate);
    
    if (daysUntilExpiry < 0) {
      return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">Expired</span>;
    } else if (daysUntilExpiry <= 30) {
      return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">Expiring Soon</span>;
    } else {
      return <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">Valid</span>;
    }
  };

  return (
    <div className="space-y-8">
      {/* Step header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Review & Submit</h2>
        <p className="text-gray-600">
          Please review all the information below before creating the worker profile. 
          You can go back to any previous step to make changes.
        </p>
      </div>

      {/* Basic Information Summary */}
      <div className="bg-white border border-gray-200 rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <User className="h-5 w-5 mr-2" />
            Basic Information
          </h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-3">
              <div>
                <span className="text-sm font-medium text-gray-500">Full Name</span>
                <p className="text-gray-900">{formData.name}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">National ID</span>
                <p className="text-gray-900">{formData.nationalId}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Gender</span>
                <p className="text-gray-900">{formData.gender}</p>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Phone Number</span>
                <p className="text-gray-900">{formData.phoneNumber}</p>
              </div>
            </div>
            <div className="space-y-3">
              <div>
                <span className="text-sm font-medium text-gray-500">Company</span>
                <p className="text-gray-900">{formData.company}</p>
              </div>
              {formData.email && (
                <div>
                  <span className="text-sm font-medium text-gray-500">Email</span>
                  <p className="text-gray-900">{formData.email}</p>
                </div>
              )}
              {formData.dateOfBirth && (
                <div>
                  <span className="text-sm font-medium text-gray-500">Date of Birth</span>
                  <p className="text-gray-900">{new Date(formData.dateOfBirth).toLocaleDateString()}</p>
                </div>
              )}
              {formData.employeeNumber && (
                <div>
                  <span className="text-sm font-medium text-gray-500">Employee Number</span>
                  <p className="text-gray-900">{formData.employeeNumber}</p>
                </div>
              )}
            </div>
          </div>

          {/* Trades and Skills */}
          <div className="mt-6 pt-6 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <span className="text-sm font-medium text-gray-500 block mb-2">Primary Trades</span>
                {selectedTrades.length > 0 ? (
                  <div className="flex flex-wrap gap-2">
                    {selectedTrades.map(trade => (
                      <span key={trade.id} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {trade.name}
                      </span>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-400 text-sm">No trades selected</p>
                )}
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500 block mb-2">Skills</span>
                {selectedSkills.length > 0 ? (
                  <div className="flex flex-wrap gap-2">
                    {selectedSkills.map(skill => (
                      <span key={skill.id} className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                        {skill.name}
                      </span>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-400 text-sm">No skills selected</p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Credentials Summary */}
      <div className="bg-white border border-gray-200 rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <GraduationCap className="h-5 w-5 mr-2" />
            Credentials Summary
          </h3>
        </div>
        <div className="p-6">
          {/* Summary stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="text-center p-3 bg-gray-50 rounded-lg">
              <div className="text-2xl font-bold text-gray-900">{credentialSummary.total}</div>
              <div className="text-sm text-gray-500">Total Credentials</div>
            </div>
            <div className="text-center p-3 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{credentialSummary.permanent}</div>
              <div className="text-sm text-gray-500">Permanent</div>
            </div>
            <div className="text-center p-3 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">{credentialSummary.temporary}</div>
              <div className="text-sm text-gray-500">Temporary</div>
            </div>
            <div className="text-center p-3 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{credentialSummary.valid}</div>
              <div className="text-sm text-gray-500">Valid</div>
            </div>
          </div>

          {/* Permanent Credentials */}
          {formData.permanentCredentials && formData.permanentCredentials.length > 0 && (
            <div className="mb-6">
              <h4 className="text-md font-medium text-gray-900 mb-3 flex items-center">
                <GraduationCap className="h-4 w-4 mr-2" />
                Permanent Credentials ({formData.permanentCredentials.length})
              </h4>
              <div className="space-y-2">
                {formData.permanentCredentials.map(credential => (
                  <div key={credential.id} className="flex items-center justify-between p-3 bg-blue-50 rounded-md">
                    <div>
                      <p className="text-sm font-medium text-gray-900">{credential.name}</p>
                      <p className="text-xs text-gray-500">{credential.institution} • {new Date(credential.issueDate).toLocaleDateString()}</p>
                    </div>
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {credential.category.replace('_', ' ')}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Temporary Credentials */}
          {formData.temporaryCredentials && formData.temporaryCredentials.length > 0 && (
            <div className="mb-6">
              <h4 className="text-md font-medium text-gray-900 mb-3 flex items-center">
                <Shield className="h-4 w-4 mr-2" />
                Temporary Credentials ({formData.temporaryCredentials.length})
              </h4>
              <div className="space-y-2">
                {formData.temporaryCredentials.map(credential => (
                  <div key={credential.id} className="flex items-center justify-between p-3 bg-orange-50 rounded-md">
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">{credential.name}</p>
                      <p className="text-xs text-gray-500">
                        {credential.trainingProvider} • Expires: {new Date(credential.expiryDate).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                        {credential.category.replace('_', ' ')}
                      </span>
                      {getCredentialStatusBadge(credential.expiryDate)}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* No credentials message */}
          {credentialSummary.total === 0 && (
            <div className="text-center py-6">
              <GraduationCap className="h-12 w-12 text-gray-300 mx-auto mb-3" />
              <p className="text-gray-500">No credentials added yet</p>
              <p className="text-sm text-gray-400">You can add credentials later from the worker profile</p>
            </div>
          )}
        </div>
      </div>

      {/* Documents Summary */}
      <div className="bg-white border border-gray-200 rounded-lg">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 flex items-center">
            <Camera className="h-5 w-5 mr-2" />
            Documents & Photo
          </h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <span className="text-sm font-medium text-gray-500 block mb-2">Profile Picture</span>
              {formData.profilePicture ? (
                <div className="flex items-center text-sm text-green-600">
                  <CheckCircle className="h-4 w-4 mr-1" />
                  {formData.profilePicture.name}
                </div>
              ) : (
                <div className="flex items-center text-sm text-red-600">
                  <AlertTriangle className="h-4 w-4 mr-1" />
                  Required - Please upload a profile picture
                </div>
              )}
            </div>
            <div>
              <span className="text-sm font-medium text-gray-500 block mb-2">Digital Signature</span>
              {formData.signature ? (
                <div className="flex items-center text-sm text-green-600">
                  <CheckCircle className="h-4 w-4 mr-1" />
                  {formData.signature.name}
                </div>
              ) : (
                <p className="text-gray-400 text-sm">Not provided</p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Warnings and Alerts */}
      {credentialSummary.expiring > 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div className="flex">
            <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5 mr-3" />
            <div className="text-sm text-yellow-700">
              <p className="font-medium mb-1">Expiring Credentials Alert</p>
              <p>
                This worker has {credentialSummary.expiring} credential(s) that will expire within 30 days. 
                Consider scheduling renewal training before site assignment.
              </p>
            </div>
          </div>
        </div>
      )}

      {credentialSummary.expired > 0 && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex">
            <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5 mr-3" />
            <div className="text-sm text-red-700">
              <p className="font-medium mb-1">Expired Credentials Alert</p>
              <p>
                This worker has {credentialSummary.expired} expired credential(s). 
                These credentials must be renewed before the worker can be assigned to sites.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Final confirmation */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-4">
        <div className="flex">
          <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
          <div className="text-sm text-green-700">
            <p className="font-medium mb-1">Ready to Create Worker</p>
            <p>
              All required information has been provided. Click "Create Worker" to add this worker to your workforce management system.
              The worker profile will be created with all credentials and documents properly organized.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
