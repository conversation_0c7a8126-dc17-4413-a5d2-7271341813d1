import { Worker, Training, Trade, Skill, TrainingStatus, WorkerTrainingHistory, ToolboxSession, WorkerCertification } from '../types';

// Mock Trades
export const mockTrades: Trade[] = [
  {
    id: 1,
    tenantId: 'tenant-1',
    name: '<PERSON>',
    description: 'Wood construction specialist',
    requiredCertifications: ['Basic Carpentry', 'Safety Training'],
    createdAt: '2025-01-01T00:00:00Z',
    createdBy: 'System',
    updatedAt: '2025-01-15T10:30:00Z',
    updatedBy: 'Admin'
  },
  {
    id: 2,
    tenantId: 'tenant-1',
    name: 'Electrician',
    description: 'Electrical systems specialist',
    requiredCertifications: ['Electrical License', 'Safety Training', 'Working at Heights'],
    createdAt: '2025-01-01T00:00:00Z',
    createdBy: 'System',
    updatedAt: '2025-01-15T10:30:00Z',
    updatedBy: 'Admin'
  },
  {
    id: 3,
    tenantId: 'tenant-1',
    name: 'Plumber',
    description: 'Plumbing and water systems specialist',
    requiredCertifications: ['Plumbing License', 'Safety Training'],
    createdAt: '2025-01-01T00:00:00Z',
    createdBy: 'System',
    updatedAt: '2025-01-15T10:30:00Z',
    updatedBy: 'Admin'
  },
  {
    id: 4,
    tenantId: 'tenant-1',
    name: 'Mason',
    description: 'Masonry and concrete specialist',
    requiredCertifications: ['Safety Training'],
    createdAt: '2025-01-01T00:00:00Z',
    createdBy: 'System',
    updatedAt: '2025-01-15T10:30:00Z',
    updatedBy: 'Admin'
  },
  {
    id: 5,
    tenantId: 'tenant-1',
    name: 'General Labor',
    description: 'General construction labor',
    requiredCertifications: ['Safety Training'],
    createdAt: '2025-01-01T00:00:00Z',
    createdBy: 'System',
    updatedAt: '2025-01-15T10:30:00Z',
    updatedBy: 'Admin'
  }
];

// Mock Skills
export const mockSkills: Skill[] = [
  {
    id: 1,
    tenantId: 'tenant-1',
    name: 'Power Tools',
    description: 'Proficient with power tools',
    certificationRequired: false,
    createdAt: '2025-01-01T00:00:00Z',
    createdBy: 'System',
    updatedAt: '2025-01-15T10:30:00Z',
    updatedBy: 'Admin'
  },
  {
    id: 2,
    tenantId: 'tenant-1',
    name: 'Heavy Machinery',
    description: 'Operation of heavy construction machinery',
    certificationRequired: true,
    createdAt: '2025-01-01T00:00:00Z',
    createdBy: 'System',
    updatedAt: '2025-01-15T10:30:00Z',
    updatedBy: 'Admin'
  },
  {
    id: 3,
    tenantId: 'tenant-1',
    name: 'Safety Protocols',
    description: 'Knowledge of construction safety protocols',
    certificationRequired: true,
    createdAt: '2025-01-01T00:00:00Z',
    createdBy: 'System',
    updatedAt: '2025-01-15T10:30:00Z',
    updatedBy: 'Admin'
  },
  {
    id: 4,
    tenantId: 'tenant-1',
    name: 'Blueprint Reading',
    description: 'Ability to read and interpret construction blueprints',
    certificationRequired: false,
    createdAt: '2025-01-01T00:00:00Z',
    createdBy: 'System',
    updatedAt: '2025-01-15T10:30:00Z',
    updatedBy: 'Admin'
  },
  {
    id: 5,
    tenantId: 'tenant-1',
    name: 'Quality Control',
    description: 'Quality assurance and control procedures',
    certificationRequired: false,
    createdAt: '2025-01-01T00:00:00Z',
    createdBy: 'System',
    updatedAt: '2025-01-15T10:30:00Z',
    updatedBy: 'Admin'
  }
];

// Mock Trainings
export const mockTrainings: Training[] = [
  {
    id: 1,
    tenantId: 'tenant-1',
    name: 'Site Safety Induction',
    description: 'Comprehensive safety orientation for new workers',
    startDate: '2025-01-15T09:00:00Z',
    endDate: '2025-01-15T12:00:00Z',
    duration: '3h',
    validityPeriodMonths: 6,
    trainingType: 'Safety',
    trainer: 'John Smith',
    frequency: 'Every 6 months',
    status: TrainingStatus.Completed,
    workers: [],
    trainingHistory: [],
    createdAt: '2025-01-01T00:00:00Z',
    createdBy: 'System',
    updatedAt: '2025-01-15T10:30:00Z',
    updatedBy: 'Admin'
  },
  {
    id: 2,
    tenantId: 'tenant-1',
    name: 'Working at Heights',
    description: 'Safety training for elevated work',
    startDate: '2025-01-20T09:00:00Z',
    endDate: '2025-01-20T17:00:00Z',
    duration: '8h',
    validityPeriodMonths: 12,
    trainingType: 'Safety',
    trainer: 'Sarah Johnson',
    frequency: 'Annually',
    status: TrainingStatus.Scheduled,
    workers: [],
    trainingHistory: [],
    createdAt: '2025-01-01T00:00:00Z',
    createdBy: 'System',
    updatedAt: '2025-01-15T10:30:00Z',
    updatedBy: 'Admin'
  },
  {
    id: 3,
    tenantId: 'tenant-1',
    name: 'Equipment Operation',
    description: 'Heavy machinery operation training',
    startDate: '2025-01-25T14:00:00Z',
    endDate: '2025-01-25T17:00:00Z',
    duration: '3h',
    validityPeriodMonths: 24,
    trainingType: 'Technical',
    trainer: 'Mike Wilson',
    frequency: 'Every 2 years',
    status: TrainingStatus.Scheduled,
    workers: [],
    trainingHistory: [],
    createdAt: '2025-01-01T00:00:00Z',
    createdBy: 'System',
    updatedAt: '2025-01-15T10:30:00Z',
    updatedBy: 'Admin'
  },
  {
    id: 4,
    tenantId: 'tenant-1',
    name: 'First Aid Level 1',
    description: 'Basic first aid and emergency response training',
    startDate: '2025-02-01T09:00:00Z',
    endDate: '2025-02-01T17:00:00Z',
    duration: '8h',
    validityPeriodMonths: 24,
    trainingType: 'Safety',
    trainer: 'Dr. Jane Doe',
    frequency: 'Every 2 years',
    status: TrainingStatus.Scheduled,
    workers: [],
    trainingHistory: [],
    createdAt: '2025-01-01T00:00:00Z',
    createdBy: 'System',
    updatedAt: '2025-01-15T10:30:00Z',
    updatedBy: 'Admin'
  }
];

// Mock Training History
export const mockTrainingHistory: WorkerTrainingHistory[] = [
  {
    id: 1,
    workerId: 1,
    trainingId: 1,
    completionDate: '2025-01-15T12:00:00Z',
    expiryDate: '2025-07-15T12:00:00Z',
    score: 95,
    notes: 'Excellent performance, demonstrated strong understanding of safety protocols',
    status: TrainingStatus.Completed,
    createdAt: '2025-01-15T12:00:00Z',
    createdBy: 'John Smith',
    updatedAt: '2025-01-15T12:00:00Z',
    updatedBy: 'John Smith'
  },
  {
    id: 2,
    workerId: 2,
    trainingId: 1,
    completionDate: '2025-01-15T12:00:00Z',
    expiryDate: '2025-07-15T12:00:00Z',
    score: 88,
    notes: 'Good performance, needs improvement in emergency procedures',
    status: TrainingStatus.Completed,
    createdAt: '2025-01-15T12:00:00Z',
    createdBy: 'John Smith',
    updatedAt: '2025-01-15T12:00:00Z',
    updatedBy: 'John Smith'
  }
];

// Mock Certifications
export const mockCertifications: WorkerCertification[] = [
  {
    id: 'cert-1',
    name: 'Basic Carpentry Certification',
    issueDate: '2024-06-15',
    expiryDate: '2026-06-15',
    status: 'valid',
    documentUrl: 'https://example.com/certificates/carpentry-cert-1.pdf'
  },
  {
    id: 'cert-2',
    name: 'Safety Training Certificate',
    issueDate: '2024-01-10',
    expiryDate: '2025-01-10',
    status: 'expiring',
    documentUrl: 'https://example.com/certificates/safety-cert-1.pdf'
  },
  {
    id: 'cert-3',
    name: 'Electrical License',
    issueDate: '2023-05-20',
    expiryDate: '2025-05-20',
    status: 'valid',
    documentUrl: 'https://example.com/certificates/electrical-license-1.pdf'
  },
  {
    id: 'cert-4',
    name: 'Working at Heights Certification',
    issueDate: '2024-03-15',
    expiryDate: '2025-03-15',
    status: 'valid',
    documentUrl: 'https://example.com/certificates/heights-cert-1.pdf'
  },
  {
    id: 'cert-5',
    name: 'First Aid Certification',
    issueDate: '2023-08-10',
    expiryDate: '2024-08-10',
    status: 'expired',
    documentUrl: 'https://example.com/certificates/first-aid-cert-1.pdf'
  },
  {
    id: 'cert-6',
    name: 'Plumbing License',
    issueDate: '2024-01-05',
    expiryDate: '2026-01-05',
    status: 'valid',
    documentUrl: 'https://example.com/certificates/plumbing-license-1.pdf'
  }
];

// Mock Workers with updated structure
export const mockWorkers: Worker[] = [
  {
    id: 1,
    tenantId: 'tenant-1',
    name: 'David Kamau',
    company: 'ABC Construction',
    nationalId: '12345678',
    phoneNumber: '+*********** 678',
    email: '<EMAIL>',
    dateOfBirth: '1990-05-15',
    gender: 'Male',
    manHours: 2080,
    photoUrl: 'https://randomuser.me/api/portraits/men/1.jpg',
    inductionDate: '2025-01-15T09:00:00Z',
    medicalCheckDate: '2025-01-10T10:00:00Z',
    rating: 4.5,
    hireDate: '2024-12-01T00:00:00Z',
    status: 'active',
    trades: [mockTrades[0]], // Carpenter
    skills: [mockSkills[0], mockSkills[2]], // Power Tools, Safety Protocols
    trainings: [mockTrainings[0]], // Site Safety Induction
    trainingHistory: [mockTrainingHistory[0]],
    certifications: [mockCertifications[0], mockCertifications[1]], // Basic Carpentry, Safety Training
    siteAssignments: [
      {
        id: 1,
        workerId: 1,
        siteId: 'site1',
        role: 'Senior Carpenter',
        startDate: '2024-12-01T00:00:00Z',
        status: 'active',
        hourlyRate: 35.00,
        createdAt: '2024-12-01T00:00:00Z',
        createdBy: 'HR Manager'
      }
    ],
    age: 34,
    trainingsCompleted: 1,
    createdAt: '2025-01-01T00:00:00Z',
    createdBy: 'System',
    updatedAt: '2025-01-15T10:30:00Z',
    updatedBy: 'Admin'
  },
  {
    id: 2,
    tenantId: 'tenant-1',
    name: 'Mary Wanjiku',
    company: 'XYZ Contractors',
    nationalId: '87654321',
    phoneNumber: '+*********** 654',
    email: '<EMAIL>',
    dateOfBirth: '1985-08-22',
    gender: 'Female',
    manHours: 3200,
    photoUrl: 'https://randomuser.me/api/portraits/women/2.jpg',
    inductionDate: '2024-12-01T09:00:00Z',
    medicalCheckDate: '2024-11-25T10:00:00Z',
    rating: 4.8,
    hireDate: '2024-11-01T00:00:00Z',
    status: 'active',
    trades: [mockTrades[1]], // Electrician
    skills: [mockSkills[1], mockSkills[2], mockSkills[3]], // Heavy Machinery, Safety Protocols, Blueprint Reading
    trainings: [mockTrainings[0], mockTrainings[1]], // Site Safety Induction, Working at Heights
    trainingHistory: [mockTrainingHistory[1]],
    certifications: [mockCertifications[2], mockCertifications[3]], // Electrical License, Working at Heights
    siteAssignments: [
      {
        id: 2,
        workerId: 2,
        siteId: 'site1',
        role: 'Lead Electrician',
        startDate: '2024-11-01T00:00:00Z',
        status: 'active',
        hourlyRate: 42.00,
        createdAt: '2024-11-01T00:00:00Z',
        createdBy: 'HR Manager'
      }
    ],
    age: 39,
    trainingsCompleted: 1,
    createdAt: '2024-12-01T00:00:00Z',
    createdBy: 'System',
    updatedAt: '2025-01-15T10:30:00Z',
    updatedBy: 'Admin'
  },
  {
    id: 3,
    tenantId: 'tenant-1',
    name: 'Peter Ochieng',
    company: 'ABC Construction',
    nationalId: '11223344',
    phoneNumber: '+*********** 789',
    email: '<EMAIL>',
    dateOfBirth: '1992-03-10',
    gender: 'Male',
    manHours: 1560,
    photoUrl: 'https://randomuser.me/api/portraits/men/3.jpg',
    inductionDate: '2025-01-10T09:00:00Z',
    medicalCheckDate: '2025-01-08T10:00:00Z',
    rating: 4.2,
    hireDate: '2025-01-05T00:00:00Z',
    status: 'active',
    trades: [mockTrades[2]], // Plumber
    skills: [mockSkills[0], mockSkills[2]], // Power Tools, Safety Protocols
    trainings: [mockTrainings[0]], // Site Safety Induction
    trainingHistory: [],
    certifications: [mockCertifications[5], mockCertifications[4]], // Plumbing License, First Aid (expired)
    siteAssignments: [
      {
        id: 3,
        workerId: 3,
        siteId: 'site1',
        role: 'Plumber',
        startDate: '2025-01-05T00:00:00Z',
        status: 'active',
        hourlyRate: 32.00,
        createdAt: '2025-01-05T00:00:00Z',
        createdBy: 'HR Manager'
      }
    ],
    age: 32,
    trainingsCompleted: 0,
    createdAt: '2025-01-10T00:00:00Z',
    createdBy: 'System',
    updatedAt: '2025-01-15T10:30:00Z',
    updatedBy: 'Admin'
  }
];

// Mock Toolbox Sessions - Aligned with backend structure
export const mockToolboxSessions: ToolboxSession[] = [
  {
    id: 1,
    sessionTime: '2025-01-15T08:00:00Z',
    topic: 'Working at Heights Safety',
    conductor: 'John Smith',
    photoUrl: 'https://example.com/toolbox-session-1.jpg',
    notes: 'Covered proper use of harnesses, fall protection systems, and emergency procedures.',
    attendances: [
      {
        id: 1,
        toolboxSessionId: 1,
        workerId: 1,
        wasPresent: true,
        notes: 'Actively participated in discussion',
        createdAt: '2025-01-15T08:30:00Z',
        createdBy: 'John Smith',
        updatedAt: '2025-01-15T08:30:00Z',
        updatedBy: 'John Smith'
      },
      {
        id: 2,
        toolboxSessionId: 1,
        workerId: 2,
        wasPresent: true,
        notes: 'Asked good questions about equipment inspection',
        createdAt: '2025-01-15T08:30:00Z',
        createdBy: 'John Smith',
        updatedAt: '2025-01-15T08:30:00Z',
        updatedBy: 'John Smith'
      },
      {
        id: 3,
        toolboxSessionId: 1,
        workerId: 3,
        wasPresent: false,
        notes: 'Absent - sick leave',
        createdAt: '2025-01-15T08:30:00Z',
        createdBy: 'John Smith',
        updatedAt: '2025-01-15T08:30:00Z',
        updatedBy: 'John Smith'
      }
    ],
    createdAt: '2025-01-15T08:00:00Z',
    createdBy: 'John Smith',
    updatedAt: '2025-01-15T08:30:00Z',
    updatedBy: 'John Smith'
  },
  {
    id: 2,
    sessionTime: '2025-01-14T08:00:00Z',
    topic: 'PPE Requirements and Inspection',
    conductor: 'Sarah Johnson',
    photoUrl: 'https://example.com/toolbox-session-2.jpg',
    notes: 'Discussed proper PPE selection, inspection procedures, and replacement criteria.',
    attendances: [
      {
        id: 4,
        toolboxSessionId: 2,
        workerId: 1,
        wasPresent: true,
        notes: 'Demonstrated proper helmet inspection',
        createdAt: '2025-01-14T08:30:00Z',
        createdBy: 'Sarah Johnson',
        updatedAt: '2025-01-14T08:30:00Z',
        updatedBy: 'Sarah Johnson'
      },
      {
        id: 5,
        toolboxSessionId: 2,
        workerId: 2,
        wasPresent: true,
        notes: 'Shared experience with safety boots',
        createdAt: '2025-01-14T08:30:00Z',
        createdBy: 'Sarah Johnson',
        updatedAt: '2025-01-14T08:30:00Z',
        updatedBy: 'Sarah Johnson'
      },
      {
        id: 6,
        toolboxSessionId: 2,
        workerId: 3,
        wasPresent: true,
        notes: 'Attentive throughout session',
        createdAt: '2025-01-14T08:30:00Z',
        createdBy: 'Sarah Johnson',
        updatedAt: '2025-01-14T08:30:00Z',
        updatedBy: 'Sarah Johnson'
      }
    ],
    createdAt: '2025-01-14T08:00:00Z',
    createdBy: 'Sarah Johnson',
    updatedAt: '2025-01-14T08:30:00Z',
    updatedBy: 'Sarah Johnson'
  },
  {
    id: 3,
    sessionTime: '2025-01-13T08:00:00Z',
    topic: 'Excavation Safety Procedures',
    conductor: 'Mike Wilson',
    notes: 'Covered trenching safety, soil classification, and protective systems.',
    attendances: [
      {
        id: 7,
        toolboxSessionId: 3,
        workerId: 1,
        wasPresent: true,
        notes: 'Asked about soil testing procedures',
        createdAt: '2025-01-13T08:30:00Z',
        createdBy: 'Mike Wilson',
        updatedAt: '2025-01-13T08:30:00Z',
        updatedBy: 'Mike Wilson'
      },
      {
        id: 8,
        toolboxSessionId: 3,
        workerId: 2,
        wasPresent: false,
        notes: 'Late arrival - missed session',
        createdAt: '2025-01-13T08:30:00Z',
        createdBy: 'Mike Wilson',
        updatedAt: '2025-01-13T08:30:00Z',
        updatedBy: 'Mike Wilson'
      }
    ],
    createdAt: '2025-01-13T08:00:00Z',
    createdBy: 'Mike Wilson',
    updatedAt: '2025-01-13T08:30:00Z',
    updatedBy: 'Mike Wilson'
  }
];
