// Mock data for Forms feature
import {
	FormTemplate,
	FormSubmission,
	FormStats,
	FORM_CATEGORIES,
} from "../types/forms";

// Mock Form Templates
export const mockFormTemplates: FormTemplate[] = [
  {
    id: 'daily-site-inspection',
    name: 'Daily Site Inspection',
    description: 'Comprehensive daily safety and progress inspection checklist',
    category: FORM_CATEGORIES.find(c => c.id === 'safety')!,
    version: '1.0',
    status: 'active',
    isPublic: true,
    estimatedDuration: 15,
    createdBy: 'admin',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ['daily', 'safety', 'inspection', 'mandatory'],
    instructions: 'Complete this inspection at the start of each workday. Take photos of any issues identified.',
    completionMessage: 'Thank you for completing the daily inspection. Any critical issues will be escalated immediately.',
    sections: [
      {
        id: 'general-info',
        title: 'General Information',
        description: 'Basic site and weather information',
        order: 1,
        fields: [
          {
            id: 'inspector-name',
            type: 'text',
            label: 'Inspector Name',
            required: true,
            order: 1
          },
          {
            id: 'inspection-date',
            type: 'date',
            label: 'Inspection Date',
            required: true,
            order: 2,
            defaultValue: new Date().toISOString().split('T')[0]
          },
          {
            id: 'weather-conditions',
            type: 'select',
            label: 'Weather Conditions',
            required: true,
            order: 3,
            options: ['Clear', 'Cloudy', 'Light Rain', 'Heavy Rain', 'Snow', 'Windy', 'Extreme Weather']
          },
          {
            id: 'temperature',
            type: 'number',
            label: 'Temperature (°C)',
            required: false,
            order: 4,
            validation: { min: -40, max: 50 }
          }
        ]
      },
      {
        id: 'safety-check',
        title: 'Safety Inspection',
        description: 'Critical safety items that must be checked daily',
        order: 2,
        fields: [
          {
            id: 'ppe-compliance',
            type: 'yesno',
            label: 'All workers wearing required PPE?',
            required: true,
            order: 1
          },
          {
            id: 'safety-barriers',
            type: 'yesno',
            label: 'Safety barriers and signage in place?',
            required: true,
            order: 2
          },
          {
            id: 'emergency-exits',
            type: 'yesno',
            label: 'Emergency exits clear and accessible?',
            required: true,
            order: 3
          },
          {
            id: 'first-aid-kit',
            type: 'yesno',
            label: 'First aid kit accessible and stocked?',
            required: true,
            order: 4
          },
          {
            id: 'safety-issues',
            type: 'textarea',
            label: 'Describe any safety issues identified',
            required: false,
            order: 5,
            placeholder: 'Detail any safety concerns or violations observed...',
            conditional: {
              dependsOn: 'ppe-compliance',
              value: 'no',
              operator: 'equals'
            }
          },
          {
            id: 'safety-photos',
            type: 'photo',
            label: 'Photos of safety issues (if any)',
            required: false,
            order: 6,
            validation: { maxFileSize: 10 }
          }
        ]
      },
      {
        id: 'equipment-check',
        title: 'Equipment & Tools',
        description: 'Daily equipment inspection and status',
        order: 3,
        fields: [
          {
            id: 'equipment-operational',
            type: 'yesno',
            label: 'All equipment operational and safe?',
            required: true,
            order: 1
          },
          {
            id: 'equipment-issues',
            type: 'multiselect',
            label: 'Equipment requiring attention',
            required: false,
            order: 2,
            options: ['Excavator', 'Crane', 'Scaffolding', 'Power Tools', 'Vehicles', 'Safety Equipment', 'Other'],
            conditional: {
              dependsOn: 'equipment-operational',
              value: 'no',
              operator: 'equals'
            }
          },
          {
            id: 'maintenance-required',
            type: 'textarea',
            label: 'Maintenance requirements details',
            required: false,
            order: 3,
            placeholder: 'Describe specific maintenance needs...'
          }
        ]
      }
    ]
  },
  {
    id: 'toolbox-talk',
    name: 'Toolbox Talk Record',
    description: 'Record safety discussions and worker attendance',
    category: FORM_CATEGORIES.find(c => c.id === 'safety')!,
    version: '1.0',
    status: 'active',
    isPublic: true,
    estimatedDuration: 10,
    createdBy: 'admin',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ['safety', 'training', 'communication'],
    sections: [
      {
        id: 'talk-details',
        title: 'Talk Details',
        order: 1,
        fields: [
          {
            id: 'talk-date',
            type: 'date',
            label: 'Talk Date',
            required: true,
            order: 1,
            defaultValue: new Date().toISOString().split('T')[0]
          },
          {
            id: 'presenter',
            type: 'text',
            label: 'Presenter Name',
            required: true,
            order: 2
          },
          {
            id: 'topic',
            type: 'select',
            label: 'Safety Topic',
            required: true,
            order: 3,
            options: [
              'Fall Protection',
              'Electrical Safety',
              'Equipment Operation',
              'Chemical Handling',
              'Emergency Procedures',
              'PPE Requirements',
              'Housekeeping',
              'Weather Hazards',
              'Other'
            ]
          },
          {
            id: 'custom-topic',
            type: 'text',
            label: 'Custom Topic (if Other selected)',
            required: false,
            order: 4,
            conditional: {
              dependsOn: 'topic',
              value: 'Other',
              operator: 'equals'
            }
          },
          {
            id: 'key-points',
            type: 'textarea',
            label: 'Key Points Discussed',
            required: true,
            order: 5,
            placeholder: 'Summarize the main safety points covered...'
          }
        ]
      },
      {
        id: 'attendance',
        title: 'Attendance',
        order: 2,
        fields: [
          {
            id: 'attendee-count',
            type: 'number',
            label: 'Number of Attendees',
            required: true,
            order: 1,
            validation: { min: 1, max: 100 }
          },
          {
            id: 'attendee-names',
            type: 'textarea',
            label: 'Attendee Names (one per line)',
            required: true,
            order: 2,
            placeholder: 'John Smith\nJane Doe\n...'
          },
          {
            id: 'questions-asked',
            type: 'yesno',
            label: 'Were questions asked by attendees?',
            required: true,
            order: 3
          },
          {
            id: 'questions-details',
            type: 'textarea',
            label: 'Questions and Answers',
            required: false,
            order: 4,
            placeholder: 'Record any questions asked and answers provided...',
            conditional: {
              dependsOn: 'questions-asked',
              value: 'yes',
              operator: 'equals'
            }
          }
        ]
      }
    ]
  },
  {
    id: 'incident-report',
    name: 'Incident Report',
    description: 'Report workplace incidents, near misses, and safety observations',
    category: FORM_CATEGORIES.find(c => c.id === 'safety')!,
    version: '1.0',
    status: 'active',
    isPublic: true,
    estimatedDuration: 20,
    createdBy: 'admin',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ['incident', 'safety', 'urgent'],
    instructions: 'Report all incidents immediately. For emergencies, call emergency services first.',
    sections: [
      {
        id: 'incident-details',
        title: 'Incident Information',
        order: 1,
        fields: [
          {
            id: 'incident-type',
            type: 'select',
            label: 'Incident Type',
            required: true,
            order: 1,
            options: ['Injury', 'Near Miss', 'Property Damage', 'Environmental', 'Security', 'Other']
          },
          {
            id: 'severity',
            type: 'select',
            label: 'Severity Level',
            required: true,
            order: 2,
            options: ['Low', 'Medium', 'High', 'Critical']
          },
          {
            id: 'incident-date',
            type: 'datetime',
            label: 'Date and Time of Incident',
            required: true,
            order: 3
          },
          {
            id: 'location',
            type: 'text',
            label: 'Location on Site',
            required: true,
            order: 4
          },
          {
            id: 'description',
            type: 'textarea',
            label: 'Detailed Description',
            required: true,
            order: 5,
            placeholder: 'Provide a detailed description of what happened...'
          }
        ]
      }
    ]
  },
  {
    id: 'worker-registration',
    name: 'Worker Registration',
    description: 'Register new workers with complete profile information',
    category: FORM_CATEGORIES.find(c => c.id === 'hr')!,
    version: '1.0',
    status: 'active',
    isPublic: true,
    estimatedDuration: 25,
    createdBy: 'admin',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ['worker', 'registration', 'onboarding', 'hr'],
    instructions: 'Complete all required fields for worker registration. Photo and documents are mandatory.',
    sections: [
      {
        id: 'personal-info',
        title: 'Personal Information',
        description: 'Basic personal details and identification',
        order: 1,
        fields: [
          {
            id: 'worker-name',
            type: 'text',
            label: 'Full Name',
            required: true,
            order: 1,
            validation: { minLength: 2, maxLength: 100 }
          },
          {
            id: 'national-id',
            type: 'text',
            label: 'National ID Number',
            required: true,
            order: 2,
            validation: { pattern: '^[0-9]{8}$', message: 'Must be 8 digits' }
          },
          {
            id: 'date-of-birth',
            type: 'date',
            label: 'Date of Birth',
            required: true,
            order: 3
          },
          {
            id: 'gender',
            type: 'select',
            label: 'Gender',
            required: true,
            order: 4,
            options: ['Male', 'Female', 'Other']
          },
          {
            id: 'phone-number',
            type: 'phone',
            label: 'Phone Number',
            required: true,
            order: 5,
            validation: { pattern: '^[+]?[0-9]{10,15}$' }
          },
          {
            id: 'email',
            type: 'email',
            label: 'Email Address',
            required: false,
            order: 6
          },
          {
            id: 'worker-photo',
            type: 'photo',
            label: 'Worker Photo',
            required: true,
            order: 7,
            validation: { maxFileSize: 5, fileTypes: ['jpg', 'jpeg', 'png'] }
          }
        ]
      },
      {
        id: 'employment-info',
        title: 'Employment Details',
        description: 'Company and role information',
        order: 2,
        fields: [
          {
            id: 'company',
            type: 'text',
            label: 'Company Name',
            required: true,
            order: 1
          },
          {
            id: 'trades',
            type: 'multiselect',
            label: 'Trades/Specializations',
            required: true,
            order: 2,
            options: ['Carpenter', 'Electrician', 'Plumber', 'Mason', 'Welder', 'Heavy Equipment Operator', 'General Labor', 'Supervisor', 'Other']
          },
          {
            id: 'skills',
            type: 'multiselect',
            label: 'Skills',
            required: false,
            order: 3,
            options: ['Crane Operation', 'Scaffolding', 'Concrete Work', 'Steel Work', 'Safety Training', 'First Aid', 'Equipment Maintenance', 'Quality Control']
          },
          {
            id: 'induction-date',
            type: 'date',
            label: 'Site Induction Date',
            required: false,
            order: 4
          },
          {
            id: 'medical-check-date',
            type: 'date',
            label: 'Medical Check Date',
            required: false,
            order: 5
          }
        ]
      }
    ]
  },
  {
    id: 'training-enrollment',
    name: 'Training Enrollment',
    description: 'Enroll workers in training programs',
    category: FORM_CATEGORIES.find(c => c.id === 'training')!,
    version: '1.0',
    status: 'active',
    isPublic: true,
    estimatedDuration: 15,
    createdBy: 'admin',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ['training', 'enrollment', 'skills', 'certification'],
    sections: [
      {
        id: 'training-details',
        title: 'Training Information',
        order: 1,
        fields: [
          {
            id: 'training-name',
            type: 'text',
            label: 'Training Program Name',
            required: true,
            order: 1
          },
          {
            id: 'training-type',
            type: 'select',
            label: 'Training Type',
            required: true,
            order: 2,
            options: ['Safety Training', 'Skills Development', 'Certification', 'Refresher', 'Mandatory', 'Optional']
          },
          {
            id: 'trainer',
            type: 'text',
            label: 'Trainer Name',
            required: true,
            order: 3
          },
          {
            id: 'start-date',
            type: 'date',
            label: 'Start Date',
            required: true,
            order: 4
          },
          {
            id: 'end-date',
            type: 'date',
            label: 'End Date',
            required: false,
            order: 5
          },
          {
            id: 'duration',
            type: 'text',
            label: 'Duration (e.g., 2 days, 4 hours)',
            required: true,
            order: 6,
            placeholder: 'e.g., 2 days, 4 hours, 1 week'
          },
          {
            id: 'validity-months',
            type: 'number',
            label: 'Certification Validity (months)',
            required: false,
            order: 7,
            validation: { min: 1, max: 120 }
          },
          {
            id: 'frequency',
            type: 'select',
            label: 'Training Frequency',
            required: false,
            order: 8,
            options: ['One-time', 'Annual', 'Bi-annual', 'Quarterly', 'Monthly', 'As needed']
          }
        ]
      },
      {
        id: 'enrollment-info',
        title: 'Enrollment Details',
        order: 2,
        fields: [
          {
            id: 'enrolled-workers',
            type: 'textarea',
            label: 'Worker Names (one per line)',
            required: true,
            order: 1,
            placeholder: 'John Mwangi\nSarah Ochieng\n...'
          },
          {
            id: 'training-description',
            type: 'textarea',
            label: 'Training Description',
            required: false,
            order: 2,
            placeholder: 'Describe the training content and objectives...'
          }
        ]
      }
    ]
  },
  {
    id: 'task-assignment',
    name: 'Task Assignment',
    description: 'Assign tasks to workers and track progress',
    category: FORM_CATEGORIES.find(c => c.id === 'progress')!,
    version: '1.0',
    status: 'active',
    isPublic: true,
    estimatedDuration: 10,
    createdBy: 'admin',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ['task', 'assignment', 'progress', 'management'],
    sections: [
      {
        id: 'task-info',
        title: 'Task Information',
        order: 1,
        fields: [
          {
            id: 'task-name',
            type: 'text',
            label: 'Task Name',
            required: true,
            order: 1,
            validation: { minLength: 3, maxLength: 100 }
          },
          {
            id: 'task-type',
            type: 'select',
            label: 'Task Type',
            required: true,
            order: 2,
            options: ['Construction', 'Maintenance', 'Inspection', 'Safety', 'Administrative', 'Training', 'Other']
          },
          {
            id: 'task-description',
            type: 'textarea',
            label: 'Task Description',
            required: true,
            order: 3,
            placeholder: 'Provide detailed description of the task...'
          },
          {
            id: 'assigned-worker',
            type: 'text',
            label: 'Assigned Worker Name',
            required: true,
            order: 4
          },
          {
            id: 'priority',
            type: 'select',
            label: 'Priority Level',
            required: true,
            order: 5,
            options: ['Low', 'Medium', 'High', 'Critical']
          },
          {
            id: 'due-date',
            type: 'date',
            label: 'Due Date',
            required: false,
            order: 6
          },
          {
            id: 'estimated-hours',
            type: 'number',
            label: 'Estimated Hours',
            required: false,
            order: 7,
            validation: { min: 0.5, max: 100 }
          }
        ]
      }
    ]
  },
  {
    id: 'equipment-inspection',
    name: 'Equipment Inspection',
    description: 'Comprehensive equipment safety and maintenance inspection',
    category: FORM_CATEGORIES.find(c => c.id === 'equipment')!,
    version: '1.0',
    status: 'active',
    isPublic: true,
    estimatedDuration: 20,
    createdBy: 'admin',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ['equipment', 'inspection', 'maintenance', 'safety'],
    sections: [
      {
        id: 'equipment-details',
        title: 'Equipment Information',
        order: 1,
        fields: [
          {
            id: 'equipment-type',
            type: 'select',
            label: 'Equipment Type',
            required: true,
            order: 1,
            options: ['Excavator', 'Crane', 'Bulldozer', 'Loader', 'Dump Truck', 'Concrete Mixer', 'Generator', 'Compressor', 'Scaffolding', 'Power Tools', 'Other']
          },
          {
            id: 'equipment-id',
            type: 'text',
            label: 'Equipment ID/Serial Number',
            required: true,
            order: 2
          },
          {
            id: 'inspection-date',
            type: 'date',
            label: 'Inspection Date',
            required: true,
            order: 3,
            defaultValue: new Date().toISOString().split('T')[0]
          },
          {
            id: 'inspector-name',
            type: 'text',
            label: 'Inspector Name',
            required: true,
            order: 4
          },
          {
            id: 'last-maintenance',
            type: 'date',
            label: 'Last Maintenance Date',
            required: false,
            order: 5
          }
        ]
      },
      {
        id: 'inspection-checklist',
        title: 'Inspection Checklist',
        order: 2,
        fields: [
          {
            id: 'visual-condition',
            type: 'select',
            label: 'Overall Visual Condition',
            required: true,
            order: 1,
            options: ['Excellent', 'Good', 'Fair', 'Poor', 'Unsafe']
          },
          {
            id: 'operational-status',
            type: 'yesno',
            label: 'Equipment Operational?',
            required: true,
            order: 2
          },
          {
            id: 'safety-features',
            type: 'yesno',
            label: 'All Safety Features Working?',
            required: true,
            order: 3
          },
          {
            id: 'fluid-levels',
            type: 'yesno',
            label: 'Fluid Levels Adequate?',
            required: true,
            order: 4
          },
          {
            id: 'wear-damage',
            type: 'textarea',
            label: 'Signs of Wear or Damage',
            required: false,
            order: 5,
            placeholder: 'Describe any visible wear, damage, or concerns...'
          },
          {
            id: 'maintenance-needed',
            type: 'yesno',
            label: 'Immediate Maintenance Required?',
            required: true,
            order: 6
          },
          {
            id: 'maintenance-details',
            type: 'textarea',
            label: 'Maintenance Requirements',
            required: false,
            order: 7,
            placeholder: 'Specify required maintenance actions...',
            conditional: {
              dependsOn: 'maintenance-needed',
              value: 'yes',
              operator: 'equals'
            }
          },
          {
            id: 'inspection-photos',
            type: 'photo',
            label: 'Inspection Photos',
            required: false,
            order: 8,
            validation: { maxFileSize: 10 }
          }
        ]
      }
    ]
  },
  {
    id: 'worker-attendance',
    name: 'Worker Attendance Record',
    description: 'Manual attendance entry and time tracking',
    category: FORM_CATEGORIES.find(c => c.id === 'administrative')!,
    version: '1.0',
    status: 'active',
    isPublic: true,
    estimatedDuration: 5,
    createdBy: 'admin',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ['attendance', 'time', 'tracking', 'payroll'],
    sections: [
      {
        id: 'attendance-info',
        title: 'Attendance Information',
        order: 1,
        fields: [
          {
            id: 'worker-name',
            type: 'text',
            label: 'Worker Name',
            required: true,
            order: 1
          },
          {
            id: 'attendance-date',
            type: 'date',
            label: 'Date',
            required: true,
            order: 2,
            defaultValue: new Date().toISOString().split('T')[0]
          },
          {
            id: 'check-in-time',
            type: 'time',
            label: 'Check-in Time',
            required: true,
            order: 3
          },
          {
            id: 'check-out-time',
            type: 'time',
            label: 'Check-out Time',
            required: false,
            order: 4
          },
          {
            id: 'break-duration',
            type: 'number',
            label: 'Break Duration (minutes)',
            required: false,
            order: 5,
            validation: { min: 0, max: 480 }
          },
          {
            id: 'overtime-hours',
            type: 'number',
            label: 'Overtime Hours',
            required: false,
            order: 6,
            validation: { min: 0, max: 12 }
          },
          {
            id: 'attendance-notes',
            type: 'textarea',
            label: 'Notes',
            required: false,
            order: 7,
            placeholder: 'Any additional notes about attendance...'
          }
        ]
      }
    ]
  },
  {
    id: 'skill-assessment',
    name: 'Worker Skill Assessment',
    description: 'Evaluate and record worker skills and competencies',
    category: FORM_CATEGORIES.find(c => c.id === 'training')!,
    version: '1.0',
    status: 'active',
    isPublic: true,
    estimatedDuration: 15,
    createdBy: 'admin',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ['skills', 'assessment', 'competency', 'evaluation'],
    sections: [
      {
        id: 'worker-info',
        title: 'Worker Information',
        order: 1,
        fields: [
          {
            id: 'worker-name',
            type: 'text',
            label: 'Worker Name',
            required: true,
            order: 1
          },
          {
            id: 'assessor-name',
            type: 'text',
            label: 'Assessor Name',
            required: true,
            order: 2
          },
          {
            id: 'assessment-date',
            type: 'date',
            label: 'Assessment Date',
            required: true,
            order: 3,
            defaultValue: new Date().toISOString().split('T')[0]
          }
        ]
      },
      {
        id: 'skill-evaluation',
        title: 'Skill Evaluation',
        order: 2,
        fields: [
          {
            id: 'primary-trade',
            type: 'select',
            label: 'Primary Trade',
            required: true,
            order: 1,
            options: ['Carpenter', 'Electrician', 'Plumber', 'Mason', 'Welder', 'Heavy Equipment Operator', 'General Labor', 'Supervisor']
          },
          {
            id: 'skill-level',
            type: 'select',
            label: 'Overall Skill Level',
            required: true,
            order: 2,
            options: ['Beginner', 'Intermediate', 'Advanced', 'Expert']
          },
          {
            id: 'technical-skills',
            type: 'rating',
            label: 'Technical Skills (1-5)',
            required: true,
            order: 3
          },
          {
            id: 'safety-knowledge',
            type: 'rating',
            label: 'Safety Knowledge (1-5)',
            required: true,
            order: 4
          },
          {
            id: 'teamwork',
            type: 'rating',
            label: 'Teamwork (1-5)',
            required: true,
            order: 5
          },
          {
            id: 'communication',
            type: 'rating',
            label: 'Communication (1-5)',
            required: true,
            order: 6
          },
          {
            id: 'additional-skills',
            type: 'multiselect',
            label: 'Additional Skills',
            required: false,
            order: 7,
            options: ['Crane Operation', 'Scaffolding', 'Concrete Work', 'Steel Work', 'First Aid', 'Equipment Maintenance', 'Quality Control', 'Leadership']
          },
          {
            id: 'improvement-areas',
            type: 'textarea',
            label: 'Areas for Improvement',
            required: false,
            order: 8,
            placeholder: 'Identify areas where the worker could improve...'
          },
          {
            id: 'training-recommendations',
            type: 'textarea',
            label: 'Training Recommendations',
            required: false,
            order: 9,
            placeholder: 'Recommend specific training programs...'
          }
        ]
      }
    ]
  },
  // Inspection Forms
  {
    id: 'crane-inspection',
    name: 'Crane Inspection Form',
    description: 'Daily safety inspection checklist for crane equipment',
    category: FORM_CATEGORIES.find(c => c.id === 'inspection')!,
    version: '1.0',
    status: 'active',
    isPublic: true,
    estimatedDuration: 30,
    createdBy: 'admin',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ['inspection', 'equipment', 'crane', 'safety'],
    instructions: 'Complete all inspection points before operating the crane. Report any defects immediately.',
    sections: [
      {
        id: 'crane-general',
        title: 'General Inspection',
        order: 1,
        fields: [
          {
            id: 'crane-id',
            type: 'text',
            label: 'Crane ID/Serial Number',
            required: true,
            order: 1
          },
          {
            id: 'inspector-name',
            type: 'text',
            label: 'Inspector Name',
            required: true,
            order: 2
          },
          {
            id: 'inspection-date',
            type: 'date',
            label: 'Inspection Date',
            required: true,
            order: 3,
            defaultValue: new Date().toISOString().split('T')[0]
          }
        ]
      }
    ]
  },
  {
    id: 'excavator-inspection',
    name: 'Excavator Inspection Form',
    description: 'Pre-operation safety inspection for excavator equipment',
    category: FORM_CATEGORIES.find(c => c.id === 'inspection')!,
    version: '1.0',
    status: 'active',
    isPublic: true,
    estimatedDuration: 25,
    createdBy: 'admin',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ['inspection', 'equipment', 'excavator', 'safety'],
    sections: [
      {
        id: 'excavator-general',
        title: 'General Inspection',
        order: 1,
        fields: [
          {
            id: 'excavator-id',
            type: 'text',
            label: 'Excavator ID/Serial Number',
            required: true,
            order: 1
          }
        ]
      }
    ]
  },
  // Permit Forms
  {
    id: 'hot-work-permit',
    name: 'Hot Work Permit',
    description: 'Safety permit for welding, cutting, and other hot work operations',
    category: FORM_CATEGORIES.find(c => c.id === 'permit')!,
    version: '1.0',
    status: 'active',
    isPublic: true,
    estimatedDuration: 20,
    createdBy: 'admin',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ['permit', 'hot-work', 'safety', 'fire'],
    instructions: 'Complete all sections before beginning hot work. Fire watch required.',
    sections: [
      {
        id: 'hot-work-details',
        title: 'Work Details',
        order: 1,
        fields: [
          {
            id: 'work-description',
            type: 'textarea',
            label: 'Description of Work',
            required: true,
            order: 1
          },
          {
            id: 'work-location',
            type: 'text',
            label: 'Work Location',
            required: true,
            order: 2
          }
        ]
      }
    ]
  },
  {
    id: 'confined-space-permit',
    name: 'Confined Space Entry Permit',
    description: 'Safety permit for entry into confined spaces',
    category: FORM_CATEGORIES.find(c => c.id === 'permit')!,
    version: '1.0',
    status: 'active',
    isPublic: true,
    estimatedDuration: 25,
    createdBy: 'admin',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ['permit', 'confined-space', 'safety', 'entry'],
    sections: [
      {
        id: 'confined-space-details',
        title: 'Space Details',
        order: 1,
        fields: [
          {
            id: 'space-description',
            type: 'textarea',
            label: 'Description of Confined Space',
            required: true,
            order: 1
          }
        ]
      }
    ]
  },
  {
    id: 'work-at-height-permit',
    name: 'Work at Height Permit',
    description: 'Safety permit for work performed at elevated heights',
    category: FORM_CATEGORIES.find(c => c.id === 'permit')!,
    version: '1.0',
    status: 'active',
    isPublic: true,
    estimatedDuration: 20,
    createdBy: 'admin',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ['permit', 'height', 'safety', 'fall-protection'],
    sections: [
      {
        id: 'height-work-details',
        title: 'Work Details',
        order: 1,
        fields: [
          {
            id: 'height-description',
            type: 'textarea',
            label: 'Description of Work at Height',
            required: true,
            order: 1
          }
        ]
      }
    ]
  },
  {
    id: 'excavation-permit',
    name: 'Excavation Permit',
    description: 'Safety permit for excavation and digging operations',
    category: FORM_CATEGORIES.find(c => c.id === 'permit')!,
    version: '1.0',
    status: 'active',
    isPublic: true,
    estimatedDuration: 20,
    createdBy: 'admin',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
    tags: ['permit', 'excavation', 'safety', 'digging'],
    sections: [
      {
        id: 'excavation-details',
        title: 'Excavation Details',
        order: 1,
        fields: [
          {
            id: 'excavation-description',
            type: 'textarea',
            label: 'Description of Excavation Work',
            required: true,
            order: 1
          }
        ]
      }
    ]
  }
];

// Mock Form Submissions
export const mockFormSubmissions: FormSubmission[] = [
  {
    id: 'sub-001',
    templateId: 'daily-site-inspection',
    templateName: 'Daily Site Inspection',
    templateVersion: '1.0',
    siteId: 'site1',
    siteName: 'Westlands Construction Site',
    submittedBy: 'user1',
    submittedByName: 'John Mwangi',
    submissionDate: new Date('2024-01-15T08:30:00'),
    status: 'submitted',
    data: {
      'inspector-name': 'John Mwangi',
      'inspection-date': '2024-01-15',
      'weather-conditions': 'Clear',
      'temperature': 22,
      'ppe-compliance': 'yes',
      'safety-barriers': 'yes',
      'emergency-exits': 'yes',
      'first-aid-kit': 'yes',
      'equipment-operational': 'no',
      'equipment-issues': ['Excavator', 'Power Tools'],
      'maintenance-required': 'Excavator hydraulic system needs inspection'
    },
    completionTime: 12,
    createdAt: new Date('2024-01-15T08:30:00'),
    updatedAt: new Date('2024-01-15T08:42:00')
  },
  {
    id: 'sub-002',
    templateId: 'toolbox-talk',
    templateName: 'Toolbox Talk Record',
    templateVersion: '1.0',
    siteId: 'site1',
    siteName: 'Westlands Construction Site',
    submittedBy: 'user2',
    submittedByName: 'Sarah Ochieng',
    submissionDate: new Date('2024-01-15T07:15:00'),
    status: 'under_review',
    data: {
      'talk-date': '2024-01-15',
      'presenter': 'Sarah Ochieng',
      'topic': 'Fall Protection',
      'key-points': 'Discussed proper harness use, anchor points, and inspection procedures',
      'attendee-count': 8,
      'attendee-names': 'John Mwangi\nDavid Kimani\nGrace Wanjiku\nPeter Ochieng\nMary Njeri\nJames Maina\nAnne Wambui\nSamuel Kiprotich',
      'questions-asked': 'yes',
      'questions-details': 'Q: How often should harnesses be inspected? A: Before each use and formally every 6 months'
    },
    reviewedBy: 'supervisor1',
    reviewedByName: 'Michael Kariuki',
    completionTime: 8,
    createdAt: new Date('2024-01-15T07:15:00'),
    updatedAt: new Date('2024-01-15T07:23:00')
  },
  {
    id: 'sub-003',
    templateId: 'worker-registration',
    templateName: 'Worker Registration',
    templateVersion: '1.0',
    siteId: 'site1',
    siteName: 'Westlands Construction Site',
    submittedBy: 'hr1',
    submittedByName: 'Grace Wanjiku',
    submissionDate: new Date('2024-01-14T10:00:00'),
    status: 'submitted',
    data: {
      'worker-name': 'David Kimani',
      'national-id': '12345678',
      'date-of-birth': '1985-03-15',
      'gender': 'Male',
      'phone-number': '+254712345678',
      'email': '<EMAIL>',
      'company': 'ABC Construction Ltd',
      'trades': ['Electrician', 'General Labor'],
      'skills': ['Safety Training', 'Equipment Maintenance'],
      'induction-date': '2024-01-14',
      'medical-check-date': '2024-01-10'
    },
    completionTime: 22,
    createdAt: new Date('2024-01-14T10:00:00'),
    updatedAt: new Date('2024-01-14T10:22:00')
  },
  {
    id: 'sub-004',
    templateId: 'equipment-inspection',
    templateName: 'Equipment Inspection',
    templateVersion: '1.0',
    siteId: 'site1',
    siteName: 'Westlands Construction Site',
    submittedBy: 'user3',
    submittedByName: 'Peter Ochieng',
    submissionDate: new Date('2024-01-15T14:30:00'),
    status: 'draft',
    data: {
      'equipment-type': 'Excavator',
      'equipment-id': 'EXC-001',
      'inspection-date': '2024-01-15',
      'inspector-name': 'Peter Ochieng',
      'last-maintenance': '2024-01-01',
      'visual-condition': 'Good',
      'operational-status': 'yes',
      'safety-features': 'yes',
      'fluid-levels': 'no',
      'wear-damage': 'Minor hydraulic fluid leak detected',
      'maintenance-needed': 'yes',
      'maintenance-details': 'Replace hydraulic seals and top up fluid'
    },
    completionTime: 18,
    createdAt: new Date('2024-01-15T14:30:00'),
    updatedAt: new Date('2024-01-15T14:48:00')
  }
];

// Mock Form Statistics
export const mockFormStats: FormStats = {
  totalSubmissions: 234,
  pendingReview: 18,
  completedToday: 12,
  averageCompletionTime: 14.2,
  topCategories: [
    { category: 'Safety', count: 89 },
    { category: 'Equipment', count: 45 },
    { category: 'Human Resources', count: 38 },
    { category: 'Progress', count: 32 },
    { category: 'Training', count: 20 },
    { category: 'Quality', count: 10 }
  ],
  recentActivity: [
    {
      id: 'sub-001',
      templateName: 'Daily Site Inspection',
      submittedBy: 'John Mwangi',
      submittedAt: new Date('2024-01-15T08:30:00'),
      status: 'submitted'
    },
    {
      id: 'sub-002',
      templateName: 'Toolbox Talk Record',
      submittedBy: 'Sarah Ochieng',
      submittedAt: new Date('2024-01-15T07:15:00'),
      status: 'under_review'
    },
    {
      id: 'sub-003',
      templateName: 'Worker Registration',
      submittedBy: 'Grace Wanjiku',
      submittedAt: new Date('2024-01-14T10:00:00'),
      status: 'submitted'
    },
    {
      id: 'sub-004',
      templateName: 'Equipment Inspection',
      submittedBy: 'Peter Ochieng',
      submittedAt: new Date('2024-01-15T14:30:00'),
      status: 'draft'
    }
  ]
};
