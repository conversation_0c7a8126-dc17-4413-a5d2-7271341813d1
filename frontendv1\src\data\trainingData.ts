import { TrainingProgram } from '../types/training';

// Company-registered Training Programs with Pre-approved Providers
export const trainingPrograms: TrainingProgram[] = [
  {
    id: 'tp-001',
    name: 'Safety Induction Training',
    description: 'Comprehensive safety orientation covering HSE policies, legal requirements, fire safety, and site-specific hazards for all new workers.',
    approvedProviders: ['prov-001', 'prov-002'], // Pre-approved provider IDs
    validityPeriod: 12, // 1 year in months
    duration: 4, // 4 hours
    category: 'safety',
    isRequired: true,
    requiredForTrades: ['construction', 'maintenance', 'electrical'],
    prerequisites: [],
    status: 'active',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: 'tp-002',
    name: 'First Aid Training',
    description: 'Emergency first aid and CPR training covering basic life support, injury management, and emergency response procedures.',
    approvedProviders: ['prov-002'], // Pre-approved provider IDs
    validityPeriod: 24, // 2 years in months
    duration: 16, // 16 hours
    category: 'emergency',
    isRequired: false,
    requiredForTrades: ['safety', 'medical'],
    prerequisites: [],
    status: 'active',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: 'tp-003',
    name: 'Fire Marshal Training',
    description: 'Fire safety and emergency evacuation procedures training for designated fire marshals and emergency response team members.',
    approvedProviders: ['prov-003'], // Pre-approved provider IDs
    validityPeriod: 12, // 1 year in months
    duration: 8, // 8 hours
    category: 'emergency',
    isRequired: false,
    requiredForTrades: ['safety', 'emergency'],
    prerequisites: ['tp-001'],
    status: 'active',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: 'tp-004',
    name: 'Working at Heights',
    description: 'Safety training for working at elevated positions, including fall protection, scaffold safety, and ladder safety procedures.',
    approvedProviders: ['prov-004', 'prov-001'], // Pre-approved provider IDs
    validityPeriod: 12, // 1 year in months
    duration: 6, // 6 hours
    category: 'safety',
    isRequired: true,
    requiredForTrades: ['construction', 'maintenance'],
    prerequisites: ['tp-001'],
    status: 'active',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: 'tp-005',
    name: 'Electrical Safety Training',
    description: 'Electrical hazard awareness, lockout/tagout procedures, and safe electrical work practices for qualified electrical workers.',
    approvedProviders: ['prov-001'], // Pre-approved provider IDs
    validityPeriod: 12, // 1 year in months
    duration: 8, // 8 hours
    category: 'technical',
    isRequired: true,
    requiredForTrades: ['electrical'],
    prerequisites: ['tp-001'],
    status: 'active',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: 'tp-006',
    name: 'Confined Space Entry',
    description: 'Safety procedures for entering and working in confined spaces, including gas testing, ventilation, and emergency rescue.',
    approvedProviders: ['prov-001'], // Pre-approved provider IDs
    validityPeriod: 12, // 1 year in months
    duration: 12, // 12 hours
    category: 'safety',
    isRequired: false,
    requiredForTrades: ['maintenance', 'construction'],
    prerequisites: ['tp-001'],
    status: 'active',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: 'tp-007',
    name: 'OSH Committee Training',
    description: 'Training for OSH committee members on their roles, responsibilities, and effective committee operation procedures.',
    approvedProviders: ['prov-001'], // Pre-approved provider IDs
    validityPeriod: 12, // 1 year in months
    duration: 4, // 4 hours
    category: 'compliance',
    isRequired: false,
    requiredForTrades: ['management', 'safety'],
    prerequisites: [],
    status: 'active',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: 'tp-008',
    name: 'Hazard Communication',
    description: 'Training on chemical safety, SDS understanding, and proper handling of hazardous materials in the workplace.',
    approvedProviders: ['prov-001'], // Pre-approved provider IDs
    validityPeriod: 12, // 1 year in months
    duration: 3, // 3 hours
    category: 'safety',
    isRequired: false,
    requiredForTrades: ['chemical', 'maintenance'],
    prerequisites: ['tp-001'],
    status: 'active',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: 'tp-009',
    name: 'Heavy Equipment Operation',
    description: 'Safety training for operators of heavy machinery including cranes, excavators, and other construction equipment.',
    approvedProviders: ['prov-004'], // Pre-approved provider IDs
    validityPeriod: 24, // 2 years in months
    duration: 20, // 20 hours
    category: 'technical',
    isRequired: true,
    requiredForTrades: ['equipment'],
    prerequisites: ['tp-001'],
    status: 'active',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: 'tp-010',
    name: 'Environmental Awareness',
    description: 'Environmental protection training covering waste management, pollution prevention, and environmental compliance requirements.',
    approvedProviders: ['prov-001'], // Pre-approved provider IDs
    validityPeriod: 12, // 1 year in months
    duration: 4, // 4 hours
    category: 'compliance',
    isRequired: false,
    requiredForTrades: ['all'],
    prerequisites: [],
    status: 'active',
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15')
  }
];

// Session Tracking Table - stores training logistics
export interface TrainingSession {
  id: string;
  programId: string;
  programName: string;
  startDate: Date;
  endDate: Date;
  modeOfDelivery: 'online' | 'onsite' | 'hybrid';
  maxParticipants: number;
  currentEnrollment: number;
  provider: {
    name: string;
    accreditations: string[];
    contactDetails: {
      email: string;
      phone: string;
      address: string;
    };
    trainingLocation: string;
  };
  status: 'scheduled' | 'in-progress' | 'completed' | 'cancelled';
  createdAt: Date;
  updatedAt: Date;
}

// Mock Session Tracking Data
export const mockTrainingSessions: TrainingSession[] = [
  {
    id: 'ts-001',
    programId: 'tp-001',
    programName: 'Safety Induction Training',
    startDate: new Date('2025-01-20T09:00:00'),
    endDate: new Date('2025-01-20T13:00:00'),
    modeOfDelivery: 'onsite',
    maxParticipants: 20,
    currentEnrollment: 15,
    provider: {
      name: 'Internal HSE Team',
      accreditations: ['OSHA Certified', 'HSE Level 3'],
      contactDetails: {
        email: '<EMAIL>',
        phone: '+***********-456',
        address: 'Training Room A, Main Building'
      },
      trainingLocation: 'Training Room A, Main Building'
    },
    status: 'scheduled',
    createdAt: new Date('2025-01-15T10:00:00'),
    updatedAt: new Date('2025-01-15T10:00:00')
  },
  {
    id: 'ts-002',
    programId: 'tp-002',
    programName: 'First Aid Training',
    startDate: new Date('2025-01-22T14:00:00'),
    endDate: new Date('2025-01-23T18:00:00'),
    modeOfDelivery: 'onsite',
    maxParticipants: 15,
    currentEnrollment: 7,
    provider: {
      name: 'Terrace Ventures & Safety Solutions Limited',
      accreditations: ['Red Cross Certified', 'First Aid Instructor Level 2'],
      contactDetails: {
        email: '<EMAIL>',
        phone: '+***********-012',
        address: 'Training Room B, Main Building'
      },
      trainingLocation: 'Training Room B, Main Building'
    },
    status: 'scheduled',
    createdAt: new Date('2025-01-16T14:00:00'),
    updatedAt: new Date('2025-01-16T14:00:00')
  },
  {
    id: 'ts-003',
    programId: 'tp-003',
    programName: 'Fire Marshal Training',
    startDate: new Date('2025-01-25T10:00:00'),
    endDate: new Date('2025-01-25T18:00:00'),
    modeOfDelivery: 'hybrid',
    maxParticipants: 25,
    currentEnrollment: 13,
    provider: {
      name: 'Strategic SHE Limited',
      accreditations: ['Fire Safety Certified', 'Emergency Response Instructor'],
      contactDetails: {
        email: '<EMAIL>',
        phone: '+***********-678',
        address: 'Online + Training Center'
      },
      trainingLocation: 'Online + Training Center'
    },
    status: 'scheduled',
    createdAt: new Date('2025-01-17T09:00:00'),
    updatedAt: new Date('2025-01-17T09:00:00')
  }
];

// Session Enrollment Table - junction table tracking attendees
export interface SessionEnrollment {
  id: string;
  workerId: string;
  workerName: string;
  workerDepartment: string;
  scheduledSessionId: string;
  enrollmentStatus: 'registered' | 'confirmed' | 'attended' | 'completed' | 'no-show' | 'cancelled';
  registrationDate: Date;
  completionStatus: 'not-started' | 'in-progress' | 'completed' | 'failed';
  completionDate?: Date;
  certificateIssued?: boolean;
  certificateNumber?: string;
  notes?: string;
}

// Mock Session Enrollment Data
export const mockSessionEnrollments: SessionEnrollment[] = [
  {
    id: 'en-001',
    workerId: 'w-001',
    workerName: 'John Smith',
    workerDepartment: 'Construction',
    scheduledSessionId: 'ts-001',
    enrollmentStatus: 'confirmed',
    registrationDate: new Date('2025-01-15T11:00:00'),
    completionStatus: 'not-started'
  },
  {
    id: 'en-002',
    workerId: 'w-002',
    workerName: 'Sarah Johnson',
    workerDepartment: 'Safety',
    scheduledSessionId: 'ts-001',
    enrollmentStatus: 'confirmed',
    registrationDate: new Date('2025-01-15T11:30:00'),
    completionStatus: 'not-started'
  },
  {
    id: 'en-003',
    workerId: 'w-003',
    workerName: 'Mike Wilson',
    workerDepartment: 'Electrical',
    scheduledSessionId: 'ts-002',
    enrollmentStatus: 'confirmed',
    registrationDate: new Date('2025-01-16T15:00:00'),
    completionStatus: 'not-started'
  },
  {
    id: 'en-004',
    workerId: 'w-004',
    workerName: 'Lisa Brown',
    workerDepartment: 'HR',
    scheduledSessionId: 'ts-002',
    enrollmentStatus: 'registered',
    registrationDate: new Date('2025-01-16T16:00:00'),
    completionStatus: 'not-started'
  },
  {
    id: 'en-005',
    workerId: 'w-005',
    workerName: 'David Lee',
    workerDepartment: 'Maintenance',
    scheduledSessionId: 'ts-003',
    enrollmentStatus: 'confirmed',
    registrationDate: new Date('2025-01-17T10:00:00'),
    completionStatus: 'not-started'
  }
];

// Worker data for enrollment
export interface Worker {
  id: string;
  name: string;
  email: string;
  department: string;
  position: string;
  employeeId: string;
  currentCertifications: string[];
  lastTrainingDate?: Date;
  trainingExpiryDate?: Date;
}

// Mock Worker Data
export const mockWorkers: Worker[] = [
  {
    id: 'w-001',
    name: 'John Smith',
    email: '<EMAIL>',
    department: 'Construction',
    position: 'Site Supervisor',
    employeeId: 'EMP001',
    currentCertifications: ['Safety Induction', 'Working at Heights'],
    lastTrainingDate: new Date('2024-06-15'),
    trainingExpiryDate: new Date('2025-06-15')
  },
  {
    id: 'w-002',
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    department: 'Safety',
    position: 'HSE Officer',
    employeeId: 'EMP002',
    currentCertifications: ['Safety Induction', 'First Aid', 'Fire Marshal'],
    lastTrainingDate: new Date('2024-08-20'),
    trainingExpiryDate: new Date('2025-08-20')
  },
  {
    id: 'w-003',
    name: 'Mike Wilson',
    email: '<EMAIL>',
    department: 'Electrical',
    position: 'Electrician',
    employeeId: 'EMP003',
    currentCertifications: ['Safety Induction', 'Electrical Safety'],
    lastTrainingDate: new Date('2024-07-10'),
    trainingExpiryDate: new Date('2025-07-10')
  },
  {
    id: 'w-004',
    name: 'Lisa Brown',
    email: '<EMAIL>',
    department: 'HR',
    position: 'HR Manager',
    employeeId: 'EMP004',
    currentCertifications: ['Safety Induction'],
    lastTrainingDate: new Date('2024-09-05'),
    trainingExpiryDate: new Date('2025-09-05')
  },
  {
    id: 'w-005',
    name: 'David Lee',
    email: '<EMAIL>',
    department: 'Maintenance',
    position: 'Maintenance Technician',
    employeeId: 'EMP005',
    currentCertifications: ['Safety Induction', 'Heavy Equipment Operation'],
    lastTrainingDate: new Date('2024-05-22'),
    trainingExpiryDate: new Date('2025-05-22')
  },
  {
    id: 'w-006',
    name: 'Emma Davis',
    email: '<EMAIL>',
    department: 'Quality Control',
    position: 'QC Inspector',
    employeeId: 'EMP006',
    currentCertifications: ['Safety Induction', 'Quality Management'],
    lastTrainingDate: new Date('2024-10-12'),
    trainingExpiryDate: new Date('2025-10-12')
  },
  {
    id: 'w-007',
    name: 'James Wilson',
    email: '<EMAIL>',
    department: 'Construction',
    position: 'Crane Operator',
    employeeId: 'EMP007',
    currentCertifications: ['Safety Induction', 'Heavy Equipment Operation'],
    lastTrainingDate: new Date('2024-04-18'),
    trainingExpiryDate: new Date('2025-04-18')
  },
  {
    id: 'w-008',
    name: 'Maria Garcia',
    email: '<EMAIL>',
    department: 'Safety',
    position: 'Safety Coordinator',
    employeeId: 'EMP008',
    currentCertifications: ['Safety Induction', 'First Aid', 'OSH Committee'],
    lastTrainingDate: new Date('2024-11-03'),
    trainingExpiryDate: new Date('2025-11-03')
  }
];

// Provider data for training sessions
export interface TrainingProvider {
  id: string;
  name: string;
  accreditations: string[];
  contactDetails: {
    email: string;
    phone: string;
    address: string;
    website?: string;
  };
  specializations: string[];
  rating: number;
  isActive: boolean;
}

// Mock Training Provider Data
export const mockTrainingProviders: TrainingProvider[] = [
  {
    id: 'prov-001',
    name: 'Internal HSE Team',
    accreditations: ['OSHA Certified', 'HSE Level 3', 'Internal Trainer Certified'],
    contactDetails: {
      email: '<EMAIL>',
      phone: '+***********-456',
      address: 'Training Room A, Main Building',
      website: 'https://company.com/hse'
    },
    specializations: ['Safety Induction', 'General Safety', 'HSE Management'],
    rating: 4.8,
    isActive: true
  },
  {
    id: 'prov-002',
    name: 'Terrace Ventures & Safety Solutions Limited',
    accreditations: ['Red Cross Certified', 'First Aid Instructor Level 2', 'OSHA Authorized'],
    contactDetails: {
      email: '<EMAIL>',
      phone: '+***********-012',
      address: 'Training Room B, Main Building',
      website: 'https://terraceventures.com'
    },
    specializations: ['First Aid', 'CPR', 'Emergency Response'],
    rating: 4.9,
    isActive: true
  },
  {
    id: 'prov-003',
    name: 'Strategic SHE Limited',
    accreditations: ['Fire Safety Certified', 'Emergency Response Instructor', 'ISO 45001 Certified'],
    contactDetails: {
      email: '<EMAIL>',
      phone: '+***********-678',
      address: 'Training Center, Block C',
      website: 'https://strategicshe.com'
    },
    specializations: ['Fire Safety', 'Emergency Response', 'Evacuation Procedures'],
    rating: 4.7,
    isActive: true
  },
  {
    id: 'prov-004',
    name: 'Height Safety Solutions',
    accreditations: ['Working at Heights Certified', 'Fall Protection Specialist', 'OSHA Authorized'],
    contactDetails: {
      email: '<EMAIL>',
      phone: '+254-700-567-890',
      address: 'Training Facility, Industrial Area',
      website: 'https://heightsafety.com'
    },
    specializations: ['Working at Heights', 'Fall Protection', 'Scaffold Safety'],
    rating: 4.6,
    isActive: true
  }
];

// Helper functions
export const getTrainingProgramById = (id: string): TrainingProgram | undefined => {
  return trainingPrograms.find(program => program.id === id);
};

export const getTrainingSessionById = (id: string): TrainingSession | undefined => {
  return mockTrainingSessions.find(session => session.id === id);
};

export const getWorkerById = (id: string): Worker | undefined => {
  return mockWorkers.find(worker => worker.id === id);
};

export const getProviderById = (id: string): TrainingProvider | undefined => {
  return mockTrainingProviders.find(provider => provider.id === id);
};

export const getEnrollmentsBySessionId = (sessionId: string): SessionEnrollment[] => {
  return mockSessionEnrollments.filter(enrollment => enrollment.scheduledSessionId === sessionId);
};

export const getWorkersByDepartment = (department: string): Worker[] => {
  return mockWorkers.filter(worker => worker.department === department);
};

export const searchWorkers = (query: string): Worker[] => {
  const lowercaseQuery = query.toLowerCase();
  return mockWorkers.filter(worker =>
    worker.name.toLowerCase().includes(lowercaseQuery) ||
    worker.email.toLowerCase().includes(lowercaseQuery) ||
    worker.employeeId.toLowerCase().includes(lowercaseQuery) ||
    worker.department.toLowerCase().includes(lowercaseQuery)
  );
};
