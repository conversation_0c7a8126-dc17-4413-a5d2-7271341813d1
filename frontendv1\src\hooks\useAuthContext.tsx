import React, { create<PERSON>ontext, useContext, useReducer, useEffect, useCallback, ReactNode } from 'react';
import {
  User,
  AuthState,
  LoginCredentials,
  RegisterData,
  AuthContextValue,
  UserSession,
  UpdateUserRequest,
  ChangePasswordRequest,
  ResetPasswordRequest,
  PermissionLevel,
  LoginPayload
} from '../types/auth';
import { authService } from '../services/authServiceSelector';

// Auth Actions
type AuthAction =
  | { type: 'AUTH_START' }
  | { type: 'AUTH_SUCCESS'; payload: { user: User; session: UserSession } }
  | { type: 'AUTH_ERROR'; payload: string }
  | { type: 'AUTH_LOGOUT' }
  | { type: 'CLEAR_ERROR' }
  | { type: 'UPDATE_USER'; payload: User }
  | { type: 'UPDATE_SESSION'; payload: UserSession };

// Initial state
const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  isLoading: true, // Start with loading to check stored session
  error: null,
  session: null
};

// Auth reducer
const authReducer = (state: AuthState, action: AuthAction): AuthState => {
  switch (action.type) {
    case 'AUTH_START':
      return {
        ...state,
        isLoading: true,
        error: null,
      };
    case 'AUTH_SUCCESS':
      return {
        ...state,
        user: action.payload.user,
        session: action.payload.session,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };
    case 'AUTH_ERROR':
      return {
        ...state,
        user: null,
        session: null,
        isAuthenticated: false,
        isLoading: false,
        error: action.payload,
      };
    case 'AUTH_LOGOUT':
      return {
        ...state,
        user: null,
        session: null,
        isAuthenticated: false,
        isLoading: false,
        error: null,
      };
    case 'CLEAR_ERROR':
      return {
        ...state,
        error: null,
      };
    case 'UPDATE_USER':
      return {
        ...state,
        user: action.payload,
      };
    case 'UPDATE_SESSION':
      return {
        ...state,
        session: action.payload,
      };
    default:
      return state;
  }
};

// Create Auth Context
const AuthContext = createContext<AuthContextValue | null>(null);

// Auth Provider Props
interface AuthProviderProps {
  children: ReactNode;
}

// Auth Provider Component
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Check for stored session on mount
  useEffect(() => {
    const checkStoredSession = async () => {
      try {
        const user = await authService.getCurrentUser();
        if (user) {
          dispatch({
            type: 'AUTH_SUCCESS',
            payload: {
              user,
              session: null! // Session is managed by HTTP-only cookies
            }
          });
        } else {
          dispatch({ type: 'AUTH_LOGOUT' });
        }
      } catch (error) {
        console.error('Error checking stored session:', error);
        dispatch({ type: 'AUTH_LOGOUT' });
      }
    };

    checkStoredSession();
  }, []);

  // Login function
  const login = useCallback(async (credentials: LoginCredentials): Promise<LoginPayload> => {
    dispatch({ type: 'AUTH_START' });
    try {
      const result = await authService.login(credentials);
      if (result.success && result.user) {
        dispatch({
          type: 'AUTH_SUCCESS',
          payload: {
            user: result.user,
            session: null! // Session is managed by HTTP-only cookies
          }
        });
      } else {
        const message = result.errorMessage || 'Login failed';
        dispatch({ type: 'AUTH_ERROR', payload: message });
      }
      return result;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Login failed';
      dispatch({ type: 'AUTH_ERROR', payload: message });
      throw error;
    }
  }, []);

  // Register function
  const register = useCallback(async (data: RegisterData): Promise<LoginPayload> => {
    dispatch({ type: 'AUTH_START' });
    try {
      const result = await authService.register(data);
      if (result.success && result.user) {
        dispatch({
          type: 'AUTH_SUCCESS',
          payload: {
            user: result.user,
            session: null! // Session is managed by HTTP-only cookies
          }
        });
      } else {
        const message = result.errorMessage || 'Registration failed';
        dispatch({ type: 'AUTH_ERROR', payload: message });
      }
      return result;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Registration failed';
      dispatch({ type: 'AUTH_ERROR', payload: message });
      throw error;
    }
  }, []);

  // Logout function
  const logout = useCallback(async (): Promise<void> => {
    try {
      await authService.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      dispatch({ type: 'AUTH_LOGOUT' });
    }
  }, []);

  // Logout all sessions function
  const logoutAllSessions = useCallback(async (): Promise<void> => {
    if (!state.user) {
      throw new Error('No user logged in');
    }

    try {
      await authService.logoutAllSessions();
    } catch (error) {
      console.error('Logout all sessions error:', error);
      throw error;
    } finally {
      dispatch({ type: 'AUTH_LOGOUT' });
    }
  }, [state.user]);

  // Refresh token function
  const refreshToken = useCallback(async (): Promise<boolean> => {
    try {
      const result = await authService.refreshToken();
      if (result.success && result.user) {
        dispatch({
          type: 'AUTH_SUCCESS',
          payload: {
            user: result.user,
            session: null! // Session is managed by HTTP-only cookies
          }
        });
      }
      return result.success;
    } catch (error) {
      console.error('Token refresh error:', error);
      dispatch({ type: 'AUTH_LOGOUT' });
      return false;
    }
  }, []);

  // Update profile function
  const updateProfile = useCallback(async (data: UpdateUserRequest): Promise<User> => {
    if (!state.user) {
      throw new Error('No user logged in');
    }

    try {
      const updatedUser = await authService.updateProfile(data);
      dispatch({ type: 'UPDATE_USER', payload: updatedUser });
      return updatedUser;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Profile update failed';
      dispatch({ type: 'AUTH_ERROR', payload: message });
      throw error;
    }
  }, [state.user]);

  // Change password function
  const changePassword = useCallback(async (data: ChangePasswordRequest): Promise<void> => {
    if (!state.user) {
      throw new Error('No user logged in');
    }

    try {
      await authService.changePassword(data);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Password change failed';
      dispatch({ type: 'AUTH_ERROR', payload: message });
      throw error;
    }
  }, [state.user]);

  // Request password reset function
  const requestPasswordReset = useCallback(async (email: string): Promise<void> => {
    try {
      await authService.requestPasswordReset(email);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Password reset request failed';
      dispatch({ type: 'AUTH_ERROR', payload: message });
      throw error;
    }
  }, []);

  // Reset password function
  const resetPassword = useCallback(async (data: ResetPasswordRequest): Promise<void> => {
    try {
      await authService.resetPassword(data);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Password reset failed';
      dispatch({ type: 'AUTH_ERROR', payload: message });
      throw error;
    }
  }, []);

  // Get active sessions function
  const getActiveSessions = useCallback(async (): Promise<UserSession[]> => {
    if (!state.user) {
      throw new Error('No user logged in');
    }

    try {
      return await authService.getActiveSessions();
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to get active sessions';
      dispatch({ type: 'AUTH_ERROR', payload: message });
      throw error;
    }
  }, [state.user]);

  // End session function
  const endSession = useCallback(async (sessionId: string): Promise<void> => {
    try {
      await authService.endSession(sessionId);
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Failed to end session';
      dispatch({ type: 'AUTH_ERROR', payload: message });
      throw error;
    }
  }, []);

  // Has permission function
  const hasPermission = useCallback((resource: string, action: string, level: PermissionLevel = PermissionLevel.Site): boolean => {
    return authService.hasPermission(state.user, resource, action, level);
  }, [state.user]);

  // Clear error function
  const clearError = useCallback((): void => {
    dispatch({ type: 'CLEAR_ERROR' });
  }, []);

  const contextValue: AuthContextValue = {
    user: state.user,
    isAuthenticated: state.isAuthenticated,
    isLoading: state.isLoading,
    error: state.error,
    session: state.session,
    login,
    register,
    logout,
    logoutAllSessions,
    refreshToken,
    updateProfile,
    changePassword,
    requestPasswordReset,
    resetPassword,
    getActiveSessions,
    endSession,
    hasPermission,
    clearError,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = (): AuthContextValue => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Export types for external use
export type { AuthContextValue, AuthProviderProps };
