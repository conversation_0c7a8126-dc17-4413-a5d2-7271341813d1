import React, { createContext, useContext, useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';

interface LayoutContextValue {
  showSidebar: boolean;
  setShowSidebar: (show: boolean) => void;
  isFullWidth: boolean;
}

const LayoutContext = createContext<LayoutContextValue | null>(null);

interface LayoutProviderProps {
  children: React.ReactNode;
}

// Routes that should not show the sidebar
const SIDEBAR_FREE_ROUTES = [
  '/sites/new',
  '/login',
  '/register',
  '/forgot-password',
  '/reset-password',
  '/landing'
];

export const LayoutProvider: React.FC<LayoutProviderProps> = ({ children }) => {
  const location = useLocation();
  const [showSidebar, setShowSidebar] = useState(true);

  // Automatically determine sidebar visibility based on route
  useEffect(() => {
    const shouldHideSidebar = SIDEBAR_FREE_ROUTES.some(route => 
      location.pathname === route || location.pathname.startsWith(route + '/')
    );
    setShowSidebar(!shouldHideSidebar);
  }, [location.pathname]);

  const isFullWidth = !showSidebar;

  const contextValue: LayoutContextValue = {
    showSidebar,
    setShowSidebar,
    isFullWidth
  };

  return (
    <LayoutContext.Provider value={contextValue}>
      {children}
    </LayoutContext.Provider>
  );
};

export const useLayout = (): LayoutContextValue => {
  const context = useContext(LayoutContext);
  if (!context) {
    throw new Error('useLayout must be used within a LayoutProvider');
  }
  return context;
};
