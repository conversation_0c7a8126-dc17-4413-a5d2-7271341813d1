import { useAuth } from './useAuthContext';
import { PermissionLevel } from '../types/auth';

export const usePermissions = () => {
  const { user, hasPermission } = useAuth();

  // Helper functions for common permission checks
  const canCreateWorkers = (level: PermissionLevel = PermissionLevel.Site) => 
    hasPermission('Workers', 'Create', level);

  const canReadWorkers = (level: PermissionLevel = PermissionLevel.Site) => 
    hasPermission('Workers', 'Read', level);

  const canUpdateWorkers = (level: PermissionLevel = PermissionLevel.Site) => 
    hasPermission('Workers', 'Update', level);

  const canDeleteWorkers = (level: PermissionLevel = PermissionLevel.Site) => 
    hasPermission('Workers', 'Delete', level);

  const canCreateSites = (level: PermissionLevel = PermissionLevel.Company) => 
    hasPermission('Sites', 'Create', level);

  const canReadSites = (level: PermissionLevel = PermissionLevel.Site) => 
    hasPermission('Sites', 'Read', level);

  const canUpdateSites = (level: PermissionLevel = PermissionLevel.Site) => 
    hasPermission('Sites', 'Update', level);

  const canDeleteSites = (level: PermissionLevel = PermissionLevel.Company) => 
    hasPermission('Sites', 'Delete', level);

  const canCreateTrainings = (level: PermissionLevel = PermissionLevel.Site) => 
    hasPermission('Trainings', 'Create', level);

  const canReadTrainings = (level: PermissionLevel = PermissionLevel.Site) => 
    hasPermission('Trainings', 'Read', level);

  const canUpdateTrainings = (level: PermissionLevel = PermissionLevel.Site) => 
    hasPermission('Trainings', 'Update', level);

  const canDeleteTrainings = (level: PermissionLevel = PermissionLevel.Site) => 
    hasPermission('Trainings', 'Delete', level);

  const canCreateDocs = (level: PermissionLevel = PermissionLevel.Site) => 
    hasPermission('Docs', 'Create', level);

  const canReadDocs = (level: PermissionLevel = PermissionLevel.Site) => 
    hasPermission('Docs', 'Read', level);

  const canUpdateDocs = (level: PermissionLevel = PermissionLevel.Site) => 
    hasPermission('Docs', 'Update', level);

  const canDeleteDocs = (level: PermissionLevel = PermissionLevel.Site) => 
    hasPermission('Docs', 'Delete', level);

  const canCreatePPE = (level: PermissionLevel = PermissionLevel.Site) => 
    hasPermission('PPE', 'Create', level);

  const canReadPPE = (level: PermissionLevel = PermissionLevel.Site) => 
    hasPermission('PPE', 'Read', level);

  const canUpdatePPE = (level: PermissionLevel = PermissionLevel.Site) => 
    hasPermission('PPE', 'Update', level);

  const canDeletePPE = (level: PermissionLevel = PermissionLevel.Site) => 
    hasPermission('PPE', 'Delete', level);

  const canManageRoles = (level: PermissionLevel = PermissionLevel.Company) => 
    hasPermission('RoleManagement', 'Create', level) || 
    hasPermission('RoleManagement', 'Update', level) || 
    hasPermission('RoleManagement', 'Delete', level);

  const canReadRoles = (level: PermissionLevel = PermissionLevel.Site) => 
    hasPermission('RoleManagement', 'Read', level);

  const canViewReports = (level: PermissionLevel = PermissionLevel.Site) => 
    hasPermission('Reports', 'Read', level);

  const canCreateReports = (level: PermissionLevel = PermissionLevel.Site) => 
    hasPermission('Reports', 'Create', level);

  // Role-based checks
  const isAdmin = () => user?.role.name === 'Administrator';
  const isSiteSafetyOfficer = () => user?.role.name === 'Site Safety Officer';
  const isSiteAgent = () => user?.role.name === 'Site Agent';
  const isWorker = () => user?.role.name === 'Worker';

  // Combined permission checks
  const canManageUsers = () => canManageRoles() || isAdmin();
  const canAccessAdminFeatures = () => isAdmin();
  const canManageSite = () => isSiteAgent() || isSiteSafetyOfficer() || isAdmin();

  return {
    user,
    hasPermission,
    
    // Worker permissions
    canCreateWorkers,
    canReadWorkers,
    canUpdateWorkers,
    canDeleteWorkers,
    
    // Site permissions
    canCreateSites,
    canReadSites,
    canUpdateSites,
    canDeleteSites,
    
    // Training permissions
    canCreateTrainings,
    canReadTrainings,
    canUpdateTrainings,
    canDeleteTrainings,
    
    // Document permissions
    canCreateDocs,
    canReadDocs,
    canUpdateDocs,
    canDeleteDocs,
    
    // PPE permissions
    canCreatePPE,
    canReadPPE,
    canUpdatePPE,
    canDeletePPE,
    
    // Role management permissions
    canManageRoles,
    canReadRoles,
    
    // Report permissions
    canViewReports,
    canCreateReports,
    
    // Role checks
    isAdmin,
    isSiteSafetyOfficer,
    isSiteAgent,
    isWorker,
    
    // Combined checks
    canManageUsers,
    canAccessAdminFeatures,
    canManageSite,
  };
};

export default usePermissions;
