import { useCallback, useEffect } from 'react';

interface UseStepNavigationProps {
  currentStep: number;
  setCurrentStep: (step: number) => void;
  totalSteps: number;
  enableKeyboardNavigation?: boolean;
}

export const useStepNavigation = ({
  currentStep,
  setCurrentStep,
  totalSteps,
  enableKeyboardNavigation = true
}: UseStepNavigationProps) => {
  
  const handleNextStep = useCallback(() => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  }, [currentStep, totalSteps, setCurrentStep]);

  const handlePreviousStep = useCallback(() => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  }, [currentStep, setCurrentStep]);

  const canGoNext = currentStep < totalSteps;
  const canGoPrevious = currentStep > 1;
  const isFirstStep = currentStep === 1;
  const isLastStep = currentStep === totalSteps;

  // Keyboard navigation
  useEffect(() => {
    if (!enableKeyboardNavigation) return;

    const onKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'ArrowRight' && canGoNext) {
        e.preventDefault();
        handleNextStep();
      } else if (e.key === 'ArrowLeft' && canGoPrevious) {
        e.preventDefault();
        handlePreviousStep();
      }
    };

    window.addEventListener('keydown', onKeyDown);
    return () => window.removeEventListener('keydown', onKeyDown);
  }, [handleNextStep, handlePreviousStep, canGoNext, canGoPrevious, enableKeyboardNavigation]);

  return {
    handleNextStep,
    handlePreviousStep,
    canGoNext,
    canGoPrevious,
    isFirstStep,
    isLastStep
  };
};
