import React, { useState, useEffect, createContext, useContext, ReactNode } from 'react';

// Simple Tenant interface to avoid circular dependencies
interface Tenant {
  id: string;
  name: string;
  subdomain: string;
  subscriptionPlan: 'basic' | 'professional' | 'enterprise';
  maxSites: number;
  status: 'active' | 'inactive' | 'suspended';
  createdAt: string;
  updatedAt?: string;
}

// Tenant Context Interface
interface TenantContextValue {
  currentTenant: Tenant | null;
  tenantId: string | null;
  isLoading: boolean;
  error: Error | null;
  switchTenant: (tenantId: string) => Promise<void>;
  refreshTenant: () => Promise<void>;
}

// Create Tenant Context
const TenantContext = createContext<TenantContextValue | null>(null);

// Tenant Provider Props
interface TenantProviderProps {
  children: ReactNode;
  defaultTenantId?: string;
}

// Tenant Provider Component
export const TenantProvider: React.FC<TenantProviderProps> = ({ 
  children, 
  defaultTenantId = 'tenant-1' // Default to first tenant for demo
}) => {
  const [currentTenant, setCurrentTenant] = useState<Tenant | null>(null);
  const [tenantId, setTenantId] = useState<string | null>(defaultTenantId);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Load tenant data (simplified for now)
  const loadTenant = async (id: string) => {
    setIsLoading(true);
    setError(null);

    try {
      // Mock tenant data for now to avoid circular dependencies
      const mockTenant: Tenant = {
        id: id,
        name: id === 'tenant-1' ? 'ABC Construction Ltd' : 'XYZ Contractors',
        subdomain: id === 'tenant-1' ? 'abc-construction' : 'xyz-contractors',
        subscriptionPlan: 'enterprise',
        maxSites: 50,
        status: 'active',
        createdAt: '2024-01-01T00:00:00Z',
        updatedAt: '2025-01-15T10:30:00Z'
      };

      setCurrentTenant(mockTenant);
      setTenantId(id);
      // Store in localStorage for persistence
      localStorage.setItem('currentTenantId', id);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to load tenant'));
      setCurrentTenant(null);
      setTenantId(null);
    } finally {
      setIsLoading(false);
    }
  };

  // Switch to a different tenant
  const switchTenant = async (newTenantId: string) => {
    await loadTenant(newTenantId);
  };

  // Refresh current tenant data
  const refreshTenant = async () => {
    if (tenantId) {
      await loadTenant(tenantId);
    }
  };

  // Load initial tenant on mount
  useEffect(() => {
    // Check localStorage for persisted tenant
    const storedTenantId = localStorage.getItem('currentTenantId');
    const initialTenantId = storedTenantId || defaultTenantId;
    
    if (initialTenantId) {
      loadTenant(initialTenantId);
    }
  }, [defaultTenantId]);

  const contextValue: TenantContextValue = {
    currentTenant,
    tenantId,
    isLoading,
    error,
    switchTenant,
    refreshTenant
  };

  return (
    <TenantContext.Provider value={contextValue}>
      {children}
    </TenantContext.Provider>
  );
};

// Custom hook to use tenant context
export const useTenantContext = (): TenantContextValue => {
  const context = useContext(TenantContext);
  if (!context) {
    throw new Error('useTenantContext must be used within a TenantProvider');
  }
  return context;
};

// Helper hook to get tenant context for GraphQL operations
export const useTenantGraphQLContext = () => {
  const { tenantId, currentTenant } = useTenantContext();
  
  return {
    tenantId: tenantId || '',
    tenantContext: {
      tenantId: tenantId || '',
      userId: 'current-user' // In real app, this would come from auth
    },
    isValidTenant: !!tenantId && !!currentTenant
  };
};

// Helper hook for tenant-aware data fetching
export const useTenantAwareQuery = () => {
  const { tenantContext, isValidTenant } = useTenantGraphQLContext();
  
  // Helper to add tenant context to GraphQL variables
  const withTenantContext = <T extends Record<string, any>>(variables: T) => ({
    ...variables,
    tenantId: tenantContext.tenantId
  });
  
  // Helper to check if query should be skipped due to missing tenant
  const shouldSkipQuery = !isValidTenant;
  
  return {
    tenantContext,
    withTenantContext,
    shouldSkipQuery,
    isValidTenant
  };
};

// Helper hook for site-specific operations with tenant context
export const useTenantSiteContext = () => {
  const { tenantId } = useTenantContext();

  // Get site context from URL or state
  const getSiteContext = (siteId: string | undefined) => {
    if (!tenantId || !siteId) {
      return null;
    }

    return {
      tenantId,
      siteId,
      isValidSiteContext: true
    };
  };

  return {
    getSiteContext,
    tenantId
  };
};

// Type guard to check if tenant context is valid
export const isValidTenantContext = (
  context: any
): context is { tenantId: string; userId: string } => {
  return context && 
         typeof context.tenantId === 'string' && 
         context.tenantId.length > 0;
};

// Helper to get mock GraphQL client with tenant context
export const useTenantGraphQLClient = () => {
  const { tenantContext, isValidTenant } = useTenantGraphQLContext();

  const executeQuery = async (
    queryFn: (context: typeof tenantContext) => Promise<any>
  ): Promise<any> => {
    if (!isValidTenant) {
      throw new Error('Invalid tenant context');
    }

    return await queryFn(tenantContext);
  };

  return {
    executeQuery,
    tenantContext,
    isValidTenant
  };
};

// Export types for external use
export type { TenantContextValue, TenantProviderProps };
