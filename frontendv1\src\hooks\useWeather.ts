import { useState, useEffect, useCallback, useRef } from 'react';
import { WeatherData, WeatherHookResult } from '../types/weather';
import { weatherService } from '../services/weatherService';
import { useAllSites } from './useSiteContext';

interface UseWeatherOptions {
  autoRefresh?: boolean;
  refreshInterval?: number; // minutes
  enableNotifications?: boolean;
}

export const useWeather = (options: UseWeatherOptions = {}): WeatherHookResult => {
  const {
    autoRefresh = true,
    refreshInterval = 30, // 30 minutes default
    enableNotifications = true
  } = options;

  const [weatherData, setWeatherData] = useState<WeatherData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const intervalRef = useRef<number | null>(null);
  const allSites = useAllSites();

  const fetchWeatherData = useCallback(async () => {
    try {
      setError(null);
      
      // Get cached data first for immediate display
      const cachedData = weatherService.getCachedWeatherData();
      if (cachedData.length > 0) {
        setWeatherData(cachedData);
        setLoading(false);
      }

      // Prepare sites data for weather service
      const sitesForWeather = allSites.map(site => ({
        id: site.id,
        name: site.name,
        location: site.location
      }));

      if (sitesForWeather.length === 0) {
        setLoading(false);
        return;
      }

      // Fetch fresh weather data
      const freshWeatherData = await weatherService.getWeatherForMultipleSites(sitesForWeather);
      setWeatherData(freshWeatherData);
      
      // Check for weather alerts and notify if enabled
      if (enableNotifications) {
        checkForWeatherAlerts(freshWeatherData);
      }
      
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, [allSites, enableNotifications]);

  const checkForWeatherAlerts = useCallback((data: WeatherData[]) => {
    data.forEach(siteWeather => {
      if (siteWeather.alerts && siteWeather.alerts.length > 0) {
        // In a real app, this would integrate with the notification system
        // Reduced logging for cleaner console
      }

      // Check safety conditions
      const safetyCheck = weatherService.isWeatherSafeForWork(siteWeather);
      if (!safetyCheck.safe) {
        // Reduced logging for cleaner console
      }
    });
  }, []);

  const refetch = useCallback(async () => {
    setLoading(true);
    await fetchWeatherData();
  }, [fetchWeatherData]);

  const getWeatherForSite = useCallback((siteId: string): WeatherData | undefined => {
    return weatherData.find(data => data.siteId === siteId);
  }, [weatherData]);

  // Initial fetch
  useEffect(() => {
    if (allSites.length > 0) {
      fetchWeatherData();
    }
  }, [allSites.length]); // Only depend on the length to avoid infinite loops

  // Auto-refresh setup
  useEffect(() => {
    if (autoRefresh && refreshInterval > 0 && allSites.length > 0) {
      intervalRef.current = setInterval(() => {
        fetchWeatherData();
      }, refreshInterval * 60 * 1000) as unknown as number;

      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      };
    }
  }, [autoRefresh, refreshInterval, allSites.length]); // Remove fetchWeatherData dependency

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  return {
    weatherData,
    loading,
    error,
    refetch,
    getWeatherForSite
  };
};

// Hook for getting weather data for a specific site
export const useSiteWeather = (siteId: string) => {
  const { weatherData, loading, error, refetch } = useWeather();
  
  const siteWeather = weatherData.find(data => data.siteId === siteId);
  
  return {
    weather: siteWeather,
    loading,
    error,
    refetch
  };
};

// Hook for weather safety checks
export const useWeatherSafety = (siteId?: string) => {
  const { weatherData } = useWeather();
  
  const getSafetyStatus = useCallback((targetSiteId?: string) => {
    if (targetSiteId) {
      const siteWeather = weatherData.find(data => data.siteId === targetSiteId);
      if (siteWeather) {
        return weatherService.isWeatherSafeForWork(siteWeather);
      }
    }
    
    // Return overall safety status for all sites
    const allSafetyChecks = weatherData.map(weather => 
      weatherService.isWeatherSafeForWork(weather)
    );
    
    const allWarnings = allSafetyChecks.flatMap(check => check.warnings);
    const allSafe = allSafetyChecks.every(check => check.safe);
    
    return {
      safe: allSafe,
      warnings: allWarnings
    };
  }, [weatherData]);
  
  return {
    getSafetyStatus: () => getSafetyStatus(siteId),
    getAllSitesSafetyStatus: () => getSafetyStatus()
  };
};
