import { createContext, useContext, useState, type ReactNode } from "react";
import { type UserType } from "../types/auth";

type UserProviderType = {
	children: ReactNode;
};
type UserContextType = {
	user: UserType | null;
	setUser: (user: UserType | null) => void;
};

const UserContext = createContext<UserContextType | null>(null);

export function UserProvider({ children }: UserProviderType) {
	const [user, setUser] = useState<UserType | null>(null);
	return <UserContext.Provider value={{ user, setUser }}>{children}</UserContext.Provider>;
}
export const useUser = () => useContext(UserContext);
