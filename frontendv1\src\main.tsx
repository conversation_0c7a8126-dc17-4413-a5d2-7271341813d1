import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import {
	ApolloClient,
	InM<PERSON>oryCache,
	ApolloProvider,
	from,
	split
} from "@apollo/client";
import { onError } from "@apollo/client/link/error";
import { GraphQLWsLink } from '@apollo/client/link/subscriptions';
import { createClient } from 'graphql-ws';
import { getMainDefinition } from '@apollo/client/utilities';
// import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import App from "./App.tsx";
import "./index.css";
import { createUploadLinkWithFormData } from "./utils/graphql-upload/createCustomUploadLink.ts";
import { ToastContainer } from "react-toastify"; 
// const uri = import.meta.env.VITE_GRAPHQL_URI_1 || "http://localhost:3000/graphql";

// Error handling link
const errorLink = onError(({ graphQLErrors, networkError }) => {
  if (graphQLErrors)
    graphQLErrors.forEach(({ message, locations, path }) =>
      console.log(
        `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`
      )
    );
  if (networkError) {
    console.log(`[Network error]: ${networkError}`);
    // Don't throw error for connection refused - just log it
    if (networkError.message.includes('fetch')) {
      console.log('GraphQL server not available - using mock data');
    }
  }
});

// Create a fallback link for when the main server is not available
// const fallbackLink = new HttpLink({
//   uri: 'http://localhost:3000/graphql', // Fallback to a mock endpoint
// });

const httpUri = import.meta.env.VITE_GRAPHQL_URI_1;
const wsUri = import.meta.env.VITE_GRAPHQL_WS_URI || httpUri?.replace('http', 'ws');

if (!httpUri) throw new Error("GraphQL endpoint is not defined.");

// Create HTTP link for queries and mutations
const httpLink = createUploadLinkWithFormData(httpUri);

// Create WebSocket link for subscriptions
const wsLink = new GraphQLWsLink(createClient({
	url: wsUri,
	connectionParams: () => {
		const token = localStorage.getItem('authToken');
		return {
			authorization: token ? `Bearer ${token}` : '',
		};
	},
}));

// Split link to route operations to appropriate transport
const splitLink = split(
	({ query }) => {
		const definition = getMainDefinition(query);
		return (
			definition.kind === 'OperationDefinition' &&
			definition.operation === 'subscription'
		);
	},
	wsLink,
	httpLink,
);

const client = new ApolloClient({
	link: from([
    errorLink,
    splitLink
  ]),
	cache: new InMemoryCache(),
	defaultOptions: {
		watchQuery: {
			errorPolicy: 'all',
		},
		query: {
			errorPolicy: 'all',
		},
	},
});

createRoot(document.getElementById("root")!).render(
	<StrictMode>
		<ApolloProvider client={client}>
			<App />
			<ToastContainer position="top-right" autoClose={3000} />
		</ApolloProvider>
	</StrictMode>,
);
