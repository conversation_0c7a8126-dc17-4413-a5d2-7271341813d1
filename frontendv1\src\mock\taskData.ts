import { SiteTask } from '../types/tasks';
import { SiteInfo } from '../types';

// Consistent mock site data used across all task-related pages
export const mockSite: SiteInfo = {
  id: "site-1",
  name: "Downtown Construction Site",
  location: "Nairobi, Kenya",
  projectManager: "<PERSON>",
  healthStatus: "green",
  workersOnSite: 45,
  activePermits: 8,
  openIncidents: 0,
  timeline: "Jan 2025 - Dec 2026",
  currentPhase: "Construction",
  progressPercentage: 65,
  tenantId: '',
  status: 'active',
  createdAt: new Date('2024-09-01T00:00:00Z')
};

// Mock task data for task detail page
export const mockTaskDetail: SiteTask = {
  id: "task-2",
  siteId: "site-1",
  templateId: 'template-1',
  status: 'approved',
  category: 'excavation',
  name: 'Excavation - Trench Installation',
  description: 'Excavate trench for utility installation in Zone A',
  workDescription: 'Dig 2m deep trench along the north wall for electrical conduit installation. Ensure proper shoring and safety measures.',
  location: 'Zone A - North Wall',
  plannedStartDate: new Date('2024-01-20T08:00:00'),
  plannedEndDate: new Date('2024-01-20T16:00:00'),
  estimatedDuration: 8,
  hazards: [
    {
      id: 'h1',
      description: 'Underground utilities',
      riskLevel: 'high',
      likelihood: 3,
      severity: 4,
      riskScore: 12,
      controlMeasures: ['c1']
    },
    {
      id: 'h2',
      description: 'Cave-in risk',
      riskLevel: 'critical',
      likelihood: 2,
      severity: 5,
      riskScore: 10,
      controlMeasures: ['c2']
    }
  ],
  controlMeasures: [
    {
      id: 'c1',
      description: 'Call 811 before digging',
      type: 'administrative',
      effectiveness: 5,
      implementationCost: 'low',
      trainingRequired: false,
      equipmentRequired: []
    },
    {
      id: 'c2',
      description: 'Proper shoring/sloping',
      type: 'engineering',
      effectiveness: 4,
      implementationCost: 'medium',
      trainingRequired: true,
      equipmentRequired: ['shoring-equipment']
    }
  ],
  riskLevel: 'high',
  attachedDocuments: [
    {
      id: 'd1',
      name: 'Equipment Certificate - Excavator-XC200',
      type: 'other',
      url: '/documents/cert-exc-200.pdf',
      version: '1.0',
      uploadedBy: 'john.smith',
      uploadedAt: new Date('2024-01-15'),
      isRequired: true
    },
    {
      id: 'd2',
      name: 'Operator Competency - John Smith',
      type: 'other',
      url: '/documents/operator-cert.pdf',
      version: '1.0',
      uploadedBy: 'jane.doe',
      uploadedAt: new Date('2024-01-16'),
      isRequired: true
    },
    {
      id: 'd3',
      name: 'RAMS-EXC-001.pdf',
      type: 'rams',
      url: '/documents/rams-exc-001.pdf',
      version: '2.1',
      uploadedBy: 'safety.officer',
      uploadedAt: new Date('2024-01-18'),
      isRequired: true
    }
  ],
  linkedPermits: ['SP-2024-045'],
  requiredDocuments: [],
  createdBy: 'engineer-1',
  createdByName: 'Site Engineer',
  assignedWorkers: [],
  assignedEquipment: [],
  createdAt: new Date('2024-01-15'),
  updatedAt: new Date('2024-01-18'),
  progressPercentage: 0,
  priority: 'high',
  tags: ['excavation', 'utilities', 'zone-a']
};

// Mock tasks for different task IDs
export const getMockTask = (taskId: string): SiteTask => {
  const baseTask = { ...mockTaskDetail };
  
  switch (taskId) {
    case 'task-1':
      return {
        ...baseTask,
        id: 'task-1',
        name: 'Foundation Excavation',
        description: 'Excavate foundation area for main building',
        workDescription: 'Excavate foundation area to 3m depth for main building structure. Install temporary shoring.',
        location: 'Main Building - Foundation Area',
        status: 'in-progress',
        priority: 'critical',
        tags: ['excavation', 'foundation', 'main-building']
      };
    case 'task-3':
      return {
        ...baseTask,
        id: 'task-3',
        name: 'Electrical Wiring - Ground Floor',
        description: 'Install electrical wiring for ground floor',
        workDescription: 'Install electrical conduits and wiring for ground floor lighting and power outlets.',
        location: 'Ground Floor - All Zones',
        category: 'electrical-installation',
        status: 'completed',
        priority: 'medium',
        tags: ['electrical', 'wiring', 'ground-floor']
      };
    default:
      return baseTask;
  }
};
