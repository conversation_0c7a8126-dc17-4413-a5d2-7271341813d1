import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useMutation, useQuery } from '@apollo/client';
import { toast } from 'react-toastify';
import {
  CheckCircle,
  XCircle,
  Eye,
  Calendar,
  User,
  Users,
  Clock,
  AlertTriangle,
  Shield,
  FileText,
  ClipboardCheck,
  Save,
  X,
  MessageSquare
} from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import { APPROVE_JOB, DISAPPROVE_JOB } from '../graphql/mutations';
import { GET_PENDING_APPROVAL_JOBS } from '../graphql/queries';

interface Job {
  id: number;
  title: string;
  description: string;
  status: string;
  requiredPermits: string[];
  timeForCompletion: string;
  startDate: string;
  requestedBy: {
    id: number;
    name: string;
  };
  reviewedBy: {
    id: number;
    name: string;
  };
  chiefEngineer: {
    id: number;
    name: string;
  };
  workers: Array<{
    id: number;
    name: string;
    company: string;
  }>;
  hazards: Array<{
    id: number;
    description: string;
    controlMeasures: Array<{
      id: number;
      description: string;
      closed: boolean;
    }>;
  }>;
  documents: Array<{
    id: number;
    name: string;
    url: string;
  }>;
  requestedDate: string;
  reviewedDate: string;
  createdAt: string;
}

interface TaskApproval {
  jobId: number;
  action: 'approve' | 'disapprove' | null;
  comment: string;
}

interface ApproveJobInput {
  jobId: number;
  approvedById: number;
  comments: string; // Required field in GraphQL schema
}

interface DisapproveJobInput {
  jobId: number;
  disapprovedById: number;
  comments: string; // Required field in GraphQL schema
}

const PERMIT_LABELS = {
  GENERAL_WORK_PERMIT: 'General Work Permit',
  HOT_WORK_PERMIT: 'Hot Work Permit',
  CONFINED_SPACE_ENTRY_PERMIT: 'Confined Space Entry Permit',
  WORK_AT_HEIGHT_PERMIT: 'Work at Height Permit',
  EXCAVATION_PERMIT: 'Excavation Permit'
};

// Comment Modal Component - moved outside to avoid recreation on every render
interface CommentModalProps {
  modalData: { jobId: number; action: 'approve' | 'disapprove' };
  onClose: () => void;
  taskApprovals: Record<number, TaskApproval>;
  jobs: Job[];
  updateTaskComment: (jobId: number, comment: string) => void;
  handleApproveTask: (jobId: number, comment?: string) => void;
  handleDisapproveTask: (jobId: number, comment: string) => void;
  isSubmitting: boolean;
}

const CommentModal: React.FC<CommentModalProps> = ({
  modalData,
  onClose,
  taskApprovals,
  jobs,
  updateTaskComment,
  handleApproveTask,
  handleDisapproveTask,
  isSubmitting
}) => {
  const { jobId, action } = modalData;
  const approval = taskApprovals[jobId];
  const job = jobs.find(j => j.id === jobId);
  const isApprove = action === 'approve';

  const handleSubmit = () => {
    if (isApprove) {
      handleApproveTask(jobId, approval?.comment || '');
    } else {
      handleDisapproveTask(jobId, approval?.comment || '');
    }
  };

  // Comments are now required for both approve and disapprove per GraphQL schema
  const canSubmit = approval?.comment?.trim();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <h3 className="text-lg font-medium text-gray-900 mb-4">
          {isApprove ? 'Approve' : 'Disapprove'} Task: {job?.title}
        </h3>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            {isApprove ? 'Approval comments' : 'Reason for disapproval'}
            <span className="text-red-500"> *</span>
          </label>
          <textarea
            value={approval?.comment || ''}
            onChange={(e) => updateTaskComment(jobId, e.target.value)}
            className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 ${
              isApprove
                ? 'focus:ring-green-500 focus:border-green-500'
                : 'focus:ring-red-500 focus:border-red-500'
            }`}
            rows={4}
            placeholder={
              isApprove
                ? 'Please provide comments about this approval...'
                : 'Please provide a detailed reason for disapproving this task...'
            }
          />
        </div>
        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={!canSubmit || isSubmitting}
            className={`px-4 py-2 text-white rounded-md transition-colors ${
              isApprove
                ? 'bg-green-600 hover:bg-green-700 disabled:bg-green-400'
                : 'bg-red-600 hover:bg-red-700 disabled:bg-red-400'
            }`}
          >
            {isSubmitting
              ? (isApprove ? 'Approving...' : 'Disapproving...')
              : (isApprove ? 'Approve Task' : 'Disapprove Task')
            }
          </button>
        </div>
      </div>
    </div>
  );
};

const ApproveTaskPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId } = useParams<{ siteId: string }>();

  const [taskApprovals, setTaskApprovals] = useState<Record<number, TaskApproval>>({});
  const [expandedTasks, setExpandedTasks] = useState<Set<number>>(new Set());
  const [commentModalOpen, setCommentModalOpen] = useState<{ jobId: number; action: 'approve' | 'disapprove' } | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const { data: jobsData, loading: jobsLoading, refetch } = useQuery(GET_PENDING_APPROVAL_JOBS);

  const [approveJob] = useMutation(APPROVE_JOB, {
    onCompleted: () => {
      toast.success('Task approved successfully!');
      refetch();
    },
    onError: (error) => {
      toast.error(`Failed to approve task: ${error.message}`);
    }
  });

  const [disapproveJob] = useMutation(DISAPPROVE_JOB, {
    onCompleted: () => {
      toast.success('Task disapproved successfully!');
      refetch();
    },
    onError: (error) => {
      toast.error(`Failed to disapprove task: ${error.message}`);
    }
  });

  const jobs: Job[] = jobsData?.pendingApprovalJobs || [];

  const toggleTaskExpansion = (jobId: number) => {
    setExpandedTasks(prev => {
      const newSet = new Set(prev);
      if (newSet.has(jobId)) {
        newSet.delete(jobId);
      } else {
        newSet.add(jobId);
      }
      return newSet;
    });
  };

  const openCommentModal = (jobId: number, action: 'approve' | 'disapprove') => {
    setCommentModalOpen({ jobId, action });
    setTaskApprovals(prev => ({
      ...prev,
      [jobId]: { jobId, action, comment: prev[jobId]?.comment || '' }
    }));
  };

  const updateTaskComment = (jobId: number, comment: string) => {
    setTaskApprovals(prev => ({
      ...prev,
      [jobId]: { ...prev[jobId], comment }
    }));
  };

  const handleApproveTask = async (jobId: number, comment: string = '') => {
    setIsSubmitting(true);
    try {
      await approveJob({
        variables: {
          input: {
            jobId,
            approvedById: 1, // Using 1 as per requirement
            comments: comment || 'Approved' // Required field - provide default if empty
          }
        }
      });

      // Remove from approvals after successful submission
      setTaskApprovals(prev => {
        const newApprovals = { ...prev };
        delete newApprovals[jobId];
        return newApprovals;
      });
      setCommentModalOpen(null);
    } catch (error) {
      console.error('Error approving task:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDisapproveTask = async (jobId: number, comment: string) => {
    if (!comment.trim()) {
      toast.warning('Please provide a comment for disapproval.');
      return;
    }

    setIsSubmitting(true);
    try {
      await disapproveJob({
        variables: {
          input: {
            jobId,
            disapprovedById: 1, // Using 1 as per requirement
            comments: comment // Required field
          }
        }
      });

      // Remove from approvals after successful submission
      setTaskApprovals(prev => {
        const newApprovals = { ...prev };
        delete newApprovals[jobId];
        return newApprovals;
      });
      setCommentModalOpen(null);
    } catch (error) {
      console.error('Error disapproving task:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const breadcrumbs = [
    { name: 'Dashboard', path: '/' },
    { name: 'Tasks', path: `/sites/${siteId}/tasks` },
    { name: 'Approve Tasks', path: `/sites/${siteId}/tasks/approve` }
  ];

  if (jobsLoading) {
    return (
      <FloatingCard title="Approve Tasks" breadcrumbs={breadcrumbs}>
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </FloatingCard>
    );
  }

  return (
    <FloatingCard title="Approve Tasks" breadcrumbs={breadcrumbs}>
      <div className="space-y-6">
        {jobs.length === 0 ? (
          <div className="text-center py-12">
            <CheckCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Tasks Pending Approval</h3>
            <p className="text-gray-500">There are currently no tasks waiting for approval.</p>
          </div>
        ) : (
          <>
            <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <ClipboardCheck className="h-5 w-5 text-blue-400" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-blue-800">
                    Task Approval Instructions
                  </h3>
                  <div className="mt-2 text-sm text-blue-700">
                    <p>
                      Review each task below and decide whether to approve or disapprove it.
                      You can expand tasks to see detailed information including hazards, control measures,
                      documents, and required permits. Each task can be approved or disapproved individually.
                      Comments are required for disapproval.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {jobs.map((job) => {
              const isExpanded = expandedTasks.has(job.id);

              return (
                <div key={job.id} className="border border-gray-200 rounded-lg overflow-hidden">
                  <div className="px-6 py-4 bg-white">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-4">
                          <h3 className="text-lg font-medium text-gray-900">{job.title}</h3>
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                            <Clock className="h-3 w-3 mr-1" />
                            Pending Approval
                          </span>
                        </div>
                        <div className="mt-2 flex items-center space-x-6 text-sm text-gray-500">
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-1" />
                            Requested by: {job.requestedBy?.name || 'Unknown'}
                          </div>
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-1" />
                            Reviewed by: {job.reviewedBy?.name || 'Unknown'}
                          </div>
                          <div className="flex items-center">
                            <User className="h-4 w-4 mr-1" />
                            Chief Engineer: {job.chiefEngineer?.name || 'Unknown'}
                          </div>
                          <div className="flex items-center">
                            <Calendar className="h-4 w-4 mr-1" />
                            Start: {new Date(job.startDate).toLocaleDateString()}
                          </div>
                          <div className="flex items-center">
                            <Clock className="h-4 w-4 mr-1" />
                            Duration: {job.timeForCompletion || 'Not specified'}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => toggleTaskExpansion(job.id)}
                          className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          {isExpanded ? 'Collapse' : 'View Details'}
                        </button>
                        <button
                          onClick={() => openCommentModal(job.id, 'approve')}
                          disabled={isSubmitting}
                          className="flex items-center px-4 py-2 text-sm font-medium bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-green-400 transition-colors"
                        >
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Approve
                        </button>
                        <button
                          onClick={() => openCommentModal(job.id, 'disapprove')}
                          disabled={isSubmitting}
                          className="flex items-center px-4 py-2 text-sm font-medium bg-red-600 text-white rounded-md hover:bg-red-700 disabled:bg-red-400 transition-colors"
                        >
                          <XCircle className="h-4 w-4 mr-2" />
                          Disapprove
                        </button>
                      </div>
                    </div>
                  </div>

                  {isExpanded && (
                    <div className="px-6 py-4 space-y-6 bg-white">
                      {/* Task Description */}
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Description</h4>
                        <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded-md">{job.description}</p>
                      </div>

                      {/* Workers */}

                      {/* Required Permits */}
                      {job.requiredPermits && job.requiredPermits.length > 0 && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 mb-2">
                            <ClipboardCheck className="h-4 w-4 inline mr-1" />
                            Required Permits
                          </h4>
                          <div className="flex flex-wrap gap-2">
                            {job.requiredPermits.map((permit) => (
                              <span
                                key={permit}
                                className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                              >
                                {PERMIT_LABELS[permit as keyof typeof PERMIT_LABELS] || permit}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Hazards and Control Measures */}
                      {job.hazards && job.hazards.length > 0 && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 mb-2">
                            <AlertTriangle className="h-4 w-4 inline mr-1" />
                            Hazards & Control Measures
                          </h4>
                          <div className="space-y-4">
                            {job.hazards.map((hazard) => (
                              <div key={hazard.id} className="border border-gray-200 rounded-md p-4">
                                <div className="mb-3">
                                  <h5 className="text-sm font-medium text-gray-900 mb-1">Hazard</h5>
                                  <p className="text-sm text-gray-700 bg-orange-50 p-2 rounded">{hazard.description}</p>
                                </div>
                                {hazard.controlMeasures && hazard.controlMeasures.length > 0 && (
                                  <div>
                                    <h5 className="text-sm font-medium text-gray-900 mb-2">
                                      <Shield className="h-4 w-4 inline mr-1" />
                                      Control Measures
                                    </h5>
                                    <ul className="space-y-1">
                                      {hazard.controlMeasures.map((measure) => (
                                        <li key={measure.id} className="flex items-start space-x-2">
                                          <div className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${
                                            measure.closed ? 'bg-green-500' : 'bg-yellow-500'
                                          }`}></div>
                                          <span className="text-sm text-gray-700">{measure.description}</span>
                                        </li>
                                      ))}
                                    </ul>
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Documents */}
                      {job.documents && job.documents.length > 0 && (
                        <div>
                          <h4 className="text-sm font-medium text-gray-900 mb-2">
                            <FileText className="h-4 w-4 inline mr-1" />
                            Documents
                          </h4>
                          <div className="space-y-2">
                            {job.documents.map((doc) => (
                              <div key={doc.id} className="flex items-center justify-between p-3 bg-gray-50 border border-gray-200 rounded-md">
                                <div className="flex items-center space-x-3">
                                  <FileText className="h-4 w-4 text-gray-500" />
                                  <span className="text-sm font-medium">{doc.name}</span>
                                </div>
                                <a
                                  href={doc.url}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  className="text-blue-600 hover:text-blue-800 text-sm"
                                >
                                  View
                                </a>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Timeline */}
                      <div>
                        <h4 className="text-sm font-medium text-gray-900 mb-2">Timeline</h4>
                        <div className="bg-gray-50 p-3 rounded-md text-sm">
                          <div className="flex justify-between items-center mb-2">
                            <span className="text-gray-600">Requested:</span>
                            <span className="font-medium">{new Date(job.requestedDate).toLocaleString()}</span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-gray-600">Reviewed:</span>
                            <span className="font-medium">{new Date(job.reviewedDate).toLocaleString()}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              );
            })}

            {/* Back Button */}
            <div className="flex justify-start pt-6 border-t border-gray-200">
              <button
                onClick={() => navigate(`/sites/${siteId}/tasks`)}
                className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
              >
                <X className="h-4 w-4 inline mr-2" />
                Back to Tasks
              </button>
            </div>
          </>
        )}
      </div>

      {/* Comment Modal */}
      {commentModalOpen && (
        <CommentModal
          modalData={commentModalOpen}
          onClose={() => setCommentModalOpen(null)}
          taskApprovals={taskApprovals}
          jobs={jobs}
          updateTaskComment={updateTaskComment}
          handleApproveTask={handleApproveTask}
          handleDisapproveTask={handleDisapproveTask}
          isSubmitting={isSubmitting}
        />
      )}
    </FloatingCard>
  );
};

export default ApproveTaskPage;
