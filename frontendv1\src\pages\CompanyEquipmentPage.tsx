import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import {
  LayoutDashboard,
  Package,
  Plus,
  Eye,
  UserCheck,
  Wrench,
  FileText,
  BarChart3
} from "lucide-react";
import FloatingCard from "../components/layout/FloatingCard";
import TabContainer, { Tab } from "../components/data/shared/TabContainer";
import CompanyEquipmentDashboard from "../components/equipment/CompanyEquipmentDashboard";
import CompanyEquipmentList from "../components/equipment/CompanyEquipmentList";
import CompanyEquipmentAdd from "../components/equipment/CompanyEquipmentAdd";
import CompanyEquipmentAssignments from "../components/equipment/CompanyEquipmentAssignments";
import CompanyEquipmentMaintenance from "../components/equipment/CompanyEquipmentMaintenance";
import CompanyEquipmentReports from "../components/equipment/CompanyEquipmentReports";

export default function CompanyEquipmentPage() {
  const location = useLocation();
  const [activeTab, setActiveTab] = useState("dashboard");
  const [initialSearchQuery, setInitialSearchQuery] = useState("");

  // Handle tab navigation and search parameters from URL
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const tab = searchParams.get("tab");
    const search = searchParams.get("search");

    if (tab) {
      setActiveTab(tab);
    }
    if (search) {
      setInitialSearchQuery(search);
      // If there's a search query, default to equipment tab
      if (!tab) {
        setActiveTab("equipment");
      }
    }
  }, [location]);

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    // Update URL without causing a page reload
    const newUrl = new URL(window.location.href);
    newUrl.searchParams.set("tab", tabId);
    window.history.pushState({}, "", newUrl.toString());
  };

  const tabs: Tab[] = [
    {
      id: "dashboard",
      label: "Dashboard",
      icon: <LayoutDashboard className="h-4 w-4" />,
      content: (
        <CompanyEquipmentDashboard
          onNavigateToTab={handleTabChange}
        />
      )
    },
    {
      id: "equipment",
      label: "Equipment",
      icon: <Package className="h-4 w-4" />,
      content: <CompanyEquipmentList initialSearchQuery={initialSearchQuery} />
    },
    {
      id: "add-import",
      label: "Add / Import Equipment",
      icon: <Plus className="h-4 w-4" />,
      content: <CompanyEquipmentAdd />
    },
    {
      id: "equipment-view",
      label: "Equipment View",
      icon: <Eye className="h-4 w-4" />,
      content: (
        <div className="p-6 text-center">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center">
            <Eye className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold mb-2">Equipment Detail View</h3>
          <p className="text-gray-600">
            Select an equipment item to view detailed information
          </p>
        </div>
      )
    },
    {
      id: "assignments",
      label: "Assignments",
      icon: <UserCheck className="h-4 w-4" />,
      content: <CompanyEquipmentAssignments />
    },
    {
      id: "maintenance",
      label: "Maintenance & Inspections",
      icon: <Wrench className="h-4 w-4" />,
      content: <CompanyEquipmentMaintenance />
    },
    {
      id: "templates",
      label: "Templates & Compliance",
      icon: <FileText className="h-4 w-4" />,
      content: (
        <div className="p-6 text-center">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center">
            <FileText className="w-8 h-8 text-gray-400" />
          </div>
          <h3 className="text-lg font-semibold mb-2">Templates & Compliance</h3>
          <p className="text-gray-600">
            Manage inspection templates and compliance requirements
          </p>
        </div>
      )
    },
    {
      id: "reports",
      label: "Reports / Exports",
      icon: <BarChart3 className="h-4 w-4" />,
      content: <CompanyEquipmentReports />
    }
  ];

  return (
    <FloatingCard>
      {/* Page Header */}
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900">Company Equipment Management</h1>
        <p className="text-gray-600 mt-1">
          Manage company-owned, rented, and contracted equipment across all sites
        </p>
      </div>

      {/* Tab Navigation */}
      <TabContainer
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={handleTabChange}
        variant="centered"
      />
    </FloatingCard>
  );
}
