import React, { useMemo, useState } from "react";
import { Link } from "react-router-dom";
import {
  Users,
  Search,
  Filter,
  Download,
  MapPin,
  CheckCircle,
  TrendingUp,
  Archive,
  Trash2,
  Plus,
} from "lucide-react";
import FloatingCard from "../components/layout/FloatingCard";
import UniversalFilterModal, { FilterValues } from "../components/common/UniversalFilterModal";
import ActiveFiltersBar from "../components/common/ActiveFiltersBar";
import { mockCompanyEquipment, getEquipmentKPIs } from "../data/equipmentMockData";

export default function CompanyEquipmentPage() {
  const [searchTerm, setSearchTerm] = useState("");
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [activeFilters, setActiveFilters] = useState<FilterValues>({});
  const [selectedIds, setSelectedIds] = useState<string[]>([]);

  // KPI cards (mirrors workers page pattern: 4 cards)
  const kpis = useMemo(() => getEquipmentKPIs(mockCompanyEquipment), []);

  // Dynamic filter config based on equipment data
  const filterConfig = useMemo(() => {
    const categories = Array.from(new Set(mockCompanyEquipment.map(e => e.category)));
    const sites = Array.from(new Set(mockCompanyEquipment.map(e => e.currentSiteId).filter(Boolean))) as string[];

    const categoryOptions = categories.map(cat => ({
      value: cat,
      label: cat,
      count: mockCompanyEquipment.filter(e => e.category === cat).length
    }));

    const siteOptions = [
      { value: "available", label: "Available (No Assignment)", count: mockCompanyEquipment.filter(e => !e.currentSiteId).length },
      ...sites.map(site => ({
        value: site,
        label: site,
        count: mockCompanyEquipment.filter(e => e.currentSiteId === site).length
      }))
    ];

    return [
      { id: "category", label: "Category", type: "multiselect" as const, options: categoryOptions },
      {
        id: "complianceStatus",
        label: "Compliance Status",
        type: "dropdown" as const,
        placeholder: "Select compliance status",
        options: [
          { value: "compliant", label: "Compliant", count: mockCompanyEquipment.filter(e => e.complianceStatus === "compliant").length },
          { value: "warning", label: "Warning", count: mockCompanyEquipment.filter(e => e.complianceStatus === "warning").length },
          { value: "critical", label: "Critical", count: mockCompanyEquipment.filter(e => e.complianceStatus === "critical").length },
          { value: "overdue", label: "Overdue", count: mockCompanyEquipment.filter(e => e.complianceStatus === "overdue").length },
        ]
      },
      {
        id: "site",
        label: "Current Site Assignment",
        type: "dropdown" as const,
        placeholder: "Select site",
        options: siteOptions
      },
      {
        id: "status",
        label: "Equipment Status",
        type: "multiselect" as const,
        options: [
          { value: "active", label: "Active", count: mockCompanyEquipment.filter(e => e.overallStatus === "active").length },
          { value: "maintenance", label: "In Maintenance", count: mockCompanyEquipment.filter(e => e.overallStatus === "maintenance").length },
          { value: "inactive", label: "Inactive", count: mockCompanyEquipment.filter(e => e.overallStatus === "inactive").length },
          { value: "retired", label: "Retired", count: mockCompanyEquipment.filter(e => e.overallStatus === "retired").length },
        ]
      }
    ];
  }, []);

  // Filtered list (mirror of workers pattern)
  const filteredEquipment = useMemo(() => {
    return mockCompanyEquipment.filter(eq => {
      const term = searchTerm.toLowerCase();
      const matchesSearch = !term ||
        eq.name.toLowerCase().includes(term) ||
        eq.equipmentNumber.toLowerCase().includes(term) ||
        (eq.model?.toLowerCase().includes(term));

      const matchesCategory = !activeFilters.category ||
        !Array.isArray(activeFilters.category) ||
        activeFilters.category.length === 0 ||
        activeFilters.category.some((c: string) => eq.category === c);

      const matchesCompliance = !activeFilters.complianceStatus ||
        eq.complianceStatus === activeFilters.complianceStatus;

      const matchesSite = !activeFilters.site ||
        (activeFilters.site === "available" && !eq.currentSiteId) ||
        eq.currentSiteId === activeFilters.site;

      const matchesStatus = !activeFilters.status ||
        !Array.isArray(activeFilters.status) ||
        activeFilters.status.length === 0 ||
        activeFilters.status.includes(eq.overallStatus);

      return matchesSearch && matchesCategory && matchesCompliance && matchesSite && matchesStatus;
    });
  }, [searchTerm, activeFilters]);

  // Selection helpers (same UX as workers)
  const isAllSelected = filteredEquipment.length > 0 && selectedIds.length === filteredEquipment.length;
  const isAnySelected = selectedIds.length > 0;
  const toggleSelectAll = () => {
    if (isAllSelected) setSelectedIds([]);
    else setSelectedIds(filteredEquipment.map(e => e.id));
  };
  const toggleSelectOne = (id: string) => {
    setSelectedIds(prev => prev.includes(id) ? prev.filter(x => x !== id) : [...prev, id]);
  };

  // Toolbar handlers
  const handleApplyFilters = (values: FilterValues) => setActiveFilters(values);
  const handleClearFilters = () => setActiveFilters({});

  // Active filter count
  const activeFilterCount = Object.values(activeFilters).filter(value => {
    if (Array.isArray(value)) return value.length > 0;
    if (typeof value === 'object' && value !== null) return Object.values(value).some(v => v !== '' && v !== null);
    return value !== '' && value !== null && value !== false;
  }).length;

  return (
    <FloatingCard title={`Equipment`} breadcrumbs={[{ name: 'Dashboard', path: '/' }, { name: 'Equipment', path: '/company-equipment' }] }>
      <div className="space-y-8">
        {/* KPI Summary (4 cards, styled like workers) */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Total Equipment</h3>
                <p className="text-2xl font-semibold mt-1">{kpis.totalEquipment}</p>
              </div>
              <div className="p-2 rounded-full bg-gray-50">
                <Users className="h-6 w-6 text-blue-500" />
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Assigned</h3>
                <p className="text-2xl font-semibold mt-1">{kpis.assignedEquipment}</p>
              </div>
              <div className="p-2 rounded-full bg-gray-50">
                <MapPin className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Compliant</h3>
                <p className="text-2xl font-semibold mt-1">{mockCompanyEquipment.filter(e => e.complianceStatus === 'compliant').length}</p>
              </div>
              <div className="p-2 rounded-full bg-gray-50">
                <CheckCircle className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Utilization</h3>
                <p className="text-2xl font-semibold mt-1">{kpis.utilizationRate}%</p>
              </div>
              <div className="p-2 rounded-full bg-gray-50">
                <TrendingUp className="h-6 w-6 text-indigo-500" />
              </div>
            </div>
          </div>
        </div>

        {/* Equipment Management Interface (copied styling) */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          {/* Toolbar */}
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
            <h2 className="text-lg font-semibold text-gray-900">Equipment</h2>

            <div className="flex items-center w-full md:w-auto gap-3">
              {/* Search */}
              <div className="relative flex-1 md:flex-initial md:w-80">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-4 w-4 text-gray-500" />
                </div>
                <input
                  type="text"
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                  placeholder="Search equipment by name or number..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              {/* Filters */}
              <button
                onClick={() => setIsFilterOpen(true)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-green-500 focus:border-green-500"
              >
                <Filter className="h-4 w-4 mr-2" />
                Filters
                {activeFilterCount > 0 && (
                  <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    {activeFilterCount}
                  </span>
                )}
              </button>

              {/* Optional Export */}
              <button
                onClick={() => alert(`Exporting ${filteredEquipment.length} equipment ...`)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-green-500 focus:border-green-500"
              >
                <Download className="h-4 w-4 mr-2" />
                Export
              </button>

              {/* Add Equipment */}
              <Link
                to={`/equipment/create`}
                className="inline-flex items-center px-4 py-2 border border-green-600 rounded-md shadow-sm text-sm font-medium text-green-600 bg-white hover:bg-green-50"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Equipment
              </Link>
            </div>
          </div>

          {/* Active Filters Display */}
          <ActiveFiltersBar
            values={activeFilters}
            config={filterConfig}
            onRemove={(filterId) => {
              const newFilters = { ...activeFilters } as Record<string, any>;
              delete newFilters[filterId];
              setActiveFilters(newFilters);
            }}
            onClear={handleClearFilters}
          />

          {/* Bulk actions bar */}
          {isAnySelected && (
            <div className="mb-3 flex items-center justify-between p-2 bg-white border border-gray-200 rounded-md shadow-sm">
              <div className="text-sm text-gray-700 font-medium">{selectedIds.length} selected</div>
              <div className="flex items-center gap-2">
                <button onClick={() => alert('Assign placeholder')} title="Assign" className="p-2 rounded hover:bg-gray-100 text-gray-700">
                  <Users className="h-5 w-5" />
                </button>
                <button onClick={() => alert('Export selected placeholder')} title="Export" className="p-2 rounded hover:bg-gray-100 text-gray-700">
                  <Download className="h-5 w-5" />
                </button>
                <button onClick={() => alert('Archive placeholder')} title="Archive" className="p-2 rounded hover:bg-gray-100 text-gray-700">
                  <Archive className="h-5 w-5" />
                </button>
                <button onClick={() => window.confirm('Delete selected equipment?') && alert('Delete placeholder')} title="Delete" className="p-2 rounded hover:bg-gray-100 text-red-600">
                  <Trash2 className="h-5 w-5" />
                </button>
              </div>
            </div>
          )}

          {/* Equipment Table (styling matches workers) */}
          <div className="bg-white shadow overflow-hidden border border-gray-200 rounded-lg">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={isAllSelected}
                      onChange={toggleSelectAll}
                      aria-label="Select all"
                      className="h-4 w-4 text-green-600 border-gray-300 rounded"
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Equipment</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current Site</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Compliance</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredEquipment.map((eq) => (
                  <tr key={eq.id} className="hover:bg-gray-50">
                    <td className="px-4 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedIds.includes(eq.id)}
                        onChange={() => toggleSelectOne(eq.id)}
                        aria-label={`Select ${eq.name}`}
                        className="h-4 w-4 text-green-600 border-gray-300 rounded"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-blue-50 border border-blue-200 flex items-center justify-center text-blue-700 text-xs font-semibold">
                            {eq.category?.slice(0, 2) || 'EQ'}
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            <Link to={`/equipment/${eq.id}`} className="hover:text-green-600">
                              {eq.name}
                            </Link>
                          </div>
                          <div className="text-xs text-gray-500">{eq.equipmentNumber}</div>
                        </div>
                      </div>
                    </td>

                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{eq.category}</td>

                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {eq.currentSiteId ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          {eq.currentSiteId}
                        </span>
                      ) : (
                        <span className="text-gray-400">Available</span>
                      )}
                    </td>

                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${eq.complianceStatus === 'compliant' ? 'bg-green-100 text-green-800' : eq.complianceStatus === 'warning' ? 'bg-yellow-100 text-yellow-800' : eq.complianceStatus === 'critical' ? 'bg-orange-100 text-orange-800' : 'bg-red-100 text-red-800'}`}>
                        <span className="ml-1 capitalize">{eq.complianceStatus}</span>
                      </span>
                    </td>

                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <Link
                          to={`/equipment/${eq.id}`}
                          className="px-3 py-1 text-xs border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors"
                          style={{ borderRadius: '5px' }}
                          title="View Details"
                        >
                          View
                        </Link>
                        
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {filteredEquipment.length === 0 && (
              <div className="px-6 py-10 text-center">
                <Users className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No equipment found</h3>
                <p className="mt-1 text-sm text-gray-500">No equipment matches your search criteria.</p>
                <button
                  className="mt-3 text-green-500 hover:text-green-600 font-medium"
                  onClick={() => { setSearchTerm(''); setActiveFilters({}); }}
                >
                  Clear all filters
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Filters Modal (mirrors workers) */}
      <UniversalFilterModal
        isOpen={isFilterOpen}
        onClose={() => setIsFilterOpen(false)}
        title="Filter Equipment"
        filters={filterConfig}
        initialValues={activeFilters}
        onApplyFilters={handleApplyFilters}
        onClearFilters={handleClearFilters}
        size="xl"
      />
    </FloatingCard>
  );
}
