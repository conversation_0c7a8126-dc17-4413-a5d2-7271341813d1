import React, { useState, useMemo } from 'react';
import { Link } from 'react-router-dom';
import {
  Users,
  Plus,
  Search,
  Filter,
  Download,
  MapPin,
  CheckCircle,
  TrendingUp,
  Archive,
  Trash2,
  UserPlus,
  FileSpreadsheet
} from 'lucide-react';

import FloatingCard from '../components/layout/FloatingCard';
import UniversalFilterModal, { FilterValues } from '../components/common/UniversalFilterModal';
import ActiveFiltersBar from '../components/common/ActiveFiltersBar';
import {
  mockCompanyWorkers,
  mockCompanyWorkerStats
} from '../data/workers';

const CompanyWorkerManagement: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [activeFilters, setActiveFilters] = useState<FilterValues>({});
  const [selectedIds, setSelectedIds] = useState<number[]>([]);

  // Dynamic filter configuration based on actual worker data
  const filterConfig = useMemo(() => {
    const uniqueTrades = new Set<string>();
    const uniqueSites = new Set<string>();

    mockCompanyWorkers.forEach(worker => {
      worker.trades.forEach(trade => uniqueTrades.add(trade.name));
      if (worker.currentSiteId) uniqueSites.add(worker.currentSiteId);
    });

    const tradeOptions = Array.from(uniqueTrades).map(trade => ({
      value: trade,
      label: trade,
      count: mockCompanyWorkers.filter(w => w.trades.some(t => t.name === trade)).length
    }));

    const siteOptions = Array.from(uniqueSites).map(site => ({
      value: site,
      label: site,
      count: mockCompanyWorkers.filter(w => w.currentSiteId === site).length
    }));

    return [
      {
        id: 'trade',
        label: 'Trade',
        type: 'multiselect' as const,
        options: tradeOptions
      },
      {
        id: 'complianceStatus',
        label: 'Compliance Status',
        type: 'dropdown' as const,
        placeholder: 'Select compliance status',
        options: [
          { value: 'compliant', label: 'Compliant', count: mockCompanyWorkers.filter(w => w.complianceStatus === 'compliant').length },
          { value: 'pending_training', label: 'Pending Training', count: mockCompanyWorkers.filter(w => w.complianceStatus === 'pending_training').length },
          { value: 'non_compliant', label: 'Non-Compliant', count: mockCompanyWorkers.filter(w => w.complianceStatus === 'non_compliant').length },
          { value: 'expired', label: 'Expired', count: mockCompanyWorkers.filter(w => w.complianceStatus === 'expired').length },
        ]
      },
      {
        id: 'currentSite',
        label: 'Current Site Assignment',
        type: 'dropdown' as const,
        placeholder: 'Select site',
        options: [
          { value: 'available', label: 'Available (No Assignment)', count: mockCompanyWorkers.filter(w => !w.currentSiteId).length },
          ...siteOptions
        ]
      },
      {
        id: 'status',
        label: 'Worker Status',
        type: 'multiselect' as const,
        options: [
          { value: 'active', label: 'Active', count: mockCompanyWorkers.filter(w => w.status === 'active').length },
          { value: 'inactive', label: 'Inactive', count: mockCompanyWorkers.filter(w => w.status === 'inactive').length },
          { value: 'terminated', label: 'Terminated', count: mockCompanyWorkers.filter(w => w.status === 'terminated').length },
        ]
      },
      {
        id: 'hireDate',
        label: 'Hire Date Range',
        type: 'daterange' as const
      }
    ];
  }, []);

  // Filter workers based on search and active filters
  const filteredWorkers = useMemo(() => {
    return mockCompanyWorkers.filter(worker => {
      // Search filter
      const matchesSearch = worker.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           worker.employeeNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           worker.nationalId.includes(searchTerm);

      // Trade filter (multiselect)
      const matchesTrade = !activeFilters.trade ||
                          !Array.isArray(activeFilters.trade) ||
                          activeFilters.trade.length === 0 ||
                          activeFilters.trade.some(trade => worker.trades.some(t => t.name === trade));

      // Compliance status filter
      const matchesCompliance = !activeFilters.complianceStatus ||
                               worker.complianceStatus === activeFilters.complianceStatus;

      // Site assignment filter
      const matchesSite = !activeFilters.currentSite ||
                         (activeFilters.currentSite === 'available' && !worker.currentSiteId) ||
                         worker.currentSiteId === activeFilters.currentSite;

      // Status filter (multiselect)
      const matchesStatus = !activeFilters.status ||
                           !Array.isArray(activeFilters.status) ||
                           activeFilters.status.length === 0 ||
                           activeFilters.status.includes(worker.status);

      // Hire date range filter
      const matchesHireDate = !activeFilters.hireDate ||
                             !activeFilters.hireDate.start ||
                             !worker.hireDate ||
                             (new Date(worker.hireDate) >= new Date(activeFilters.hireDate.start) &&
                              (!activeFilters.hireDate.end || new Date(worker.hireDate) <= new Date(activeFilters.hireDate.end)));

      return matchesSearch && matchesTrade && matchesCompliance && matchesSite && matchesStatus && matchesHireDate;
    });
  }, [searchTerm, activeFilters]);

  // Filter handlers
  const handleApplyFilters = (values: FilterValues) => {
    setActiveFilters(values);
  };

  const handleClearFilters = () => {
    setActiveFilters({});
  };

  // Simple export handler (placeholder)
  const handleExport = () => {
    // In a real app, export filteredWorkers to CSV/XLSX here
    console.log('Exporting company workers...', { count: filteredWorkers.length, filters: activeFilters });
    alert('Export will download the current filtered workers list.');
  };

  // Count active filters for display
  const activeFilterCount = Object.values(activeFilters).filter(value => {
    if (Array.isArray(value)) return value.length > 0;
    if (typeof value === 'object' && value !== null) {
      return Object.values(value).some(v => v !== '' && v !== null);
    }
    return value !== '' && value !== null && value !== false;
  }).length;

  // Selection helpers
  const isAllSelected = filteredWorkers.length > 0 && selectedIds.length === filteredWorkers.length;
  const isAnySelected = selectedIds.length > 0;
  const toggleSelectAll = () => {
    if (isAllSelected) {
      setSelectedIds([]);
    } else {
      setSelectedIds(filteredWorkers.map(w => w.id));
    }
  };
  const toggleSelectOne = (id: number) => {
    setSelectedIds(prev => prev.includes(id) ? prev.filter(x => x !== id) : [...prev, id]);
  };

  // Bulk actions (placeholders)
  const handleBulkAssign = () => {
    alert(`Assign ${selectedIds.length} workers to a site (placeholder)`);
  };
  const handleBulkExport = () => {
    alert(`Export ${selectedIds.length} selected workers (placeholder)`);
  };
  const handleBulkArchive = () => {
    alert(`Archive ${selectedIds.length} workers (placeholder)`);
  };
  const handleBulkDelete = () => {
    if (confirm(`Are you sure you want to delete ${selectedIds.length} workers? This action cannot be undone.`)) {
      alert('Delete placeholder executed');
    }
  };



  const breadcrumbs = [
    { name: 'Dashboard', path: '/' },
    { name: 'Workers', path: '/workers' }
  ];

  return (
    <FloatingCard
      title={`Workers`}
      breadcrumbs={breadcrumbs}
    >
      <div className="space-y-8">
        {/* Company Worker Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Total Workers</h3>
                <p className="text-2xl font-semibold mt-1">{mockCompanyWorkerStats.totalWorkers}</p>
              </div>
              <div className="p-2 rounded-full bg-gray-50">
                <Users className="h-6 w-6 text-blue-500" />
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Workers on Sites</h3>
                <p className="text-2xl font-semibold mt-1">{mockCompanyWorkerStats.workersOnSites}</p>
              </div>
              <div className="p-2 rounded-full bg-gray-50">
                <MapPin className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Compliant Workers</h3>
                <p className="text-2xl font-semibold mt-1">{mockCompanyWorkerStats.compliantWorkers}</p>
              </div>
              <div className="p-2 rounded-full bg-gray-50">
                <CheckCircle className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Avg Performance</h3>
                <p className="text-2xl font-semibold mt-1">{mockCompanyWorkerStats.averagePerformanceRating.toFixed(1)}</p>
              </div>
              <div className="p-2 rounded-full bg-gray-50">
                <TrendingUp className="h-6 w-6 text-indigo-500" />
              </div>
            </div>
          </div>
        </div>

        {/* Worker Management Interface */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          {/* Toolbar: Title left, controls right (match site-level) */}
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
            <h2 className="text-lg font-semibold text-gray-900">Workers</h2>

            <div className="flex items-center w-full md:w-auto gap-3">
              {/* Search */}
              <div className="relative flex-1 md:flex-initial md:w-80">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-4 w-4 text-gray-500" />
                </div>
                <input
                  type="text"
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                  placeholder="Search workers by name or trade..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              {/* Filter Button */}
              <button
                onClick={() => setIsFilterOpen(true)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-green-500 focus:border-green-500"
              >
                <Filter className="h-4 w-4 mr-2" />
                Filters
                {activeFilterCount > 0 && (
                  <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    {activeFilterCount}
                  </span>
                )}
              </button>

              {/* Export */}
              <button
                onClick={handleExport}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-green-500 focus:border-green-500"
              >
                <Download className="h-4 w-4 mr-2" />
                Export
              </button>

              {/* Add Workers */}
              <Link
                to={`/workers/create`}
                className="inline-flex items-center px-4 py-2 border border-green-600 rounded-md shadow-sm text-sm font-medium text-green-600 bg-white hover:bg-green-50"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Workers
              </Link>
            </div>
          </div>

          {/* Active Filters Display - shared */}
          <ActiveFiltersBar
            values={activeFilters}
            config={filterConfig}
            onRemove={(filterId) => {
              const newFilters = { ...activeFilters } as Record<string, any>;
              delete newFilters[filterId];
              setActiveFilters(newFilters);
            }}
            onClear={handleClearFilters}
          />

          {/* Bulk actions bar (Gmail-like) */}
          {isAnySelected && (
            <div className="mb-3 flex items-center justify-between p-2 bg-white border border-gray-200 rounded-md shadow-sm">
              <div className="text-sm text-gray-700 font-medium">{selectedIds.length} selected</div>
              <div className="flex items-center gap-2">
                <button onClick={handleBulkAssign} title="Assign" className="p-2 rounded hover:bg-gray-100 text-gray-700">
                  <UserPlus className="h-5 w-5" />
                </button>
                <button onClick={handleBulkExport} title="Export" className="p-2 rounded hover:bg-gray-100 text-gray-700">
                  <FileSpreadsheet className="h-5 w-5" />
                </button>
                <button onClick={handleBulkArchive} title="Archive" className="p-2 rounded hover:bg-gray-100 text-gray-700">
                  <Archive className="h-5 w-5" />
                </button>
                <button onClick={handleBulkDelete} title="Delete" className="p-2 rounded hover:bg-gray-100 text-red-600">
                  <Trash2 className="h-5 w-5" />
                </button>
              </div>
            </div>
          )}

          {/* Workers Table - match site-level look while keeping company columns */}
          <div className="bg-white shadow overflow-hidden border border-gray-200 rounded-lg">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={isAllSelected}
                      onChange={toggleSelectAll}
                      aria-label="Select all"
                      className="h-4 w-4 text-green-600 border-gray-300 rounded"
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Worker
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Trade
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Current Site
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredWorkers.map((worker) => (
                  <tr key={worker.id} className="hover:bg-gray-50">
                    <td className="px-4 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedIds.includes(worker.id)}
                        onChange={() => toggleSelectOne(worker.id)}
                        aria-label={`Select ${worker.name}`}
                        className="h-4 w-4 text-green-600 border-gray-300 rounded"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <img
                            className="h-10 w-10 rounded-full"
                            src={worker.photoUrl || `https://ui-avatars.com/api/?name=${encodeURIComponent(worker.name)}&background=3B82F6&color=fff`}
                            alt={worker.name}
                          />
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            <Link to={`/workers/${worker.id}`} className="hover:text-green-500">
                              {worker.name}
                            </Link>
                          </div>
                          <div className="text-xs text-gray-500">
                            {worker.employeeNumber}
                          </div>
                          <div className="text-sm text-gray-500">
                            {worker.email}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {worker.trades.map(trade => trade.name).join(', ')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {worker.currentSiteId ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          {worker.currentSiteId}
                        </span>
                      ) : (
                        <span className="text-gray-400">Available</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <Link
                          to={`/workers/${worker.id}`}
                          className="px-3 py-1 text-xs border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors"
                          style={{ borderRadius: '5px' }}
                          title="View Details"
                        >
                          View
                        </Link>
                        <Link
                          to={`/workers/${worker.id}/edit`}
                          className="px-3 py-1 text-xs border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors"
                          style={{ borderRadius: '5px' }}
                          title="Edit Worker"
                        >
                          Edit
                        </Link>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredWorkers.length === 0 && (
            <div className="text-center py-12">
              <Users className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No workers found</h3>
              <p className="mt-1 text-sm text-gray-500">
                Try adjusting your search criteria or add a new worker.
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Universal Filter Modal */}
      <UniversalFilterModal
        isOpen={isFilterOpen}
        onClose={() => setIsFilterOpen(false)}
        title="Filter Company Workers"
        filters={filterConfig}
        initialValues={activeFilters}
        onApplyFilters={handleApplyFilters}
        onClearFilters={handleClearFilters}
        size="xl"
      />
    </FloatingCard>
  );
};

export default CompanyWorkerManagement;
