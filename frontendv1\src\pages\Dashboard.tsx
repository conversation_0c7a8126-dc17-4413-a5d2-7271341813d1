import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
	AlertTriangle,
	Briefcase,
	Clock,
	ShieldCheck,
	Users,
	Wrench,
} from "lucide-react";
import FloatingCard from "../components/layout/FloatingCard";
import KPICard from "../components/dashboard/KPICard";
import SiteCard from "../components/dashboard/SiteCard";
import { SiteInfo } from "../types";
import { DashboardKPIService, CompanyKPIStats } from "../services/dashboardKPIService";
import { useTopBarConfig } from "../hooks/useTopBarContext";
import { useAuth } from "../hooks/useAuthContext";
import UniversalFilter, { TagOption } from "../components/shared/UniversalFilter";

const mockSites: SiteInfo[] = [
	{
		id: "site1",
		name: "Downtown Tower Construction",
		healthStatus: "green",
		workersOnSite: 45,
		activePermits: 3,
		openIncidents: 0,
		projectManager: "<PERSON> Smith",
		location: "Downtown, City Center",
		timeline: "Jan 2023 - Dec 2024",
		currentPhase: "Foundation",
		progressPercentage: 25,
		tenantId: "tenant1",
		status: "active",
		createdAt: new Date("2023-01-15"),
	},
	{
		id: "site2",
		name: "Riverside Apartments",
		healthStatus: "amber",
		workersOnSite: 32,
		activePermits: 2,
		openIncidents: 1,
		projectManager: "Sarah Johnson",
		location: "Riverside District",
		timeline: "Mar 2023 - Jun 2024",
		currentPhase: "Framing",
		progressPercentage: 40,
		tenantId: "tenant1",
		status: "active",
		createdAt: new Date("2023-03-10"),
	},
	{
		id: "site3",
		name: "Industrial Park Expansion",
		healthStatus: "red",
		workersOnSite: 28,
		activePermits: 1,
		openIncidents: 3,
		projectManager: "Michael Brown",
		location: "North Industrial Zone",
		timeline: "Feb 2023 - Apr 2024",
		currentPhase: "Excavation",
		progressPercentage: 15,
		tenantId: "tenant1",
		status: "active",
		createdAt: new Date("2023-02-20"),
	},
	{
		id: "site4",
		name: "Community Center Renovation",
		healthStatus: "green",
		workersOnSite: 18,
		activePermits: 2,
		openIncidents: 0,
		projectManager: "Emily Davis",
		location: "West End",
		timeline: "Apr 2023 - Oct 2023",
		currentPhase: "Planning",
		progressPercentage: 5,
		tenantId: "tenant1",
		status: "active",
		createdAt: new Date("2023-04-05"),
	},
];

const Dashboard = () => {
	const [viewType, setViewType] = useState<"card" | "list">("card");
	const [kpiStats, setKpiStats] = useState<CompanyKPIStats | null>(null);
	const [searchQuery, setSearchQuery] = useState("");
	const [selectedStatusTag, setSelectedStatusTag] = useState("all");
	const navigate = useNavigate();
	const { setTitle } = useTopBarConfig();
	const { user } = useAuth();

	// Tag options for project status filtering
	const statusTags: TagOption[] = [
		{ id: "all", name: "All" },
		{ id: "draft", name: "Draft" },
		{ id: "active", name: "Active" },
		{ id: "paused", name: "Paused" },
		{ id: "completed", name: "Completed" },
	];

	useEffect(() => {
		// Set welcome message in topbar
		if (user) {
			setTitle(`Welcome back, ${user.firstName}`);
		} else {
			setTitle("Dashboard");
		}
	}, [user, setTitle]);

	useEffect(() => {
		// Load KPI statistics
		const loadKPIStats = async () => {
			try {
				// In a real implementation, get tenantId from auth context
				const stats = await DashboardKPIService.fetchCompanyKPIStatsFromAPI("tenant1");
				setKpiStats(stats);
			} catch (error) {
				console.error("Failed to load KPI stats:", error);
				// Fallback to mock data
				setKpiStats(DashboardKPIService.getCompanyKPIStats());
			}
		};

		loadKPIStats();
	}, []);

	// Filter sites based on search query and selected status tag
	const filteredSites = mockSites.filter((site) => {
		// Status filter
		const statusMatch = selectedStatusTag === "all" || site.status === selectedStatusTag;

		// Search filter (name, location, status)
		const searchMatch = searchQuery === "" ||
			site.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
			site.location.toLowerCase().includes(searchQuery.toLowerCase()) ||
			site.status.toLowerCase().includes(searchQuery.toLowerCase());

		return statusMatch && searchMatch;
	});

	return (
		<FloatingCard>
			{/* KPI Section */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
				{kpiStats && (
					<>
						<KPICard
							title="All Workers"
							value={kpiStats.workers.value}
							contextualMessage={kpiStats.workers.contextualMessage}
							priority={kpiStats.workers.priority}
							icon={<Users className="h-6 w-6 text-blue-500" />}
							navigateTo="/workers"
						/>
						<KPICard
							title="Projects"
							value={kpiStats.sites.value}
							contextualMessage={kpiStats.sites.contextualMessage}
							priority={kpiStats.sites.priority}
							icon={<Briefcase className="h-6 w-6 text-green-500" />}
							navigateTo="/sites"
						/>
						<KPICard
							title="Incidents"
							value={kpiStats.incidents.value}
							contextualMessage={kpiStats.incidents.contextualMessage}
							priority={kpiStats.incidents.priority}
							icon={<AlertTriangle className="h-6 w-6 text-red-500" />}
							navigateTo="/incidents"
						/>
						<KPICard
							title="Equipment"
							value={kpiStats.equipment.value}
							contextualMessage={kpiStats.equipment.contextualMessage}
							priority={kpiStats.equipment.priority}
							icon={<Wrench className="h-6 w-6 text-orange-500" />}
							navigateTo="/equipment"
						/>
					</>
				)}
			</div>

			{/* Projects Section */}
			<div className="mb-6">
				<div className="flex justify-between items-center mb-4">
					<h2 className="text-lg font-semibold text-gray-800">Projects</h2>
					<div className="flex space-x-2">
						<div className="flex space-x-3">
							<button
								className={`p-1.5 text-xs ${viewType === "card" ? "text-green-500" : "text-gray-400 hover:text-gray-600"}`}
								onClick={() => setViewType("card")}
								aria-label="Card View"
							>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									width="16"
									height="16"
									viewBox="0 0 24 24"
									fill="none"
									stroke="currentColor"
									strokeWidth="2"
									strokeLinecap="round"
									strokeLinejoin="round"
								>
									<rect width="7" height="7" x="3" y="3" rx="1" />
									<rect width="7" height="7" x="14" y="3" rx="1" />
									<rect width="7" height="7" x="14" y="14" rx="1" />
									<rect width="7" height="7" x="3" y="14" rx="1" />
								</svg>
							</button>
							<button
								className={`p-1.5 text-xs ${viewType === "list" ? "text-green-500" : "text-gray-400 hover:text-gray-600"}`}
								onClick={() => setViewType("list")}
								aria-label="List View"
							>
								<svg
									xmlns="http://www.w3.org/2000/svg"
									width="16"
									height="16"
									viewBox="0 0 24 24"
									fill="none"
									stroke="currentColor"
									strokeWidth="2"
									strokeLinecap="round"
									strokeLinejoin="round"
								>
									<line x1="8" x2="21" y1="6" y2="6" />
									<line x1="8" x2="21" y1="12" y2="12" />
									<line x1="8" x2="21" y1="18" y2="18" />
									<line x1="3" x2="3.01" y1="6" y2="6" />
									<line x1="3" x2="3.01" y1="12" y2="12" />
									<line x1="3" x2="3.01" y1="18" y2="18" />
								</svg>
							</button>
						</div>
						<button
							onClick={() => navigate('/sites/new')}
							className="inline-flex items-center px-4 py-2 border border-green-600 rounded-md shadow-sm text-sm font-medium text-green-600 bg-white hover:bg-green-50"
						>
							Add Site
						</button>
					</div>
				</div>

				{/* Project Filter */}
				<div className="mb-6">
					<UniversalFilter
						variant="search-tags"
						searchQuery={searchQuery}
						onSearchChange={setSearchQuery}
						searchPlaceholder="Search projects by name, location, or status..."
						tags={statusTags}
						selectedTagId={selectedStatusTag}
						onTagChange={setSelectedStatusTag}
					/>
				</div>

				{/* Card View */}
				{viewType === "card" && (
					<div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
						{filteredSites.map((site) => (
							<SiteCard key={site.id} site={site} />
						))}
					</div>
				)}

				{/* List View */}
				{viewType === "list" && (
					<div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
						<table className="min-w-full divide-y divide-gray-200">
							<thead className="bg-gray-50">
								<tr>
									<th
										scope="col"
										className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
									>
										Site Name
									</th>
									<th
										scope="col"
										className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
									>
										Health
									</th>
									<th
										scope="col"
										className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
									>
										Workers
									</th>
									<th
										scope="col"
										className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
									>
										Permits
									</th>
									<th
										scope="col"
										className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
									>
										Incidents
									</th>
									<th
										scope="col"
										className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
									>
										Project Manager
									</th>
									<th
										scope="col"
										className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider"
									>
										Actions
									</th>
								</tr>
							</thead>
							<tbody className="bg-white divide-y divide-gray-200">
								{filteredSites.map((site) => (
									<tr key={site.id}>
										<td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
											{site.name}
										</td>
										<td className="px-6 py-4 whitespace-nowrap">
											<div
												className={`w-3 h-3 rounded-full ${
													site.healthStatus === "green"
														? "bg-green-500"
														: site.healthStatus === "amber"
															? "bg-amber-500"
															: "bg-red-500"
												}`}
											></div>
										</td>
										<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
											{site.workersOnSite}
										</td>
										<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
											{site.activePermits}
										</td>
										<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
											{site.openIncidents}
										</td>
										<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
											{site.projectManager}
										</td>
										<td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
											<a
												href={`/sites/${site.id}/dashboard`}
												className="text-green-500 hover:text-green-600"
											>
												View Dashboard
											</a>
										</td>
									</tr>
								))}
							</tbody>
						</table>
					</div>
				)}
			</div>
		</FloatingCard>
	);
};

export default Dashboard;
