import React, { useMemo, useState } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import FloatingCard from '../components/layout/FloatingCard';
import TabContainer, { Tab } from '../components/data/shared/TabContainer';
import { siteDevices, HikDevice, rawEvents, attendanceRecords } from '../data/timeMock';
import { Wifi, WifiOff, AlertTriangle, MapPin, RefreshCw, Pencil } from 'lucide-react';

const findDeviceById = (deviceId: string): HikDevice | undefined => {
  for (const siteId of Object.keys(siteDevices)) {
    const found = siteDevices[siteId].find(d => d.id === deviceId);
    if (found) return found;
  }
  return undefined;
};

const statusBadge = (status: HikDevice['status']) => {
  const base = 'text-xs px-2 py-1 rounded-full';
  if (status === 'online') return <span className={`${base} bg-green-100 text-green-800`}>online</span>;
  if (status === 'offline') return <span className={`${base} bg-gray-100 text-gray-800`}>offline</span>;
  return <span className={`${base} bg-red-100 text-red-800`}>error</span>;
};

const statusIcon = (status: HikDevice['status']) => {
  switch (status) {
    case 'online':
      return <Wifi className="h-4 w-4 text-green-600" />;
    case 'offline':
      return <WifiOff className="h-4 w-4 text-gray-400" />;
    case 'error':
      return <AlertTriangle className="h-4 w-4 text-red-600" />;
    default:
      return <WifiOff className="h-4 w-4 text-gray-400" />;
  }
};

const DeviceDetailPage: React.FC = () => {
  const navigate = useNavigate();
  const { deviceId } = useParams<{ deviceId: string }>();
  const device = useMemo(() => (deviceId ? findDeviceById(deviceId) : undefined), [deviceId]);
  const [activeTab, setActiveTab] = useState<string>('about');
  const [saving, setSaving] = useState(false);

  if (!device) {
    return (
      <FloatingCard title="Device Not Found" breadcrumbs={[]}>
        <div className="text-center py-16">
          <p className="text-gray-600 mb-4">No device with ID "{deviceId}" was found.</p>
          <button onClick={() => navigate(-1)} className="px-4 py-2 bg-gray-800 text-white rounded">Go Back</button>
        </div>
      </FloatingCard>
    );
  }

  // Data sources for tabs
  const deviceEvents = rawEvents.filter(e => e.deviceId === device.id);
  const deviceAttendance = attendanceRecords.filter(r => r.deviceId === device.id);

  // Header content similar in spirit to WorkerProfile top summary
  const Header = (
    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
      <div>
        <h2 className="text-xl font-semibold text-gray-900">{device.name}</h2>
        <div className="text-sm text-gray-600">
          Model {device.model} • v{device.firmwareVersion} • SN: {device.serialNumber}
        </div>
        <div className="flex items-center gap-2 mt-1 text-sm text-gray-700">
          {statusIcon(device.status)} {statusBadge(device.status)}
          <span className="text-gray-400">•</span>
          <span className="inline-flex items-center"><MapPin className="h-3.5 w-3.5 mr-1 text-gray-500" />{device.siteId}</span>
          <span className="text-gray-400">•</span>
          <span>{device.ipAddress}:{device.port}</span>
        </div>
      </div>
      <div className="flex items-center gap-2">
        <button
          onClick={() => navigate(0)}
          className="px-3 py-1 text-xs border border-gray-300 rounded hover:bg-gray-50 inline-flex items-center"
        >
          <RefreshCw className="h-3.5 w-3.5 mr-1" /> Refresh
        </button>
        <button
          onClick={() => setActiveTab('settings')}
          className="px-3 py-1 text-xs border border-blue-300 text-blue-700 rounded hover:bg-blue-50 inline-flex items-center"
        >
          <Pencil className="h-3.5 w-3.5 mr-1" /> Manage
        </button>
      </div>
    </div>
  );

  // Tabs
  const tabs: Tab[] = [
    {
      id: 'about',
      label: 'About',
      content: (
        <div className="space-y-6">
          <div className="bg-white rounded-lg border border-gray-200 p-5">
            <h4 className="text-sm font-semibold text-gray-900 mb-3">Specifications</h4>
            <div className="text-sm text-gray-700 space-y-1">
              <div>Model: {device.model}</div>
              <div>Firmware: {device.firmwareVersion}</div>
              <div>Serial Number: {device.serialNumber}</div>
              <div>IP Address: {device.ipAddress}:{device.port}</div>
              <div>Location: {device.location || '—'}</div>
            </div>
          </div>
          <div className="bg-white rounded-lg border border-gray-200 p-5">
            <h4 className="text-sm font-semibold text-gray-900 mb-3">Capacity & Usage</h4>
            <div className="text-sm text-gray-700 space-y-1">
              <div>Users: {device.usage.users} / {device.capacity.maxUsers}</div>
              <div>Faces: {device.usage.faces} / {device.capacity.maxFaces}</div>
              <div>Storage: {device.usage.storageUsedGB}GB / {device.capacity.storageGB}GB</div>
            </div>
          </div>
          <div className="bg-white rounded-lg border border-gray-200 p-5">
            <h4 className="text-sm font-semibold text-gray-900 mb-3">Status</h4>
            <div className="text-sm text-gray-700 space-y-1">
              <div className="inline-flex items-center gap-2">{statusIcon(device.status)} {statusBadge(device.status)}</div>
              <div>Last Seen: {new Date(device.lastSeen).toLocaleString()}</div>
              <div>Site: {device.siteId}</div>
            </div>
          </div>
        </div>
      )
    },
    {
      id: 'events',
      label: 'Events',
      content: (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Employee</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Verify</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {deviceEvents.map(e => (
                <tr key={e.id} className="hover:bg-gray-50">
                  <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-700">{new Date(e.timestamp).toLocaleString()}</td>
                  <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-700">{e.employeeName} ({e.employeeNo})</td>
                  <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-700">{e.eventType}</td>
                  <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-700">{e.verifyMode}</td>
                  <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-700 capitalize">{e.attendanceStatus}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )
    },
    {
      id: 'attendance',
      label: 'Attendance',
      content: (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Worker</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Site</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Check-in</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Check-out</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Hours</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Verified</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {deviceAttendance.map(r => (
                <tr key={r.id} className="hover:bg-gray-50">
                  <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-700">{r.workerName} (#{r.workerId})</td>
                  <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-700">{r.siteId}</td>
                  <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-700">{r.checkIn ? new Date(r.checkIn).toLocaleString() : '-'}</td>
                  <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-700">{r.checkOut ? new Date(r.checkOut).toLocaleString() : '-'}</td>
                  <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-700">{r.hours ?? '-'}</td>
                  <td className="px-6 py-3 whitespace-nowrap text-sm text-gray-700">{r.isVerified ? 'Yes' : 'No'}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )
    },
    {
      id: 'settings',
      label: 'Settings',
      content: (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h4 className="text-sm font-semibold text-gray-900 mb-3">Reassign Device</h4>
            <form
              onSubmit={(e) => {
                e.preventDefault();
                setSaving(true);
                setTimeout(() => { setSaving(false); alert('Device reassigned (mock).'); }, 400);
              }}
              className="space-y-3"
            >
              <div>
                <label className="block text-xs text-gray-600 mb-1">New Site ID</label>
                <input className="w-full rounded-md border-gray-300" defaultValue={device.siteId} />
              </div>
              <button type="submit" disabled={saving} className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700">
                {saving ? 'Saving...' : 'Save'}
              </button>
            </form>
          </div>
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h4 className="text-sm font-semibold text-gray-900 mb-3">Rename Device</h4>
            <form
              onSubmit={(e) => {
                e.preventDefault();
                setSaving(true);
                setTimeout(() => { setSaving(false); alert('Device name updated (mock).'); }, 400);
              }}
              className="space-y-3"
            >
              <div>
                <label className="block text-xs text-gray-600 mb-1">Device Name</label>
                <input className="w-full rounded-md border-gray-300" defaultValue={device.name} />
              </div>
              <button type="submit" disabled={saving} className="px-3 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700">
                {saving ? 'Saving...' : 'Save'}
              </button>
            </form>
          </div>
        </div>
      )
    }
  ];

  const breadcrumbs = [
    { name: 'Dashboard', path: '/' },
    { name: 'Data', path: '/data#time' },
    { name: device.name, path: `/devices/${device.id}` },
  ];

  return (
    <FloatingCard title={`${device.name} – Device`} breadcrumbs={breadcrumbs} layout="custom" topBarRightActions={undefined}>
      {/* Centered container ~2/3 width with consistent padding; text remains left-aligned */}
      <div className="w-full max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="space-y-8 py-2">
          {Header}
          <TabContainer tabs={tabs} activeTab={activeTab} onTabChange={setActiveTab} />
        </div>
      </div>
    </FloatingCard>
  );
};

export default DeviceDetailPage;
