import React, { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import {
	LayoutDashboard,
	FileText,
	Shield,
	FolderOpen,
	BarChart3,
} from "lucide-react";
import FloatingCard from "../components/layout/FloatingCard";
import TabContainer, { Tab } from "../components/data/shared/TabContainer";
import DocumentsDashboard from "../components/documents/DocumentsDashboard";
import DocumentLibrary from "../components/documents/DocumentLibrary";
import ComplianceTracker from "../components/documents/ComplianceTracker";
import DocumentCategories from "../components/documents/DocumentCategories";
import DocumentReports from "../components/documents/DocumentReports";
import { EntityType } from "../types/documents";

const DocumentsPage: React.FC = () => {
	const location = useLocation();
	const [activeTab, setActiveTab] = useState("dashboard");

	const validTabs = [
		"dashboard",
		"library",
		"compliance",
		"categories",
		"reports",
	];

	// Handle URL hash navigation
	useEffect(() => {
		const hash = location.hash.replace("#", "");
		if (hash && validTabs.includes(hash)) {
			setActiveTab(hash);
		} else if (!hash) {
			setActiveTab("dashboard");
		}
	}, [location.hash]);

	const handleNavigateToTab = (tabId: string) => {
		setActiveTab(tabId);
		// Update URL hash without triggering a page reload
		window.history.pushState(null, "", `#${tabId}`);
	};

	const breadcrumbs = [
		{ name: "Dashboard", path: "/" },
		{ name: "Documents", path: "/documents" },
	];

	const tabs: Tab[] = [
		{
			id: "dashboard",
			label: "Dashboard",
			icon: <LayoutDashboard className="h-4 w-4" />,
			content: <DocumentsDashboard onNavigateToTab={handleNavigateToTab} />,
		},
		{
			id: "library",
			label: "Document Library",
			icon: <FileText className="h-4 w-4" />,
			content: (
				<DocumentLibrary
					entityType={EntityType.COMPANY}
					entityId="company-1" // This would come from context in real app
					showUpload={true}
					showFilters={true}
					viewMode="grid"
				/>
			),
		},
		{
			id: "compliance",
			label: "Compliance Tracker",
			icon: <Shield className="h-4 w-4" />,
			content: <ComplianceTracker />,
		},
		{
			id: "categories",
			label: "Categories",
			icon: <FolderOpen className="h-4 w-4" />,
			content: <DocumentCategories />,
		},
		{
			id: "reports",
			label: "Reports",
			icon: <BarChart3 className="h-4 w-4" />,
			content: <DocumentReports />,
		},
	];

	return (
		<FloatingCard title="Document Management" breadcrumbs={breadcrumbs}>
			<TabContainer
				tabs={tabs}
				activeTab={activeTab}
				onTabChange={handleNavigateToTab}
			/>
		</FloatingCard>
	);
};

export default DocumentsPage;
