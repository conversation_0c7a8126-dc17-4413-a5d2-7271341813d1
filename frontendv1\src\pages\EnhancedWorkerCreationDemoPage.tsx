import React, { useState } from 'react';
import { ArrowLeft, Users, CheckCircle, AlertCircle, Star, Zap, Shield } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';

import { EnhancedWorkerCreationForm } from '../components/workers/EnhancedWorkerCreationForm';
import FloatingCard from '../components/layout/FloatingCard';

const EnhancedWorkerCreationDemoPage: React.FC = () => {
  const navigate = useNavigate();
  const [showForm, setShowForm] = useState(false);
  const [createdWorker, setCreatedWorker] = useState<any>(null);

  const handleWorkerCreated = (worker: any) => {
    setCreatedWorker(worker);
    setShowForm(false);
    toast.success('Worker created successfully!');
  };

  const handleCancel = () => {
    setShowForm(false);
  };

  if (showForm) {
    return (
      <div className="min-h-screen bg-gray-50">
        <EnhancedWorkerCreationForm
          onSuccess={handleWorkerCreated}
          onCancel={handleCancel}
        />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <FloatingCard>
        <div className="max-w-6xl mx-auto p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate(-1)}
                className="flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
              >
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back
              </button>
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Enhanced Worker Creation System</h1>
                <p className="text-gray-600 mt-1">
                  Modern multi-step form with dual credential management
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                <CheckCircle className="h-4 w-4 mr-1" />
                Production Ready
              </span>
            </div>
          </div>

          {/* Success Message */}
          {createdWorker && (
            <div className="mb-8 bg-green-50 border border-green-200 rounded-lg p-6">
              <div className="flex items-start">
                <CheckCircle className="h-6 w-6 text-green-600 mt-0.5 mr-3" />
                <div>
                  <h3 className="text-lg font-medium text-green-800 mb-2">
                    Worker Created Successfully!
                  </h3>
                  <p className="text-green-700 mb-3">
                    <strong>{createdWorker.name}</strong> has been added to your workforce management system
                    with all credentials and documents properly organized.
                  </p>
                  <div className="flex space-x-3">
                    <button
                      onClick={() => setShowForm(true)}
                      className="text-sm font-medium text-green-700 hover:text-green-800"
                    >
                      Create Another Worker
                    </button>
                    <button
                      onClick={() => setCreatedWorker(null)}
                      className="text-sm font-medium text-green-700 hover:text-green-800"
                    >
                      Dismiss
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Feature Overview */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
            {/* Key Features */}
            <div className="lg:col-span-2">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Key Features</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center mb-4">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Users className="h-6 w-6 text-blue-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 ml-3">
                      Multi-Step Workflow
                    </h3>
                  </div>
                  <ul className="text-sm text-gray-600 space-y-2">
                    <li>• 5-step guided process</li>
                    <li>• Progress tracking with visual indicators</li>
                    <li>• Auto-save functionality</li>
                    <li>• Step validation and error handling</li>
                    <li>• Responsive design for all devices</li>
                  </ul>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center mb-4">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <Shield className="h-6 w-6 text-green-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 ml-3">
                      Dual Credential System
                    </h3>
                  </div>
                  <ul className="text-sm text-gray-600 space-y-2">
                    <li>• Permanent credentials (diplomas, licenses)</li>
                    <li>• Temporary credentials (safety, equipment)</li>
                    <li>• Automatic expiration tracking</li>
                    <li>• Renewal reminders and alerts</li>
                    <li>• Compliance status monitoring</li>
                  </ul>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center mb-4">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <Zap className="h-6 w-6 text-purple-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 ml-3">
                      Smart Document Management
                    </h3>
                  </div>
                  <ul className="text-sm text-gray-600 space-y-2">
                    <li>• Auto-generated certificate names</li>
                    <li>• Organized folder structure</li>
                    <li>• File type and size validation</li>
                    <li>• Secure document storage</li>
                    <li>• OCR-ready for future integration</li>
                  </ul>
                </div>

                <div className="bg-white border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center mb-4">
                    <div className="p-2 bg-orange-100 rounded-lg">
                      <Star className="h-6 w-6 text-orange-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 ml-3">
                      Training Integration
                    </h3>
                  </div>
                  <ul className="text-sm text-gray-600 space-y-2">
                    <li>• Seamless workflow integration</li>
                    <li>• Compliance calculation</li>
                    <li>• Site eligibility assessment</li>
                    <li>• Training requirement mapping</li>
                    <li>• Performance tracking ready</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Quick Start */}
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Quick Start</h2>

              <div className="bg-white border border-gray-200 rounded-lg p-6 mb-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Try the Enhanced Form</h3>
                <p className="text-gray-600 mb-4">
                  Experience the new worker creation process with dual credential management,
                  smart document handling, and seamless training integration.
                </p>
                <button
                  onClick={() => setShowForm(true)}
                  className="w-full flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 font-medium"
                >
                  <Users className="h-5 w-5 mr-2" />
                  Create New Worker
                </button>
              </div>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div className="flex">
                  <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 mr-3" />
                  <div className="text-sm text-blue-700">
                    <p className="font-medium mb-1">Demo Features</p>
                    <ul className="space-y-1">
                      <li>• All form steps are functional</li>
                      <li>• File uploads are simulated</li>
                      <li>• Validation is fully implemented</li>
                      <li>• Data is not persisted (demo mode)</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Workflow Steps */}
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Workflow Steps</h2>

            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              {[
                {
                  step: 1,
                  title: 'Basic Information',
                  description: 'Personal details, contact info, trades & skills',
                  icon: Users,
                  color: 'blue'
                },
                {
                  step: 2,
                  title: 'Permanent Credentials',
                  description: 'Diplomas, degrees, professional licenses',
                  icon: Star,
                  color: 'purple'
                },
                {
                  step: 3,
                  title: 'Temporary Credentials',
                  description: 'Safety training, equipment certifications',
                  icon: Shield,
                  color: 'orange'
                },
                {
                  step: 4,
                  title: 'Documents & Photo',
                  description: 'Profile picture, signature, additional docs',
                  icon: Users,
                  color: 'green'
                },
                {
                  step: 5,
                  title: 'Review & Submit',
                  description: 'Final review and worker creation',
                  icon: CheckCircle,
                  color: 'green'
                }
              ].map((item) => (
                <div key={item.step} className="bg-white border border-gray-200 rounded-lg p-4 text-center">
                  <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full bg-${item.color}-100 mb-3`}>
                    <item.icon className={`h-6 w-6 text-${item.color}-600`} />
                  </div>
                  <h3 className="text-sm font-semibold text-gray-900 mb-2">
                    Step {item.step}: {item.title}
                  </h3>
                  <p className="text-xs text-gray-600">
                    {item.description}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Technical Implementation */}
          <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
            <h3 className="text-lg font-medium text-gray-800 mb-4">
              Technical Implementation
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-gray-600">
              <div>
                <h4 className="font-medium text-gray-800 mb-2">Frontend Architecture</h4>
                <ul className="space-y-1">
                  <li>• React 19 with TypeScript for type safety</li>
                  <li>• Multi-step form with state management</li>
                  <li>• Tailwind CSS for responsive design</li>
                  <li>• Real-time validation and error handling</li>
                  <li>• File upload with preview and validation</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium text-gray-800 mb-2">Data Management</h4>
                <ul className="space-y-1">
                  <li>• Enhanced TypeScript interfaces</li>
                  <li>• Credential type discrimination</li>
                  <li>• Auto-naming utility functions</li>
                  <li>• Expiration tracking and alerts</li>
                  <li>• Training workflow integration ready</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </FloatingCard>
    </div>
  );
};

export default EnhancedWorkerCreationDemoPage;
