import React, { useMemo, useState } from "react";
import { use<PERSON>ara<PERSON>, Link } from "react-router-dom";
import { Users, Search, Filter, MapPin, CheckCircle, Clock, XCircle, AlertTriangle, FileSpreadsheet, Plus } from "lucide-react";
import FloatingCard from "../components/layout/FloatingCard";
import UniversalFilterModal, { FilterValues } from "../components/common/UniversalFilterModal";
import ActiveFiltersBar from "../components/common/ActiveFiltersBar";
import { getSiteEquipment } from "../data/equipmentMockData";

type Compliance = "compliant" | "warning" | "critical" | "overdue";

const EquipmentPage: React.FC = () => {
  const { siteId } = useParams<{ siteId: string }>();

  const [searchTerm, setSearchTerm] = useState("");
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [activeFilters, setActiveFilters] = useState<FilterValues>({});
  const [selectedIds, setSelectedIds] = useState<string[]>([]);

  // Site-scoped equipment
  const siteEquipment = useMemo(() => {
    return siteId ? getSiteEquipment(siteId) : [];
  }, [siteId]);

  // KPI summary (4 cards), mirroring workers page
  const siteStats = useMemo(() => {
    const total = siteEquipment.length;
    const assigned = siteEquipment.filter(e => !!e.currentSiteId).length; // will be == total
    const compliant = siteEquipment.filter(e => e.complianceStatus === "compliant").length;
    const avgHours = total > 0 ? (siteEquipment.reduce((s, e) => s + (e.totalHours || 0), 0) / total) : 0;
    return { total, assigned, compliant, avgHours };
  }, [siteEquipment]);

  // Dynamic filter config
  const filterConfig = useMemo(() => {
    const categories = Array.from(new Set(siteEquipment.map(e => e.category)));
    const categoryOptions = categories.map(cat => ({
      value: cat,
      label: cat,
      count: siteEquipment.filter(e => e.category === cat).length
    }));

    return [
      { id: "category", label: "Category", type: "multiselect" as const, options: categoryOptions },
      {
        id: "complianceStatus",
        label: "Compliance Status",
        type: "dropdown" as const,
        placeholder: "Select compliance status",
        options: [
          { value: "compliant", label: "Compliant", count: siteEquipment.filter(e => e.complianceStatus === "compliant").length },
          { value: "warning", label: "Warning", count: siteEquipment.filter(e => e.complianceStatus === "warning").length },
          { value: "critical", label: "Critical", count: siteEquipment.filter(e => e.complianceStatus === "critical").length },
          { value: "overdue", label: "Overdue", count: siteEquipment.filter(e => e.complianceStatus === "overdue").length },
        ]
      },
      {
        id: "status",
        label: "Equipment Status",
        type: "multiselect" as const,
        options: [
          { value: "active", label: "Active", count: siteEquipment.filter(e => e.overallStatus === "active").length },
          { value: "maintenance", label: "In Maintenance", count: siteEquipment.filter(e => e.overallStatus === "maintenance").length },
          { value: "inactive", label: "Inactive", count: siteEquipment.filter(e => e.overallStatus === "inactive").length },
          { value: "retired", label: "Retired", count: siteEquipment.filter(e => e.overallStatus === "retired").length },
        ]
      }
    ];
  }, [siteEquipment]);

  // Filtered list (search + filters)
  const filteredEquipment = useMemo(() => {
    return siteEquipment.filter(eq => {
      const term = searchTerm.toLowerCase();
      const matchesSearch = !term ||
        eq.name.toLowerCase().includes(term) ||
        eq.equipmentNumber.toLowerCase().includes(term) ||
        (eq.model?.toLowerCase().includes(term));

      const matchesCategory = !activeFilters.category ||
        !Array.isArray(activeFilters.category) ||
        activeFilters.category.length === 0 ||
        activeFilters.category.some((c: string) => eq.category === c);

      const matchesCompliance = !activeFilters.complianceStatus || eq.complianceStatus === activeFilters.complianceStatus;

      const matchesStatus = !activeFilters.status ||
        !Array.isArray(activeFilters.status) ||
        activeFilters.status.length === 0 ||
        activeFilters.status.includes(eq.overallStatus);

      return matchesSearch && matchesCategory && matchesCompliance && matchesStatus;
    });
  }, [siteEquipment, searchTerm, activeFilters]);

  // Selection helpers
  const isAllSelected = filteredEquipment.length > 0 && selectedIds.length === filteredEquipment.length;
  const isAnySelected = selectedIds.length > 0;
  const toggleSelectAll = () => {
    if (isAllSelected) setSelectedIds([]);
    else setSelectedIds(filteredEquipment.map(e => e.id));
  };
  const toggleSelectOne = (id: string) => setSelectedIds(prev => prev.includes(id) ? prev.filter(x => x !== id) : [...prev, id]);

  // Helpers copied from workers page style
  const getComplianceStatusIcon = (status: Compliance) => {
    switch (status) {
      case "compliant": return <CheckCircle className="w-4 h-4 text-green-500" />;
      case "warning": return <Clock className="w-4 h-4 text-yellow-500" />;
      case "critical": return <AlertTriangle className="w-4 h-4 text-orange-500" />;
      case "overdue": return <XCircle className="w-4 h-4 text-red-600" />;
      default: return <AlertTriangle className="w-4 h-4 text-gray-400" />;
    }
  };
  const getComplianceStatusText = (status: Compliance) => status.charAt(0).toUpperCase() + status.slice(1);
  const getComplianceStatusColor = (status: Compliance) => {
    switch (status) {
      case "compliant": return "bg-green-100 text-green-800";
      case "warning": return "bg-yellow-100 text-yellow-800";
      case "critical": return "bg-orange-100 text-orange-800";
      case "overdue": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const handleApplyFilters = (values: FilterValues) => setActiveFilters(values);
  const handleClearFilters = () => setActiveFilters({});
  const activeFilterCount = Object.values(activeFilters).filter(v => Array.isArray(v) ? v.length > 0 : (typeof v === 'object' && v !== null ? Object.values(v).some(x => x !== '' && x !== null) : v !== '' && v !== null && v !== false)).length;

  const breadcrumbs = [
    { name: 'Dashboard', path: '/' },
    { name: `Site ${siteId}`, path: `/sites/${siteId}/dashboard` },
    { name: 'Equipment', path: `/sites/${siteId}/equipment` }
  ];

  return (
    <FloatingCard title={`Site ${siteId} - Equipment`} breadcrumbs={breadcrumbs}>
      <div className="space-y-8">
        {/* KPI cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Total Equipment</h3>
                <p className="text-2xl font-semibold mt-1">{siteStats.total}</p>
              </div>
              <div className="p-2 rounded-full bg-gray-50">
                <Users className="h-6 w-6 text-blue-500" />
              </div>
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Assigned</h3>
                <p className="text-2xl font-semibold mt-1">{siteStats.assigned}</p>
              </div>
              <div className="p-2 rounded-full bg-gray-50">
                <MapPin className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Compliant</h3>
                <p className="text-2xl font-semibold mt-1">{siteStats.compliant}</p>
              </div>
              <div className="p-2 rounded-full bg-gray-50">
                <CheckCircle className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Avg Hours</h3>
                <p className="text-2xl font-semibold mt-1">{siteStats.avgHours.toFixed(1)}h</p>
              </div>
              <div className="p-2 rounded-full bg-gray-50">
                <Clock className="h-6 w-6 text-indigo-500" />
              </div>
            </div>
          </div>
        </div>

        {/* Management Interface */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          {/* Toolbar */}
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
            <h2 className="text-lg font-semibold text-gray-900">Equipment</h2>

            <div className="flex items-center w-full md:w-auto gap-3">
              {/* Search */}
              <div className="relative flex-1 md:flex-initial md:w-80">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-4 w-4 text-gray-500" />
                </div>
                <input
                  type="text"
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                  placeholder="Search equipment by name or number..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              {/* Filters */}
              <button
                onClick={() => setIsFilterOpen(true)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-green-500 focus:border-green-500"
              >
                <Filter className="h-4 w-4 mr-2" />
                Filters
                {activeFilterCount > 0 && (
                  <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    {activeFilterCount}
                  </span>
                )}
              </button>

              {/* Import Button (site-level) */}
              <Link
                to={`/sites/${siteId}/equipment/import`}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-green-500 focus:border-green-500"
              >
                <FileSpreadsheet className="h-4 w-4 mr-2" />
                Import
              </Link>

              {/* Add Equipment */}
              <Link
                to={`/sites/${siteId}/equipment/create`}
                className="inline-flex items-center px-4 py-2 border border-green-600 rounded-md shadow-sm text-sm font-medium text-green-600 bg-white hover:bg-green-50"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Equipment
              </Link>
            </div>
          </div>

          {/* Active Filters */}
          <ActiveFiltersBar
            values={activeFilters}
            config={filterConfig}
            onRemove={(filterId) => {
              const newFilters = { ...activeFilters } as Record<string, any>;
              delete newFilters[filterId];
              setActiveFilters(newFilters);
            }}
            onClear={handleClearFilters}
          />

          {/* Bulk actions bar */}
          {isAnySelected && (
            <div className="mb-3 flex items-center justify-between p-2 bg-white border border-gray-200 rounded-md shadow-sm">
              <div className="text-sm text-gray-700 font-medium">{selectedIds.length} selected</div>
              <div className="flex items-center gap-2">
                {/* Mirroring worker actions but placeholder */}
                <button title="Export" className="p-2 rounded hover:bg-gray-100 text-gray-700">
                  <FileSpreadsheet className="h-5 w-5" />
                </button>
              </div>
            </div>
          )}

          {/* Table */}
          <div className="bg-white shadow overflow-hidden border border-gray-200 rounded-lg">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={isAllSelected}
                      onChange={toggleSelectAll}
                      aria-label="Select all"
                      className="h-4 w-4 text-green-600 border-gray-300 rounded"
                    />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Equipment</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Compliance</th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredEquipment.map((eq) => (
                  <tr key={eq.id} className="hover:bg-gray-50">
                    <td className="px-4 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedIds.includes(eq.id)}
                        onChange={() => toggleSelectOne(eq.id)}
                        aria-label={`Select ${eq.name}`}
                        className="h-4 w-4 text-green-600 border-gray-300 rounded"
                      />
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-blue-50 border border-blue-200 flex items-center justify-center text-blue-700 text-xs font-semibold">
                            {eq.category?.slice(0, 2) || 'EQ'}
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            <Link to={`/sites/${siteId}/equipment/${eq.id}`} className="hover:text-green-500">
                              {eq.name}
                            </Link>
                          </div>
                          <div className="text-xs text-gray-500">{eq.equipmentNumber}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{eq.category}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${eq.overallStatus === 'active' ? 'bg-green-100 text-green-800' : eq.overallStatus === 'maintenance' ? 'bg-yellow-100 text-yellow-800' : eq.overallStatus === 'inactive' ? 'bg-gray-100 text-gray-800' : 'bg-red-100 text-red-800'}`}>
                        {eq.overallStatus}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getComplianceStatusColor(eq.complianceStatus as Compliance)}`}>
                        {getComplianceStatusIcon(eq.complianceStatus as Compliance)}
                        <span className="ml-1">{getComplianceStatusText(eq.complianceStatus as Compliance)}</span>
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <Link
                          to={`/sites/${siteId}/equipment/${eq.id}`}
                          className="px-3 py-1 text-xs border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors"
                          style={{ borderRadius: '5px' }}
                        >
                          View
                        </Link>
                        <Link
                          to={`/sites/${siteId}/equipment/${eq.id}/edit`}
                          className="px-3 py-1 text-xs border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors"
                          style={{ borderRadius: '5px' }}
                        >
                          Edit
                        </Link>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {filteredEquipment.length === 0 && (
              <div className="px-6 py-10 text-center">
                <Users className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No equipment found</h3>
                <p className="mt-1 text-sm text-gray-500">No equipment matches your search criteria.</p>
                <button
                  className="mt-3 text-green-500 hover:text-green-600 font-medium"
                  onClick={() => { setSearchTerm(''); setActiveFilters({}); }}
                >
                  Clear all filters
                </button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Universal Filter Modal */}
      <UniversalFilterModal
        isOpen={isFilterOpen}
        onClose={() => setIsFilterOpen(false)}
        title="Filter Equipment"
        filters={filterConfig}
        initialValues={activeFilters}
        onApplyFilters={handleApplyFilters}
        onClearFilters={handleClearFilters}
        size="xl"
      />
    </FloatingCard>
  );
};

export default EquipmentPage;
