import { useState, useEffect } from "react";
import { useParams, useLocation, Link } from "react-router-dom";
import {
	LayoutDashboard,
	Package,
	Plus,
	UserCheck,
	Wrench,
	ClipboardCheck,
	BarChart3,
	Building
} from "lucide-react";
import FloatingCard from "../components/layout/FloatingCard";
import TabContainer, { Tab } from "../components/data/shared/TabContainer";
import EquipmentDashboard from "../components/equipment/EquipmentDashboard";
import GeneralEquipment from "../components/equipment/GeneralEquipment";
import EquipmentInspections from "../components/equipment/EquipmentInspections";
import EquipmentMaintenance from "../components/equipment/EquipmentMaintenance";
import EquipmentAnalytics from "../components/equipment/EquipmentAnalytics";
import SiteEquipmentRequest from "../components/equipment/SiteEquipmentRequest";

const EquipmentPage = () => {
	const { siteId } = useParams<{ siteId: string }>();
	const location = useLocation();
	const [activeTab, setActiveTab] = useState("dashboard");

	// Handle hash navigation
	useEffect(() => {
		const hash = location.hash.replace("#", "");
		if (
			hash &&
			[
				"dashboard",
				"general-equipment",
				"inspections",
				"maintenance",
				"analytics",
			].includes(hash)
		) {
			setActiveTab(hash);
		}
	}, [location.hash]);

	// Update URL hash when tab changes
	const handleTabChange = (tabId: string) => {
		setActiveTab(tabId);
		window.location.hash = tabId;
	};

	const tabs: Tab[] = [
		{
			id: "dashboard",
			label: "Dashboard",
			icon: <LayoutDashboard className="h-4 w-4" />,
			content: (
				<EquipmentDashboard
					siteId={siteId!}
					onNavigateToTab={handleTabChange}
				/>
			)
		},
		{
			id: "general-equipment",
			label: "Equipment",
			icon: <Package className="h-4 w-4" />,
			content: <GeneralEquipment siteId={siteId!} />
		},
		{
			id: "request-equipment",
			label: "Request Equipment",
			icon: <Plus className="h-4 w-4" />,
			content: <SiteEquipmentRequest siteId={siteId!} />
		},
		{
			id: "assignments",
			label: "Assignments",
			icon: <UserCheck className="h-4 w-4" />,
			content: (
				<div className="p-6 text-center">
					<div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gray-100 flex items-center justify-center">
						<UserCheck className="w-8 h-8 text-gray-400" />
					</div>
					<h3 className="text-lg font-semibold mb-2">Equipment Assignments</h3>
					<p className="text-gray-600">
						View and manage equipment assignments for this site
					</p>
				</div>
			)
		},
		{
			id: "inspections",
			label: "Inspections",
			icon: <ClipboardCheck className="h-4 w-4" />,
			content: <EquipmentInspections siteId={siteId!} />
		},
		{
			id: "maintenance",
			label: "Maintenance",
			icon: <Wrench className="h-4 w-4" />,
			content: <EquipmentMaintenance siteId={siteId!} />
		},
		{
			id: "analytics",
			label: "Analytics",
			icon: <BarChart3 className="h-4 w-4" />,
			content: <EquipmentAnalytics siteId={siteId!} />
		}
	];



	return (
		<FloatingCard>
			{/* Breadcrumb Navigation */}
			<div className="flex items-center gap-2 mb-6 text-sm text-gray-600">
				<Link
					to="/company-equipment"
					className="flex items-center gap-1 hover:text-blue-600 transition-colors"
				>
					<Building className="h-4 w-4" />
					Company Equipment
				</Link>
				<span>/</span>
				<span className="text-gray-900 font-medium">Site Equipment</span>
			</div>

			{/* Page Header */}
			<div className="mb-6">
				<h1 className="text-2xl font-bold text-gray-900">Site Equipment Management</h1>
				<p className="text-gray-600 mt-1">
					Manage equipment assigned to this site and request additional equipment from company inventory
				</p>
			</div>

			{/* Tab Navigation */}
			<TabContainer
				tabs={tabs}
				activeTab={activeTab}
				onTabChange={handleTabChange}
				variant="centered"
			/>
		</FloatingCard>
	);
};

export default EquipmentPage;
