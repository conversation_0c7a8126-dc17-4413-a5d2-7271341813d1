import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import {
	LayoutDashboard,
	ClipboardList,
	FileText,
	Plus,
	TrendingUp,
	Clock,
	CheckCircle,
	AlertCircle,
	Users,
	Calendar,
	BarChart3,
} from "lucide-react";
import FloatingCard from "../components/layout/FloatingCard";
import TabContainer, { Tab } from "../components/data/shared/TabContainer";
import {
	mockFormTemplates,
	mockFormSubmissions,
	mockFormStats,
} from "../data/mockForms";
import { FormTemplate, FormSubmission } from "../types/forms";
import DynamicForm from "../components/forms/DynamicForm";

// Enhanced FormsDashboard component
const FormsDashboard = () => {
	const stats = mockFormStats;

	const getStatusColor = (status: FormSubmission["status"]) => {
		switch (status) {
			case "submitted":
				return "bg-green-100 text-green-800";
			case "under_review":
				return "bg-yellow-100 text-yellow-800";
			case "approved":
				return "bg-blue-100 text-blue-800";
			case "rejected":
				return "bg-red-100 text-red-800";
			case "draft":
				return "bg-gray-100 text-gray-800";
			default:
				return "bg-gray-100 text-gray-800";
		}
	};

	const formatStatus = (status: FormSubmission["status"]) => {
		return status.replace("_", " ").replace(/\b\w/g, (l) => l.toUpperCase());
	};

	return (
		<div className="space-y-6">
			{/* Key Metrics */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
				<div className="bg-white rounded-lg border border-gray-200 p-6">
					<div className="flex items-center">
						<div className="flex-shrink-0">
							<ClipboardList className="h-8 w-8 text-blue-600" />
						</div>
						<div className="ml-4">
							<p className="text-sm font-medium text-gray-500">
								Available Forms
							</p>
							<p className="text-2xl font-semibold text-gray-900">
								{mockFormTemplates.length}
							</p>
						</div>
					</div>
				</div>

				<div className="bg-white rounded-lg border border-gray-200 p-6">
					<div className="flex items-center">
						<div className="flex-shrink-0">
							<FileText className="h-8 w-8 text-green-600" />
						</div>
						<div className="ml-4">
							<p className="text-sm font-medium text-gray-500">
								Total Submissions
							</p>
							<p className="text-2xl font-semibold text-gray-900">
								{stats.totalSubmissions}
							</p>
						</div>
					</div>
				</div>

				<div className="bg-white rounded-lg border border-gray-200 p-6">
					<div className="flex items-center">
						<div className="flex-shrink-0">
							<AlertCircle className="h-8 w-8 text-yellow-600" />
						</div>
						<div className="ml-4">
							<p className="text-sm font-medium text-gray-500">
								Pending Review
							</p>
							<p className="text-2xl font-semibold text-gray-900">
								{stats.pendingReview}
							</p>
						</div>
					</div>
				</div>

				<div className="bg-white rounded-lg border border-gray-200 p-6">
					<div className="flex items-center">
						<div className="flex-shrink-0">
							<CheckCircle className="h-8 w-8 text-purple-600" />
						</div>
						<div className="ml-4">
							<p className="text-sm font-medium text-gray-500">
								Completed Today
							</p>
							<p className="text-2xl font-semibold text-gray-900">
								{stats.completedToday}
							</p>
						</div>
					</div>
				</div>
			</div>

			{/* Performance Metrics */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				<div className="bg-white rounded-lg border border-gray-200 p-6">
					<div className="flex items-center justify-between mb-4">
						<h3 className="text-lg font-medium text-gray-900">Performance</h3>
						<BarChart3 className="h-5 w-5 text-gray-400" />
					</div>
					<div className="space-y-4">
						<div className="flex items-center justify-between">
							<span className="text-sm text-gray-600">
								Average Completion Time
							</span>
							<div className="flex items-center space-x-1">
								<Clock className="h-4 w-4 text-gray-400" />
								<span className="text-sm font-medium">
									{stats.averageCompletionTime} min
								</span>
							</div>
						</div>
						<div className="flex items-center justify-between">
							<span className="text-sm text-gray-600">Completion Rate</span>
							<span className="text-sm font-medium text-green-600">94%</span>
						</div>
						<div className="flex items-center justify-between">
							<span className="text-sm text-gray-600">Response Time</span>
							<span className="text-sm font-medium">2.3 hours</span>
						</div>
					</div>
				</div>

				<div className="bg-white rounded-lg border border-gray-200 p-6">
					<div className="flex items-center justify-between mb-4">
						<h3 className="text-lg font-medium text-gray-900">
							Top Categories
						</h3>
						<TrendingUp className="h-5 w-5 text-gray-400" />
					</div>
					<div className="space-y-3">
						{stats.topCategories.map((category) => (
							<div
								key={category.category}
								className="flex items-center justify-between"
							>
								<span className="text-sm text-gray-600">
									{category.category}
								</span>
								<div className="flex items-center space-x-2">
									<div className="w-16 bg-gray-200 rounded-full h-2">
										<div
											className="bg-green-600 h-2 rounded-full"
											style={{
												width: `${(category.count / stats.totalSubmissions) * 100}%`,
											}}
										/>
									</div>
									<span className="text-sm font-medium w-8 text-right">
										{category.count}
									</span>
								</div>
							</div>
						))}
					</div>
				</div>
			</div>

			{/* Recent Activity */}
			<div className="bg-white rounded-lg border border-gray-200 p-6">
				<div className="flex items-center justify-between mb-4">
					<h3 className="text-lg font-medium text-gray-900">Recent Activity</h3>
					<Calendar className="h-5 w-5 text-gray-400" />
				</div>
				<div className="space-y-3">
					{stats.recentActivity.map((activity) => (
						<div
							key={activity.id}
							className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0"
						>
							<div className="flex items-center space-x-3">
								<div className="flex-shrink-0">
									<FileText className="h-5 w-5 text-gray-400" />
								</div>
								<div>
									<p className="text-sm font-medium text-gray-900">
										{activity.templateName}
									</p>
									<div className="flex items-center space-x-2 text-sm text-gray-500">
										<Users className="h-3 w-3" />
										<span>{activity.submittedBy}</span>
										<span>•</span>
										<span>{activity.submittedAt.toLocaleDateString()}</span>
									</div>
								</div>
							</div>
							<span
								className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(activity.status)}`}
							>
								{formatStatus(activity.status)}
							</span>
						</div>
					))}
				</div>
			</div>
		</div>
	);
};

const AvailableForms = ({
	onSelectForm,
}: {
	onSelectForm?: (template: FormTemplate) => void;
}) => {
	const [searchTerm, setSearchTerm] = useState("");
	const [selectedCategory, setSelectedCategory] = useState("all");

	const filteredTemplates = mockFormTemplates.filter((template) => {
		const matchesSearch =
			template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			template.description.toLowerCase().includes(searchTerm.toLowerCase());
		const matchesCategory =
			selectedCategory === "all" || template.category.id === selectedCategory;
		return matchesSearch && matchesCategory && template.status === "active";
	});

	const categories = [
		"all",
		...Array.from(new Set(mockFormTemplates.map((t) => t.category.id))),
	];

	const getCategoryColor = (categoryId: string) => {
		const category = mockFormTemplates.find(
			(t) => t.category.id === categoryId,
		)?.category;
		switch (category?.color) {
			case "red":
				return "bg-red-100 text-red-800";
			case "blue":
				return "bg-blue-100 text-blue-800";
			case "green":
				return "bg-green-100 text-green-800";
			case "purple":
				return "bg-purple-100 text-purple-800";
			case "orange":
				return "bg-orange-100 text-orange-800";
			case "emerald":
				return "bg-emerald-100 text-emerald-800";
			default:
				return "bg-gray-100 text-gray-800";
		}
	};

	return (
		<div className="space-y-6">
			{/* Header and Search */}
			<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
				<h2 className="text-xl font-semibold text-gray-900">Available Forms</h2>
				<div className="flex items-center space-x-4">
					<div className="relative">
						<input
							type="text"
							placeholder="Search forms..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="w-64 pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
						/>
						<div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
							<ClipboardList className="h-4 w-4 text-gray-400" />
						</div>
					</div>
				</div>
			</div>

			{/* Category Filter */}
			<div className="flex flex-wrap gap-2">
				{categories.map((categoryId) => {
					const categoryName =
						categoryId === "all"
							? "All"
							: mockFormTemplates.find((t) => t.category.id === categoryId)
									?.category.name || categoryId;

					return (
						<button
							key={categoryId}
							onClick={() => setSelectedCategory(categoryId)}
							className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
								selectedCategory === categoryId
									? "bg-green-600 text-white"
									: "bg-gray-100 text-gray-700 hover:bg-gray-200"
							}`}
						>
							{categoryName}
						</button>
					);
				})}
			</div>

			{/* Forms Grid */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
				{filteredTemplates.map((template) => (
					<div
						key={template.id}
						className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-md transition-shadow"
					>
						<div className="flex items-start justify-between mb-4">
							<div className="flex-1">
								<h3 className="text-lg font-medium text-gray-900 mb-2">
									{template.name}
								</h3>
								<p className="text-sm text-gray-500 mb-3">
									{template.description}
								</p>

								<div className="flex items-center space-x-2 mb-3">
									<span
										className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getCategoryColor(template.category.id)}`}
									>
										{template.category.name}
									</span>
									{template.estimatedDuration && (
										<div className="flex items-center text-xs text-gray-500">
											<Clock className="h-3 w-3 mr-1" />
											<span>~{template.estimatedDuration} min</span>
										</div>
									)}
								</div>

								{template.tags.length > 0 && (
									<div className="flex flex-wrap gap-1 mb-3">
										{template.tags.slice(0, 3).map((tag) => (
											<span
												key={tag}
												className="inline-flex items-center px-2 py-0.5 rounded text-xs bg-gray-100 text-gray-600"
											>
												{tag}
											</span>
										))}
										{template.tags.length > 3 && (
											<span className="text-xs text-gray-500">
												+{template.tags.length - 3} more
											</span>
										)}
									</div>
								)}
							</div>
						</div>

						<div className="space-y-2">
							<button
								onClick={() => onSelectForm?.(template)}
								className="w-full flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 transition-colors"
							>
								<ClipboardList className="h-4 w-4 mr-2" />
								Fill Form
							</button>

							<button className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 transition-colors">
								<FileText className="h-4 w-4 mr-2" />
								Preview
							</button>
						</div>
					</div>
				))}
			</div>

			{filteredTemplates.length === 0 && (
				<div className="text-center py-12">
					<ClipboardList className="h-12 w-12 text-gray-400 mx-auto mb-4" />
					<h3 className="text-lg font-medium text-gray-900 mb-2">
						No forms found
					</h3>
					<p className="text-gray-500">
						{searchTerm || selectedCategory !== "all"
							? "Try adjusting your search or filter criteria"
							: "No forms are currently available"}
					</p>
				</div>
			)}
		</div>
	);
};

const MySubmissions = ({
	onViewSubmission,
}: {
	onViewSubmission?: (submission: FormSubmission) => void;
}) => {
	const [statusFilter, setStatusFilter] = useState<string>("all");

	const filteredSubmissions = mockFormSubmissions.filter((submission) => {
		return statusFilter === "all" || submission.status === statusFilter;
	});

	const getStatusColor = (status: FormSubmission["status"]) => {
		switch (status) {
			case "submitted":
				return "bg-green-100 text-green-800";
			case "under_review":
				return "bg-yellow-100 text-yellow-800";
			case "approved":
				return "bg-blue-100 text-blue-800";
			case "rejected":
				return "bg-red-100 text-red-800";
			case "draft":
				return "bg-gray-100 text-gray-800";
			case "requires_action":
				return "bg-orange-100 text-orange-800";
			default:
				return "bg-gray-100 text-gray-800";
		}
	};

	const formatStatus = (status: FormSubmission["status"]) => {
		return status.replace("_", " ").replace(/\b\w/g, (l) => l.toUpperCase());
	};

	const statusOptions = [
		{ value: "all", label: "All Submissions" },
		{ value: "draft", label: "Drafts" },
		{ value: "submitted", label: "Submitted" },
		{ value: "under_review", label: "Under Review" },
		{ value: "approved", label: "Approved" },
		{ value: "rejected", label: "Rejected" },
		{ value: "requires_action", label: "Requires Action" },
	];

	return (
		<div className="space-y-6">
			{/* Header and Filters */}
			<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
				<h2 className="text-xl font-semibold text-gray-900">My Submissions</h2>
				<div className="flex items-center space-x-4">
					<select
						value={statusFilter}
						onChange={(e) => setStatusFilter(e.target.value)}
						className="border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
					>
						{statusOptions.map((option) => (
							<option key={option.value} value={option.value}>
								{option.label}
							</option>
						))}
					</select>
				</div>
			</div>

			{/* Submissions Table */}
			<div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
				<div className="px-6 py-4 border-b border-gray-200">
					<div className="flex items-center justify-between">
						<h3 className="text-lg font-medium text-gray-900">
							{statusFilter === "all"
								? "All Submissions"
								: statusOptions.find((o) => o.value === statusFilter)?.label}
						</h3>
						<span className="text-sm text-gray-500">
							{filteredSubmissions.length} submission
							{filteredSubmissions.length !== 1 ? "s" : ""}
						</span>
					</div>
				</div>

				{filteredSubmissions.length > 0 ? (
					<div className="divide-y divide-gray-200">
						{filteredSubmissions.map((submission) => (
							<div
								key={submission.id}
								className="px-6 py-4 hover:bg-gray-50 transition-colors"
							>
								<div className="flex items-center justify-between">
									<div className="flex-1">
										<div className="flex items-center space-x-3 mb-2">
											<h4 className="text-sm font-medium text-gray-900">
												{submission.templateName}
											</h4>
											<span
												className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(submission.status)}`}
											>
												{formatStatus(submission.status)}
											</span>
										</div>

										<div className="flex items-center space-x-4 text-sm text-gray-500">
											<div className="flex items-center space-x-1">
												<Calendar className="h-3 w-3" />
												<span>
													Submitted{" "}
													{submission.submissionDate.toLocaleDateString()}
												</span>
											</div>

											{submission.completionTime && (
												<div className="flex items-center space-x-1">
													<Clock className="h-3 w-3" />
													<span>{submission.completionTime} min</span>
												</div>
											)}

											<div className="flex items-center space-x-1">
												<Users className="h-3 w-3" />
												<span>{submission.siteName}</span>
											</div>
										</div>

										{submission.reviewedBy && (
											<p className="text-sm text-gray-500 mt-1">
												Reviewed by {submission.reviewedByName} on{" "}
												{submission.reviewedAt?.toLocaleDateString()}
											</p>
										)}

										{submission.reviewComments && (
											<p className="text-sm text-gray-600 mt-2 italic">
												"{submission.reviewComments}"
											</p>
										)}
									</div>

									<div className="flex items-center space-x-2 ml-4">
										<button
											onClick={() => onViewSubmission?.(submission)}
											className="text-green-600 hover:text-green-900 text-sm font-medium"
										>
											View
										</button>

										{submission.status === "draft" && (
											<button className="text-blue-600 hover:text-blue-900 text-sm font-medium">
												Continue
											</button>
										)}

										{submission.status === "requires_action" && (
											<button className="text-orange-600 hover:text-orange-900 text-sm font-medium">
												Action Required
											</button>
										)}
									</div>
								</div>
							</div>
						))}
					</div>
				) : (
					<div className="text-center py-12">
						<FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
						<h3 className="text-lg font-medium text-gray-900 mb-2">
							No submissions found
						</h3>
						<p className="text-gray-500">
							{statusFilter === "all"
								? "You haven't submitted any forms yet"
								: `No submissions with status "${statusOptions.find((o) => o.value === statusFilter)?.label}"`}
						</p>
					</div>
				)}
			</div>
		</div>
	);
};

const CreateSubmission = ({
	selectedTemplate,
	onBack,
}: {
	selectedTemplate?: FormTemplate;
	onBack?: () => void;
}) => {
	if (selectedTemplate) {
		const handleSave = async (data: any, isDraft: boolean) => {
			console.log("Saving form data:", {
				data,
				isDraft,
				templateId: selectedTemplate.id,
			});
			// In a real app, this would call an API
			alert(`Form ${isDraft ? "saved as draft" : "submitted"} successfully!`);
		};

		const handleSubmit = async (data: any) => {
			console.log("Submitting form data:", {
				data,
				templateId: selectedTemplate.id,
			});
			// In a real app, this would call an API
			alert("Form submitted successfully!");
			onBack?.();
		};

		return (
			<div className="space-y-6">
				<div className="flex items-center space-x-4">
					<button
						onClick={onBack}
						className="flex items-center space-x-2 text-gray-600 hover:text-gray-900"
					>
						<ClipboardList className="h-4 w-4" />
						<span>Back to Available Forms</span>
					</button>
				</div>

				<DynamicForm
					template={selectedTemplate}
					onSave={handleSave}
					onSubmit={handleSubmit}
					showProgress={true}
				/>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			<h2 className="text-xl font-semibold text-gray-900">
				Create New Submission
			</h2>

			<div className="bg-white rounded-lg border border-gray-200 p-6">
				<div className="text-center py-12">
					<ClipboardList className="h-12 w-12 text-gray-400 mx-auto mb-4" />
					<h3 className="text-lg font-medium text-gray-900 mb-2">
						Select a Form
					</h3>
					<p className="text-gray-500 mb-6">
						Choose from available forms to create a new submission
					</p>
					<p className="text-sm text-gray-400">
						Use the "Available Forms" tab to browse and select a form to fill
						out
					</p>
				</div>
			</div>
		</div>
	);
};

const FormsPage = () => {
	const location = useLocation();
	const [activeTab, setActiveTab] = useState("dashboard");
	const [selectedTemplate, setSelectedTemplate] = useState<FormTemplate | undefined>(
		undefined,
	);
	const [_selectedSubmission, setSelectedSubmission] =
		useState<FormSubmission | null>(null);

	const validTabs = ["dashboard", "available", "submissions", "create"];

	// Handle URL hash navigation
	useEffect(() => {
		const hash = location.hash.replace("#", "");
		if (hash && validTabs.includes(hash)) {
			setActiveTab(hash);
		} else if (!hash) {
			setActiveTab("dashboard");
		}
	}, [location.hash]);

	// Handle tab navigation with hash updates
	const handleNavigateToTab = (tabId: string) => {
		setActiveTab(tabId);
		window.location.hash = tabId;

		// Clear selections when navigating away
		if (tabId !== "create") {
			setSelectedTemplate(undefined);
		}
		if (tabId !== "submissions") {
			setSelectedSubmission(null);
		}
	};

	// Handle form selection from Available Forms
	const handleSelectForm = (template: FormTemplate) => {
		setSelectedTemplate(template);
		setActiveTab("create");
		window.location.hash = "create";
	};

	// Handle submission viewing
	const handleViewSubmission = (submission: FormSubmission) => {
		setSelectedSubmission(submission);
		// In a real app, this might navigate to a detailed view
		console.log("Viewing submission:", submission);
	};

	// Handle going back from form filling
	const handleBackFromForm = () => {
		setSelectedTemplate(undefined);
		setActiveTab("available");
		window.location.hash = "available";
	};

	const tabs: Tab[] = [
		{
			id: "dashboard",
			label: "Dashboard",
			icon: <LayoutDashboard className="h-4 w-4" />,
			content: <FormsDashboard />,
		},
		{
			id: "available",
			label: "Available Forms",
			icon: <ClipboardList className="h-4 w-4" />,
			content: <AvailableForms onSelectForm={handleSelectForm} />,
		},
		{
			id: "submissions",
			label: "My Submissions",
			icon: <FileText className="h-4 w-4" />,
			content: <MySubmissions onViewSubmission={handleViewSubmission} />,
		},
		{
			id: "create",
			label: "Fill Form",
			icon: <Plus className="h-4 w-4" />,
			content: (
				<CreateSubmission
					selectedTemplate={selectedTemplate}
					onBack={handleBackFromForm}
				/>
			),
		},
	];

	return (
		<FloatingCard title="Forms">
			<TabContainer
				tabs={tabs}
				activeTab={activeTab}
				onTabChange={handleNavigateToTab}
			/>
		</FloatingCard>
	);
};

export default FormsPage;
