# Inspection Forms - Usage Guide

This guide explains how to access and use the Inspection Form system in the workforce management application.

## Available Routes

### 1. Demo Page (Recommended for Testing)
**URL:** `/demo/inspection-forms`

This is a standalone demo page that showcases all available inspection forms with:
- List of all equipment inspection forms
- Form descriptions and inspection point counts
- Easy access to individual forms
- Feature explanations and usage instructions

**How to access:**
1. Start the development server: `npm run dev`
2. Navigate to: `http://localhost:5173/demo/inspection-forms`
3. Select any equipment type to open its inspection form

### 2. Direct Form Access
**URL:** `/inspections/form/{id}`

Access specific inspection forms directly using their ID.

**Available Form IDs:**
- `crane-inspection` - Crane Inspection Form
- `excavator-inspection` - Excavator Inspection Form
- `forklift-inspection` - Forklift Inspection Form
- `generator-inspection` - Generator Inspection Form
- `scaffolding-inspection` - Scaffolding Inspection Form

**Example URLs:**
- `/inspections/form/crane-inspection`
- `/inspections/form/excavator-inspection`
- `/inspections/form/forklift-inspection`

### 3. Site-Specific Form Access
**URL:** `/sites/{siteId}/inspections/form/{id}`

Access inspection forms within a specific site context.

**Example URLs:**
- `/sites/site-1/inspections/form/crane-inspection`
- `/sites/westlands-site/inspections/form/excavator-inspection`

## Form Structure

Each inspection form follows the same structure based on the Vue template you provided:

### Header Section
- **Form Title**: Equipment-specific inspection form name
- **Navigation**: Back button to return to previous page
- **Disclaimer**: Instructions for YES/NO responses

### Main Table
- **S/No**: Serial number for each inspection point
- **Description**: Detailed description of what to inspect
- **Answer**: YES/NO radio button selection
- **Remarks**: Text area for detailed comments

### Footer Section
- **Approval**: Overall approval status (YES/NO) with remarks
- **General Comments**: Overall observations and recommendations
- **Inspector Details**: Name, date, time, and signature

## Form Features

### 1. YES/NO Responses
- Radio button selection for each inspection point
- Clear visual distinction between YES and NO options
- Required selection for comprehensive inspection

### 2. Remarks System
- Individual remarks for each inspection point
- General comments section for overall observations
- Approval remarks for final assessment

### 3. Inspector Information
- **Inspected By**: Inspector's name (editable)
- **Date**: Auto-generated current date (read-only)
- **Time**: Auto-generated current time (read-only)
- **Signature**: Text area for digital signature or description

### 4. Form Validation
- All form data is captured and validated
- Real-time state management
- Submit button with loading states

## Data Source

The inspection forms are generated from `inspectionFormTemplate.ts` which contains:

```typescript
export const inspectionFormTypes = [
  {
    id: "crane-inspection",
    name: "Crane Inspection Form",
    information: [
      { serialNo: 1, description: "Check crane hook for cracks or deformation" },
      { serialNo: 2, description: "Inspect wire ropes for fraying or damage" },
      // ... more inspection points
    ]
  },
  // ... more form types
];
```

## Form Submission

When a form is submitted, it captures:

```typescript
interface InspectionFormData {
  approved: {
    value: boolean;        // Overall approval status
    remarks: string;       // Approval remarks
  };
  generalComments: string; // General observations
  inspectedBy: string;     // Inspector name
  date: string;           // Auto-generated inspection date (current date)
  time: string;           // Auto-generated inspection time (current time)
  signature: string;      // Digital signature
  responses: {            // Individual responses
    [serialNo]: {
      response: 'YES' | 'NO';
      remarks: string;
    }
  };
}
```

## Integration Examples

### Adding to Existing Components

```tsx
import { Link } from 'react-router-dom';
import { ClipboardCheck } from 'lucide-react';

// Link to specific form
<Link 
  to="/inspections/form/crane-inspection"
  className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
>
  <ClipboardCheck className="h-4 w-4 mr-2" />
  Crane Inspection
</Link>

// Dynamic form selection
const handleFormSelect = (formId: string) => {
  navigate(`/inspections/form/${formId}`);
};
```

### Adding to Quick Actions

```tsx
import QuickActionCard from '../components/data/shared/QuickActionCard';

<QuickActionCard
  title="Equipment Inspections"
  description="Perform safety inspections"
  icon={<ClipboardCheck className="h-5 w-5" />}
  onClick={() => navigate('/demo/inspection-forms')}
/>
```

## Customization

### Adding New Inspection Forms

1. Add new form data to `inspectionFormTemplate.ts`:

```typescript
{
  id: "new-equipment-inspection",
  name: "New Equipment Inspection Form",
  information: [
    { serialNo: 1, description: "Check equipment condition" },
    { serialNo: 2, description: "Verify safety features" },
    // ... more inspection points
  ]
}
```

2. The form will automatically be available at `/inspections/form/new-equipment-inspection`

### Modifying Form Structure

The form structure follows the Vue template you provided:
- Table-based layout with inspection points
- YES/NO radio button responses
- Remarks text areas
- Footer with approval and inspector details

## Styling

The forms use Tailwind CSS and follow the existing design system:
- Consistent with the application's green color scheme
- Responsive design for mobile and desktop
- Professional table layout
- Clear form controls and validation states

## Backend Integration

The forms are ready for backend integration:
- Form submission handler captures all data
- Structured data format for API calls
- Error handling and loading states
- Success feedback and navigation

## Mobile Responsiveness

The forms are fully responsive:
- Table scrolls horizontally on mobile
- Form controls adapt to screen size
- Touch-friendly interface elements
- Optimized for tablet and phone use

## Accessibility

The forms include accessibility features:
- Proper form labels and associations
- Keyboard navigation support
- Screen reader compatible
- High contrast design elements
