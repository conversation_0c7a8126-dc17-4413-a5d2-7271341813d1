import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLef<PERSON>, ClipboardCheck, FileText, ChevronRight, Calendar} from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import { inspectionFormTypes } from '../data/inspectionFormTemplate';

const InspectionFormsListPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId } = useParams<{ siteId: string }>();

  const handleFormSelect = (formId: string) => {
    if (siteId) {
      navigate(`/sites/${siteId}/inspections/form/${formId}`);
    } else {
      navigate(`/inspections/form/${formId}`);
    }
  };

  const handleBack = () => {
    if (siteId) {
      navigate(`/sites/${siteId}/inspections`);
    } else {
      navigate('/');
    }
  };

  return (
    <FloatingCard title="Inspection Forms">
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBack}
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                Back
              </button>
              <div className="h-6 w-px bg-gray-300" />
              <div className="flex items-center space-x-2">
                <ClipboardCheck className="h-6 w-6 text-green-600" />
                <h1 className="text-2xl font-bold text-gray-900">Inspection Forms</h1>
              </div>
            </div>
          </div>
          
          {siteId && (
            <div className="mt-2">
              <p className="text-sm text-gray-600">
                Site: <span className="font-medium">{siteId}</span>
              </p>
            </div>
          )}
        </div>

        {/* Main Content */}
        <div className="px-6 py-8">
          <div className="max-w-4xl mx-auto">
            {/* Introduction */}
            <div className="bg-white rounded-lg shadow p-6 mb-8">
              <div className="flex items-center mb-4">
                <ClipboardCheck className="h-8 w-8 text-green-600 mr-3" />
                <div>
                  <h2 className="text-xl font-bold text-gray-900">Equipment Inspection Forms</h2>
                  <p className="text-gray-600">Select an equipment type to perform safety inspection</p>
                </div>
              </div>
              
              <div className="bg-blue-50 border-l-4 border-blue-400 p-4 rounded">
                <div className="flex">
                  <div className="ml-3">
                    <p className="text-sm text-blue-700">
                      <strong>Important:</strong> All equipment must be inspected regularly according to safety regulations. 
                      Complete all inspection points and provide detailed remarks where necessary.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Forms List */}
            <div className="bg-white rounded-lg shadow overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-medium text-gray-900">Available Inspection Forms</h3>
                <p className="text-sm text-gray-600 mt-1">
                  {inspectionFormTypes.length} equipment types available for inspection
                </p>
              </div>

              <div className="divide-y divide-gray-200">
                {inspectionFormTypes.map((form) => (
                  <div
                    key={form.id}
                    className="px-6 py-4 hover:bg-gray-50 cursor-pointer transition-colors group"
                    onClick={() => handleFormSelect(form.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        <div className="flex-shrink-0">
                          <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center group-hover:bg-green-200 transition-colors">
                            <FileText className="h-5 w-5 text-green-600" />
                          </div>
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <h4 className="text-lg font-medium text-gray-900 group-hover:text-green-600 transition-colors">
                            {form.name}
                          </h4>
                          <div className="flex items-center space-x-4 mt-1">
                            <div className="flex items-center text-sm text-gray-500">
                              <ClipboardCheck className="h-4 w-4 mr-1" />
                              {form.information.length} inspection points
                            </div>
                            <div className="flex items-center text-sm text-gray-500">
                              <FileText className="h-4 w-4 mr-1" />
                              Form ID: {form.id}
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-3">
                        <div className="text-right">
                          <div className="text-sm font-medium text-gray-900">Ready for Inspection</div>
                          <div className="text-xs text-gray-500">Click to start</div>
                        </div>
                        <ChevronRight className="h-5 w-5 text-gray-400 group-hover:text-green-600 transition-colors" />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Quick Stats */}
            <div className="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <FileText className="h-8 w-8 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <div className="text-2xl font-bold text-gray-900">{inspectionFormTypes.length}</div>
                    <div className="text-sm text-gray-600">Equipment Types</div>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <ClipboardCheck className="h-8 w-8 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <div className="text-2xl font-bold text-gray-900">
                      {inspectionFormTypes.reduce((total, form) => total + form.information.length, 0)}
                    </div>
                    <div className="text-sm text-gray-600">Total Inspection Points</div>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <Calendar className="h-8 w-8 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <div className="text-2xl font-bold text-gray-900">Daily</div>
                    <div className="text-sm text-gray-600">Inspection Frequency</div>
                  </div>
                </div>
              </div>
            </div>

            {/* Instructions */}
            <div className="mt-8 bg-white rounded-lg shadow p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">How to Complete an Inspection</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-3">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                      <span className="text-xs font-medium text-green-600">1</span>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Select Equipment</h4>
                      <p className="text-sm text-gray-600">Choose the equipment type you want to inspect from the list above</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                      <span className="text-xs font-medium text-green-600">2</span>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Complete Inspection Points</h4>
                      <p className="text-sm text-gray-600">Go through each inspection point and select YES or NO based on equipment condition</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                      <span className="text-xs font-medium text-green-600">3</span>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Add Remarks</h4>
                      <p className="text-sm text-gray-600">Provide detailed comments for any issues or observations</p>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-3">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                      <span className="text-xs font-medium text-green-600">4</span>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Overall Approval</h4>
                      <p className="text-sm text-gray-600">Set the overall approval status and add general comments</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                      <span className="text-xs font-medium text-green-600">5</span>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Inspector Details</h4>
                      <p className="text-sm text-gray-600">Enter your name and signature (date/time are auto-generated)</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mr-3 mt-0.5">
                      <span className="text-xs font-medium text-green-600">6</span>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900">Submit Form</h4>
                      <p className="text-sm text-gray-600">Review and submit the completed inspection form</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </FloatingCard>
  );
};

export default InspectionFormsListPage;
