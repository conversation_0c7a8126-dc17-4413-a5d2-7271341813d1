import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@apollo/client';
import { ArrowLeft, CheckCircle, XCircle, Calendar, User, Eye, X } from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import { GET_INSPECTION_BY_ID } from '../graphql/queries';
import { FILE_BASE_URL } from '../utils/constants';

interface InspectionItem {
  id: number;
  description: string;
  isTrue: boolean;
  remarks: string;
  imageFiles: Array<{
    id: number;
    fileName: string;
    url: string;
    contentType: string;
    size: number;
  }>;
}

interface Inspection {
  id: number;
  approved: boolean;
  comments: string;
  inspectionType: string;
  inspectedBy: {
    id: number;
    name: string;
  };
  signatureFile?: {
    id: number;
    fileName: string;
    url: string;
  };
  inspectionItems: InspectionItem[];
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
}

const InspectionViewPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [previewImage, setPreviewImage] = useState<string | null>(null);

  const { data, loading, error } = useQuery(GET_INSPECTION_BY_ID, {
    variables: { id: parseInt(id || '0') },
    skip: !id,
  });

  const inspection: Inspection | null = data?.inspectionById || null;

  const handleBack = () => {
    navigate(-1);
  };

  const formatInspectionType = (type: string) => {
    return type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // const formatFileSize = (bytes: number) => {
  //   if (bytes === 0) return '0 Bytes';
  //   const k = 1024;
  //   const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  //   const i = Math.floor(Math.log(bytes) / Math.log(k));
  //   return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  // };

  const getStatusIcon = (approved: boolean) => {
    return approved ? (
      <CheckCircle className="h-6 w-6 text-green-500" />
    ) : (
      <XCircle className="h-6 w-6 text-red-500" />
    );
  };

  const getStatusColor = (approved: boolean) => {
    return approved
      ? 'bg-green-100 text-green-800 border-green-200'
      : 'bg-red-100 text-red-800 border-red-200';
  };

  const getAnswerColor = (isTrue: boolean) => {
    return isTrue
      ? 'bg-green-100 text-green-800'
      : 'bg-red-100 text-red-800';
  };

  if (loading) {
    return (
      <FloatingCard title='Loading...'>
        <div className="animate-pulse p-6">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="space-y-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </FloatingCard>
    );
  }

  if (error || !inspection) {
    return (
      <FloatingCard title='Error'>
        <div className="p-6 text-center">
          <p className="text-red-600 mb-4">
            {error ? 'Error loading inspection' : 'Inspection not found'}
          </p>
          <button
            onClick={handleBack}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            Go Back
          </button>
        </div>
      </FloatingCard>
    );
  }

  return (
    <FloatingCard title='Inspection View'>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBack}
                className="p-2 hover:bg-gray-100 rounded-full transition-colors"
              >
                <ArrowLeft className="h-5 w-5 text-gray-600" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">
                  {formatInspectionType(inspection.inspectionType)}
                </h1>
                <p className="text-sm text-gray-600">Inspection #{inspection.id}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              {getStatusIcon(inspection.approved)}
              <span
                className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(inspection.approved)}`}
              >
                {inspection.approved ? 'Approved' : 'Failed'}
              </span>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="px-6 py-8">
          {/* Inspection Details */}
          <div className="bg-white rounded-lg shadow mb-6 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Inspection Details</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-3">
                <div className="flex items-center text-sm">
                  <User className="h-4 w-4 text-gray-400 mr-2" />
                  <span className="text-gray-600">Inspector:</span>
                  <span className="ml-2 font-medium">{inspection.inspectedBy.name}</span>
                </div>
                <div className="flex items-center text-sm">
                  <Calendar className="h-4 w-4 text-gray-400 mr-2" />
                  <span className="text-gray-600">Date:</span>
                  <span className="ml-2 font-medium">{formatDate(inspection.createdAt)}</span>
                </div>
              </div>
              <div className="space-y-3">
                <div className="text-sm">
                  <span className="text-gray-600">Type:</span>
                  <span className="ml-2 font-medium">{formatInspectionType(inspection.inspectionType)}</span>
                </div>
                <div className="text-sm">
                  <span className="text-gray-600">Items:</span>
                  <span className="ml-2 font-medium">{inspection.inspectionItems.length} items</span>
                </div>
              </div>
            </div>

            {inspection.comments && (
              <div className="mt-4 pt-4 border-t border-gray-200">
                <h3 className="text-sm font-medium text-gray-900 mb-2">General Comments</h3>
                <p className="text-sm text-gray-600">{inspection.comments}</p>
              </div>
            )}
          </div>

          {/* Inspection Items */}
          <div className="bg-white rounded-lg shadow overflow-hidden">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Inspection Items</h2>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      #
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Description
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Result
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Remarks
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Photos
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {inspection.inspectionItems.map((item, index) => (
                    <tr key={item.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {index + 1}
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-900">
                        {item.description}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span
                          className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getAnswerColor(item.isTrue)}`}
                        >
                          {item.isTrue ? 'YES' : 'NO'}
                        </span>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-600">
                        {item.remarks || '-'}
                      </td>
                      <td className="px-6 py-4">
                        {item.imageFiles.length > 0 ? (
                          <div className="flex flex-wrap gap-2">
                            {item.imageFiles.map((file) => (
                              <div key={file.id} className="relative group"
                                onClick={() => {
                                  setPreviewImage(`${FILE_BASE_URL}${file.url}`)
                                }}>
                                <img
                                  src={`${FILE_BASE_URL}${file.url}`}
                                  alt={file.fileName}
                                  className="w-16 h-16 object-cover rounded border border-gray-200 cursor-pointer hover:opacity-75"

                                />
                                <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded flex items-center justify-center opacity-0 group-hover:opacity-100">
                                  <Eye className="h-4 w-4 text-white" />
                                </div>
                              </div>
                            ))}
                          </div>
                        ) : (
                          <span className="text-gray-400 text-sm">No photos</span>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* Image Preview Modal */}
        {previewImage && (
          <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
            <div className="relative max-w-4xl max-h-full">
              <button
                onClick={() => setPreviewImage(null)}
                className="absolute top-0 right-0 p-2 bg-transparent rounded-full hover:bg-gray-900/50"
              >
                <X className="h-5 w-5 text-gray-600" />
              </button>
              <img
                src={previewImage}
                alt="Photo preview"
                className="max-w-full max-h-full object-contain rounded-lg"
              />
            </div>
          </div>
        )}
      </div>
    </FloatingCard>
  );
};

export default InspectionViewPage;
