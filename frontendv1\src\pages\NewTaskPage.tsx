import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useMutation, useQuery } from '@apollo/client';
import { toast } from 'react-toastify';
import {
  User,
  Users,
  Calendar,
  Clock,
  FileText,
  Save,
  X,
  Search,
  Plus,
  Minus,
  MapPin
} from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import { CREATE_JOB } from '../graphql/mutations';
import { GET_ALL_WORKERS, GET_ALL_TRADES } from '../graphql/queries';

interface Worker {
  id: number;
  name: string;
  company: string;
}

interface FormData {
  title: string;
  description: string;
  location: string;
  tradeIds: number[];
  chiefEngineerId: number | null;
  timeForCompletion: string;
  startDate: string;
}

interface CreateJobInput {
  title: string;
  tradeIds: number[];
  chiefEngineerId: number;
  location: string;
  timeForCompletion?: string;
  startDate?: string;
  description?: string;
}

// Common site locations
const SITE_LOCATIONS = [
  "Building A - Floor 1",
  "Building A - Floor 2",
  "Building A - Floor 3",
  "Building B - Basement",
  "Building B - Floor 1",
  "Building B - Floor 2",
  "Parking Garage",
  "Main Entrance",
  "Construction Zone",
  "Equipment Storage",
  "Mechanical Room",
  "Rooftop",
  "Foundation Area",
  "Exterior Areas",
  "Utility Rooms",
];

const NewTaskPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId } = useParams<{ siteId: string }>();

  const [formData, setFormData] = useState<FormData>({
    title: '',
    description: '',
    location: '',
    tradeIds: [],
    chiefEngineerId: null,
    timeForCompletion: 'P1D', // ISO-8601 duration format (1 day)
    startDate: ''
  });

  const [chiefEngineerSearchTerm, setChiefEngineerSearchTerm] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Set default start date to next day
  useEffect(() => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowStr = tomorrow.toISOString().split('T')[0];
    setFormData(prev => ({ ...prev, startDate: tomorrowStr }));
  }, []);

  const { data: workersData, loading: workersLoading } = useQuery(GET_ALL_WORKERS);
  const { data: tradesData, loading: tradesLoading } = useQuery(GET_ALL_TRADES);

  const [createJob] = useMutation(CREATE_JOB, {
    onCompleted: (data) => {
      toast.success(`Task "${data.createJob.title}" created successfully!`);
      navigate(`/sites/${siteId}/tasks`);
    },
    onError: (error) => {
      toast.error(`Failed to create task: ${error.message}`);
    }
  });

  const workers = workersData?.allWorkers || [];
  const trades = tradesData?.allTrades || [];

  const filteredChiefEngineers = workers.filter((worker: Worker) =>
    worker.name.toLowerCase().includes(chiefEngineerSearchTerm.toLowerCase()) ||
    worker.company.toLowerCase().includes(chiefEngineerSearchTerm.toLowerCase())
  );

  const selectedTrades = trades.filter((trade: any) =>
    formData.tradeIds.includes(trade.id)
  );

  const selectedChiefEngineer = workers.find((worker: Worker) =>
    worker.id === formData.chiefEngineerId
  );

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    }

    if (!formData.location.trim()) {
      newErrors.location = 'Location is required';
    }

    if (!formData.chiefEngineerId) {
      newErrors.chiefEngineer = 'Chief Engineer must be selected';
    }

    if (!formData.startDate) {
      newErrors.startDate = 'Start date is required';
    } else {
      // Check if start date is not in the past
      const selectedDate = new Date(formData.startDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      if (selectedDate < today) {
        newErrors.startDate = 'Start date cannot be in the past';
      }
    }

    if (!formData.timeForCompletion) {
      newErrors.timeForCompletion = 'Time for completion is required';
    }

    if (formData.tradeIds.length === 0) {
      newErrors.trades = 'At least one trade must be selected';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      const variables = {
        input: {
          title: formData.title,
          description: formData.description,
          location: formData.location,
          tradeIds: formData.tradeIds,
          chiefEngineerId: formData.chiefEngineerId!,
          timeForCompletion: formData.timeForCompletion,
          startDate: new Date(formData.startDate ? formData.startDate + "T00:00:00" : new Date()).toISOString(),
        }
      }
      
      await createJob({
        variables: variables,
      });
    } catch (error) {
      console.error('Error creating task:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleTradeToggle = (tradeId: number) => {
    setFormData(prev => ({
      ...prev,
      tradeIds: prev.tradeIds.includes(tradeId)
        ? prev.tradeIds.filter(id => id !== tradeId)
        : [...prev.tradeIds, tradeId]
    }));
  };

  const handleChiefEngineerSelect = (workerId: number) => {
    setFormData(prev => ({ ...prev, chiefEngineerId: workerId }));
    setChiefEngineerSearchTerm('');
  };

  const breadcrumbs = [
    { name: 'Dashboard', path: '/' },
    { name: 'Tasks', path: `/sites/${siteId}/tasks` },
    { name: 'New Task', path: `/sites/${siteId}/tasks/new` }
  ];

  return (
    <FloatingCard title="Create New Task" breadcrumbs={breadcrumbs}>
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Title */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <FileText className="h-4 w-4 inline mr-2" />
            Task Title *
          </label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.title ? 'border-red-500' : 'border-gray-300'
              }`}
            placeholder="Enter task title"
          />
          {errors.title && <p className="text-red-500 text-sm mt-1">{errors.title}</p>}
        </div>

        {/* Description */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <FileText className="h-4 w-4 inline mr-2" />
            Description *
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            rows={4}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.description ? 'border-red-500' : 'border-gray-300'
              }`}
            placeholder="Enter task description"
          />
          {errors.description && <p className="text-red-500 text-sm mt-1">{errors.description}</p>}
        </div>

        {/* Start Date */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Calendar className="h-4 w-4 inline mr-2" />
            Start Date *
          </label>
          <input
            type="date"
            value={formData.startDate}
            onChange={(e) => setFormData(prev => ({ ...prev, startDate: new Date(e.target.value).toISOString().split("T")[0] }))}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.startDate ? 'border-red-500' : 'border-gray-300'
              }`}
            min={new Date().toISOString().split('T')[0]}
          />
          {errors.startDate && <p className="text-red-500 text-sm mt-1">{errors.startDate}</p>}
          <p className="text-xs text-gray-500 mt-1">
            Default is tomorrow. Cannot select past dates.
          </p>
        </div>

        {/* Time for Completion */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Clock className="h-4 w-4 inline mr-2" />
            Estimated Duration *
          </label>
          <select
            value={formData.timeForCompletion}
            onChange={(e) => setFormData(prev => ({ ...prev, timeForCompletion: e.target.value }))}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.timeForCompletion ? 'border-red-500' : 'border-gray-300'
              }`}
          >
            <option value="PT1H">1 hour</option>
            <option value="PT2H">2 hours</option>
            <option value="PT4H">4 hours</option>
            <option value="P1D">8 hours (1 day)</option>
            <option value="P2D">16 hours (2 days)</option>
            <option value="P3D">24 hours (3 days)</option>
            <option value="P1W">40 hours (1 week)</option>
          </select>
          {errors.timeForCompletion && <p className="text-red-500 text-sm mt-1">{errors.timeForCompletion}</p>}
        </div>

        {/* Chief Engineer Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <User className="h-4 w-4 inline mr-2" />
            Chief Engineer *
          </label>
          {selectedChiefEngineer ? (
            <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-md">
              <div>
                <p className="font-medium">{selectedChiefEngineer.name}</p>
                <p className="text-sm text-gray-600">{selectedChiefEngineer.company}</p>
              </div>
              <button
                type="button"
                onClick={() => setFormData(prev => ({ ...prev, chiefEngineerId: null }))}
                className="text-red-500 hover:text-red-700"
              >
                <X className="h-4 w-4" />
              </button>
            </div>
          ) : (
            <div className="space-y-2">
              <div className="relative">
                <Search className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                <input
                  type="text"
                  value={chiefEngineerSearchTerm}
                  onChange={(e) => setChiefEngineerSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder={workersLoading ? "Loading engineers..." : "Search for chief engineer..."}
                  disabled={workersLoading}
                />
              </div>
              {chiefEngineerSearchTerm && (
                <div className="max-h-40 overflow-y-auto border border-gray-300 rounded-md">
                  {workersLoading ? (
                    <div className="p-3 text-center text-gray-500">
                      Loading engineers...
                    </div>
                  ) : filteredChiefEngineers.length > 0 ? (
                    filteredChiefEngineers.map((worker: Worker) => (
                      <button
                        key={worker.id}
                        type="button"
                        onClick={() => handleChiefEngineerSelect(worker.id)}
                        className="w-full text-left p-3 hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                      >
                        <p className="font-medium">{worker.name}</p>
                        <p className="text-sm text-gray-600">{worker.company}</p>
                      </button>
                    ))
                  ) : (
                    <div className="p-3 text-center text-gray-500">
                      No engineers found matching "{chiefEngineerSearchTerm}"
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
          {errors.chiefEngineer && <p className="text-red-500 text-sm mt-1">{errors.chiefEngineer}</p>}
        </div>

        {/* Location */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <MapPin className="h-4 w-4 inline mr-2" />
            Location *
          </label>
          <select
            value={formData.location}
            onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${errors.location ? 'border-red-500' : 'border-gray-300'
              }`}
          >
            <option value="">Select location...</option>
            {SITE_LOCATIONS.map((location) => (
              <option key={location} value={location}>
                {location}
              </option>
            ))}
          </select>
          {errors.location && <p className="text-red-500 text-sm mt-1">{errors.location}</p>}
        </div>

        {/* Trade Selection */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Users className="h-4 w-4 inline mr-2" />
            Required Trades ({selectedTrades.length} selected)
          </label>

          {/* Selected Trades */}
          {selectedTrades.length > 0 && (
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
              <p className="text-sm font-medium text-blue-800 mb-2">Selected Trades:</p>
              <div className="space-y-2">
                {selectedTrades.map((trade: any) => (
                  <div key={trade.id} className="flex items-center justify-between p-2 bg-white border border-blue-200 rounded">
                    <div>
                      <p className="font-medium text-sm text-gray-900">{trade.name}</p>
                    </div>
                    <button
                      type="button"
                      onClick={() => handleTradeToggle(trade.id)}
                      className="text-red-500 hover:text-red-700"
                      title="Remove trade"
                    >
                      <Minus className="h-4 w-4" />
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Available Trades */}
          <div className="space-y-2">
            <p className="text-sm text-gray-600">Available Trades:</p>
            <div className="max-h-40 overflow-y-auto border border-gray-300 rounded-md p-2">
              {trades.map((trade: any) => {
                const isSelected = formData.tradeIds.includes(trade.id);
                return (
                  <label
                    key={trade.id}
                    className={`flex items-center justify-between p-2 hover:bg-gray-50 rounded cursor-pointer ${isSelected ? 'bg-blue-50 border border-blue-200' : ''
                      }`}
                  >
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        checked={isSelected}
                        onChange={() => handleTradeToggle(trade.id)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <div>
                        <p className="font-medium text-sm text-gray-900">{trade.name}</p>
                      </div>
                    </div>
                    <button
                      type="button"
                      onClick={() => handleTradeToggle(trade.id)}
                      className={`p-1 rounded ${isSelected
                        ? 'text-red-500 hover:text-red-700'
                        : 'text-green-500 hover:text-green-700'
                        }`}
                      title={isSelected ? 'Remove trade' : 'Add trade'}
                    >
                      {isSelected ? <Minus className="h-4 w-4" /> : <Plus className="h-4 w-4" />}
                    </button>
                  </label>
                );
              })}
              {trades.length === 0 && !tradesLoading && (
                <div className="p-3 text-center text-gray-500">
                  No trades available
                </div>
              )}
              {tradesLoading && (
                <div className="p-3 text-center text-gray-500">
                  Loading trades...
                </div>
              )}
            </div>
          </div>
          {errors.trades && <p className="text-red-500 text-sm mt-1">{errors.trades}</p>}
        </div>



        {/* Form Actions */}
        <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={() => navigate(`/sites/${siteId}/tasks`)}
            className="px-6 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors font-medium"
          >
            <X className="h-4 w-4 inline mr-2" />
            Cancel
          </button>
          <button
            type="submit"
            disabled={isSubmitting || workersLoading || tradesLoading}
            className="px-6 py-3 bg-blue-600 text-white hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed rounded-lg transition-colors font-medium flex items-center"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Creating...
              </>
            ) : workersLoading || tradesLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Loading...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 inline mr-2" />
                Create Task
              </>
            )}
          </button>
        </div>
      </form>
    </FloatingCard>
  );
};

export default NewTaskPage;
