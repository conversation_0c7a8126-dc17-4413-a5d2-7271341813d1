import { useState, useEffect } from "react";
import { useParams, useLocation } from "react-router-dom";
import {
	HardHat,
	BarChart3,
	Shield,
  Users } from "lucide-react";
import FloatingCard from "../components/layout/FloatingCard";
import TabContainer, { Tab } from "../components/data/shared/TabContainer";
import PPEDashboard from "../components/ppe/PPEDashboard";
import PPEInventory from "../components/equipment/PPEInventory";
import PPEMaintenance from "../components/ppe/PPEMaintenance";
import PPEWorkerRelation from "../components/ppe/PPEWorkerRelation";

const PPEPage = () => {
	const { siteId } = useParams<{ siteId: string }>();
	const location = useLocation();
	const [activeTab, setActiveTab] = useState("dashboard");

	// Handle hash navigation
	useEffect(() => {
		const hash = location.hash.replace("#", "");
		if (
			hash &&
			[
				"dashboard",
				"inventory",
				"maintenance",
				"worker-relation",
			].includes(hash)
		) {
			setActiveTab(hash);
		}
	}, [location.hash]);

	// Update URL hash when tab changes
	const handleTabChange = (tabId: string) => {
		setActiveTab(tabId);
		window.location.hash = tabId;
	};

	const tabs: Tab[] = [
		{
			id: "dashboard",
			label: "Dashboard",
			icon: <BarChart3 className="h-4 w-4" />,
			content: (
				<PPEDashboard
					siteId={siteId!}
					onNavigateToTab={handleTabChange}
				/>
			) },
		{
			id: "inventory",
			label: "PPE Inventory",
			icon: <HardHat className="h-4 w-4" />,
			content: <PPEInventory siteId={siteId!} /> },
		{
			id: "maintenance",
			label: "Maintenance",
			icon: <Shield className="h-4 w-4" />,
			content: <PPEMaintenance siteId={siteId!} /> },
		{
			id: "worker-relation",
			label: "PPE-Worker Relation",
			icon: <Users className="h-4 w-4" />,
			content: <PPEWorkerRelation siteId={siteId!} /> },
	];

	const breadcrumbs = [
		{ name: "Sites", path: "/" },
		{ name: "Site Dashboard", path: `/sites/${siteId}/dashboard` },
		{ name: "PPE Management", path: `/sites/${siteId}/ppe` },
	];

	return (
		<FloatingCard title="PPE Management" breadcrumbs={breadcrumbs}>
			<TabContainer
				tabs={tabs}
				activeTab={activeTab}
				onTabChange={handleTabChange}
			/>
		</FloatingCard>
	);
};

export default PPEPage;
