import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, ArrowRight, Save, FileText, CheckCircle, Plus, X } from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';

// Import the PTW structure from GeneralPTW.ts
const sheet_1 = [
    {
        "details": [
            {
                name: "PTW Ref No",
                type: "text",
                required: true
            },
            {
                name: "Project Name",
                type: "text",
                required: true
            },
            {
                name: "No. of employees involved",
                type: "number",
                required: true
            },
            {
                name: "Starting from",
                type: "datetime",
                required: true
            },
            {
                name: "Expected completion",
                type: "datetime",
                required: true
            },
        ]
    },
    {
        "Other Permits in use": [
            {
                name: "WAH",
                type: "checkbox",
                required: true
            },
            {
                name: "HWP",
                type: "checkbox",
                required: true
            },
            {
                name: "Excavation Permit",
                type: "checkbox",
                required: true
            },
            {
                name: "Electric Permit",
                type: "checkbox",
                required: true
            },
        ]
    },
    {
        "Description of work": [
            {
                name: "Description of work",
                type: "textarea",
                required: true
            },
            {
                name: "Location",
                type: "text",
                required: true
            },
        ]
    },
    {
        "Hazards": [
            {
                name: "Hazards",
                type: "textarea",
                required: true
            },
        ]
    },
    {
        "Isolation": [
            {
                name: "Electricity",
                type: "checkbox",
                required: true
            },
            {
                name: "Water",
                type: "checkbox",
                required: true
            },
            {
                name: "Gas",
                type: "checkbox",
                required: true
            },
            {
                name: "Moving parts",
                type: "checkbox",
                required: true
            },
        ]
    },
    {
        "Documents to be attached/availed at the work area": [
            {
                name: "Method statement",
                type: "checkbox",
                required: true
            },
            {
                name: "Risk assessment",
                type: "checkbox",
                required: true
            },
            {
                name: "Trainings",
                type: "checkbox",
                required: true
            },
            {
                name: "MSDS",
                type: "checkbox",
                required: true
            },
            {
                name: "JHA",
                type: "checkbox",
                required: true
            },
        ]
    },
    {
        "Precautions required": [
            {
                name: "Precautions required",
                type: "textarea",
                required: true
            },
        ]
    },
    {
        "PPE": [
            {
                name: "Clear impact goggles/glass",
                type: "checkbox",
                required: true
            },
            {
                name: "Cut resistant gloves",
                type: "checkbox",
                required: true
            },
            {
                name: "Hard hats",
                type: "checkbox",
                required: true
            },
            {
                name: "Ear plugs",
                type: "checkbox",
                required: true
            },
            {
                name: "Full body harness with double lanyards",
                type: "checkbox",
                required: true
            },
            {
                name: "Coveralls",
                type: "checkbox",
                required: true
            },
            {
                name: "Sheppards hooks",
                type: "checkbox",
                required: true
            },
            {
                name: "Steel toe safety shoes",
                type: "checkbox",
                required: true
            },
            {
                name: "Steel toe gumboots",
                type: "checkbox",
                required: true
            },
            {
                name: "Face shield",
                type: "checkbox",
                required: true
            },
            {
                name: "Rubber/latex gloves",
                type: "checkbox",
                required: true
            },
            {
                name: "Dust suits",
                type: "checkbox",
                required: true
            },
            {
                name: "Respiratory mask",
                type: "checkbox",
                required: true
            },
            {
                name: "Ear muffs",
                type: "checkbox",
                required: true
            },
            {
                name: "Aprons",
                type: "checkbox",
                required: true
            },
            {
                name: "Welding goggles",
                type: "checkbox",
                required: true
            },
            {
                name: "HiVis reflective vests",
                type: "checkbox",
                required: true
            },
            {
                name: "Leather gloves",
                type: "checkbox",
                required: true
            },
            {
                name: "Other(Specify)",
                type: "textarea",
                required: true
            },
        ]
    },
    {
        "Permit Issue": {
            description: "I have read this permit and confirm that the location where the work outlined is being carried out has been personally examined and the precautions have been checked as indicated. The permit requirements shall be communicated to all the persons involved in the work/task and I will immediately notify the issuer of any changes to the conditions governing this permit.",
            items: [
                {
                    "Competent person(Permit Received)": [
                        {
                            name: "Name",
                            type: "text",
                            required: true
                        },
                        {
                            name: "Signature",
                            type: "signature",
                            required: true
                        },
                        {
                            name: "Date",
                            type: "date",
                            required: true
                        },
                        {
                            name: "Time",
                            type: "time",
                            required: true
                        },
                    ]
                },
                {
                    "Authorised person(Permit Issuer)": [
                        {
                            name: "Name",
                            type: "text",
                            required: true
                        },
                        {
                            name: "Signature",
                            type: "signature",
                            required: true
                        },
                        {
                            name: "Date",
                            type: "date",
                            required: true
                        },
                        {
                            name: "Time",
                            type: "time",
                            required: true
                        },
                    ]
                },
            ]
        }
    },
    {
        "Permit Return": {
            description: "I accept that this work has been completed satisfactorily, the personnel and the equipment have been inspected and tagged as appropriate. The work area has been properly housekept, OR this work was unsafe hence incomplete. It will require a new permit before the recommencement of the task and the task and the work area has been left in a safe and in order.",
            items: [
                {
                    "Competent person(Permit Received)": [
                        {
                            name: "Name",
                            type: "text",
                            required: true
                        },
                        {
                            name: "Signature",
                            type: "signature",
                            required: true
                        },
                        {
                            name: "Date",
                            type: "date",
                            required: true
                        },
                        {
                            name: "Time",
                            type: "time",
                            required: true
                        },
                    ]
                },
                {
                    "Authorised person(Permit Issuer)": [
                        {
                            name: "Name",
                            type: "text",
                            required: true
                        },
                        {
                            name: "Signature",
                            type: "signature",
                            required: true
                        },
                        {
                            name: "Date",
                            type: "date",
                            required: true
                        },
                        {
                            name: "Time",
                            type: "time",
                            required: true
                        },
                    ]
                },
            ]
        }
    },
];

const sheet_2 = {
    title: "WORK AREA INSPECTION AND PERMIT RENEWAL BY THE AUTHORIZED PERSON",
    tableHeader: [
        {
            name: "Name",
            type: "text"
        },
        {
            name: "Date",
            type: "date"
        },
        {
            name: "Time",
            type: "time"
        },
        {
            name: "Comments",
            type: "text"
        },
        {
            name: "signature",
            type: "signature"
        },
    ]
};

const sheet_3 = {
    title: "PTW Sign off",
    description: "By signing this permit to work, I accept to abide by the instituted control measures that will enhance safe working",
    firstRow: [
        {
            name: "Date",
            type: "date"
        },
        {
            name: "Time",
            type: "time"
        },
    ],
    tableHeader: [
        {
            name: "Name",
            type: "text"
        },
        {
            name: "Designation",
            type: "text"
        },
        {
            name: "Date Time",
            type: "datetime"
        },
        {
            name: "Signature",
            type: "signature"
        },
    ]
};

interface PTWFormData {
  serialNumber: string;
  formData: { [key: string]: any };
  sheet2Data: Array<{ [key: string]: any }>;
  sheet3Data: Array<{ [key: string]: any }>;
}

const PTWFormPage: React.FC = () => {
  const {} = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Generate random serial number
  const [serialNumber] = useState(() => Math.floor(Math.random() * 1000000).toString());

  const [formData, setFormData] = useState<PTWFormData>({
    serialNumber,
    formData: {},
    sheet2Data: [
      { Name: '', Date: '', Time: '', Comments: '', signature: '' },
      { Name: '', Date: '', Time: '', Comments: '', signature: '' },
      { Name: '', Date: '', Time: '', Comments: '', signature: '' }
    ],
    sheet3Data: [
      { Name: '', Designation: '', 'Date Time': new Date().toISOString().slice(0, 16), Signature: '' },
      { Name: '', Designation: '', 'Date Time': new Date().toISOString().slice(0, 16), Signature: '' },
      { Name: '', Designation: '', 'Date Time': new Date().toISOString().slice(0, 16), Signature: '' }
    ]
  });

  // Initialize form data for sheet_1
  useEffect(() => {
    const initialFormData: { [key: string]: any } = {};

    sheet_1.forEach((section) => {
      Object.keys(section).forEach((key) => {
        if (key === 'Permit Issue' || key === 'Permit Return') {
          const sectionData = section[key as keyof typeof section] as any;
          if (sectionData.items) {
            sectionData.items.forEach((item: any) => {
              Object.keys(item).forEach((itemKey) => {
                item[itemKey].forEach((field: any) => {
                  const fieldKey = `${key}_${itemKey}_${field.name}`;
                  if (field.type === 'date') {
                    initialFormData[fieldKey] = new Date().toISOString().split('T')[0];
                  } else if (field.type === 'time') {
                    initialFormData[fieldKey] = new Date().toTimeString().slice(0, 5);
                  } else if (field.type === 'datetime') {
                    initialFormData[fieldKey] = new Date().toISOString().slice(0, 16);
                  } else {
                    initialFormData[fieldKey] = '';
                  }
                });
              });
            });
          }
        } else {
          const fields = section[key as keyof typeof section] as any[];
          fields.forEach((field) => {
            const fieldKey = `${key}_${field.name}`;
            if (field.type === 'checkbox') {
              initialFormData[fieldKey] = false;
            } else if (field.type === 'number') {
              initialFormData[fieldKey] = 0;
            } else if (field.type === 'date') {
              initialFormData[fieldKey] = new Date().toISOString().split('T')[0];
            } else if (field.type === 'time') {
              initialFormData[fieldKey] = new Date().toTimeString().slice(0, 5);
            } else if (field.type === 'datetime') {
              initialFormData[fieldKey] = new Date().toISOString().slice(0, 16);
            } else {
              initialFormData[fieldKey] = '';
            }
          });
        }
      });
    });

    setFormData(prev => ({ ...prev, formData: initialFormData }));
  }, []);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      formData: { ...prev.formData, [field]: value }
    }));
  };

  const handleTableChange = (sheet: 'sheet2Data' | 'sheet3Data', rowIndex: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [sheet]: prev[sheet].map((row, index) =>
        index === rowIndex ? { ...row, [field]: value } : row
      )
    }));
  };

  const addTableRow = (sheet: 'sheet2Data' | 'sheet3Data') => {
    const newRow = sheet === 'sheet2Data'
      ? { Name: '', Date: '', Time: '', Comments: '', signature: '' }
      : { Name: '', Designation: '', 'Date Time': new Date().toISOString().slice(0, 16), Signature: '' };

    setFormData(prev => ({
      ...prev,
      [sheet]: [...prev[sheet], newRow]
    }));
  };

  const removeTableRow = (sheet: 'sheet2Data' | 'sheet3Data', rowIndex: number) => {
    setFormData(prev => {
      // Don't remove if we only have 3 rows left
      if (prev[sheet].length <= 3) {
        return prev;
      }
      return {
        ...prev,
        [sheet]: prev[sheet].filter((_, index) => index !== rowIndex)
      };
    });
  };

  const handleNext = () => {
    if (currentStep < 3) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 2000));
      console.log('PTW Form submitted:', formData);
      alert('Permit to Work form submitted successfully!');
      navigate(-1);
    } catch (error) {
      console.error('Error submitting PTW form:', error);
      alert('Error submitting form. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getStepTitle = () => {
    switch (currentStep) {
      case 1: return 'General Permit to Work - Sheet 1';
      case 2: return 'Work Area Inspection and Permit Renewal';
      case 3: return 'PTW Sign off';
      default: return 'PTW Form';
    }
  };

  const renderField = (field: any, sectionKey: string, disabled: boolean = false) => {
    const fieldKey = `${sectionKey}_${field.name}`;
    let value = formData.formData[fieldKey] || '';

    // Handle name mirroring from Permit Issue to Permit Return
    if (disabled && sectionKey.includes('Permit Return') && field.name === 'Name') {
      const issueKey = sectionKey.replace('Permit Return', 'Permit Issue');
      const issueValue = formData.formData[issueKey] || '';
      value = issueValue;
    }

    const baseClassName = `w-full px-3 py-2 border border-gray-300 rounded-md ${disabled ? 'bg-gray-100 text-gray-500 cursor-not-allowed' : 'focus:ring-green-500 focus:border-green-500'}`;

    switch (field.type) {
      case 'text':
        return (
          <input
            type="text"
            value={value}
            onChange={(e) => !disabled && handleInputChange(fieldKey, e.target.value)}
            className={baseClassName}
            required={field.required}
            disabled={disabled}
            readOnly={disabled}
          />
        );
      case 'number':
        return (
          <input
            type="number"
            value={value}
            onChange={(e) => !disabled && handleInputChange(fieldKey, parseInt(e.target.value) || 0)}
            className={baseClassName}
            required={field.required}
            disabled={disabled}
            readOnly={disabled}
          />
        );
      case 'textarea':
        return (
          <textarea
            rows={3}
            value={value}
            onChange={(e) => !disabled && handleInputChange(fieldKey, e.target.value)}
            className={baseClassName}
            required={field.required}
            disabled={disabled}
            readOnly={disabled}
          />
        );
      case 'checkbox':
        return (
          <input
            type="checkbox"
            checked={value}
            onChange={(e) => !disabled && handleInputChange(fieldKey, e.target.checked)}
            className={`h-4 w-4 text-green-600 border-gray-300 rounded ${disabled ? 'cursor-not-allowed' : 'focus:ring-green-500'}`}
            disabled={disabled}
          />
        );
      case 'date':
        return (
          <input
            type="date"
            value={value}
            onChange={(e) => !disabled && handleInputChange(fieldKey, e.target.value)}
            className={baseClassName}
            required={field.required}
            disabled={disabled}
            readOnly={disabled}
          />
        );
      case 'time':
        return (
          <input
            type="time"
            value={value}
            onChange={(e) => !disabled && handleInputChange(fieldKey, e.target.value)}
            className={baseClassName}
            required={field.required}
            disabled={disabled}
            readOnly={disabled}
          />
        );
      case 'datetime':
        return (
          <input
            type="datetime-local"
            value={value}
            onChange={(e) => !disabled && handleInputChange(fieldKey, e.target.value)}
            className={baseClassName}
            required={field.required}
            disabled={disabled}
            readOnly={disabled}
          />
        );
      case 'signature':
        return (
          <input
            type="text"
            value={value}
            onChange={(e) => !disabled && handleInputChange(fieldKey, e.target.value)}
            placeholder="Digital signature"
            className={baseClassName}
            required={field.required}
            disabled={disabled}
            readOnly={disabled}
          />
        );
      default:
        return null;
    }
  };

  const renderStepIndicator = () => (
    <div className="flex items-center justify-center mb-6">
      {[1, 2, 3].map((step) => (
        <div key={step} className="flex items-center">
          <div
            className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium ${
              step === currentStep
                ? 'bg-green-600 text-white'
                : step < currentStep
                ? 'bg-green-100 text-green-600'
                : 'bg-gray-200 text-gray-500'
            }`}
          >
            {step < currentStep ? <CheckCircle className="h-4 w-4" /> : step}
          </div>
          {step < 3 && (
            <div
              className={`w-16 h-1 mx-2 ${
                step < currentStep ? 'bg-green-600' : 'bg-gray-200'
              }`}
            />
          )}
        </div>
      ))}
    </div>
  );

  const getStepDescription = () => {
    switch (currentStep) {
      case 1: return 'Complete permit application and work details';
      case 2: return 'Work area inspection and permit renewal by authorized person';
      case 3: return 'PTW sign off by all personnel involved';
      default: return '';
    }
  };

  const renderStep1 = () => (
    <div className="space-y-4">
      {/* Header Section */}
      <div className="bg-green-50 border border-green-200 rounded-lg p-3">
        <div className="text-center mb-3">
          <div className="flex items-center justify-center gap-4 mb-2">
            <h2 className="text-lg font-bold text-green-800">
              GENERAL PERMIT TO WORK
            </h2>
            <span className="text-xs text-gray-500 font-light">Serial No: {formData.serialNumber}</span>
          </div>
          <div className="text-xs text-red-600 font-medium">
            This permit is valid for 7 days.
          </div>
        </div>
      </div>

      {/* Form Sections */}
      {sheet_1.map((section, sectionIndex) => {
        const sectionKey = Object.keys(section)[0];
        const sectionData = section[sectionKey as keyof typeof section];

        // Handle special sections with description and items
        if (typeof sectionData === 'object' && 'description' in sectionData) {
          const specialSection = sectionData as any;
          const isPermitReturn = sectionKey === 'Permit Return';

          return (
            <div key={sectionIndex} className={`border border-gray-200 rounded-lg p-4 ${isPermitReturn ? 'bg-gray-100' : 'bg-white'}`}>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">{sectionKey}</h3>
              {specialSection.description && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-2 mb-3">
                  <p className="text-xs text-blue-800 leading-tight">{specialSection.description}</p>
                </div>
              )}

              {specialSection.items && specialSection.items.map((item: any, itemIndex: number) => {
                const itemKey = Object.keys(item)[0];
                const itemFields = item[itemKey];

                return (
                  <div key={itemIndex} className="mb-6">
                    <h4 className="text-md font-medium text-gray-800 mb-3">{itemKey}</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {itemFields.map((field: any, fieldIndex: number) => (
                        <div key={fieldIndex}>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            {field.name} {field.required && '*'}
                          </label>
                          {renderField(field, `${sectionKey}_${itemKey}`, isPermitReturn)}
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          );
        }

        // Handle regular sections with field arrays
        const fields = sectionData as any[];
        return (
          <div key={sectionIndex} className="bg-white border border-gray-200 rounded-lg p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-3">{sectionKey}</h3>

            {/* For checkbox sections, use grid layout */}
            {fields.every(field => field.type === 'checkbox') ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-1">
                {fields.map((field, fieldIndex) => (
                  <div key={fieldIndex} className="flex items-center space-x-1">
                    {renderField(field, sectionKey)}
                    <label className="text-xs text-gray-700 leading-tight">{field.name}</label>
                  </div>
                ))}
              </div>
            ) : (
              /* For other field types, use standard layout */
              <div className="space-y-3">
                {fields.map((field, fieldIndex) => (
                  <div key={fieldIndex}>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      {field.name} {field.required && '*'}
                    </label>
                    {renderField(field, sectionKey)}
                  </div>
                ))}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-4">
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">{sheet_2.title}</h3>

        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {sheet_2.tableHeader.map((header, index) => (
                  <th key={index} className="px-2 py-1 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {header.name}
                  </th>
                ))}
                <th className="px-2 py-1 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {formData.sheet2Data.map((row, rowIndex) => (
                <tr key={rowIndex}>
                  {sheet_2.tableHeader.map((header, colIndex) => (
                    <td key={colIndex} className="px-2 py-1">
                      {header.type === 'date' ? (
                        <input
                          type="date"
                          value={row[header.name] || ''}
                          onChange={(e) => handleTableChange('sheet2Data', rowIndex, header.name, e.target.value)}
                          className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:ring-green-500 focus:border-green-500"
                        />
                      ) : header.type === 'time' ? (
                        <input
                          type="time"
                          value={row[header.name] || ''}
                          onChange={(e) => handleTableChange('sheet2Data', rowIndex, header.name, e.target.value)}
                          className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:ring-green-500 focus:border-green-500"
                        />
                      ) : header.type === 'signature' ? (
                        <input
                          type="text"
                          value={row[header.name] || ''}
                          onChange={(e) => handleTableChange('sheet2Data', rowIndex, header.name, e.target.value)}
                          placeholder="Digital signature"
                          className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:ring-green-500 focus:border-green-500"
                        />
                      ) : header.name === 'Comments' ? (
                        <textarea
                          rows={2}
                          value={row[header.name] || ''}
                          onChange={(e) => handleTableChange('sheet2Data', rowIndex, header.name, e.target.value)}
                          className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:ring-green-500 focus:border-green-500 resize-none"
                          placeholder="Enter comments..."
                        />
                      ) : (
                        <input
                          type="text"
                          value={row[header.name] || ''}
                          onChange={(e) => handleTableChange('sheet2Data', rowIndex, header.name, e.target.value)}
                          className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:ring-green-500 focus:border-green-500"
                        />
                      )}
                    </td>
                  ))}
                  <td className="px-2 py-1 text-center">
                    <button
                      type="button"
                      onClick={() => removeTableRow('sheet2Data', rowIndex)}
                      disabled={formData.sheet2Data.length <= 3}
                      className={`w-5 h-5 rounded-full flex items-center justify-center transition-colors ${
                        formData.sheet2Data.length <= 3
                          ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                          : 'bg-red-100 text-red-600 hover:bg-red-200 hover:text-red-700'
                      }`}
                    >
                      <X className="h-2.5 w-2.5" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div className="mt-4 flex justify-end">
          <button
            type="button"
            onClick={() => addTableRow('sheet2Data')}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Row
          </button>
        </div>
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="space-y-4">
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-gray-900 mb-3">{sheet_3.title}</h3>

        {sheet_3.description && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-2 mb-4">
            <p className="text-xs text-blue-800 leading-tight">{sheet_3.description}</p>
          </div>
        )}

        {/* First Row - Date and Time */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          {sheet_3.firstRow.map((field, index) => (
            <div key={index}>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {field.name}
              </label>
              <input
                type={field.type}
                value={field.type === 'date' ? new Date().toISOString().split('T')[0] : new Date().toTimeString().slice(0, 5)}
                onChange={(e) => handleInputChange(`${sheet_3.title}_${field.name}`, e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
              />
            </div>
          ))}
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-2 py-1 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  S/No
                </th>
                {sheet_3.tableHeader.map((header, index) => (
                  <th key={index} className="px-2 py-1 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    {header.name}
                  </th>
                ))}
                <th className="px-2 py-1 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {formData.sheet3Data.map((row, rowIndex) => (
                <tr key={rowIndex}>
                  <td className="px-2 py-1 text-xs text-gray-900">
                    {rowIndex + 1}
                  </td>
                  {sheet_3.tableHeader.map((header, colIndex) => (
                    <td key={colIndex} className="px-2 py-1">
                      {header.type === 'signature' ? (
                        <input
                          type="text"
                          value={row[header.name] || ''}
                          onChange={(e) => handleTableChange('sheet3Data', rowIndex, header.name, e.target.value)}
                          placeholder="Digital signature"
                          className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:ring-green-500 focus:border-green-500"
                        />
                      ) : header.type === 'datetime' ? (
                        <input
                          type="datetime-local"
                          value={row[header.name] || ''}
                          onChange={(e) => handleTableChange('sheet3Data', rowIndex, header.name, e.target.value)}
                          className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:ring-green-500 focus:border-green-500"
                        />
                      ) : (
                        <input
                          type="text"
                          value={row[header.name] || ''}
                          onChange={(e) => handleTableChange('sheet3Data', rowIndex, header.name, e.target.value)}
                          className="w-full px-2 py-1 border border-gray-300 rounded text-sm focus:ring-green-500 focus:border-green-500"
                        />
                      )}
                    </td>
                  ))}
                  <td className="px-2 py-1 text-center">
                    <button
                      type="button"
                      onClick={() => removeTableRow('sheet3Data', rowIndex)}
                      disabled={formData.sheet3Data.length <= 3}
                      className={`w-5 h-5 rounded-full flex items-center justify-center transition-colors ${
                        formData.sheet3Data.length <= 3
                          ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
                          : 'bg-red-100 text-red-600 hover:bg-red-200 hover:text-red-700'
                      }`}
                    >
                      <X className="h-2.5 w-2.5" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div className="mt-4 flex justify-end">
          <button
            type="button"
            onClick={() => addTableRow('sheet3Data')}
            className="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Row
          </button>
        </div>
      </div>
    </div>
  );

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 1: return renderStep1();
      case 2: return renderStep2();
      case 3: return renderStep3();
      default: return null;
    }
  };

  return (
    <FloatingCard title="Permit to Work Form">
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate(-1)}
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm leading-4 font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Permit to Work Form</h1>
                <p className="text-sm text-gray-500">Step {currentStep} of 3 - {getStepTitle()}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-gray-400" />
              <span className="text-sm text-gray-500">Serial: {formData.serialNumber}</span>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="px-2 py-4">
          <div className="max-w-6xl mx-auto">
            {/* Step Indicator */}
            {renderStepIndicator()}

            {/* Step Description */}
            <div className="text-center mb-6">
              <p className="text-gray-600">{getStepDescription()}</p>
            </div>

            {/* Form Content */}
            <div className="bg-white rounded-lg shadow-lg p-4">
              {renderCurrentStep()}
            </div>

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-4">
              <button
                onClick={handlePrevious}
                disabled={currentStep === 1}
                className={`inline-flex items-center px-6 py-3 border border-gray-300 rounded-md text-sm font-medium ${
                  currentStep === 1
                    ? 'text-gray-400 cursor-not-allowed'
                    : 'text-gray-700 hover:bg-gray-50'
                }`}
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Previous
              </button>

              {currentStep < 3 ? (
                <button
                  onClick={handleNext}
                  className="inline-flex items-center px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                >
                  Next
                  <ArrowRight className="h-4 w-4 ml-2" />
                </button>
              ) : (
                <button
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="inline-flex items-center px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Submitting...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Submit Permit
                    </>
                  )}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
    </FloatingCard>
  );
};

export default PTWFormPage;
