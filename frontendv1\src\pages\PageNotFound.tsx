import { Link } from "react-router-dom";
import FloatingCard from "../components/layout/FloatingCard";

const PageNotFound = () => {
	return (
		<FloatingCard title="Page Not Found">
			<div className="flex flex-col items-center justify-center h-full">
				<h1 className="text-6xl font-bold text-gray-200">404</h1>
				<p className="text-xl text-gray-600 mt-4">
					The page you're looking for doesn't exist.
				</p>
				<Link
					to="/"
					className="mt-8 bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded"
				>
					Return to Dashboard
				</Link>
			</div>
		</FloatingCard>
	);
};

export default PageNotFound;
