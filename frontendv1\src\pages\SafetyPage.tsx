/**
 * Safety Page - Backend Aligned Implementation
 *
 * This page has been updated to align with the backend capabilities:
 *
 * 1. ToolboxTalks: Now uses GraphQL ToolboxSession and ToolboxAttendance models
 *    - Matches backend entities from technical-docs/Shared/GraphQL/Models/ToolboxSession.cs
 *    - Uses real-time data from useToolboxSessions hook
 *    - Implements audit fields (createdAt, createdBy, updatedAt, updatedBy)
 *
 * 2. Safety Dashboard: Enhanced with real-time metrics
 *    - SafetyMetrics component shows live data from backend
 *    - Calculates attendance rates from actual GraphQL data
 *    - Provides trend analysis and status indicators
 *
 * 3. Backend Integration Points:
 *    - GraphQL API endpoints for toolbox sessions
 *    - Mock data aligned with backend structure
 *    - Type definitions match backend models
 *
 * 4. Future Enhancements:
 *    - Incident Management: Awaiting backend HSE module implementation
 *    - Safety Observations: Ready for backend integration
 *    - Site RAMS: Document management system integration
 *
 * Note: The HealthSafetyEnvironment backend module is currently a placeholder,
 * so some features still use mock data until backend implementation is complete.
 */

import React, { useState, useEffect } from 'react';
import { useParams, useLocation } from 'react-router-dom';
import { LayoutDashboard, AlertTriangle, ListChecks, FileText, MessageSquare } from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import TabContainer, { Tab } from '../components/data/shared/TabContainer';
import SafetyDashboard from '../components/safety/SafetyDashboard';
import IncidentManagement from '../components/safety/IncidentManagement';
import ToolboxTalks from '../components/safety/ToolboxTalks';
import SafetyObservations from '../components/safety/SafetyObservations';
import SiteRAMSComponent from '../components/safety/SiteRAMS';
import { useSiteContext } from '../hooks/useSiteContext';

const SafetyPage: React.FC = () => {
	const { siteId } = useParams<{ siteId: string }>();
	const location = useLocation();
	const { currentSite } = useSiteContext();
	const [activeTab, setActiveTab] = useState("dashboard");

	const validTabs = [
		"dashboard",
		"incidents",
		"toolbox-talks",
		"observations",
		"rams",
	];

	// Handle URL hash navigation
	useEffect(() => {
		const hash = location.hash.replace("#", "");
		if (hash && validTabs.includes(hash)) {
			setActiveTab(hash);
		} else if (!hash) {
			setActiveTab("dashboard");
		}
	}, [location.hash]);

	// Listen for direct hash changes (for browser back/forward)
	useEffect(() => {
		const handleHashChange = () => {
			const newHash = window.location.hash.replace("#", "");
			if (newHash && validTabs.includes(newHash)) {
				setActiveTab(newHash);
			} else if (!newHash) {
				setActiveTab("dashboard");
			}
		};

		window.addEventListener("hashchange", handleHashChange);
		return () => window.removeEventListener("hashchange", handleHashChange);
	}, []);

	const handleNavigateToTab = (tabId: string) => {
		setActiveTab(tabId);
		// Update URL hash without triggering a page reload
		window.history.replaceState(null, "", `#${tabId}`);
	};

	const breadcrumbs = [
		{ name: "Dashboard", path: "/" },
		{ name: currentSite?.name || "Site", path: `/sites/${siteId}/dashboard` },
		{ name: "Health & Safety", path: `/sites/${siteId}/safety` },
	];

	const tabs: Tab[] = [
		{
			id: "dashboard",
			label: "Dashboard",
			icon: <LayoutDashboard className="h-4 w-4" />,
			content: (
				<SafetyDashboard
					siteId={siteId || ""}
					onNavigateToTab={handleNavigateToTab}
				/>
			),
		},
		{
			id: "incidents",
			label: "Incidents",
			icon: <AlertTriangle className="h-4 w-4" />,
			content: <IncidentManagement siteId={siteId || ""} />,
		},
		{
			id: "toolbox-talks",
			label: "Toolbox Talks",
			icon: <ListChecks className="h-4 w-4" />,
			content: <ToolboxTalks siteId={siteId || ""} />,
		},
		{
			id: "observations",
			label: "Observations",
			icon: <MessageSquare className="h-4 w-4" />,
			content: <SafetyObservations siteId={siteId || ""} />,
		},
		{
			id: "rams",
			label: "Site RAMS",
			icon: <FileText className="h-4 w-4" />,
			content: <SiteRAMSComponent siteId={siteId || ""} />,
		},
	];

	return (
		<FloatingCard title="Health & Safety Management" breadcrumbs={breadcrumbs}>
			<TabContainer
				tabs={tabs}
				activeTab={activeTab}
				onTabChange={handleNavigateToTab}
			/>
		</FloatingCard>
	);
};

export default SafetyPage;
