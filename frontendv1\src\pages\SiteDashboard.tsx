import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { AlertTriangle, Calendar, ClipboardCheck, Clock, FileSpreadsheet, HardHat, ShieldCheck, Users, Timer, Wrench, Truck, UserPlus, CheckSquare, MessageSquare, FileText } from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import KPICard from '../components/dashboard/KPICard';
import QuickActions, { QuickActionItem } from '../components/shared/QuickActions';
import { SiteInfo,  } from '../types';
import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer, Legend, Tooltip, BarChart, Bar, XAxis, YAxis, CartesianGrid } from 'recharts';

// Mock data
const mockSite: SiteInfo = {
  id: "site1",
  name: "Westlands Construction Site",
  healthStatus: "green",
  workersOnSite: 42,
  activePermits: 8,
  openIncidents: 0,
  projectManager: "<PERSON>",
  location: "Waiyaki Way, Westlands, Nairobi",
  timeline: "Jan 2025 - Dec 2026",
  currentPhase: "Foundation",
  progressPercentage: 25,
  tenantId: '',
  status: 'active',
  createdAt: new Date()
};

// Mock data for trades pie chart - ensuring it matches the site.workersOnSite
const mockTradesData = [
  { name: 'Electricians', value: 7, color: '#3B82F6' },
  { name: 'Plumbers', value: 5, color: '#10B981' },
  { name: 'Carpenters', value: 10, color: '#F59E0B' },
  { name: 'Masons', value: 8, color: '#EF4444' },
  { name: 'Painters', value: 4, color: '#8B5CF6' },
  { name: 'Welders', value: 3, color: '#06B6D4' },
  { name: 'General Labor', value: 5, color: '#84CC16' },
];

// This should equal mockSite.workersOnSite (42)
const totalTradesCount = mockTradesData.reduce((sum, trade) => sum + trade.value, 0);
const mockIncidents = [
  { id: 'inc1', date: '2025-05-15', type: 'Near Miss', severity: 'low' as const, location: 'Foundation Area', status: 'resolved' as const },
  { id: 'inc2', date: '2025-05-21', type: 'First Aid', severity: 'medium' as const, location: 'Materials Storage', status: 'closed' as const },
  { id: 'inc3', date: '2025-05-10', type: 'Equipment faliure', severity: 'high' as const, location: 'Equipment Yard', status: 'closed' as const },
];

const mockObservations = [
  { id: 'obs1', date: '2025-05-22', type: 'Safety Observation', category: 'positive' as const, description: 'Workers properly using fall protection equipment', location: 'Building A - Level 3', timestamp: '2 hours ago' },
  { id: 'obs2', date: '2025-05-21', type: 'Safety Observation', category: 'improvement' as const, description: 'Housekeeping could be improved in material storage area', location: 'Materials Storage', timestamp: '1 day ago' },
  { id: 'obs3', date: '2025-05-20', type: 'Safety Observation', category: 'positive' as const, description: 'Excellent safety briefing conducted by foreman', location: 'Site Office', timestamp: '2 days ago' },
];

const mockTasks = [
  { id: 'task1', title: 'Foundation Inspection', status: 'pending' as const, assignee: 'John Mwangi', dueDate: '2025-05-25', priority: 'high' as const, timestamp: '1 hour ago' },
  { id: 'task2', title: 'Material Delivery Coordination', status: 'in-progress' as const, assignee: 'Sarah Njeri', dueDate: '2025-05-24', priority: 'medium' as const, timestamp: '3 hours ago' },
  { id: 'task3', title: 'Safety Equipment Check', status: 'completed' as const, assignee: 'Mike Ochieng', dueDate: '2025-05-23', priority: 'low' as const, timestamp: '1 day ago' },
];

const mockPermitsData = [
  { id: 'permit1', type: 'Hot Work Permit', status: 'active' as const, location: 'Building A - Level 2', expiresAt: '2025-05-25 18:00', timestamp: '2 hours ago' },
  { id: 'permit2', type: 'Confined Space Entry', status: 'pending' as const, location: 'Underground Utilities', expiresAt: '2025-05-26 12:00', timestamp: '4 hours ago' },
  { id: 'permit3', type: 'Crane Operation', status: 'expired' as const, location: 'Main Construction Area', expiresAt: '2025-05-22 17:00', timestamp: '2 days ago' },
];

const mockInspectionsData = [
  { id: 'insp1', type: 'Daily Safety Inspection', status: 'completed' as const, inspector: 'John Mwangi', location: 'Foundation Area', timestamp: '1 hour ago' },
  { id: 'insp2', type: 'Equipment Inspection', status: 'scheduled' as const, inspector: 'Sarah Njeri', location: 'Equipment Yard', timestamp: '3 hours ago' },
  { id: 'insp3', type: 'Scaffold Inspection', status: 'overdue' as const, inspector: 'Mike Ochieng', location: 'Building A - Level 3', timestamp: '1 day ago' },
];

// Mock data for visualization tabs
// const mockTimeTrackingData = [
//   { worker: 'John Doe', trade: 'Electrician', hoursToday: 8, hoursWeek: 40, status: 'On-site', clockIn: '07:30', clockOut: null },
//   { worker: 'Jane Smith', trade: 'Plumber', hoursToday: 7.5, hoursWeek: 37.5, status: 'On-site', clockIn: '08:00', clockOut: null },
//   { worker: 'Mike Johnson', trade: 'Carpenter', hoursToday: 8, hoursWeek: 42, status: 'On-site', clockIn: '07:45', clockOut: null },
//   { worker: 'Sarah Wilson', trade: 'Mason', hoursToday: 6, hoursWeek: 35, status: 'Break', clockIn: '08:15', clockOut: null },
//   { worker: 'David Brown', trade: 'Welder', hoursToday: 7, hoursWeek: 38, status: 'On-site', clockIn: '07:30', clockOut: null },
// ];



const mockEquipmentData = [
  { id: 'E001', name: 'Tower Crane TC-1', type: 'Heavy Equipment', status: 'Operational', lastInspection: '2025-01-10', nextMaintenance: '2025-01-20' },
  { id: 'E002', name: 'Excavator CAT-320', type: 'Heavy Equipment', status: 'Maintenance Required', lastInspection: '2025-01-08', nextMaintenance: '2025-01-15' },
  { id: 'E003', name: 'Concrete Mixer CM-5', type: 'Construction Equipment', status: 'Operational', lastInspection: '2025-01-12', nextMaintenance: '2025-01-25' },
  { id: 'E004', name: 'Safety Harnesses (10x)', type: 'PPE', status: 'Available', lastInspection: '2025-01-11', nextMaintenance: '2025-02-11' },
  { id: 'E005', name: 'Hard Hats (25x)', type: 'PPE', status: 'Low Stock', lastInspection: '2025-01-09', nextMaintenance: 'N/A' },
];

// Data for visualization charts
const mockTimeChartData = [
  { day: 'Monday', workers: 38, dayShort: 'Mon' },
  { day: 'Tuesday', workers: 42, dayShort: 'Tue' },
  { day: 'Wednesday', workers: 40, dayShort: 'Wed' },
  { day: 'Thursday', workers: 41, dayShort: 'Thu' },
  { day: 'Friday', workers: 39, dayShort: 'Fri' },
  { day: 'Saturday', workers: 25, dayShort: 'Sat' }, // Weekend reduced staff
  { day: 'Sunday', workers: 12, dayShort: 'Sun' }, // Minimal weekend staff
];

const mockPermitChartData = mockPermitsData.reduce((acc, permit) => {
  const existing = acc.find(item => item.name === permit.type);
  if (existing) {
    existing.value += 1;
  } else {
    acc.push({ name: permit.type, value: 1, color: getPermitColor(permit.type) });
  }
  return acc;
}, [] as { name: string; value: number; color: string }[]);

const mockEquipmentChartData = mockEquipmentData.reduce((acc, equipment) => {
  const existing = acc.find(item => item.name === equipment.status);
  if (existing) {
    existing.value += 1;
  } else {
    acc.push({ name: equipment.status, value: 1, color: getEquipmentColor(equipment.status) });
  }
  return acc;
}, [] as { name: string; value: number; color: string }[]);

function getPermitColor(type: string): string {
  const colors: Record<string, string> = {
    'Hot Work Permit': '#EF4444',
    'Confined Space Entry': '#F59E0B',
    'Crane Operation': '#3B82F6',
    'Working at Height': '#3B82F6',
    'Electrical Work': '#8B5CF6',
  };
  return colors[type] || '#6B7280';
}

function getEquipmentColor(status: string): string {
  const colors: Record<string, string> = {
    'Operational': '#10B981',
    'Available': '#3B82F6',
    'Maintenance Required': '#EF4444',
    'Low Stock': '#F59E0B',
  };
  return colors[status] || '#6B7280';
}

const mockAlerts = [
  { id: 'alt1', type: 'certification', message: '3 worker certifications expiring in the next 14 days', timestamp: '2 hours ago' },
  { id: 'alt2', type: 'equipment', message: 'Crane inspection due in 3 days', timestamp: '5 hours ago' },
  { id: 'alt3', type: 'permit', message: 'Hot work permit for Section B expires today', timestamp: '1 day ago' },
];

const mockNotifications = [
  { id: 'not1', type: 'info', message: 'Daily toolbox talk completed for all teams', timestamp: '1 hour ago' },
  { id: 'not2', type: 'success', message: 'Weekly safety inspection passed with no issues', timestamp: '3 hours ago' },
  { id: 'not3', type: 'info', message: 'New worker John Smith added to site roster', timestamp: '6 hours ago' },
  { id: 'not4', type: 'success', message: 'Equipment maintenance completed for Excavator CAT-320', timestamp: '1 day ago' },
];

const mockHoursData = [
  { name: 'Safe Hours', value: 1250, color: '#10B981' }, // Green
  { name: 'Incident Hours', value: 45, color: '#EF4444' }, // Red
  { name: 'Training Hours', value: 180, color: '#3B82F6' }, // Blue
  { name: 'Maintenance Hours', value: 95, color: '#F59E0B' }, // Amber
];

const totalHoursWorked = mockHoursData.reduce((sum, item) => sum + item.value, 0);

const SiteDashboard = () => {
  const { siteId } = useParams<{ siteId: string }>();
  const navigate = useNavigate();
  const [site, _setSite] = useState<SiteInfo>(mockSite);
  const [activeVisualizationTab, setActiveVisualizationTab] = useState<'trades' | 'time' | 'permits' | 'equipment' | 'hours'>('trades');
  const [activeNotificationTab, setActiveNotificationTab] = useState<'notifications' | 'tasks' | 'permits' | 'observations'>('notifications');

  // In a real app, we would fetch the site data based on siteId
  useEffect(() => {
    // Simulating API fetch
    // setSite(fetchedSite);
  }, [siteId]);

  return (
    <FloatingCard>
      {/* KPI Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {/* Row 1 - Core Metrics */}
        <KPICard
          title="Workers On-Site (Today)"
          value={site.workersOnSite}
          icon={<Users className="h-6 w-6 text-blue-500" />}
          navigateTo={`/sites/${siteId}/workers`}
        />
        <KPICard
          title="Active Permits"
          value={site.activePermits}
          icon={<ClipboardCheck className="h-6 w-6 text-green-500" />}
          navigateTo={`/sites/${siteId}/permits`}
        />
        <KPICard
          title="Open Incidents"
          value={site.openIncidents}
          icon={<AlertTriangle className="h-6 w-6 text-red-500" />}
          navigateTo={`/sites/${siteId}/incidents`}
        />
        <KPICard
          title="Equipment On-Site"
          value={mockEquipmentData.filter(e => e.status === 'Operational' || e.status === 'Available').length}
          icon={<Truck className="h-6 w-6 text-cyan-500" />}
          navigateTo={`/sites/${siteId}/equipment`}
        />
      </div>

      {/* Secondary KPI Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <KPICard
          title="Overdue Trainings"
          value={3}
          icon={<HardHat className="h-6 w-6 text-orange-500" />}
          navigateTo={`/sites/${siteId}/training?filter=overdue`}
        />
        <KPICard
          title="Upcoming Inspections"
          value={2}
          icon={<FileSpreadsheet className="h-6 w-6 text-purple-500" />}
          navigateTo={`/sites/${siteId}/inspections?filter=upcoming`}
        />
        <KPICard
          title="Toolbox Talk Attendance %"
          value="92%"
          change={2}
          icon={<Clock className="h-6 w-6 text-indigo-500" />}
          navigateTo={`/sites/${siteId}/toolbox`}
        />
        <KPICard
          title="Safe Man Hours"
          value="336"
          icon={<Timer className="h-6 w-6 text-teal-500" />}
          navigateTo={`/sites/${siteId}/time#reports`}
        />
      </div>

      {/* New QuickActions Section */}
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <QuickActions
        actions={[
          {
            title: "Add Worker",
            description: "Add a new worker to the site",
            icon: <UserPlus className="h-6 w-6 text-blue-600" />,
            onClick: () => navigate(`/sites/${siteId}/workers#add`),
          },
          {
            title: "Review Task",
            description: "Review and manage site tasks",
            icon: <CheckSquare className="h-6 w-6 text-purple-600" />,
            onClick: () => navigate(`/sites/${siteId}/tasks`),
          },
          {
            title: "Start Toolbox",
            description: "Start a new toolbox talk session",
            icon: <MessageSquare className="h-6 w-6 text-indigo-600" />,
            onClick: () => navigate(`/sites/${siteId}/safety#toolbox`),
          },
          {
            title: "Generate Permit",
            description: "Create a new work permit",
            icon: <FileText className="h-6 w-6 text-purple-600" />,
            onClick: () => navigate(`/sites/${siteId}/permits#create`),
          },
        ] as QuickActionItem[]}
        className=""
      />
      </div>

      {/* Charts and Data Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Interactive Visualization Card */}
        <div className="bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
          {/* Tab Navigation */}
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-4">
            <button
              onClick={() => setActiveVisualizationTab('trades')}
              className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                activeVisualizationTab === 'trades'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Trades
            </button>
            <button
              onClick={() => setActiveVisualizationTab('time')}
              className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                activeVisualizationTab === 'time'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Time
            </button>
            <button
              onClick={() => setActiveVisualizationTab('permits')}
              className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                activeVisualizationTab === 'permits'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Permits
            </button>
            <button
              onClick={() => setActiveVisualizationTab('equipment')}
              className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                activeVisualizationTab === 'equipment'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Equipment
            </button>
            <button
              onClick={() => setActiveVisualizationTab('hours')}
              className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                activeVisualizationTab === 'hours'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Hours
            </button>
          </div>

          {/* Dynamic Content Based on Active Tab */}
          {activeVisualizationTab === 'trades' && (
            <>
              <div className="text-center mb-4">
                <p className="text-lg font-normal text-gray-600">{totalTradesCount} Workers on Site</p>
              </div>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={mockTradesData}
                      cx="50%"
                      cy="50%"
                      innerRadius={40}
                      outerRadius={80}
                      paddingAngle={2}
                      dataKey="value"
                    >
                      {mockTradesData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value, name) => [value, name]} />
                    <Legend
                      verticalAlign="middle"
                      align="right"
                      layout="vertical"
                      iconType="circle"
                      wrapperStyle={{ paddingLeft: '20px', fontSize: '12px' }}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </>
          )}

          {activeVisualizationTab === 'time' && (
            <>
              <div className="text-center mb-4">
                <p className="text-lg font-normal text-gray-600">Weekly Attendance Pattern</p>
              </div>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={mockTimeChartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="dayShort"
                      tick={{ fontSize: 12 }}
                      axisLine={{ stroke: '#E5E7EB' }}
                      tickLine={{ stroke: '#E5E7EB' }}
                    />
                    <YAxis
                      tick={{ fontSize: 12 }}
                      axisLine={{ stroke: '#E5E7EB' }}
                      tickLine={{ stroke: '#E5E7EB' }}
                      label={{ value: 'Workers', angle: -90, position: 'insideLeft' }}
                    />
                    <Tooltip
                      formatter={(value) => [value, 'Workers']}
                      labelFormatter={(label) => {
                        const dayData = mockTimeChartData.find(d => d.dayShort === label);
                        return dayData ? dayData.day : label;
                      }}
                      contentStyle={{
                        backgroundColor: '#F9FAFB',
                        border: '1px solid #E5E7EB',
                        borderRadius: '6px'
                      }}
                    />
                    <Bar
                      dataKey="workers"
                      fill="#3B82F6"
                      radius={[4, 4, 0, 0]}
                      name="Workers"
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </>
          )}

          {activeVisualizationTab === 'permits' && (
            <>
              <div className="text-center mb-4">
                <p className="text-lg font-normal text-gray-600">{mockPermitsData.length} Active Permits</p>
              </div>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={mockPermitChartData}
                      cx="50%"
                      cy="50%"
                      innerRadius={40}
                      outerRadius={80}
                      paddingAngle={2}
                      dataKey="value"
                    >
                      {mockPermitChartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value, name) => [value, name]} />
                    <Legend
                      verticalAlign="middle"
                      align="right"
                      layout="vertical"
                      iconType="circle"
                      wrapperStyle={{ paddingLeft: '20px', fontSize: '12px' }}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </>
          )}

          {activeVisualizationTab === 'equipment' && (
            <>
              <div className="text-center mb-4">
                <p className="text-lg font-normal text-gray-600">{mockEquipmentData.length} Equipment Items</p>
              </div>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={mockEquipmentChartData}
                      cx="50%"
                      cy="50%"
                      innerRadius={40}
                      outerRadius={80}
                      paddingAngle={2}
                      dataKey="value"
                    >
                      {mockEquipmentChartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value, name) => [value, name]} />
                    <Legend
                      verticalAlign="middle"
                      align="right"
                      layout="vertical"
                      iconType="circle"
                      wrapperStyle={{ paddingLeft: '20px', fontSize: '12px' }}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </>
          )}

          {activeVisualizationTab === 'hours' && (
            <>
              <div className="text-center mb-4">
                <p className="text-lg font-normal text-gray-600">{totalHoursWorked} Total Hours Worked</p>
              </div>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={mockHoursData}
                      cx="50%"
                      cy="50%"
                      innerRadius={40}
                      outerRadius={80}
                      paddingAngle={2}
                      dataKey="value"
                    >
                      {mockHoursData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value, name) => [`${value} hours`, name]} />
                    <Legend
                      verticalAlign="middle"
                      align="right"
                      layout="vertical"
                      iconType="circle"
                      wrapperStyle={{ paddingLeft: '20px', fontSize: '12px' }}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </>
          )}
        </div>

        {/* Alerts & Notifications */}
        <div className="bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
          <div className="mb-4">
            {/* Tab Navigation */}
            <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
              <button
                onClick={() => setActiveNotificationTab('notifications')}
                className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeNotificationTab === 'notifications'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Notifications
              </button>
              <button
                onClick={() => setActiveNotificationTab('tasks')}
                className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeNotificationTab === 'tasks'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Tasks
              </button>
              <button
                onClick={() => setActiveNotificationTab('permits')}
                className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeNotificationTab === 'permits'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Permits
              </button>
              <button
                onClick={() => setActiveNotificationTab('observations')}
                className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeNotificationTab === 'observations'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Observations
              </button>
            </div>
          </div>

          {/* Tab Content */}
          <div className="h-64 overflow-y-auto overflow-x-hidden">
            {activeNotificationTab === 'notifications' && (
              <div className="space-y-4">
                {mockNotifications.map((notification) => (
                  <div
                    key={notification.id}
                    className="flex items-start border-b border-gray-100 pb-3 cursor-pointer hover:bg-gray-50 transition-colors rounded-md p-2"
                    onClick={() => console.log('Notification clicked:', notification.id)}
                  >
                    <div className={`flex-shrink-0 rounded-full p-2 mr-3 ${
                      notification.type === 'success' ? 'bg-green-100 text-green-500' :
                      'bg-blue-100 text-blue-500'
                    }`}>
                      {notification.type === 'success' ? <ShieldCheck className="h-5 w-5" /> :
                       <ClipboardCheck className="h-5 w-5" />}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-800 break-words">{notification.message}</p>
                      <p className="text-xs text-gray-500 mt-1">{notification.timestamp}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {activeNotificationTab === 'tasks' && (
              <div className="space-y-4">
                {mockTasks.map((task) => (
                  <div
                    key={task.id}
                    className="flex items-start border-b border-gray-100 pb-3 cursor-pointer hover:bg-gray-50 transition-colors rounded-md p-2"
                    onClick={() => console.log('Task clicked:', task.id)}
                  >
                    <div className={`flex-shrink-0 rounded-full p-2 mr-3 ${
                      task.status === 'completed' ? 'bg-green-100 text-green-500' :
                      task.status === 'in-progress' ? 'bg-blue-100 text-blue-500' :
                      'bg-yellow-100 text-yellow-500'
                    }`}>
                      <CheckSquare className="h-4 w-4" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-800 break-words">{task.title}</p>
                      <p className="text-xs text-gray-500 mt-1">Assigned to: {task.assignee} • Due: {task.dueDate}</p>
                      <p className="text-xs text-gray-600">Status: {task.status.replace('-', ' ')}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {activeNotificationTab === 'permits' && (
              <div className="space-y-4">
                {mockPermitsData.map((permit) => (
                  <div
                    key={permit.id}
                    className="flex items-start border-b border-gray-100 pb-3 cursor-pointer hover:bg-gray-50 transition-colors rounded-md p-2"
                    onClick={() => console.log('Permit clicked:', permit.id)}
                  >
                    <div className={`flex-shrink-0 rounded-full p-2 mr-3 ${
                      permit.status === 'active' ? 'bg-green-100 text-green-500' :
                      permit.status === 'pending' ? 'bg-yellow-100 text-yellow-500' :
                      'bg-red-100 text-red-500'
                    }`}>
                      <FileText className="h-4 w-4" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-800 break-words">{permit.type}</p>
                      <p className="text-xs text-gray-500 mt-1">{permit.location} • Expires: {permit.expiresAt}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {activeNotificationTab === 'observations' && (
              <div className="space-y-4">
                {mockObservations.map((observation) => (
                  <div
                    key={observation.id}
                    className="flex items-start border-b border-gray-100 pb-3 cursor-pointer hover:bg-gray-50 transition-colors rounded-md p-2"
                    onClick={() => console.log('Observation clicked:', observation.id)}
                  >
                    <div className={`flex-shrink-0 rounded-full p-2 mr-3 ${
                      observation.category === 'positive' ? 'bg-green-100 text-green-500' :
                      'bg-yellow-100 text-yellow-500'
                    }`}>
                      <MessageSquare className="h-4 w-4" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-gray-800 break-words">{observation.description}</p>
                      <p className="text-xs text-gray-500 mt-1">{observation.timestamp} • {observation.location}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>


    </FloatingCard>
  );
};

export default SiteDashboard;
