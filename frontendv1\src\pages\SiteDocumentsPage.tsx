import React, { useState, useEffect } from "react";
import { useParams, useLocation } from "react-router-dom";
import {
	FileText,
	Shield,
	Building,
	CheckCircle,
	BarChart3,
} from "lucide-react";
import FloatingCard from "../components/layout/FloatingCard";
import TabContainer, { Tab } from "../components/data/shared/TabContainer";
import DocumentLibrary from "../components/documents/DocumentLibrary";
import SiteComplianceTracker from "../components/documents/SiteComplianceTracker";
import SiteDocumentReports from "../components/documents/SiteDocumentReports";
import { useSiteContext } from "../hooks/useSiteContext";
import { EntityType, DocumentCategory } from "../types/documents";

const SiteDocumentsPage: React.FC = () => {
	const { siteId } = useParams<{ siteId: string }>();
	const location = useLocation();
	const siteInfo = useSiteContext();
	const [activeTab, setActiveTab] = useState("all");

	const validTabs = ["all", "safety", "project", "compliance", "reports"];

	// Handle URL hash navigation
	useEffect(() => {
		const hash = location.hash.replace("#", "");
		if (hash && validTabs.includes(hash)) {
			setActiveTab(hash);
		} else if (!hash) {
			setActiveTab("all");
		}
	}, [location.hash]);

	const handleNavigateToTab = (tabId: string) => {
		setActiveTab(tabId);
		// Update URL hash without triggering a page reload
		window.history.pushState(null, "", `#${tabId}`);
	};

	const breadcrumbs = [
		{ name: "Dashboard", path: "/" },
		{ name: siteInfo?.siteName || "Site", path: `/sites/${siteId}/dashboard` },
		{ name: "Documents", path: `/sites/${siteId}/documents` },
	];

	const tabs: Tab[] = [
		{
			id: "all",
			label: "All Documents",
			icon: <FileText className="h-4 w-4" />,
			content: (
				<DocumentLibrary
					entityType={EntityType.SITE}
					entityId={siteId!}
					showUpload={true}
					showFilters={true}
					viewMode="grid"
				/>
			),
		},
		{
			id: "safety",
			label: "Safety Documents",
			icon: <Shield className="h-4 w-4" />,
			content: (
				<DocumentLibrary
					entityType={EntityType.SITE}
					entityId={siteId!}
					category={DocumentCategory.SAFETY}
					showUpload={true}
					showFilters={true}
					viewMode="grid"
				/>
			),
		},
		{
			id: "project",
			label: "Project Documents",
			icon: <Building className="h-4 w-4" />,
			content: (
				<DocumentLibrary
					entityType={EntityType.SITE}
					entityId={siteId!}
					category={DocumentCategory.PROJECT}
					showUpload={true}
					showFilters={true}
					viewMode="grid"
				/>
			),
		},
		{
			id: "compliance",
			label: "Compliance",
			icon: <CheckCircle className="h-4 w-4" />,
			content: <SiteComplianceTracker siteId={siteId!} />,
		},
		{
			id: "reports",
			label: "Reports",
			icon: <BarChart3 className="h-4 w-4" />,
			content: <SiteDocumentReports siteId={siteId!} />,
		},
	];

	return (
		<FloatingCard
			title={`${siteInfo?.siteName || "Site"} - Documents`}
			breadcrumbs={breadcrumbs}
		>
			<TabContainer
				tabs={tabs}
				activeTab={activeTab}
				onTabChange={handleNavigateToTab}
			/>
		</FloatingCard>
	);
};

export default SiteDocumentsPage;
