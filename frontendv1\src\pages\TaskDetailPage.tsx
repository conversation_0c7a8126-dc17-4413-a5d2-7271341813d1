import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { useQuery } from '@apollo/client';
import FloatingCard from '../components/layout/FloatingCard';
import TaskExplorer from '../components/tasks/TaskExplorer';
import TaskTabs from '../components/tasks/TaskTabs';
import { SiteTask } from '../types/tasks';
import { SiteInfo } from '../types';
import { mockSite } from '../mock/taskData';
import { GET_JOB_BY_ID } from '../graphql/queries';

// GraphQL Job interface
interface Job {
  id: number;
  title: string;
  description: string;
  status: string;
  requiredPermits: string[];
  timeForCompletion: string;
  startDate: string;
  dueDate: string;
  calculatedDueDate: string;
  categoryId: number;
  category: {
    id: number;
    description: string;
  } | null;
  requestedById: number;
  requestedBy: {
    id: number;
    name: string;
  } | null;
  requestedDate: string;
  blockedById: number;
  blockedBy: {
    id: number;
    name: string;
  } | null;
  blockedDate: string;
  reviewedById: number;
  reviewedBy: {
    id: number;
    name: string;
  } | null;
  reviewedDate: string;
  approvedById: number;
  approvedBy: {
    id: number;
    name: string;
  } | null;
  approvedDate: string;
  finishedById: number;
  finishedBy: {
    id: number;
    name: string;
  } | null;
  finishedDate: string;
  chiefEngineerId: number;
  chiefEngineer: {
    id: number;
    name: string;
  } | null;
  hazards: Array<{
    id: number;
    description: string;
    controlMeasures: Array<{
      id: number;
      description: string;
      closed: boolean;
    }>;
  }>;
  documents: Array<{
    id: number;
    name: string;
    url: string;
  }>;
  createdAt: string;
  createdBy: string;
  updatedAt: string;
  updatedBy: string;
  isDeleted: boolean;
}

// Function to map GraphQL Job to SiteTask
const mapJobToSiteTask = (job: Job): SiteTask => {
  return {
    id: job.id.toString(),
    siteId: "site-1", // Default site ID - could be passed as prop
    templateId: 'template-1', // Default template ID
    status: mapJobStatusToTaskStatus(job.status),
    category: mapJobCategoryToTaskCategory(job.category?.description || 'general'),
    name: job.title,
    description: job.description || '',
    workDescription: job.description || '',
    location: 'Site Location', // Default location - not available in Job
    plannedStartDate: new Date(job.startDate),
    plannedEndDate: new Date(job.dueDate || job.calculatedDueDate),
    estimatedDuration: parseTimeForCompletion(job.timeForCompletion),
    hazards: job.hazards.map(h => ({
      id: h.id.toString(),
      description: h.description,
      riskLevel: 'medium' as const,
      likelihood: 2,
      severity: 2,
      riskScore: 4,
      controlMeasures: h.controlMeasures.map(cm => cm.id.toString())
    })),
    controlMeasures: job.hazards.flatMap(h =>
      h.controlMeasures.map(cm => ({
        id: cm.id.toString(),
        description: cm.description,
        type: 'engineering' as const,
        effectiveness: 3,
        implementationCost: 'medium' as const,
        trainingRequired: false,
        equipmentRequired: []
      }))
    ),
    riskLevel: 'medium' as const,
    attachedDocuments: job.documents.map(d => ({
      id: d.id.toString(),
      name: d.name,
      type: 'other' as const,
      url: d.url,
      version: '1.0',
      uploadedBy: 'system',
      uploadedAt: new Date(),
      isRequired: false
    })),
    linkedPermits: job.requiredPermits || [],
    requiredDocuments: [],
    createdBy: job.createdBy,
    createdByName: job.requestedBy?.name || 'Unknown',
    assignedWorkers: [],
    assignedEquipment: [],
    createdAt: new Date(job.createdAt),
    updatedAt: new Date(job.updatedAt),
    progressPercentage: 0,
    priority: 'medium' as const,
    tags: []
  };
};

// Helper functions for mapping
const mapJobStatusToTaskStatus = (status: string): SiteTask['status'] => {
  switch (status.toLowerCase()) {
    case 'requested': return 'requested';
    case 'pending_approval': return 'requested'; // Map to closest valid status
    case 'approved': return 'approved';
    case 'disapproved': return 'rejected'; // Map to valid TaskStatus
    case 'finished': return 'completed';
    case 'blocked': return 'blocked';
    default: return 'requested';
  }
};

const mapJobCategoryToTaskCategory = (category: string): SiteTask['category'] => {
  switch (category.toLowerCase()) {
    case 'excavation': return 'excavation';
    case 'electrical': return 'electrical-installation';
    case 'plumbing': return 'plumbing';
    case 'hvac': return 'hvac';
    case 'structural': return 'steel-erection'; // Map to closest valid category
    case 'safety': return 'safety';
    case 'concrete': return 'concrete-work';
    default: return 'other'; // Use valid TaskCategory
  }
};

const parseTimeForCompletion = (timeStr: string): number => {
  if (!timeStr) return 8; // Default 8 hours
  // Parse time string like "08:00:00" to hours
  const parts = timeStr.split(':');
  if (parts.length >= 2) {
    return parseInt(parts[0]) + (parseInt(parts[1]) / 60);
  }
  return 8;
};

const TaskDetailPage: React.FC = () => {
  const { siteId, taskId } = useParams<{ siteId: string; taskId: string }>();
  const [site] = useState<SiteInfo>(mockSite);
  const [task, setTask] = useState<SiteTask | null>(null);
  const [selectedItem, setSelectedItem] = useState<string | null>(null);
  const [openTabs, setOpenTabs] = useState<Array<{
    id: string;
    title: string;
    type: 'details' | 'document' | 'hazards' | 'control-measures' | 'audit-trail' | 'requester' | 'schedule' | 'location';
    data?: any;
  }>>([]);
  const [activeTabId, setActiveTabId] = useState<string | null>(null);

  // GraphQL query to fetch job by ID
  const { data: jobData, loading: jobLoading, error: jobError } = useQuery(GET_JOB_BY_ID, {
    variables: { id: parseInt(taskId || '0') },
    skip: !taskId || isNaN(parseInt(taskId)),
  });

  useEffect(() => {
    // Map GraphQL job data to SiteTask
    if (jobData?.jobById && jobData.jobById.length > 0) {
      const job = jobData.jobById[0] as Job;
      const mappedTask = mapJobToSiteTask(job);
      setTask(mappedTask);

      // Auto-open task details tab
      const detailsTab = {
        id: 'task-details',
        title: 'Task Details',
        type: 'details' as const,
        data: mappedTask
      };
      setOpenTabs([detailsTab]);
      setActiveTabId('task-details');
    }
  }, [jobData]);



  const handleItemSelect = (itemId: string, itemType: string, itemData?: any) => {
    setSelectedItem(itemId);

    // Check if tab is already open
    const existingTab = openTabs.find(tab => tab.id === itemId);
    if (!existingTab) {
      const newTab = {
        id: itemId,
        title: itemData?.name || itemData?.title || `${itemType} ${itemId}`,
        type: itemType as any,
        data: itemData
      };
      setOpenTabs(prev => [...prev, newTab]);
    }
    setActiveTabId(itemId);
  };

  const handleTabClose = (tabId: string) => {
    setOpenTabs(prev => prev.filter(tab => tab.id !== tabId));
    if (activeTabId === tabId) {
      const remainingTabs = openTabs.filter(tab => tab.id !== tabId);
      setActiveTabId(remainingTabs.length > 0 ? remainingTabs[remainingTabs.length - 1].id : null);
    }
  };

  const handleTabChange = (tabId: string) => {
    setActiveTabId(tabId);
  };

  const breadcrumbs = [
    { name: 'Dashboard', path: '/' },
    { name: site.name, path: `/sites/${siteId}/dashboard` },
    { name: 'Tasks', path: `/sites/${siteId}/tasks` },
    { name: task?.name || 'Task Details', path: `/sites/${siteId}/tasks/${taskId}` },
  ];

  // Handle loading state
  if (jobLoading) {
    return (
      <FloatingCard title="Task Details" breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">Loading task details...</div>
        </div>
      </FloatingCard>
    );
  }

  // Handle error state
  if (jobError) {
    return (
      <FloatingCard title="Task Details" breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center h-64">
          <div className="text-red-500">Error loading task: {jobError.message}</div>
        </div>
      </FloatingCard>
    );
  }

  // Handle no task found
  if (!task) {
    return (
      <FloatingCard title="Task Details" breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">Task not found</div>
        </div>
      </FloatingCard>
    );
  }

  return (
    <FloatingCard title="Task Details" breadcrumbs={breadcrumbs} layout="custom">
      <div className="h-full flex overflow-hidden min-w-0">
        {/* Left Panel - Task Explorer */}
        <div className="w-80 flex-shrink-0 border-r border-gray-200 bg-[#fafaf8] flex flex-col">
          <TaskExplorer
            task={task}
            onItemSelect={handleItemSelect}
            selectedItem={selectedItem}
          />
        </div>

        {/* Right Panel - Task Tabs */}
        <div className="flex-1 bg-white min-w-0">
          <TaskTabs
            tabs={openTabs}
            activeTabId={activeTabId}
            onTabChange={handleTabChange}
            onTabClose={handleTabClose}
            task={task}
          />
        </div>
      </div>
    </FloatingCard>
  );
};

export default TaskDetailPage;
