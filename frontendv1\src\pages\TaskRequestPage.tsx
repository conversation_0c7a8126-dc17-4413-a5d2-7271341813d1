import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import FloatingCard from '../components/layout/FloatingCard';
import TaskRequestWorkflow from '../components/tasks/TaskRequestWorkflow';
import { SiteTask } from '../types/tasks';
import { SiteInfo } from '../types';
import { mockSite, getMockTask } from '../mock/taskData';

const TaskRequestPage: React.FC = () => {
  const { siteId, taskId } = useParams<{ siteId: string; taskId: string }>();
  const [site] = useState<SiteInfo>(mockSite);
  const [task, setTask] = useState<SiteTask | null>(null);

  useEffect(() => {
    // Fetch task request details
    if (taskId) {
      // Use centralized mock data but ensure it's a request
      const mockTask = getMockTask(taskId);
      // Override status to make it a request for this demo
      const requestTask = {
        ...mockTask,
        status: 'permit-pending' as const
      };
      setTask(requestTask);
    }
  }, [taskId]);

  const breadcrumbs = [
    { name: 'Dashboard', path: '/' },
    { name: site.name, path: `/sites/${siteId}/dashboard` },
    { name: 'Tasks', path: `/sites/${siteId}/tasks` },
    { name: 'Requests', path: `/sites/${siteId}/tasks#requests` },
    { name: task?.name || 'Task Request', path: `/sites/${siteId}/tasks/request/${taskId}` },
  ];

  if (!task) {
    return (
      <FloatingCard title="Task Request" breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center h-64">
          <div className="text-gray-500">Loading task request...</div>
        </div>
      </FloatingCard>
    );
  }

  return (
    <FloatingCard title="Task Request Management" breadcrumbs={breadcrumbs} layout="custom">
      <TaskRequestWorkflow 
        task={task} 
        siteId={siteId || ''} 
        onTaskUpdate={setTask}
      />
    </FloatingCard>
  );
};

export default TaskRequestPage;
