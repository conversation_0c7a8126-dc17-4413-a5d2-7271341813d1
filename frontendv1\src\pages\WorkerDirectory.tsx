import { useState, useMemo } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import {
  Search,
  UserPlus,
  AlertTriangle,
  CheckCircle,
  Clock,
  XCircle,
  MapPin,
  Users,
  Filter,
  Archive,
  Trash2,
  FileSpreadsheet
} from 'lucide-react';
import { useQuery } from '@apollo/client';
import FloatingCard from '../components/layout/FloatingCard';
import { TableSkeleton } from '../components/common/LoadingSpinner';
import { SiteInfo } from '../types';
import { GET_SITE_WORKERS } from '../graphql/queries';
// import { FILE_BASE_URL } from '../utils/constants';
import UniversalFilterModal, { FilterValues } from '../components/common/UniversalFilterModal';
import ActiveFiltersBar from '../components/common/ActiveFiltersBar';
import {
  getSiteWorkerSummary,
  SiteWorkerSummary,
  TrainingComplianceStatus
} from '../data/workers';

// Mock data
const mockSite: SiteInfo = {
  id: "site1",
  name: "Westlands Construction Site",
  healthStatus: "green",
  workersOnSite: 42,
  activePermits: 8,
  openIncidents: 0,
  projectManager: "John Mwangi",
  location: "Waiyaki Way, Westlands, Nairobi",
  timeline: "Jan 2025 - Dec 2026",
  currentPhase: "Foundation",
  progressPercentage: 25,
  tenantId: '',
  status: 'active',
  createdAt: new Date('2024-01-01T00:00:00Z')
};

// Using imported mock workers from centralized data file

const WorkerDirectory = () => {
  const { siteId } = useParams<{ siteId: string }>();
  const [site, _setSite] = useState<SiteInfo>(mockSite);
  const [searchTerm, setSearchTerm] = useState('');
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [activeFilters, setActiveFilters] = useState<FilterValues>({});
  const [selectedIds, setSelectedIds] = useState<number[]>([]);


  // Get site workers from centralized data (using mock data for now)
  const siteWorkers = useMemo(() => {
    return getSiteWorkerSummary(siteId || 'site1');
  }, [siteId]);

  // For backward compatibility, also fetch using GraphQL (will be replaced with site-specific query)
  const { loading, error, refetch } = useQuery(GET_SITE_WORKERS, {
    variables: { siteId: siteId || 'site1' },
    skip: true // Skip for now since we're using mock data
  });

  // Use mock data for now, but structure for easy transition to GraphQL
  const workers = siteWorkers;

  const breadcrumbs = [
    { name: 'Dashboard', path: '/' },
    { name: site.name, path: `/sites/${siteId}/dashboard` },
    { name: 'Workers', path: `/sites/${siteId}/workers` }
  ];

  // Dynamic filter configuration based on actual worker data
  const filterConfig = useMemo(() => {
    const uniqueTrades = Array.from(new Set(workers.map(worker => worker.primaryTrade)));
    const tradeOptions = uniqueTrades.map(trade => ({
      value: trade,
      label: trade,
      count: workers.filter(w => w.primaryTrade === trade).length
    }));

    return [
      {
        id: 'trade',
        label: 'Trade',
        type: 'dropdown' as const,
        placeholder: 'Select trade',
        options: tradeOptions
      },
      {
        id: 'complianceStatus',
        label: 'Compliance Status',
        type: 'dropdown' as const,
        placeholder: 'Select compliance status',
        options: [
          { value: 'compliant', label: 'Compliant', count: workers.filter(w => w.complianceStatus === 'compliant').length },
          { value: 'pending_training', label: 'Pending Training', count: workers.filter(w => w.complianceStatus === 'pending_training').length },
          { value: 'non_compliant', label: 'Non-Compliant', count: workers.filter(w => w.complianceStatus === 'non_compliant').length },
          { value: 'expired', label: 'Expired', count: workers.filter(w => w.complianceStatus === 'expired').length },
        ]
      },
      {
        id: 'onSite',
        label: 'Currently On Site',
        type: 'checkbox' as const,
        options: [
          { value: 'true', label: 'Show only workers currently on site', count: workers.filter(w => w.isOnSite).length }
        ]
      }
    ];
  }, [workers]);

  // Filter workers based on search term and active filters
  const filteredWorkers = useMemo(() => {
    return workers.filter((worker: SiteWorkerSummary) => {
      // Search filter
      const matchesSearch = worker.workerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        worker.employeeNumber.toLowerCase().includes(searchTerm.toLowerCase());

      // Trade filter
      const matchesTrade = !activeFilters.trade || worker.primaryTrade === activeFilters.trade;

      // Compliance status filter
      const matchesCompliance = !activeFilters.complianceStatus ||
        worker.complianceStatus === activeFilters.complianceStatus;

      // On-site filter
      const matchesOnSite = !activeFilters.onSite ||
        !Array.isArray(activeFilters.onSite) ||
        activeFilters.onSite.length === 0 ||
        (activeFilters.onSite.includes('true') && worker.isOnSite);

      return matchesSearch && matchesTrade && matchesCompliance && matchesOnSite;
    });
  }, [workers, searchTerm, activeFilters]);

  // Filter handlers
  const handleApplyFilters = (values: FilterValues) => {
    setActiveFilters(values);
  };

  const handleClearFilters = () => {
    setActiveFilters({});
  };

  // Count active filters for display
  const activeFilterCount = Object.values(activeFilters).filter(value => {
    if (Array.isArray(value)) return value.length > 0;
    if (typeof value === 'object' && value !== null) {
      return Object.values(value).some(v => v !== '' && v !== null);
    }
    return value !== '' && value !== null && value !== false;
  }).length;

  // Selection helpers
  const isAllSelected = filteredWorkers.length > 0 && selectedIds.length === filteredWorkers.length;
  const isAnySelected = selectedIds.length > 0;
  const toggleSelectAll = () => {
    if (isAllSelected) {
      setSelectedIds([]);
    } else {
      setSelectedIds(filteredWorkers.map(w => w.workerId));
    }
  };
  const toggleSelectOne = (id: number) => {
    setSelectedIds(prev => prev.includes(id) ? prev.filter(x => x !== id) : [...prev, id]);
  };

  // Bulk actions (placeholders)
  const handleBulkAssign = () => {
    alert(`Assign ${selectedIds.length} workers to a task/site (placeholder)`);
  };
  const handleBulkExport = () => {
    alert(`Export ${selectedIds.length} selected workers (placeholder)`);
  };
  const handleBulkArchive = () => {
    alert(`Archive ${selectedIds.length} workers (placeholder)`);
  };
  const handleBulkDelete = () => {
    if (confirm(`Are you sure you want to delete ${selectedIds.length} workers? This action cannot be undone.`)) {
      alert('Delete placeholder executed');
    }
  };

  // Calculate site worker statistics
  const siteStats = useMemo(() => {
    const totalWorkers = workers.length;
    const workersOnSite = workers.filter(w => w.isOnSite).length;
    const compliantWorkers = workers.filter(w => w.complianceStatus === 'compliant').length;
    const averageAttendance = workers.reduce((sum, w) => sum + w.attendanceRate, 0) / totalWorkers || 0;

    return {
      totalWorkers,
      workersOnSite,
      compliantWorkers,
      averageAttendance
    };
  }, [workers]);

  const getComplianceStatusIcon = (status: TrainingComplianceStatus) => {
    switch (status) {
      case 'compliant':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'pending_training':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'non_compliant':
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'expired':
        return <XCircle className="w-4 h-4 text-red-600" />;
      default:
        return <AlertTriangle className="w-4 h-4 text-gray-400" />;
    }
  };

  const getComplianceStatusText = (status: TrainingComplianceStatus) => {
    switch (status) {
      case 'compliant':
        return 'Compliant';
      case 'pending_training':
        return 'Pending Training';
      case 'non_compliant':
        return 'Non-Compliant';
      case 'expired':
        return 'Expired';
      default:
        return 'Unknown';
    }
  };

  const getComplianceStatusColor = (status: TrainingComplianceStatus) => {
    switch (status) {
      case 'compliant':
        return 'bg-green-100 text-green-800';
      case 'pending_training':
        return 'bg-yellow-100 text-yellow-800';
      case 'non_compliant':
        return 'bg-red-100 text-red-800';
      case 'expired':
        return 'bg-red-100 text-red-900';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatTime = (timeString?: string) => {
    if (!timeString) return 'N/A';
    return new Date(timeString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Handle loading and error states (simplified since we're using mock data)
  if (loading) {
    return (
      <FloatingCard title={`${site.name} - Worker Directory`} breadcrumbs={breadcrumbs}>
        <TableSkeleton rows={5} cols={6} />
      </FloatingCard>
    );
  }

  if (error) {
    return (
      <FloatingCard title="Error Loading Workers" breadcrumbs={[]}>
        <div className="text-center py-12">
          <p className="text-red-600 mb-4">Failed to load workers: {error.message}</p>
          <button
            onClick={() => refetch()}
            className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600"
          >
            Retry
          </button>
        </div>
      </FloatingCard>
    );
  }



  return (
    <FloatingCard
      title={`${site.name} - Workers`}
      breadcrumbs={breadcrumbs}
    >
      <div className="space-y-8">
        {/* Site Worker Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Total Workers</h3>
                <p className="text-2xl font-semibold mt-1">{siteStats.totalWorkers}</p>
              </div>
              <div className="p-2 rounded-full bg-gray-50">
                <Users className="h-6 w-6 text-blue-500" />
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-sm font-medium text-gray-500">On Site Now</h3>
                <p className="text-2xl font-semibold mt-1">{siteStats.workersOnSite}</p>
              </div>
              <div className="p-2 rounded-full bg-gray-50">
                <MapPin className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Compliant</h3>
                <p className="text-2xl font-semibold mt-1">{siteStats.compliantWorkers}</p>
              </div>
              <div className="p-2 rounded-full bg-gray-50">
                <CheckCircle className="h-6 w-6 text-green-500" />
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
            <div className="flex justify-between items-start">
              <div>
                <h3 className="text-sm font-medium text-gray-500">Avg Attendance</h3>
                <p className="text-2xl font-semibold mt-1">{siteStats.averageAttendance.toFixed(1)}%</p>
              </div>
              <div className="p-2 rounded-full bg-gray-50">
                <Clock className="h-6 w-6 text-indigo-500" />
              </div>
            </div>
          </div>
        </div>

        {/* Worker Management Interface */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">

          {/* Toolbar: Title left, Search/Filters/Add right */}
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
            {/* Left: Section Title */}
            <h2 className="text-lg font-semibold text-gray-900">Workers</h2>

            {/* Right: Search, Filters, Add Workers */}
            <div className="flex items-center w-full md:w-auto gap-3">
              {/* Search */}
              <div className="relative flex-1 md:flex-initial md:w-80">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-4 w-4 text-gray-500" />
                </div>
                <input
                  type="text"
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                  placeholder="Search workers by name or trade..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>

              {/* Filter Button */}
              <button
                onClick={() => setIsFilterOpen(true)}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-green-500 focus:border-green-500"
              >
                <Filter className="h-4 w-4 mr-2" />
                Filters
                {activeFilterCount > 0 && (
                  <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    {activeFilterCount}
                  </span>
                )}
              </button>

              {/* Import Button (site-level) */}
              <Link
                to={`/sites/${siteId}/workers/import`}
                className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-green-500 focus:border-green-500"
              >
                <FileSpreadsheet className="h-4 w-4 mr-2" />
                Import
              </Link>

              {/* Add Workers */}
              <Link
                to={`/sites/${siteId}/workers/create`}
                className="inline-flex items-center px-4 py-2 border border-green-600 rounded-md shadow-sm text-sm font-medium text-green-600 bg-white hover:bg-green-50"
              >
                <UserPlus className="h-4 w-4 mr-2" />
                Add Workers
              </Link>
            </div>
          </div>

          {/* Active Filters Display - shared */}
          <ActiveFiltersBar
            values={activeFilters}
            config={filterConfig}
            onRemove={(filterId) => {
              const newFilters = { ...activeFilters };
              delete newFilters[filterId];
              setActiveFilters(newFilters);
            }}
            onClear={handleClearFilters}
          />

          {/* Bulk actions bar (Gmail-like) */}
          {isAnySelected && (
            <div className="mb-3 flex items-center justify-between p-2 bg-white border border-gray-200 rounded-md shadow-sm">
              <div className="text-sm text-gray-700 font-medium">{selectedIds.length} selected</div>
              <div className="flex items-center gap-2">
                <button onClick={handleBulkAssign} title="Assign" className="p-2 rounded hover:bg-gray-100 text-gray-700">
                  {/* Reuse UserPlus from toolbar to imply assignment */}
                  <UserPlus className="h-5 w-5" />
                </button>
                <button onClick={handleBulkExport} title="Export" className="p-2 rounded hover:bg-gray-100 text-gray-700">
                  <FileSpreadsheet className="h-5 w-5" />
                </button>
                <button onClick={handleBulkArchive} title="Archive" className="p-2 rounded hover:bg-gray-100 text-gray-700">
                  <Archive className="h-5 w-5" />
                </button>
                <button onClick={handleBulkDelete} title="Delete" className="p-2 rounded hover:bg-gray-100 text-red-600">
                  <Trash2 className="h-5 w-5" />
                </button>
              </div>
            </div>
          )}

          {/* Workers Table */}
          <div className="bg-white shadow overflow-hidden border border-gray-200 rounded-lg">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left">
                    <input
                      type="checkbox"
                      checked={isAllSelected}
                      onChange={toggleSelectAll}
                      aria-label="Select all"
                      className="h-4 w-4 text-green-600 border-gray-300 rounded"
                    />
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Worker
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Role & Trade
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Compliance
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Attendance
                  </th>
                  <th scope="col" className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredWorkers.map((worker: SiteWorkerSummary) => (
                  <tr key={worker.workerId} className="hover:bg-gray-50">
                    {/* Select */}
                    <td className="px-4 py-4 whitespace-nowrap">
                      <input
                        type="checkbox"
                        checked={selectedIds.includes(worker.workerId)}
                        onChange={() => toggleSelectOne(worker.workerId)}
                        aria-label={`Select ${worker.workerName}`}
                        className="h-4 w-4 text-green-600 border-gray-300 rounded"
                      />
                    </td>
                    {/* Worker Column */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <img
                            className="h-10 w-10 rounded-full"
                            src={worker.photoUrl || `https://ui-avatars.com/api/?name=${encodeURIComponent(worker.workerName)}&background=3B82F6&color=fff`}
                            alt={worker.workerName}
                          />
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">
                            <Link to={`/sites/${siteId}/workers/${worker.workerId}`} className="hover:text-green-500">
                              {worker.workerName}
                            </Link>
                          </div>
                          <div className="text-sm text-gray-500">{worker.employeeNumber}</div>
                        </div>
                      </div>
                    </td>

                    {/* Role & Trade Column */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm font-medium text-gray-900">{worker.currentRole}</div>
                      <div className="text-sm text-gray-500">{worker.primaryTrade}</div>
                    </td>

                    {/* Status Column */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex flex-col space-y-1">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${worker.isOnSite ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                          }`}>
                          <MapPin className="w-3 h-3 mr-1" />
                          {worker.isOnSite ? 'On Site' : 'Off Site'}
                        </span>
                        {worker.lastCheckIn && (
                          <span className="text-xs text-gray-500">
                            Last: {formatTime(worker.lastCheckIn)}
                          </span>
                        )}
                      </div>
                    </td>

                    {/* Compliance Column */}
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getComplianceStatusColor(worker.complianceStatus)}`}>
                        {getComplianceStatusIcon(worker.complianceStatus)}
                        <span className="ml-1">{getComplianceStatusText(worker.complianceStatus)}</span>
                      </span>
                    </td>

                    {/* Attendance Column */}
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <div className="flex flex-col">
                        <span className="font-medium">{worker.attendanceRate.toFixed(1)}%</span>
                        <span className="text-xs text-gray-500">
                          {worker.hoursWorkedThisWeek?.toFixed(1) || 0}h this week
                        </span>
                      </div>
                    </td>

                    {/* Actions Column */}
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        <Link
                          to={`/sites/${siteId}/workers/${worker.workerId}`}
                          className="px-3 py-1 text-xs border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors"
                          style={{ borderRadius: '5px' }}
                          title="View Details"
                        >
                          View
                        </Link>
                        <Link
                          to={`/sites/${siteId}/workers/${worker.workerId}/edit`}
                          className="px-3 py-1 text-xs border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors"
                          style={{ borderRadius: '5px' }}
                          title="Edit Worker"
                        >
                          Edit
                        </Link>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {/* Empty state */}
            {filteredWorkers.length === 0 && (
              <div className="px-6 py-10 text-center">
                <Users className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No workers found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  No workers match your search criteria.
                </p>
                <button
                  className="mt-3 text-green-500 hover:text-green-600 font-medium"
                  onClick={() => {
                    setSearchTerm('');
                    setActiveFilters({});
                  }}
                >
                  Clear all filters
                </button>
              </div>
            )}
          </div>
        </div>

      </div>

      {/* Universal Filter Modal */}
      <UniversalFilterModal
        isOpen={isFilterOpen}
        onClose={() => setIsFilterOpen(false)}
        title="Filter Workers"
        filters={filterConfig}
        initialValues={activeFilters}
        onApplyFilters={handleApplyFilters}
        onClearFilters={handleClearFilters}
        size="xl"
      />

    </FloatingCard>
  );
};

export default WorkerDirectory;
