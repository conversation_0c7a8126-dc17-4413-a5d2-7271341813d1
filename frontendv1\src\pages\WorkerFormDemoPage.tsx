import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Users, CheckCircle, AlertCircle } from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import CreateWorkerForm from '../components/workers/CreateWorkerForm';

const WorkerFormDemoPage: React.FC = () => {
  const navigate = useNavigate();
  const [showForm, setShowForm] = useState(false);
  const [lastCreatedWorker, setLastCreatedWorker] = useState<any>(null);

  const handleWorkerCreated = (worker: any) => {
    console.log('Worker created successfully:', worker);
    setLastCreatedWorker(worker);
    setShowForm(false);
    
    // Show success message
    alert(`Worker "${worker.name}" has been created successfully!`);
  };

  const handleCancel = () => {
    setShowForm(false);
  };

  const handleBackClick = () => {
    navigate('/');
  };

  if (showForm) {
    return (
      <FloatingCard title="Worker Form Demo">
        <div className="min-h-screen bg-gray-50">
          {/* Header */}
          <div className="bg-white border-b border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <button
                  onClick={handleCancel}
                  className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
                >
                  <ArrowLeft className="h-5 w-5 mr-2" />
                  Back to Demo
                </button>
                <div className="h-6 w-px bg-gray-300" />
                <div className="flex items-center space-x-2">
                  <Users className="h-6 w-6 text-green-600" />
                  <h1 className="text-2xl font-bold text-gray-900">Create Worker Demo</h1>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <div className="px-6 py-8">
            <div className="max-w-6xl mx-auto">
              <CreateWorkerForm
                onSuccess={handleWorkerCreated}
                onCancel={handleCancel}
                useDummyData={true} // Using dummy data for demo
              />
            </div>
          </div>
        </div>
      </FloatingCard>
    );
  }

  return (
    <FloatingCard title="Worker Form">
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBackClick}
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                Back to Dashboard
              </button>
              <div className="h-6 w-px bg-gray-300" />
              <div className="flex items-center space-x-2">
                <Users className="h-6 w-6 text-green-600" />
                <h1 className="text-2xl font-bold text-gray-900">Worker Form Demo</h1>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="px-6 py-8">
          <div className="max-w-4xl mx-auto space-y-8">
            {/* Demo Introduction */}
            <div className="bg-white rounded-lg shadow-lg p-8 text-center">
              <div className="flex justify-center mb-4">
                <Users className="h-16 w-16 text-green-600" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Worker Creation Form Demo
              </h2>
              <p className="text-gray-600 mb-8 text-lg">
                Experience our comprehensive worker creation form with validation, photo upload, and multi-select dropdowns.
              </p>
              
              <button
                onClick={() => setShowForm(true)}
                className="inline-flex items-center px-8 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 text-lg font-medium"
              >
                <Users className="h-5 w-5 mr-2" />
                Try the Form
              </button>
            </div>

            {/* Last Created Worker */}
            {lastCreatedWorker && (
              <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                <div className="flex items-start">
                  <CheckCircle className="h-6 w-6 text-green-600 mt-0.5 mr-3" />
                  <div>
                    <h3 className="text-lg font-medium text-green-800 mb-2">
                      Last Worker Created Successfully!
                    </h3>
                    <div className="text-sm text-green-700">
                      <p><strong>Name:</strong> {lastCreatedWorker.name}</p>
                      <p><strong>Company:</strong> {lastCreatedWorker.company}</p>
                      <p><strong>National ID:</strong> {lastCreatedWorker.nationalId}</p>
                      <p><strong>Phone:</strong> {lastCreatedWorker.phoneNumber}</p>
                      {lastCreatedWorker.email && (
                        <p><strong>Email:</strong> {lastCreatedWorker.email}</p>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Form Features */}
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Form Features</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="font-medium text-gray-900">Comprehensive Validation</h4>
                      <p className="text-sm text-gray-600">Real-time validation for all fields with clear error messages</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="font-medium text-gray-900">Photo Upload</h4>
                      <p className="text-sm text-gray-600">File upload with preview and validation (independent of form validation)</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="font-medium text-gray-900">Multi-Select Dropdowns</h4>
                      <p className="text-sm text-gray-600">Checkbox-based selection for Skills, Training, and Trades</p>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="font-medium text-gray-900">GraphQL Integration</h4>
                      <p className="text-sm text-gray-600">Ready for real GraphQL queries with dummy data fallback</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="font-medium text-gray-900">Responsive Design</h4>
                      <p className="text-sm text-gray-600">Works perfectly on mobile and desktop devices</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="font-medium text-gray-900">Loading States</h4>
                      <p className="text-sm text-gray-600">Proper loading indicators and error handling</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Technical Details */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <div className="flex items-start">
                <AlertCircle className="h-6 w-6 text-blue-600 mt-0.5 mr-3" />
                <div>
                  <h3 className="text-lg font-medium text-blue-800 mb-2">
                    Technical Implementation
                  </h3>
                  <div className="text-sm text-blue-700 space-y-2">
                    <p>• Built with React 19 and TypeScript for type safety</p>
                    <p>• Uses Apollo Client for GraphQL integration</p>
                    <p>• Styled with Tailwind CSS for responsive design</p>
                    <p>• Includes comprehensive form validation with real-time feedback</p>
                    <p>• Photo upload with file type and size validation</p>
                    <p>• Multi-select dropdowns with dummy data (ready for real GraphQL)</p>
                    <p>• Follows existing codebase patterns and conventions</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </FloatingCard>
  );
};

export default WorkerFormDemoPage;
