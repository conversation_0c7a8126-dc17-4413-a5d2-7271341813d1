import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import PermitFormDisplay from '../../components/permits/PermitFormDisplay';

const ConfinedSpacePermitDisplayPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId, permitId } = useParams<{ siteId: string; permitId: string }>();

  // Mock permit data - in real app this would come from API
  const mockPermitData = {
    id: permitId || 'cs-001',
    projectName: 'Storage Tank Inspection',
    location: 'Tank Farm - Tank #3',
    startDateTime: new Date().toLocaleString(),
    endDateTime: new Date(Date.now() + 12 * 60 * 60 * 1000).toLocaleString(),
    workDescription: 'Internal inspection and maintenance of storage tank including cleaning, welding repairs, and safety equipment testing. Entry requires continuous atmospheric monitoring and emergency standby personnel.',
    hazards: 'Oxygen deficiency, toxic gases, engulfment, limited egress, fire/explosion risk. Precautions include atmospheric testing, ventilation, emergency rescue team, and continuous monitoring.',
    issuedBy: '<PERSON> - Safety Coordinator',
    issueDateTime: new Date().toLocaleString(),
    returnedBy: '<PERSON> - Operations Manager',
    returnDate<PERSON>ime: new Date(Date.now() + 10 * 60 * 60 * 1000).toLocaleString(),
    formData: {
      'Basic Information_PTW Ref No': 'CSE-2024-002',
      'Basic Information_Project Name': 'Storage Tank Inspection',
      'Basic Information_Location': 'Tank Farm - Tank #3',
      'Basic Information_Starting from': new Date().toISOString().slice(0, 16),
      'Basic Information_Ending at': new Date(Date.now() + 12 * 60 * 60 * 1000).toISOString().slice(0, 16),
      'Basic Information_Description of work': 'Internal inspection and maintenance of storage tank',
      'Training Verification_Entrants and observer trained on Confined Space Entry and rescue within the last 2 years': true,
      'Training Verification_Name of Training Organization': 'Kenya Safety Training Institute',
      'Atmospheric Testing_Oxygen (19.5% - 23.5%)': '20.8%',
      'Atmospheric Testing_Combustible gases (Less than 10% LEL)': '0%',
      'Atmospheric Testing_Toxic gases (Within permissible limits)': 'Within limits',
      'Atmospheric Testing_Time of testing': new Date().toTimeString().slice(0, 5),
      'Atmospheric Testing_Tested by': 'James Kiprotich',
      'Entry Procedures_Atmospheric monitoring continuous': true,
      'Entry Procedures_Ventilation adequate': true,
      'Entry Procedures_Communication established': true,
      'Entry Procedures_Emergency rescue equipment available': true,
      'Entry Procedures_Attendant stationed outside': true,
      'Entry Procedures_Entry permit posted': true,
      'Hazard Assessment_Oxygen deficiency': true,
      'Hazard Assessment_Flammable atmosphere': false,
      'Hazard Assessment_Toxic atmosphere': false,
      'Hazard Assessment_Engulfment': true,
      'Hazard Assessment_Mechanical hazards': true,
      'Hazard Assessment_Electrical hazards': false,
      'Safety Equipment_Gas detector': true,
      'Safety Equipment_Ventilation fan': true,
      'Safety Equipment_Communication device': true,
      'Safety Equipment_Emergency lighting': true,
      'Safety Equipment_Rescue equipment': true,
      'Safety Equipment_First aid kit': true,
      'PPE_Hard hat': true,
      'PPE_Safety harness': true,
      'PPE_Gas mask/SCBA': true,
      'PPE_Safety boots': true,
      'PPE_Gloves': true,
      'PPE_Coveralls': true,
      'Emergency Procedures_Rescue team on standby': true,
      'Emergency Procedures_Emergency contacts posted': true,
      'Emergency Procedures_Evacuation plan established': true,
      'Emergency Procedures_Medical assistance available': true
    }
  };

  const handleBack = () => {
    if (siteId) {
      navigate(`/sites/${siteId}/permits`);
    } else {
      navigate('/permits');
    }
  };

  return (
    <PermitFormDisplay
      permitType="confined-space"
      permitData={mockPermitData}
      onBack={handleBack}
    />
  );
};

export default ConfinedSpacePermitDisplayPage;
