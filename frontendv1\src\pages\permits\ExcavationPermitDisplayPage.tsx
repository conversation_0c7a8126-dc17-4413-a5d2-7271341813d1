import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import PermitFormDisplay from '../../components/permits/PermitFormDisplay';

const ExcavationPermitDisplayPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId, permitId } = useParams<{ siteId: string; permitId: string }>();

  // Mock permit data - in real app this would come from API
  const mockPermitData = {
    id: permitId || 'exc-001',
    projectName: 'Underground Utility Installation',
    location: 'Building A - East Side Parking',
    startDateTime: new Date().toLocaleString(),
    endDateTime: new Date(Date.now() + 24 * 60 * 60 * 1000).toLocaleString(),
    workDescription: 'Excavation work for installing new water and electrical utilities. Work includes digging trenches, laying pipes and cables, backfilling, and site restoration. All work to be conducted according to safety protocols.',
    hazards: 'Underground utilities, cave-in risks, heavy machinery operation, confined space entry. Precautions include utility location, proper shoring, safety barriers, and continuous monitoring.',
    issuedBy: '<PERSON> - Site Safety Officer',
    issueDateTime: new Date().toLocaleString(),
    returnedBy: '<PERSON> - Project Supervisor',
    returnDateTime: new Date(Date.now() + 8 * 60 * 60 * 1000).toLocaleString(),
    formData: {
      'Details_PTW Ref No': 'EXC-2024-001',
      'Details_Project Name': 'Underground Utility Installation',
      'Details_Location': 'Building A - East Side Parking',
      'Details_Starting from': new Date().toISOString().slice(0, 16),
      'Details_Ending at': new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().slice(0, 16),
      'Details_Description of work': 'Excavation work for installing new water and electrical utilities',
      'Details_No. of employees involved': '4',
      'Details_Depth of excavation (m)': '2.5',
      'Details_Length of excavation (m)': '15',
      'Details_Width of excavation (m)': '1.2',
      'Hazard Identification_Underground utilities': true,
      'Hazard Identification_Overhead power lines': false,
      'Hazard Identification_Traffic': true,
      'Hazard Identification_Pedestrians': true,
      'Hazard Identification_Cave-in': true,
      'Hazard Identification_Water ingress': false,
      'Hazard Identification_Contaminated soil': false,
      'Hazard Identification_Other hazards': 'Heavy machinery operation',
      'Precautions_Utilities located and marked': true,
      'Precautions_Excavation permit obtained': true,
      'Precautions_Shoring/sloping adequate': true,
      'Precautions_Safe access/egress provided': true,
      'Precautions_Spoil pile properly located': true,
      'Precautions_Traffic control in place': true,
      'Precautions_Atmospheric testing completed': false,
      'Precautions_Emergency equipment available': true,
      'Equipment_Excavator': true,
      'Equipment_Compactor': true,
      'Equipment_Hand tools': true,
      'Equipment_Safety barriers': true,
      'Equipment_Warning signs': true,
      'Equipment_Gas detector': false,
      'Equipment_Ventilation equipment': false,
      'PPE_Hard hat': true,
      'PPE_Safety boots': true,
      'PPE_High visibility vest': true,
      'PPE_Safety glasses': true,
      'PPE_Gloves': true,
      'PPE_Hearing protection': true,
      'PPE_Fall protection': false,
      'Training_Excavation safety': true,
      'Training_Equipment operation': true,
      'Training_Emergency procedures': true,
      'Training_First aid': true,
      'Permit Issue_Competent Person (Permit Receiver)_Name': 'Peter Kamau',
      'Permit Issue_Competent Person (Permit Receiver)_Date': new Date().toISOString().slice(0, 10),
      'Permit Issue_Competent Person (Permit Receiver)_Time': new Date().toTimeString().slice(0, 5),
      'Permit Issue_Competent Person (Permit Receiver)_Signature': 'P. Kamau',
      'Permit Issue_Authorizing Person (Permit Issuer)_Name': 'John Mwangi',
      'Permit Issue_Authorizing Person (Permit Issuer)_Date': new Date().toISOString().slice(0, 10),
      'Permit Issue_Authorizing Person (Permit Issuer)_Time': new Date().toTimeString().slice(0, 5),
      'Permit Issue_Authorizing Person (Permit Issuer)_Signature': 'J. Mwangi',
      'Permit Return_Competent Person (Permit Receiver)_Name': 'Peter Kamau',
      'Permit Return_Competent Person (Permit Receiver)_Date': new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString().slice(0, 10),
      'Permit Return_Competent Person (Permit Receiver)_Time': new Date(Date.now() + 8 * 60 * 60 * 1000).toTimeString().slice(0, 5),
      'Permit Return_Competent Person (Permit Receiver)_Signature': 'P. Kamau',
      'Permit Return_Authorizing Person (Permit Issuer)_Name': 'Sarah Njeri',
      'Permit Return_Authorizing Person (Permit Issuer)_Date': new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString().slice(0, 10),
      'Permit Return_Authorizing Person (Permit Issuer)_Time': new Date(Date.now() + 8 * 60 * 60 * 1000).toTimeString().slice(0, 5),
      'Permit Return_Authorizing Person (Permit Issuer)_Signature': 'S. Njeri'
    }
  };

  const handleBack = () => {
    if (siteId) {
      navigate(`/sites/${siteId}/permits`);
    } else {
      navigate('/permits');
    }
  };

  return (
    <PermitFormDisplay
      permitType="excavation"
      permitData={mockPermitData}
      onBack={handleBack}
    />
  );
};

export default ExcavationPermitDisplayPage;
