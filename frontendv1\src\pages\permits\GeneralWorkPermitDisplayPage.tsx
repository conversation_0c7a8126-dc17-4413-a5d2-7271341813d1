import React from 'react';
import { useNavigate, usePara<PERSON> } from 'react-router-dom';
import PermitFormDisplay from '../../components/permits/PermitFormDisplay';

const GeneralWorkPermitDisplayPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId, permitId } = useParams<{ siteId: string; permitId: string }>();

  // Mock permit data - in real app this would come from API
  const mockPermitData = {
    id: permitId || 'gwp-001',
    projectName: 'General Maintenance Work',
    location: 'Main Building - Multiple Areas',
    startDateTime: new Date().toLocaleString(),
    endDateTime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toLocaleString(),
    workDescription: 'General maintenance activities including painting, minor repairs, equipment servicing, and facility upkeep. Work to be conducted during normal business hours with appropriate safety measures.',
    hazards: 'Slip/trip hazards, chemical exposure, ladder work, power tools. Precautions include proper PPE, area isolation, tool inspection, and safety briefings.',
    issuedBy: '<PERSON> - Maintenance Supervisor',
    issueDateTime: new Date().toLocaleString(),
    returnedBy: '<PERSON> - Facility Manager',
    returnDateTime: new Date(Date.now() + 6 * 24 * 60 * 60 * 1000).toLocaleString(),
    formData: {
      'details_PTW Ref No': 'GWP-2024-003',
      'details_Project Name': 'General Maintenance Work',
      'details_No. of employees involved': '6',
      'details_Starting from': new Date().toISOString().slice(0, 16),
      'details_Expected completion': new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().slice(0, 16),
      'Other Permits in use_WAH': false,
      'Other Permits in use_HWP': false,
      'Other Permits in use_Excavation Permit': false,
      'Other Permits in use_Electric Permit': false,
      'Work Description_Nature of work': 'General maintenance and repair activities',
      'Work Description_Location of work': 'Main Building - Multiple Areas',
      'Work Description_Equipment to be used': 'Hand tools, ladders, cleaning equipment, paint sprayers',
      'Team Information_Team Leader': 'Michael Otieno',
      'Team Information_Team Members': 'Joseph Maina, Alice Wanjiru, Robert Kimani, Susan Achieng, Daniel Mutua',
      'Team Information_Emergency Contact': '+254 700 123 456',
      'Hazard Identification_Slip/trip hazards': true,
      'Hazard Identification_Chemical exposure': true,
      'Hazard Identification_Electrical hazards': false,
      'Hazard Identification_Fire hazards': false,
      'Hazard Identification_Fall hazards': true,
      'Hazard Identification_Mechanical hazards': false,
      'Precautions_Area isolated/barricaded': true,
      'Precautions_PPE provided and used': true,
      'Precautions_Tools inspected': true,
      'Precautions_Safety briefing conducted': true,
      'Precautions_Emergency procedures reviewed': true,
      'Precautions_First aid available': true,
      'PPE Requirements_Hard hat': true,
      'PPE Requirements_Safety glasses': true,
      'PPE Requirements_Gloves': true,
      'PPE Requirements_Safety boots': true,
      'PPE Requirements_High visibility vest': true,
      'PPE Requirements_Hearing protection': false,
      'PPE Requirements_Respiratory protection': true,
      'Isolation Requirements_Electrical isolation': false,
      'Isolation Requirements_Mechanical isolation': false,
      'Isolation Requirements_Process isolation': false,
      'Isolation Requirements_Area isolation': true,
      'Hot Work_Welding': false,
      'Hot Work_Cutting': false,
      'Hot Work_Grinding': false,
      'Hot Work_Other hot work': false,
      'Confined Space_Entry required': false,
      'Confined Space_Atmospheric testing': false,
      'Confined Space_Ventilation': false,
      'Confined Space_Attendant assigned': false
    }
  };

  const handleBack = () => {
    if (siteId) {
      navigate(`/sites/${siteId}/permits`);
    } else {
      navigate('/permits');
    }
  };

  return (
    <PermitFormDisplay
      permitType="general-work"
      permitData={mockPermitData}
      onBack={handleBack}
    />
  );
};

export default GeneralWorkPermitDisplayPage;
