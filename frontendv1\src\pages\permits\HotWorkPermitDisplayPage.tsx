import React from 'react';
import { useNavigate, usePara<PERSON> } from 'react-router-dom';
import PermitFormDisplay from '../../components/permits/PermitFormDisplay';

const HotWorkPermitDisplayPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId, permitId } = useParams<{ siteId: string; permitId: string }>();

  // Mock permit data - in real app this would come from API
  const mockPermitData = {
    id: permitId || 'hwp-001',
    projectName: 'Pipe Welding Operations',
    location: 'Mechanical Room - Level B1',
    startDateTime: new Date().toLocaleString(),
    endDateTime: new Date(Date.now() + 8 * 60 * 60 * 1000).toLocaleString(),
    workDescription: 'Welding and cutting operations for pipe installation and repair. Work includes arc welding, oxy-fuel cutting, and grinding. Fire watch required throughout operations.',
    hazards: 'Fire/explosion, burns, toxic fumes, UV radiation, electrical shock. Precautions include fire watch, ventilation, fire extinguishers, and proper PPE.',
    issuedBy: '<PERSON> - Welding Supervisor',
    issueDateTime: new Date().toLocaleString(),
    returnedBy: '<PERSON> - Safety Officer',
    returnDateTime: new Date(Date.now() + 8 * 60 * 60 * 1000).toLocaleString(),
    formData: {
      'Details_PTW Ref No': 'HWP-2024-004',
      'Details_Project Name': 'Pipe Welding Operations',
      'Details_Location': 'Mechanical Room - Level B1',
      'Details_Starting from': new Date().toISOString().slice(0, 16),
      'Details_Ending at': new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString().slice(0, 16),
      'Details_Description of work': 'Welding and cutting operations for pipe installation and repair',
      'Details_No. of employees involved': '3',
      'Details_Type of hot work': 'Arc welding, Oxy-fuel cutting',
      'Fire Prevention_Combustible materials removed': true,
      'Fire Prevention_Fire extinguisher available': true,
      'Fire Prevention_Fire watch assigned': true,
      'Fire Prevention_Hot work permit posted': true,
      'Fire Prevention_Emergency procedures reviewed': true,
      'Fire Prevention_Fire blankets available': true,
      'Atmospheric Conditions_Adequate ventilation': true,
      'Atmospheric Conditions_No flammable vapors': true,
      'Atmospheric Conditions_Wind conditions acceptable': true,
      'Atmospheric Conditions_Atmospheric testing completed': true,
      'Equipment Inspection_Welding equipment inspected': true,
      'Equipment Inspection_Cutting equipment inspected': true,
      'Equipment Inspection_Electrical connections secure': true,
      'Equipment Inspection_Gas cylinders secured': true,
      'Equipment Inspection_Hoses and fittings checked': true,
      'PPE_Welding helmet': true,
      'PPE_Leather gloves': true,
      'PPE_Welding jacket': true,
      'PPE_Safety boots': true,
      'PPE_Safety glasses': true,
      'PPE_Respiratory protection': true,
      'Area Preparation_Work area cleared': true,
      'Area Preparation_Floor swept clean': true,
      'Area Preparation_Drains covered': true,
      'Area Preparation_Walls/ceiling protected': true,
      'Area Preparation_Adjacent areas notified': true,
      'Fire Watch_Fire watch trained': true,
      'Fire Watch_Fire watch equipped': true,
      'Fire Watch_Communication established': true,
      'Fire Watch_30-minute post-work watch': true,
      'Welding Procedures_Qualified welder': true,
      'Welding Procedures_Proper electrodes': true,
      'Welding Procedures_Correct settings': true,
      'Welding Procedures_Work procedure followed': true,
      'Emergency Procedures_Emergency contacts posted': true,
      'Emergency Procedures_First aid available': true,
      'Emergency Procedures_Evacuation plan known': true,
      'Emergency Procedures_Burn treatment available': true,
      'Permit Issue_Competent Person (Permit Receiver)_Name': 'Patrick Njoroge',
      'Permit Issue_Competent Person (Permit Receiver)_Date': new Date().toISOString().slice(0, 10),
      'Permit Issue_Competent Person (Permit Receiver)_Time': new Date().toTimeString().slice(0, 5),
      'Permit Issue_Competent Person (Permit Receiver)_Signature': 'P. Njoroge',
      'Permit Issue_Authorizing Person (Permit Issuer)_Name': 'Catherine Wambui',
      'Permit Issue_Authorizing Person (Permit Issuer)_Date': new Date().toISOString().slice(0, 10),
      'Permit Issue_Authorizing Person (Permit Issuer)_Time': new Date().toTimeString().slice(0, 5),
      'Permit Issue_Authorizing Person (Permit Issuer)_Signature': 'C. Wambui'
    }
  };

  const handleBack = () => {
    if (siteId) {
      navigate(`/sites/${siteId}/permits`);
    } else {
      navigate('/permits');
    }
  };

  return (
    <PermitFormDisplay
      permitType="hot-work"
      permitData={mockPermitData}
      onBack={handleBack}
    />
  );
};

export default HotWorkPermitDisplayPage;
