import React from 'react';
import { useNavigate, usePara<PERSON> } from 'react-router-dom';
import PermitFormDisplay from '../../components/permits/PermitFormDisplay';

const WorkAtHeightPermitDisplayPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId, permitId } = useParams<{ siteId: string; permitId: string }>();

  // Mock permit data - in real app this would come from API
  const mockPermitData = {
    id: permitId || 'wah-001',
    projectName: 'Roof Maintenance Work',
    location: 'Main Building - Rooftop',
    startDateTime: new Date().toLocaleString(),
    endDateTime: new Date(Date.now() + 6 * 60 * 60 * 1000).toLocaleString(),
    workDescription: 'Roof inspection, repair, and maintenance work including gutter cleaning, leak repairs, and equipment servicing. All work conducted at heights above 2 meters requiring fall protection.',
    hazards: 'Falls from height, unstable surfaces, weather conditions, equipment failure. Precautions include fall protection systems, safety harnesses, guardrails, and weather monitoring.',
    issuedBy: '<PERSON> - Height Safety Coordinator',
    issueDateTime: new Date().toLocaleString(),
    returnedBy: '<PERSON> - Building Manager',
    returnDateTime: new Date(Date.now() + 6 * 60 * 60 * 1000).toLocaleString(),
    formData: {
      'Details_PTW Ref No': 'WAH-2024-005',
      'Details_Project Name': 'Roof Maintenance Work',
      'Details_Location': 'Main Building - Rooftop',
      'Details_Starting from': new Date().toISOString().slice(0, 16),
      'Details_Ending at': new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString().slice(0, 16),
      'Details_Description of work': 'Roof inspection, repair, and maintenance work',
      'Details_No. of employees involved': '4',
      'Details_Height of work (m)': '12',
      'Details_Type of access': 'Ladder, Safety platform',
      'Fall Protection_Safety harness required': true,
      'Fall Protection_Guardrails installed': true,
      'Fall Protection_Safety nets provided': false,
      'Fall Protection_Fall arrest system': true,
      'Fall Protection_Anchor points inspected': true,
      'Fall Protection_Rescue plan established': true,
      'Access Equipment_Ladders inspected': true,
      'Access Equipment_Scaffolding erected': false,
      'Access Equipment_Mobile platform used': true,
      'Access Equipment_Roof access secured': true,
      'Access Equipment_Edge protection installed': true,
      'Weather Conditions_Wind speed acceptable': true,
      'Weather Conditions_No precipitation': true,
      'Weather Conditions_Visibility adequate': true,
      'Weather Conditions_Temperature suitable': true,
      'Weather Conditions_Weather forecast checked': true,
      'PPE_Safety harness': true,
      'PPE_Hard hat': true,
      'PPE_Safety boots': true,
      'PPE_Gloves': true,
      'PPE_High visibility vest': true,
      'PPE_Eye protection': true,
      'Training_Height safety training': true,
      'Training_Equipment use training': true,
      'Training_Rescue procedures': true,
      'Training_Emergency response': true,
      'Training_Valid certification': true,
      'Equipment Inspection_Harnesses inspected': true,
      'Equipment Inspection_Lanyards inspected': true,
      'Equipment Inspection_Anchor points tested': true,
      'Equipment Inspection_Ladders checked': true,
      'Equipment Inspection_Tools secured': true,
      'Emergency Procedures_Rescue team available': true,
      'Emergency Procedures_Communication established': true,
      'Emergency Procedures_First aid accessible': true,
      'Emergency Procedures_Emergency contacts posted': true,
      'Work Area_Area cordoned off': true,
      'Work Area_Warning signs posted': true,
      'Work Area_Ground personnel assigned': true,
      'Work Area_Tools and materials secured': true,
      'Work Area_Overhead hazards identified': true,
      'Permit Issue_Competent Person (Permit Receiver)_Name': 'Samuel Kiprotich',
      'Permit Issue_Competent Person (Permit Receiver)_Date': new Date().toISOString().slice(0, 10),
      'Permit Issue_Competent Person (Permit Receiver)_Time': new Date().toTimeString().slice(0, 5),
      'Permit Issue_Competent Person (Permit Receiver)_Signature': 'S. Kiprotich',
      'Permit Issue_Authorizing Person (Permit Issuer)_Name': 'Nancy Akinyi',
      'Permit Issue_Authorizing Person (Permit Issuer)_Date': new Date().toISOString().slice(0, 10),
      'Permit Issue_Authorizing Person (Permit Issuer)_Time': new Date().toTimeString().slice(0, 5),
      'Permit Issue_Authorizing Person (Permit Issuer)_Signature': 'N. Akinyi',
      'Permit Return_Competent Person (Permit Receiver)_Name': 'Samuel Kiprotich',
      'Permit Return_Competent Person (Permit Receiver)_Date': new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString().slice(0, 10),
      'Permit Return_Competent Person (Permit Receiver)_Time': new Date(Date.now() + 6 * 60 * 60 * 1000).toTimeString().slice(0, 5),
      'Permit Return_Competent Person (Permit Receiver)_Signature': 'S. Kiprotich',
      'Permit Return_Authorizing Person (Permit Issuer)_Name': 'Nancy Akinyi',
      'Permit Return_Authorizing Person (Permit Issuer)_Date': new Date(Date.now() + 6 * 60 * 60 * 1000).toISOString().slice(0, 10),
      'Permit Return_Authorizing Person (Permit Issuer)_Time': new Date(Date.now() + 6 * 60 * 60 * 1000).toTimeString().slice(0, 5),
      'Permit Return_Authorizing Person (Permit Issuer)_Signature': 'N. Akinyi'
    }
  };

  const handleBack = () => {
    if (siteId) {
      navigate(`/sites/${siteId}/permits`);
    } else {
      navigate('/permits');
    }
  };

  return (
    <PermitFormDisplay
      permitType="work-at-height"
      permitData={mockPermitData}
      onBack={handleBack}
    />
  );
};

export default WorkAtHeightPermitDisplayPage;
