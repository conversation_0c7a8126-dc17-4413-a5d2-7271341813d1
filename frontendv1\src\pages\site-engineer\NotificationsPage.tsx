import React, { useState } from 'react';

import {
  <PERSON>,
  Check<PERSON><PERSON>cle,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  FileText,
  Sun,

  Check,
  X
} from 'lucide-react';
import { SiteInfo } from '../../types';
import { mockSite } from '../../mock/taskData';
import SiteEngineerLayout from '../../components/site-engineer/SiteEngineerLayout';

// Mock notifications data
const NOTIFICATIONS_DATA = [
  {
    id: 'notif-001',
    type: 'task_approved',
    title: 'Task Approved',
    message: 'Your electrical panel installation task has been approved and scheduled.',
    timestamp: '2024-08-05T14:30:00Z',
    read: false,
    priority: 'normal',
    relatedId: 'task-001',
    relatedType: 'task'
  },
  {
    id: 'notif-002',
    type: 'weather_alert',
    title: 'Weather Alert',
    message: 'Heavy rain expected tomorrow. Outdoor work may be affected.',
    timestamp: '2024-08-05T12:15:00Z',
    read: false,
    priority: 'high',
    relatedId: null,
    relatedType: 'weather'
  },
  {
    id: 'notif-003',
    type: 'permit_ready',
    title: 'Permit Ready',
    message: 'Work permit PRM-2024-001 is ready for review.',
    timestamp: '2024-08-05T10:45:00Z',
    read: true,
    priority: 'normal',
    relatedId: 'PRM-2024-001',
    relatedType: 'permit'
  },
  {
    id: 'notif-004',
    type: 'task_overdue',
    title: 'Task Overdue',
    message: 'HVAC maintenance task is now overdue. Please update status.',
    timestamp: '2024-08-05T08:00:00Z',
    read: false,
    priority: 'high',
    relatedId: 'task-004',
    relatedType: 'task'
  },
  {
    id: 'notif-005',
    type: 'system_update',
    title: 'System Update',
    message: 'New features have been added to the task management system.',
    timestamp: '2024-08-04T16:20:00Z',
    read: true,
    priority: 'low',
    relatedId: null,
    relatedType: 'system'
  }
];

const NotificationsPage: React.FC = () => {
  const [site] = useState<SiteInfo>(mockSite);
  const [notifications, setNotifications] = useState(NOTIFICATIONS_DATA);

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'task_approved':
      case 'task_completed':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'task_overdue':
      case 'weather_alert':
        return <AlertTriangle className="h-5 w-5 text-red-600" />;
      case 'permit_ready':
        return <FileText className="h-5 w-5 text-blue-600" />;
      case 'weather_update':
        return <Sun className="h-5 w-5 text-yellow-600" />;
      case 'system_update':
        return <Bell className="h-5 w-5 text-gray-600" />;
      default:
        return <Bell className="h-5 w-5 text-gray-600" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'border-l-red-500';
      case 'normal': return 'border-l-blue-500';
      case 'low': return 'border-l-gray-400';
      default: return 'border-l-gray-400';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
      return `${diffInMinutes}m ago`;
    } else if (diffInHours < 24) {
      return `${diffInHours}h ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays}d ago`;
    }
  };

  const markAsRead = (notificationId: string) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === notificationId 
          ? { ...notif, read: true }
          : notif
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notif => ({ ...notif, read: true }))
    );
  };

  const deleteNotification = (notificationId: string) => {
    setNotifications(prev => 
      prev.filter(notif => notif.id !== notificationId)
    );
  };

  const unreadCount = notifications.filter(n => !n.read).length;

  return (
    <SiteEngineerLayout site={site} title="Notifications" showBackButton={true}>
      <div className="px-4 sm:px-6 py-4 sm:py-6">
        
        {/* Notifications Header */}
        <div className="flex items-center justify-between mb-4 sm:mb-6">
          <div>
            <h2 className="text-lg sm:text-xl font-semibold text-gray-900">Notifications</h2>
            <p className="text-xs sm:text-sm text-gray-600 mt-1">
              {unreadCount > 0 ? `${unreadCount} unread` : 'All caught up!'}
            </p>
          </div>
          {unreadCount > 0 && (
            <button
              onClick={markAllAsRead}
              className="text-blue-600 hover:text-blue-700 text-xs sm:text-sm font-medium"
            >
              Mark all read
            </button>
          )}
        </div>

        {/* Notifications List */}
        <div className="space-y-3">
          {notifications.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <Bell className="h-16 w-16 mx-auto" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No Notifications</h3>
              <p className="text-gray-600">You're all caught up! New notifications will appear here.</p>
            </div>
          ) : (
            notifications.map((notification) => (
              <div
                key={notification.id}
                className={`bg-white rounded-lg border-l-4 ${getPriorityColor(notification.priority)} p-4 hover:shadow-md transition-shadow ${
                  !notification.read ? 'bg-blue-50' : ''
                }`}
              >
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 mt-0.5">
                    {getNotificationIcon(notification.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <h3 className={`text-sm sm:text-base font-semibold ${!notification.read ? 'text-gray-900' : 'text-gray-700'}`}>
                            {notification.title}
                          </h3>
                          {!notification.read && (
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 mb-2 leading-relaxed">
                          {notification.message}
                        </p>
                        <div className="flex items-center space-x-4 text-xs text-gray-500">
                          <div className="flex items-center space-x-1">
                            <Clock className="h-3 w-3" />
                            <span>{formatTimestamp(notification.timestamp)}</span>
                          </div>
                          {notification.priority === 'high' && (
                            <span className="px-2 py-1 bg-red-100 text-red-600 rounded-full font-medium">
                              High Priority
                            </span>
                          )}
                        </div>
                      </div>
                      
                      {/* Actions */}
                      <div className="flex items-center space-x-2 ml-4">
                        {!notification.read && (
                          <button
                            onClick={() => markAsRead(notification.id)}
                            className="p-1 text-gray-400 hover:text-green-600 transition-colors"
                            title="Mark as read"
                          >
                            <Check className="h-4 w-4" />
                          </button>
                        )}
                        <button
                          onClick={() => deleteNotification(notification.id)}
                          className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                          title="Delete notification"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Quick Actions */}
        {notifications.length > 0 && (
          <div className="mt-8 pt-6 border-t border-gray-200">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <span>{notifications.length} total notifications</span>
              <button
                onClick={() => setNotifications([])}
                className="text-red-600 hover:text-red-700 font-medium"
              >
                Clear all
              </button>
            </div>
          </div>
        )}
      </div>
    </SiteEngineerLayout>
  );
};

export default NotificationsPage;
