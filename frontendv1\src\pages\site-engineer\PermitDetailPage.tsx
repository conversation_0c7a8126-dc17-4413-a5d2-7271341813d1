import React, { useState } from 'react';
import { useParams } from 'react-router-dom';
import {
  FileText,
  Calendar,
  Clock,
  CheckCircle,
  AlertTriangle,
  User,
  MapPin,
  Shield,
  Download,
  Printer
} from 'lucide-react';
import { SiteInfo } from '../../types';
import { mockSite } from '../../mock/taskData';
import SiteEngineerLayout from '../../components/site-engineer/SiteEngineerLayout';

// Mock permit detail data (would normally be fetched based on permitId)
const getPermitDetail = (permitId: string) => {
  const permits: { [key: string]: any } = {
    'PRM-2024-001': {
      id: 'PRM-2024-001',
      title: 'Electrical Work Permit',
      taskName: 'Electrical Panel Installation',
      status: 'approved',
      issueDate: '2024-08-01',
      expiryDate: '2024-08-15',
      issuedBy: '<PERSON>',
      issuedByTitle: 'HSE Manager',
      approvedBy: '<PERSON>',
      approvedByTitle: 'Site Safety Officer',
      category: 'Electrical',
      riskLevel: 'high',
      location: 'Building A - Ground Floor',
      workDescription: 'Installation of main electrical panel for Building A ground floor including connecting main power lines and setting up distribution circuits.',
      validFrom: '08:00',
      validTo: '17:00',
      conditions: [
        'Work must be performed by certified electrician only',
        'Power shutdown required during installation period',
        'Personal protective equipment mandatory at all times',
        'Work area must be cordoned off with safety barriers',
        'Fire extinguisher must be readily available',
        'Emergency contact numbers must be posted',
        'Work must be supervised by qualified electrical supervisor'
      ],
      hazards: [
        'Electrical shock risk',
        'Arc flash potential',
        'Fire hazard',
        'Fall risk from elevated work'
      ],
      safetyMeasures: [
        'Lockout/Tagout procedures implemented',
        'Insulated tools and equipment used',
        'Proper grounding procedures followed',
        'Safety harness when working at height',
        'Regular voltage testing before work'
      ],
      emergencyContacts: [
        { role: 'Site Emergency', number: '911' },
        { role: 'Site Safety Officer', number: '+1-555-0123' },
        { role: 'Electrical Supervisor', number: '+1-555-0124' },
        { role: 'Site Manager', number: '+1-555-0125' }
      ],
      requiredPersonnel: [
        'Licensed Electrician (John Smith - License #EL-2024-001)',
        'Electrical Supervisor (Mike Johnson)',
        'Safety Observer (David Brown)'
      ],
      equipment: [
        'Insulated hand tools',
        'Digital multimeter',
        'Personal protective equipment',
        'Safety barriers and signage',
        'Fire extinguisher (Class C)'
      ]
    }
  };
  
  return permits[permitId] || null;
};

const PermitDetailPage: React.FC = () => {
  const { permitId } = useParams<{ siteId: string; permitId: string }>();
  const [site] = useState<SiteInfo>(mockSite);
  const permit = getPermitDetail(permitId!);

  if (!permit) {
    return (
      <SiteEngineerLayout site={site} title="Permit Not Found" showBackButton={true}>
        <div className="px-6 py-12 text-center">
          <FileText className="h-16 w-16 mx-auto text-gray-400 mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Permit Not Found</h2>
          <p className="text-gray-600">The requested permit could not be found.</p>
        </div>
      </SiteEngineerLayout>
    );
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-6 w-6 text-green-600" />;
      default:
        return <AlertTriangle className="h-6 w-6 text-yellow-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'text-green-600 bg-green-50 border-green-200';
      default: return 'text-yellow-600 bg-yellow-50 border-yellow-200';
    }
  };

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return 'text-green-600 bg-green-50 border-green-200';
      case 'medium': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'high': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'critical': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <SiteEngineerLayout site={site} title={`Permit ${permit.id}`} showBackButton={true}>
      <div className="px-6 py-6">
        {/* Permit Header */}
        <div className="bg-white rounded-xl border border-gray-200 p-6 mb-6">
          <div className="flex items-start justify-between mb-4">
            <div className="flex items-start space-x-4">
              {getStatusIcon(permit.status)}
              <div>
                <h1 className="text-2xl font-bold text-gray-900 mb-2">{permit.title}</h1>
                <p className="text-lg text-gray-700 mb-2">{permit.taskName}</p>
                <div className="flex items-center space-x-4 text-sm text-gray-600">
                  <span className="font-medium">Permit ID: {permit.id}</span>
                  <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getRiskLevelColor(permit.riskLevel)}`}>
                    {permit.riskLevel.toUpperCase()} RISK
                  </span>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <span className={`px-4 py-2 rounded-lg text-sm font-medium border ${getStatusColor(permit.status)}`}>
                {permit.status.toUpperCase()}
              </span>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-between pt-4 border-t border-gray-200">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
              <span>Live permit data from database</span>
            </div>
            <div className="flex items-center space-x-4">
              <button
                className="flex items-center space-x-2 text-gray-600 hover:text-gray-700 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                title="PDF will be generated at end of day"
              >
                <Printer className="h-4 w-4" />
                <span>Print View</span>
              </button>
              <button
                className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors opacity-50 cursor-not-allowed"
                disabled
                title="PDF will be available at end of day"
              >
                <Download className="h-4 w-4" />
                <span>Download PDF</span>
              </button>
            </div>
          </div>
        </div>

        {/* Permit Details */}
        <div className="space-y-6">
          {/* Basic Information */}
          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Permit Information</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-500 uppercase tracking-wide">Issue Date</label>
                  <div className="flex items-center space-x-2 mt-1">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-900">{new Date(permit.issueDate).toLocaleDateString()}</span>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500 uppercase tracking-wide">Expiry Date</label>
                  <div className="flex items-center space-x-2 mt-1">
                    <Calendar className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-900">{new Date(permit.expiryDate).toLocaleDateString()}</span>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500 uppercase tracking-wide">Valid Hours</label>
                  <div className="flex items-center space-x-2 mt-1">
                    <Clock className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-900">{permit.validFrom} - {permit.validTo}</span>
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-500 uppercase tracking-wide">Location</label>
                  <div className="flex items-center space-x-2 mt-1">
                    <MapPin className="h-4 w-4 text-gray-400" />
                    <span className="text-gray-900">{permit.location}</span>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500 uppercase tracking-wide">Issued By</label>
                  <div className="flex items-center space-x-2 mt-1">
                    <User className="h-4 w-4 text-gray-400" />
                    <div>
                      <div className="text-gray-900">{permit.issuedBy}</div>
                      <div className="text-sm text-gray-600">{permit.issuedByTitle}</div>
                    </div>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-500 uppercase tracking-wide">Approved By</label>
                  <div className="flex items-center space-x-2 mt-1">
                    <Shield className="h-4 w-4 text-gray-400" />
                    <div>
                      <div className="text-gray-900">{permit.approvedBy}</div>
                      <div className="text-sm text-gray-600">{permit.approvedByTitle}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Work Description */}
          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Work Description</h2>
            <p className="text-gray-700 leading-relaxed">{permit.workDescription}</p>
          </div>

          {/* Conditions */}
          <div className="bg-white rounded-xl border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Permit Conditions</h2>
            <div className="space-y-3">
              {permit.conditions.map((condition: string, index: number) => (
                <div key={index} className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <span className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white text-sm font-medium rounded-full flex items-center justify-center">
                    {index + 1}
                  </span>
                  <span className="text-gray-900">{condition}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Hazards and Safety Measures */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Identified Hazards */}
            <div className="bg-white rounded-xl border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <AlertTriangle className="h-5 w-5 text-orange-600 mr-2" />
                Identified Hazards
              </h2>
              <div className="space-y-2">
                {permit.hazards.map((hazard: string, index: number) => (
                  <div key={index} className="flex items-center space-x-2 p-2 bg-orange-50 rounded-lg">
                    <span className="text-orange-600">⚠️</span>
                    <span className="text-gray-900">{hazard}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Safety Measures */}
            <div className="bg-white rounded-xl border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Shield className="h-5 w-5 text-green-600 mr-2" />
                Safety Measures
              </h2>
              <div className="space-y-2">
                {permit.safetyMeasures.map((measure: string, index: number) => (
                  <div key={index} className="flex items-center space-x-2 p-2 bg-green-50 rounded-lg">
                    <span className="text-green-600">✓</span>
                    <span className="text-gray-900">{measure}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Required Personnel and Equipment */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Required Personnel */}
            <div className="bg-white rounded-xl border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Required Personnel</h2>
              <div className="space-y-2">
                {permit.requiredPersonnel.map((person: string, index: number) => (
                  <div key={index} className="flex items-center space-x-2 p-2 bg-gray-50 rounded-lg">
                    <User className="h-4 w-4 text-gray-600" />
                    <span className="text-gray-900">{person}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Required Equipment */}
            <div className="bg-white rounded-xl border border-gray-200 p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Required Equipment</h2>
              <div className="space-y-2">
                {permit.equipment.map((item: string, index: number) => (
                  <div key={index} className="flex items-center space-x-2 p-2 bg-gray-50 rounded-lg">
                    <span className="text-gray-600">🔧</span>
                    <span className="text-gray-900">{item}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Emergency Contacts */}
          <div className="bg-red-50 border border-red-200 rounded-xl p-6">
            <h2 className="text-lg font-semibold text-red-800 mb-4 flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2" />
              Emergency Contacts
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {permit.emergencyContacts.map((contact: any, index: number) => (
                <div key={index} className="bg-white rounded-lg p-3 border border-red-200">
                  <div className="font-medium text-gray-900">{contact.role}</div>
                  <div className="text-lg font-mono text-red-600">{contact.number}</div>
                </div>
              ))}
            </div>
          </div>

          {/* PDF Archive Information */}
          <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
            <h2 className="text-lg font-semibold text-blue-800 mb-3 flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Document Archival
            </h2>
            <div className="space-y-2 text-sm text-blue-700">
              <p>
                <strong>Current Status:</strong> This permit is stored as live database data and can be updated in real-time.
              </p>
              <p>
                <strong>End of Day Process:</strong> At the end of each workday, all active permits are automatically archived as PDF documents for permanent record keeping.
              </p>
              <p>
                <strong>PDF Availability:</strong> Downloadable PDF versions will be available after the daily archival process completes.
              </p>
            </div>
          </div>
        </div>
      </div>
    </SiteEngineerLayout>
  );
};

export default PermitDetailPage;
