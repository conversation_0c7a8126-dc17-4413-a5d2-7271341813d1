import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  FileText,
  Calendar,
  Clock,
  CheckCircle,
  AlertTriangle,
  XCircle,
  Eye,
  Download
} from 'lucide-react';
import { SiteInfo } from '../../types';
import { mockSite } from '../../mock/taskData';
import SiteEngineerLayout from '../../components/site-engineer/SiteEngineerLayout';

// Mock permits data
const PERMITS_DATA = [
  {
    id: 'PRM-2024-001',
    title: 'Electrical Work Permit',
    taskName: 'Electrical Panel Installation',
    status: 'approved',
    issueDate: '2024-08-01',
    expiryDate: '2024-08-15',
    issuedBy: 'HSE Department',
    category: 'Electrical',
    riskLevel: 'high',
    description: 'Permit for electrical panel installation in Building A ground floor including main power connections.',
    conditions: [
      'Work must be performed by certified electrician',
      'Power shutdown required during installation',
      'Safety equipment mandatory at all times',
      'Work area must be cordoned off'
    ],
    validFrom: '08:00',
    validTo: '17:00'
  },
  {
    id: 'PRM-2024-002',
    title: 'Excavation Permit',
    taskName: 'Foundation Excavation',
    status: 'pending',
    issueDate: '2024-08-02',
    expiryDate: '2024-08-20',
    issuedBy: 'Site Safety Officer',
    category: 'Excavation',
    riskLevel: 'medium',
    description: 'Permit for foundation excavation work in north section of site compound.',
    conditions: [
      'Utility location verification completed',
      'Heavy machinery operator certification required',
      'Soil stability assessment completed',
      'Emergency procedures briefing mandatory'
    ],
    validFrom: '07:00',
    validTo: '16:00'
  },
  {
    id: 'PRM-2024-003',
    title: 'Roof Work Permit',
    taskName: 'HVAC System Maintenance',
    status: 'approved',
    issueDate: '2024-07-28',
    expiryDate: '2024-08-10',
    issuedBy: 'HSE Department',
    category: 'Maintenance',
    riskLevel: 'medium',
    description: 'Permit for HVAC maintenance work on main building roof.',
    conditions: [
      'Fall protection equipment mandatory',
      'Weather conditions must be suitable',
      'Spotter required at all times',
      'Emergency rescue plan in place'
    ],
    validFrom: '09:00',
    validTo: '15:00'
  },
  {
    id: 'PRM-2024-004',
    title: 'Hot Work Permit',
    taskName: 'Welding Repairs',
    status: 'expired',
    issueDate: '2024-07-20',
    expiryDate: '2024-08-03',
    issuedBy: 'Fire Safety Officer',
    category: 'Welding',
    riskLevel: 'high',
    description: 'Permit for welding repairs on structural steel components.',
    conditions: [
      'Fire watch required during and after work',
      'Hot work area cleared of combustibles',
      'Fire extinguisher readily available',
      'Certified welder only'
    ],
    validFrom: '08:00',
    validTo: '16:00'
  }
];

const PermitsPage: React.FC = () => {
  const { siteId } = useParams<{ siteId: string }>();
  const navigate = useNavigate();
  const [site] = useState<SiteInfo>(mockSite);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-600" />;
      case 'expired':
        return <XCircle className="h-5 w-5 text-red-600" />;
      default:
        return <AlertTriangle className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'text-green-600 bg-green-50 border-green-200';
      case 'pending': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'expired': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return 'text-green-600 bg-green-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'high': return 'text-orange-600 bg-orange-50';
      case 'critical': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const handleViewPermit = (permitId: string) => {
    navigate(`/sites/${siteId}/engineer/permits/${permitId}`);
  };

  const isExpiringSoon = (expiryDate: string) => {
    const expiry = new Date(expiryDate);
    const today = new Date();
    const daysUntilExpiry = Math.ceil((expiry.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
    return daysUntilExpiry <= 3 && daysUntilExpiry > 0;
  };

  const isExpired = (expiryDate: string) => {
    const expiry = new Date(expiryDate);
    const today = new Date();
    return expiry < today;
  };

  return (
    <SiteEngineerLayout site={site} title="Permits" showBackButton={true}>
      <div className="px-6 py-6">
        {/* Permits Header */}
        <div className="mb-6">
          <div className="flex items-center justify-between mb-3">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Work Permits</h2>
              <p className="text-sm text-gray-600 mt-1">{PERMITS_DATA.length} permits total</p>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">
                {PERMITS_DATA.filter(p => p.status === 'approved').length} approved
              </span>
              <span className="text-gray-300">•</span>
              <span className="text-sm text-gray-600">
                {PERMITS_DATA.filter(p => p.status === 'pending').length} pending
              </span>
            </div>
          </div>

          {/* Database Status Info */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <div className="flex items-center space-x-2 text-sm text-blue-700">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <span>
                <strong>Live Database:</strong> Permits are stored as live data and updated in real-time.
                PDF archives will be generated at end of day.
              </span>
            </div>
          </div>
        </div>

        {/* Permits List */}
        <div className="space-y-4">
          {PERMITS_DATA.map((permit) => (
            <div key={permit.id} className="bg-white rounded-xl border border-gray-200 p-5 hover:shadow-md transition-shadow">
              {/* Permit Header */}
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-start space-x-3 flex-1 min-w-0">
                  {getStatusIcon(permit.status)}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <h3 className="font-semibold text-gray-900">{permit.title}</h3>
                      <span className="text-sm text-gray-500">({permit.id})</span>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{permit.taskName}</p>
                    <div className="flex items-center space-x-4 text-sm text-gray-600">
                      <span>{permit.category}</span>
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-3 w-3" />
                        <span>Expires: {new Date(permit.expiryDate).toLocaleDateString()}</span>
                      </div>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskLevelColor(permit.riskLevel)}`}>
                        {permit.riskLevel} risk
                      </span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {isExpiringSoon(permit.expiryDate) && (
                    <span className="px-2 py-1 bg-orange-100 text-orange-600 text-xs rounded-full font-medium">
                      Expiring Soon
                    </span>
                  )}
                  {isExpired(permit.expiryDate) && (
                    <span className="px-2 py-1 bg-red-100 text-red-600 text-xs rounded-full font-medium">
                      EXPIRED
                    </span>
                  )}
                  <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(permit.status)}`}>
                    {permit.status}
                  </span>
                </div>
              </div>

              {/* Permit Details */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                  <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Issued By</span>
                  <div className="text-sm text-gray-900 mt-1">{permit.issuedBy}</div>
                </div>
                <div>
                  <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Valid Hours</span>
                  <div className="text-sm text-gray-900 mt-1">{permit.validFrom} - {permit.validTo}</div>
                </div>
                <div>
                  <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Issue Date</span>
                  <div className="text-sm text-gray-900 mt-1">{new Date(permit.issueDate).toLocaleDateString()}</div>
                </div>
              </div>

              {/* Description */}
              <div className="mb-4">
                <p className="text-sm text-gray-700 bg-gray-50 rounded-lg p-3">
                  {permit.description}
                </p>
              </div>

              {/* Key Conditions Preview */}
              <div className="mb-4">
                <h4 className="text-sm font-medium text-gray-900 mb-2">Key Conditions</h4>
                <div className="space-y-1">
                  {permit.conditions.slice(0, 2).map((condition, index) => (
                    <div key={index} className="text-sm text-gray-700 flex items-start space-x-2">
                      <span className="text-blue-600 mt-1">•</span>
                      <span>{condition}</span>
                    </div>
                  ))}
                  {permit.conditions.length > 2 && (
                    <div className="text-sm text-gray-500 ml-3">
                      +{permit.conditions.length - 2} more conditions
                    </div>
                  )}
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-200">
                <div className="flex items-center space-x-3 text-sm text-gray-500">
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Live data</span>
                  </div>
                  <span>•</span>
                  <span>Last updated: {new Date(permit.issueDate).toLocaleDateString()}</span>
                </div>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => handleViewPermit(permit.id)}
                    className="flex items-center space-x-2 text-blue-600 hover:text-blue-700 text-sm font-medium"
                  >
                    <Eye className="h-4 w-4" />
                    <span>View Full Permit</span>
                  </button>
                  <button
                    className="flex items-center space-x-2 text-gray-400 text-sm font-medium cursor-not-allowed"
                    disabled
                    title="PDF will be available at end of day"
                  >
                    <Download className="h-4 w-4" />
                    <span>Download</span>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State (if no permits) */}
        {PERMITS_DATA.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-4">
              <FileText className="h-16 w-16 mx-auto" />
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No Permits Found</h3>
            <p className="text-gray-600">Work permits will appear here once they are issued.</p>
          </div>
        )}
      </div>
    </SiteEngineerLayout>
  );
};

export default PermitsPage;
