# Site Engineer Pages

This directory contains all pages specifically designed for site engineers. These pages are optimized for mobile-first usage and provide a focused, distraction-free experience for site engineers working on construction sites.

## Pages

### SiteEngineerPage.tsx
The main site engineer dashboard that provides:
- **Task Creation**: Streamlined workflow for creating multiple tasks
- **Task Review**: Comprehensive review of submitted tasks before submission
- **Weather Information**: Real-time weather data and work impact assessments
- **Mobile-First Design**: Optimized for touch interaction and mobile usage
- **No Sidebar**: Clean, focused interface without HSE-related navigation

## Features

### 🎯 **Core Functionality**
- **Bulk Task Creation**: Create multiple related tasks in a single session
- **Category-Based Templates**: Predefined task categories with specific templates
- **Weather-Aware Planning**: Real-time weather conditions and alerts
- **Mobile Optimization**: Touch-friendly interface designed for on-site usage

### 🎨 **Design System**
- **Floating Card Layout**: Modern, clean design with proper spacing
- **Truncated Site Names**: Prevents header layout issues with long site names
- **Consistent Styling**: Unified design language across all components
- **Responsive Design**: Works seamlessly across all device sizes

### 📱 **Mobile-First Features**
- **Large Touch Targets**: Easy interaction on mobile devices
- **Simplified Navigation**: Tab-based navigation without complex menus
- **Notification System**: Bell icon with badge for important updates
- **Account Access**: Quick access to user account settings

## Usage

### Accessing the Site Engineer Page
The site engineer page is accessible via the route:
```
/sites/:siteId/tasks/create
```

### Navigation Structure
```
Site Engineer Page
├── Create Tasks Tab
│   ├── Category Selection
│   ├── Template Selection
│   └── Task Details Form
├── Review Tasks Tab
│   ├── Task List
│   ├── Task Summary
│   └── Submission Overview
└── Weather Tab
    ├── Current Conditions
    ├── Weather Alerts
    └── 2-Day Forecast
```

## Integration

### Route Configuration
The page is configured as a standalone route in `App.tsx`:
```tsx
<Route path="/sites/:siteId/tasks/create" element={
  <ProtectedRoute>
    <SiteEngineerPage />
  </ProtectedRoute>
} />
```

### Component Structure
The page uses modular components from `src/components/site-engineer/`:
- `TaskCreationTab`: Handles the task creation workflow
- `TaskReviewTab`: Manages task review and submission
- `WeatherTab`: Displays weather information and alerts

## Future Enhancements

### Planned Features
- **Task Templates Management**: Allow customization of task templates
- **Offline Support**: Enable task creation without internet connection
- **Photo Attachments**: Add ability to attach photos to task descriptions
- **Voice Notes**: Voice-to-text functionality for task descriptions
- **GPS Integration**: Automatic location detection for tasks

### Scalability
The modular structure allows for easy addition of new features:
- New tabs can be added to the main page
- Additional components can be created in the `site-engineer` folder
- New pages can be added to this directory for expanded functionality

## Development Notes

### File Organization
```
src/
├── pages/site-engineer/
│   ├── SiteEngineerPage.tsx    # Main page component
│   ├── index.ts                # Export index
│   └── README.md               # This documentation
└── components/site-engineer/
    ├── TaskCreationTab.tsx     # Task creation workflow
    ├── TaskReviewTab.tsx       # Task review interface
    ├── WeatherTab.tsx          # Weather information display
    └── index.ts                # Export index
```

### Best Practices
- **Component Separation**: Each major feature is separated into its own component
- **Type Safety**: All components use TypeScript interfaces for type safety
- **Consistent Styling**: All components follow the same design patterns
- **Mobile-First**: All components are designed with mobile usage as the primary concern

## Testing

### Manual Testing Checklist
- [ ] Page loads correctly without sidebar
- [ ] Header displays truncated site name properly
- [ ] Notification bell and account icons are functional
- [ ] Tab navigation works smoothly
- [ ] Task creation workflow completes successfully
- [ ] Task review displays all information correctly
- [ ] Weather information loads and displays properly
- [ ] Submit button appears when tasks are added
- [ ] Mobile responsiveness works across different screen sizes

### Automated Testing
Future automated tests should cover:
- Component rendering
- User interaction flows
- Data validation
- API integration
- Mobile responsiveness
