import React, { useState } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { toast } from "react-toastify";
import { useMutation, useQuery } from "@apollo/client";
import {
  ArrowLeft,
  Plus,
  Zap,
  Check,
  X,
  Clock,
  MapPin,
  Calendar,
  Building,
  Shield,
  Thermometer,
  Wrench,
  CheckCircle,
  Search,
} from "lucide-react";
import { SiteInfo } from "../../types";
import { mockSite } from "../../mock/taskData";
import SiteEngineerLayout from "../../components/site-engineer/SiteEngineerLayout";
import { CREATE_JOBS } from "../../graphql/mutations";
import { GET_ALL_WORKERS, GET_ALL_TRADES } from "../../graphql/queries";

// Task interface
interface CreateJobInput {
  title: string;
  tradeIds: number[];
  chiefEngineerId: number;
  location: string;
  timeForCompletion?: string;
  startDate?: string;
  description?: string;
}
interface QuickTask extends CreateJobInput {
  id: string;
}
// Task category interface
interface TaskCategory {
  id: string;
  name: string;
  icon: string;
  color: string;
  tasks: TaskTemplate[];
}

// Individual task template
interface TaskTemplate {
  id: string;
  name: string;
  category: string;
}

// Task categories with their tasks
const TASK_CATEGORIES: TaskCategory[] = [
  {
    id: "electrical",
    name: "Electrical",
    icon: "Zap",
    color: "text-yellow-600",
    tasks: [
      {
        id: "electrical-inspection",
        name: "Electrical System Inspection",
        category: "electrical",
      },
      {
        id: "electrical-testing",
        name: "Electrical Testing & Commissioning",
        category: "electrical",
      },
      {
        id: "electrical-installation",
        name: "Electrical Installation Review",
        category: "electrical",
      },
      {
        id: "electrical-maintenance",
        name: "Electrical Maintenance Check",
        category: "electrical",
      },
      {
        id: "electrical-safety",
        name: "Electrical Safety Audit",
        category: "electrical",
      },
      {
        id: "electrical-upgrade",
        name: "Electrical System Upgrade",
        category: "electrical",
      },
    ],
  },
  {
    id: "structural",
    name: "Structural",
    icon: "Building",
    color: "text-orange-600",
    tasks: [
      {
        id: "concrete-inspection",
        name: "Concrete Pour Inspection",
        category: "structural",
      },
      {
        id: "structural-assessment",
        name: "Structural Integrity Assessment",
        category: "structural",
      },
      {
        id: "foundation-check",
        name: "Foundation Inspection",
        category: "structural",
      },
      {
        id: "steel-inspection",
        name: "Steel Framework Inspection",
        category: "structural",
      },
      { id: "load-testing", name: "Load Bearing Test", category: "structural" },
      {
        id: "structural-repair",
        name: "Structural Repair Assessment",
        category: "structural",
      },
    ],
  },
  {
    id: "safety",
    name: "Safety",
    icon: "Shield",
    color: "text-green-600",
    tasks: [
      {
        id: "safety-inspection",
        name: "General Safety Inspection",
        category: "safety",
      },
      {
        id: "safety-equipment",
        name: "Safety Equipment Check",
        category: "safety",
      },
      {
        id: "safety-training",
        name: "Safety Training Session",
        category: "safety",
      },
      {
        id: "safety-audit",
        name: "Safety Compliance Audit",
        category: "safety",
      },
      {
        id: "emergency-drill",
        name: "Emergency Response Drill",
        category: "safety",
      },
      {
        id: "ppe-inspection",
        name: "PPE Inspection & Maintenance",
        category: "safety",
      },
    ],
  },
  {
    id: "hvac",
    name: "HVAC",
    icon: "Thermometer",
    color: "text-blue-600",
    tasks: [
      {
        id: "hvac-inspection",
        name: "HVAC System Inspection",
        category: "hvac",
      },
      {
        id: "hvac-maintenance",
        name: "HVAC Maintenance Check",
        category: "hvac",
      },
      {
        id: "hvac-installation",
        name: "HVAC Installation Review",
        category: "hvac",
      },
      {
        id: "hvac-testing",
        name: "HVAC Performance Testing",
        category: "hvac",
      },
      { id: "hvac-repair", name: "HVAC Repair Assessment", category: "hvac" },
      { id: "hvac-upgrade", name: "HVAC System Upgrade", category: "hvac" },
    ],
  },
  {
    id: "plumbing",
    name: "Plumbing",
    icon: "Wrench",
    color: "text-cyan-600",
    tasks: [
      {
        id: "plumbing-inspection",
        name: "Plumbing System Inspection",
        category: "plumbing",
      },
      {
        id: "plumbing-installation",
        name: "Plumbing Installation Review",
        category: "plumbing",
      },
      {
        id: "plumbing-maintenance",
        name: "Plumbing Maintenance Check",
        category: "plumbing",
      },
      {
        id: "plumbing-testing",
        name: "Plumbing Pressure Testing",
        category: "plumbing",
      },
      {
        id: "plumbing-repair",
        name: "Plumbing Repair Assessment",
        category: "plumbing",
      },
      {
        id: "drainage-inspection",
        name: "Drainage System Inspection",
        category: "plumbing",
      },
    ],
  },
  {
    id: "quality",
    name: "Quality Control",
    icon: "CheckCircle",
    color: "text-purple-600",
    tasks: [
      {
        id: "quality-inspection",
        name: "General Quality Inspection",
        category: "quality",
      },
      {
        id: "quality-audit",
        name: "Quality Assurance Audit",
        category: "quality",
      },
      {
        id: "material-inspection",
        name: "Material Quality Check",
        category: "quality",
      },
      {
        id: "workmanship-review",
        name: "Workmanship Review",
        category: "quality",
      },
      {
        id: "compliance-check",
        name: "Code Compliance Check",
        category: "quality",
      },
      {
        id: "final-inspection",
        name: "Final Quality Inspection",
        category: "quality",
      },
    ],
  },
];

// Common site locations
const SITE_LOCATIONS = [
  "Building A - Floor 1",
  "Building A - Floor 2",
  "Building A - Floor 3",
  "Building B - Basement",
  "Building B - Floor 1",
  "Building B - Floor 2",
  "Parking Garage",
  "Main Entrance",
  "Construction Zone",
  "Equipment Storage",
  "Mechanical Room",
  "Rooftop",
  "Foundation Area",
  "Exterior Areas",
  "Utility Rooms",
];

const SiteEngineerPage: React.FC = () => {
  const { siteId } = useParams<{ siteId: string }>();
  const navigate = useNavigate();
  const [site] = useState<SiteInfo>(mockSite);

  // GraphQL queries and mutations
  const { data: workersData } = useQuery(GET_ALL_WORKERS, {
    onError: (error) => {
      console.error("Error fetching workers:", error);
    },
  });

  const { data: tradesData } = useQuery(GET_ALL_TRADES, {
    onError: (error) => {
      console.error("Error fetching trades:", error);
    },
  });

  const [createJobs] = useMutation(CREATE_JOBS, {
    onCompleted: (data: { createJobs: CreateJobInput[] }) => {
      toast.success(
        `${data.createJobs.length} task${data.createJobs.length > 1 ? "s" : ""
        } submitted successfully! HSE team will review and add safety documentation.`,
        {
          position: "top-right",
          autoClose: 6000,
        }
      );
      // toast.success(`Task "${data.createJob.title}" created successfully!`);
      setTasks([]);
      navigate(`/sites/${siteId}/engineer`);
    },
    onError: (error) => {
      console.error("Error creating job:", error);
      toast.error("Failed to create task. Please try again.", {
        position: "top-right",
        autoClose: 5000,
      });
      setIsSubmitting(false);
    },
  });

  // View state management
  const [currentView, setCurrentView] = useState<"main" | "category" | "form">(
    "main"
  );
  const [selectedCategory, setSelectedCategory] = useState<TaskCategory | null>(
    null
  );
  const [selectedTask, setSelectedTask] = useState<TaskTemplate | null>(null);

  // Task management state
  const [tasks, setTasks] = useState<QuickTask[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Category view state
  const [searchQuery, setSearchQuery] = useState("");
  const [filterCategory, setFilterCategory] = useState<string>("");

  // Task form state - updated to match CreateJobInput
  const [taskForm, setTaskForm] = useState<CreateJobInput>({
    title: "",
    description: "",
    location: "",
    startDate: new Date(Date.now() + 86400000).toISOString().split("T")[0], // Tomorrow as default
    timeForCompletion: "P1D", // 1 day in ISO-8601 duration format
    chiefEngineerId: 0,
    tradeIds: [],
  });

  // Navigate to category view
  const openCategory = (category: TaskCategory) => {
    setSelectedCategory(category);
    setFilterCategory(category.id);
    setCurrentView("category");
    setSearchQuery("");
  };

  // Navigate to task form
  const openTaskForm = (task: TaskTemplate) => {
    setSelectedTask(task);
    setTaskForm(prev => ({...prev, title: task.name}));
    setCurrentView("form");
  };

  // Add task to queue
  const addTaskToQueue = () => {
    if (
      !taskForm.title.trim() ||
      !taskForm.description?.trim() ||
      !taskForm.location.trim()
    ) {
      toast.error("Please fill in all required fields.");
      return;
    }

    if (taskForm.chiefEngineerId === 0) {
      toast.error("Please select a chief engineer.");
      return;
    }
    // console.log(taskForm)
    setTasks((prev) => [...prev, {
      ...taskForm,
      startDate: new Date(taskForm.startDate ? taskForm.startDate + "T00:00:00" : new Date()).toISOString(),
      id: `task-${Date.now()}-${(Math.random() * 1000).toString(36)}`,
    }]);
    // Reset form and navigate back to main view
    setTaskForm({
      title: "",
      description: "",
      location: "",
      startDate: new Date(Date.now() + 86400000).toISOString().split("T")[0],
      timeForCompletion: "P1D", // 1 day in ISO-8601 duration format
      chiefEngineerId: workersData?.allWorkers?.[0]?.id || 0,
      tradeIds: [],
    });

    setCurrentView("main");

    toast.success("Task added to queue!", {
      position: "top-center",
      autoClose: 2000,
    });

    // Scroll to top to show the queue
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  // Remove task from queue
  const removeTask = (taskId: string) => {
    setTasks((prev) => prev.filter((task) => task.id !== taskId));
  };

  // Scroll to categories section
  const scrollToCategories = () => {
    const categoriesElement = document.getElementById("categories-section");
    if (categoriesElement) {
      categoriesElement.scrollIntoView({ behavior: "smooth" });
    }
  };

  // Filter tasks based on search and category
  const getFilteredTasks = () => {
    let allTasks: TaskTemplate[] = [];

    if (filterCategory) {
      const category = TASK_CATEGORIES.find((cat) => cat.id === filterCategory);
      allTasks = category ? category.tasks : [];
    } else {
      allTasks = TASK_CATEGORIES.flatMap((cat) => cat.tasks);
    }

    if (searchQuery.trim()) {
      allTasks = allTasks.filter((task) =>
        task.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return allTasks;
  };

  // Get icon component for category
  const getCategoryIcon = (iconName: string, className: string = "h-6 w-6") => {
    const iconProps = { className };
    switch (iconName) {
      case "Zap":
        return <Zap {...iconProps} />;
      case "Building":
        return <Building {...iconProps} />;
      case "Shield":
        return <Shield {...iconProps} />;
      case "Thermometer":
        return <Thermometer {...iconProps} />;
      case "Wrench":
        return <Wrench {...iconProps} />;
      case "CheckCircle":
        return <CheckCircle {...iconProps} />;
      default:
        return <CheckCircle {...iconProps} />;
    }
  };

  // Submit all tasks using GraphQL
  const handleSubmitTasks = async () => {
    console.log("Submitting tasks:", tasks);
    if (tasks.length === 0) {
      toast.error("Please add at least one task before submitting.");
      return;
    }

    setIsSubmitting(true);


    // Submit each task individually using createJob mutation
    const inputs: CreateJobInput[] = tasks.map((task) => ({
      title: task.title,
      description: task.description,
      location: task.location,
      startDate: task.startDate,
      timeForCompletion: task.timeForCompletion,
      chiefEngineerId: task.chiefEngineerId,
      tradeIds: task.tradeIds,
    }));

    createJobs({ variables: { inputs } }); // Submit tasks to GraphQL mutation

  };

  return (
    <SiteEngineerLayout site={site} title="Create Tasks" showBackButton={true}>
      <div className="px-4 sm:px-6 py-4 sm:py-6 space-y-6">
        {/* Main View - Task Queue + Categories */}
        {currentView === "main" && (
          <>
            {/* Task Queue Section */}
            <div className="bg-white rounded-xl border border-gray-200 p-4">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-lg font-semibold text-gray-900">
                  Task Queue
                </h2>
                <button
                  onClick={scrollToCategories}
                  className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                >
                  <Plus className="h-5 w-5" />
                </button>
              </div>

              {/* Queue Content */}
              {tasks.length === 0 ? (
                <div className="text-center py-8">
                  <div className="text-gray-400 mb-3">
                    <Clock className="h-12 w-12 mx-auto" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 mb-2">
                    Queue is Empty
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Add tasks from the categories below to get started.
                  </p>
                  <button
                    onClick={scrollToCategories}
                    className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Browse Tasks
                  </button>
                </div>
              ) : (
                <>
                  <div className="space-y-3 mb-4">
                    {tasks.map((task) => (
                      <div
                        key={task.id}
                        className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg"
                      >
                        <div className="flex-1 min-w-0">
                          <h4 className="font-medium text-gray-900 text-sm">
                            {task.title}
                          </h4>
                          <p className="text-xs text-gray-600 mt-1 line-clamp-2">
                            {task.description}
                          </p>
                          <div className="flex items-center space-x-3 mt-2 text-xs text-gray-500">
                            <div className="flex items-center space-x-1">
                              <MapPin className="h-3 w-3" />
                              <span>{task.location}</span>
                            </div>
                            <div className="flex items-center space-x-1">
                              <Calendar className="h-3 w-3" />
                              <span>
                                {task.startDate ? new Date(task.startDate).toLocaleDateString() : new Date(new Date().getTime() + 86400000).toLocaleDateString()}
                              </span>
                            </div>
                          </div>
                        </div>
                        <button
                          onClick={() => removeTask(task.id)}
                          className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                          title="Remove task"
                        >
                          <X className="h-4 w-4" />
                        </button>
                      </div>
                    ))}
                  </div>

                  {/* Submit Button */}
                  <button
                    onClick={handleSubmitTasks}
                    disabled={isSubmitting}
                    className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all flex items-center justify-center space-x-2 font-medium"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        <span>Submitting...</span>
                      </>
                    ) : (
                      <>
                        <Check className="h-4 w-4" />
                        <span>
                          Submit {tasks.length} Task
                          {tasks.length > 1 ? "s" : ""} for Review
                        </span>
                      </>
                    )}
                  </button>
                  <p className="text-xs text-gray-500 text-center mt-2">
                    Tasks will be sent to HSE team for safety review and
                    approval
                  </p>
                </>
              )}
            </div>

            {/* Categories Section */}
            <div
              id="categories-section"
              className="bg-white rounded-xl border border-gray-200 p-4"
            >
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                Task Categories
              </h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                {TASK_CATEGORIES.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => openCategory(category)}
                    className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow text-left"
                  >
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        {getCategoryIcon(
                          category.icon,
                          `h-6 w-6 ${category.color}`
                        )}
                      </div>
                      <div className="ml-3">
                        <h3 className="text-sm font-medium text-gray-900">
                          {category.name}
                        </h3>
                        <p className="text-xs text-gray-500">
                          {category.tasks.length} tasks available
                        </p>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          </>
        )}

        {/* Category View - Task Selection */}
        {currentView === "category" && selectedCategory && (
          <div className="bg-white rounded-xl border border-gray-200 p-4">
            {/* Category Header */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setCurrentView("main")}
                  className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
                >
                  <ArrowLeft className="h-5 w-5" />
                </button>
                <div className="flex items-center space-x-2">
                  <span className="text-xl">{selectedCategory.icon}</span>
                  <h2 className="text-lg font-semibold text-gray-900">
                    {selectedCategory.name}
                  </h2>
                </div>
              </div>
            </div>

            {/* Search and Filter */}
            <div className="mb-4 space-y-3">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search tasks..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div className="flex items-center space-x-2 overflow-x-auto pb-2">
                <button
                  onClick={() => setFilterCategory("")}
                  className={`px-3 py-1 rounded-full text-sm font-medium whitespace-nowrap transition-colors ${filterCategory === ""
                    ? "bg-blue-100 text-blue-700 border border-blue-300"
                    : "bg-gray-100 text-gray-700 border border-gray-300"
                    }`}
                >
                  All Categories
                </button>
                {TASK_CATEGORIES.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setFilterCategory(category.id)}
                    className={`flex items-center space-x-1 px-3 py-1 rounded-full text-sm font-medium whitespace-nowrap transition-colors ${filterCategory === category.id
                      ? "bg-blue-100 text-blue-700 border border-blue-300"
                      : "bg-gray-100 text-gray-700 border border-gray-300"
                      }`}
                  >
                    {getCategoryIcon(
                      category.icon,
                      `h-3 w-3 ${filterCategory === category.id
                        ? "text-blue-600"
                        : "text-gray-600"
                      }`
                    )}
                    <span>{category.name}</span>
                  </button>
                ))}
              </div>
            </div>

            {/* Task List */}
            <div className="space-y-2">
              {getFilteredTasks().map((task) => (
                <button
                  key={task.id}
                  onClick={() => openTaskForm(task)}
                  className="w-full text-left p-3 border border-gray-200 rounded-lg hover:shadow-md hover:border-blue-300 transition-all"
                >
                  <div className="font-medium text-gray-900">{task.name}</div>
                  <div className="text-sm text-gray-600 mt-1">
                    {
                      TASK_CATEGORIES.find((cat) => cat.id === task.category)
                        ?.name
                    }
                  </div>
                </button>
              ))}

              {getFilteredTasks().length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <p>No tasks found matching your search.</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Task Form View */}
        {currentView === "form" && selectedTask && (
          <div className="bg-white rounded-xl border border-gray-200 p-4">
            {/* Form Header */}
            <div className="flex items-center space-x-3 mb-6">
              <button
                onClick={() => setCurrentView("category")}
                className="p-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-colors"
              >
                <ArrowLeft className="h-5 w-5" />
              </button>
              <h2 className="text-lg font-semibold text-gray-900">
                Create Task
              </h2>
            </div>

            {/* Task Form */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Task Title
                </label>
                <input
                  type="text"
                  value={taskForm.title}
                  onChange={(e) =>
                    setTaskForm((prev) => ({ ...prev, title: e.target.value }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-700"
                  readOnly
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description of Work *
                </label>
                <textarea
                  value={taskForm.description}
                  onChange={(e) =>
                    setTaskForm((prev) => ({
                      ...prev,
                      description: e.target.value,
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  rows={3}
                  placeholder="Describe the specific work to be performed..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Location *
                </label>
                <select
                  value={taskForm.location}
                  onChange={(e) =>
                    setTaskForm((prev) => ({
                      ...prev,
                      location: e.target.value,
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select location...</option>
                  {SITE_LOCATIONS.map((location) => (
                    <option key={location} value={location}>
                      {location}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Start Date
                </label>
                <input
                  type="date"
                  value={taskForm.startDate}
                  onChange={(e) =>
                    setTaskForm((prev) => ({
                      ...prev,
                      startDate: new Date(e.target.value).toISOString().split("T")[0],
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  min={new Date().toISOString() .split("T")[0]}
                />
                <p className="text-xs text-gray-500 mt-1">
                  Default is tomorrow
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Estimated Duration
                </label>
                <select
                  value={taskForm.timeForCompletion}
                  onChange={(e) =>
                    setTaskForm((prev) => ({
                      ...prev,
                      timeForCompletion: e.target.value,
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="P12H">Half a day</option>
                  <option value="P1D">1 day</option>
                  <option value="P2D">2 day</option>
                  <option value="P3D">3 day</option>
                  <option value="P4D">4 day</option>
                  <option value="P5D">5 day</option>
                  <option value="P6D">6 day</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Chief Engineer *
                </label>
                <select
                  value={taskForm.chiefEngineerId}
                  onChange={(e) =>
                    setTaskForm((prev) => ({
                      ...prev,
                      chiefEngineerId: parseInt(e.target.value),
                    }))
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value={0}>Select chief engineer...</option>
                  {workersData?.allWorkers?.map((worker: any) => (
                    <option key={worker.id} value={worker.id}>
                      {worker.name}
                    </option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Required Trades
                </label>
                <div className="space-y-2 max-h-32 overflow-y-auto border border-gray-300 rounded-lg p-2">
                  {tradesData?.allTrades?.map((trade: any) => (
                    <label
                      key={trade.id}
                      className="flex items-center space-x-2"
                    >
                      <input
                        type="checkbox"
                        checked={taskForm.tradeIds.includes(trade.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setTaskForm((prev) => ({
                              ...prev,
                              tradeIds: [...prev.tradeIds, trade.id],
                            }));
                          } else {
                            setTaskForm((prev) => ({
                              ...prev,
                              tradeIds: prev.tradeIds.filter(
                                (id) => id !== trade.id
                              ),
                            }));
                          }
                        }}
                        className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                      />
                      <span className="text-sm text-gray-700">
                        {trade.name}
                      </span>
                    </label>
                  ))}
                </div>
              </div>

              <div className="flex space-x-3 pt-4">
                <button
                  onClick={addTaskToQueue}
                  className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  Add Task
                </button>
                <button
                  onClick={() => setCurrentView("category")}
                  className="px-4 py-3 text-gray-600 hover:text-gray-800 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </SiteEngineerLayout>
  );
};

export default SiteEngineerPage;
