import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom';
import {
  ChevronDown,
  ChevronRight,
  Calendar,
  MapPin,
  Clock,
  FileText,
  User,
  CheckCircle,
  XCircle,
  Pause,
  AlertCircle
} from 'lucide-react';
import { useQuery } from '@apollo/client';
import { toast } from 'react-toastify';
import { SiteInfo } from '../../types';
import { mockSite } from '../../mock/taskData';
import SiteEngineerLayout from '../../components/site-engineer/SiteEngineerLayout';
import { GET_ALL_JOBS } from '../../graphql/queries';

// Job status mapping from GraphQL to display
const getJobStatusDisplay = (status: string) => {
  switch (status?.toLowerCase()) {
    case 'requested': return 'pending';
    case 'reviewed': return 'in-progress';
    case 'approved': return 'in-progress';
    case 'finished': return 'completed';
    case 'blocked': return 'overdue';
    default: return 'pending';
  }
};

// Priority mapping (we'll derive from status for now)
const getJobPriority = (status: string, dueDate?: string) => {
  if (status?.toLowerCase() === 'blocked') return 'high';
  if (dueDate && new Date(dueDate) < new Date()) return 'high';
  return 'normal';
};

// Risk level mapping (we'll derive from required permits)
const getJobRiskLevel = (requiredPermits: string[]) => {
  if (!requiredPermits || requiredPermits.length === 0) return 'low';
  if (requiredPermits.length >= 3) return 'high';
  if (requiredPermits.length >= 2) return 'medium';
  return 'low';
};

const TasksPage: React.FC = () => {
  const { siteId } = useParams<{ siteId: string }>();
  const navigate = useNavigate();
  const [site] = useState<SiteInfo>(mockSite);
  const [expandedTask, setExpandedTask] = useState<string | null>(null);

  // Fetch all jobs using GraphQL
  const { data, loading, error } = useQuery(GET_ALL_JOBS, {
    onError: (error) => {
      console.error('Error fetching jobs:', error);
      toast.error('Failed to load tasks. Please try again.');
    }
  });

  const jobs = data?.allJobs || [];

  const toggleTaskExpansion = (taskId: string) => {
    setExpandedTask(expandedTask === taskId ? null : taskId);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'in-progress':
        return <Pause className="h-5 w-5 text-blue-600" />;
      case 'overdue':
        return <XCircle className="h-5 w-5 text-red-600" />;
      default:
        return <Clock className="h-5 w-5 text-yellow-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'FINISHED': return 'text-green-600 bg-green-50 border-green-200';
      case 'REQUESTED': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'APPROVED': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'PENDING_APPROVAL': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'BLOCKED': return 'text-red-600 bg-red-50 border-red-200';
      case 'DISAPPROVED': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'text-red-600 bg-red-50 border-red-200';
      case 'normal': return 'text-blue-600 bg-blue-50 border-blue-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return 'text-green-600 bg-green-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'high': return 'text-orange-600 bg-orange-50';
      case 'critical': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const handleViewPermit = (permitId: string) => {
    navigate(`/sites/${siteId}/engineer/permits/${permitId}`);
  };

  // Loading state
  if (loading) {
    return (
      <SiteEngineerLayout site={site} title="Tasks" showBackButton={true}>
        <div className="px-4 sm:px-6 py-4 sm:py-6">
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-3 text-gray-600">Loading tasks...</span>
          </div>
        </div>
      </SiteEngineerLayout>
    );
  }

  // Error state
  if (error) {
    return (
      <SiteEngineerLayout site={site} title="Tasks" showBackButton={true}>
        <div className="px-4 sm:px-6 py-4 sm:py-6">
          <div className="flex flex-col items-center justify-center py-12">
            <AlertCircle className="h-12 w-12 text-red-500 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to load tasks</h3>
            <p className="text-gray-600 text-center mb-4">
              There was an error loading the tasks. Please try again.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </SiteEngineerLayout>
    );
  }

  return (
    <SiteEngineerLayout site={site} title="Tasks" showBackButton={true}>
      <div className="px-4 sm:px-6 py-4 sm:py-6">
        {/* Tasks Header */}
        <div className="flex items-center justify-between mb-4 sm:mb-6">
          <div>
            <h2 className="text-lg sm:text-xl font-semibold text-gray-900">All Tasks</h2>
            <p className="text-xs sm:text-sm text-gray-600 mt-1">{jobs.length} tasks total</p>
          </div>
          <button
            onClick={() => navigate(`/sites/${siteId}/engineer/tasks/create`)}
            className="bg-blue-600 text-white px-3 sm:px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-xs sm:text-sm font-medium"
          >
            <span className="hidden sm:inline">New Task</span>
            <span className="sm:hidden">New</span>
          </button>
        </div>

        {/* Empty state */}
        {jobs.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12">
            <FileText className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No tasks found</h3>
            <p className="text-gray-600 text-center mb-4">
              There are no tasks available at the moment.
            </p>
            <button
              onClick={() => navigate(`/sites/${siteId}/engineer/tasks/create`)}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              Create First Task
            </button>
          </div>
        ) : (
          /* Task List - FAQ Style */
          <div className="space-y-3">
            {jobs.map((job: any) => {
              // const displayStatus = getJobStatusDisplay(job.status);
              const displayStatus = job.status;
              const priority = getJobPriority(job.status, job.dueDate);
              const riskLevel = getJobRiskLevel(job.requiredPermits || []);

              return (
            <div key={job.id} className="bg-white rounded-xl border border-gray-200 overflow-hidden">
              {/* Task Header - Clickable */}
              <button
                onClick={() => toggleTaskExpansion(job.id.toString())}
                className="w-full p-3 sm:p-4 text-left hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-2 sm:space-x-3 flex-1 min-w-0">
                    <div className="flex-shrink-0 mt-0.5">
                      {getStatusIcon(displayStatus)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between mb-1 sm:mb-2">
                        <h3 className="font-semibold text-gray-900 text-sm sm:text-base leading-tight pr-2">{job.title}</h3>
                        <div className="flex items-center space-x-1 flex-shrink-0">
                          {/* Mobile: Priority and overdue as dots */}
                          <div className="sm:hidden flex items-center space-x-1">
                            <div className={`w-2 h-2 rounded-full ${priority === 'high' ? 'bg-red-500' : 'bg-blue-500'}`}></div>
                            {displayStatus === 'overdue' && (
                              <div className="w-2 h-2 bg-red-600 rounded-full animate-pulse"></div>
                            )}
                          </div>
                          {/* Desktop: Priority and overdue badges */}
                          {/* <div className="hidden sm:flex items-center space-x-2">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium border ${getPriorityColor(priority)}`}>
                              {priority}
                            </span>
                            {displayStatus === 'overdue' && (
                              <span className="px-2 py-1 bg-red-100 text-red-600 text-xs rounded-full font-medium">
                                OVERDUE
                              </span>
                            )}
                          </div> */}
                        </div>
                      </div>

                      {/* Mobile: Simplified info */}
                      <div className="sm:hidden">
                        <div className="text-xs text-gray-500 mb-1">{job.category?.description || 'General'}</div>
                        <div className="flex items-center justify-between text-xs text-gray-600">
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-3 w-3" />
                            <span>{job.dueDate ? new Date(job.dueDate).toLocaleDateString('en-US', { month: 'short', day: 'numeric' }) : 'No due date'}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <MapPin className="h-3 w-3" />
                            <span className="truncate max-w-[100px]">{job.location || 'No location'}</span>
                          </div>
                        </div>
                      </div>

                      {/* Desktop: Full info */}
                      <div className="hidden sm:flex items-center space-x-4 text-sm text-gray-600">
                        <span>{job.category?.description || 'General'}</span>
                        <div className="flex items-center space-x-1">
                          <Calendar className="h-3 w-3" />
                          <span>{job.dueDate ? new Date(job.dueDate).toLocaleDateString() : 'No due date'}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <MapPin className="h-3 w-3" />
                          <span className="truncate">{job.location || 'No location'}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2 flex-shrink-0 ml-2">
                    {/* Mobile: Status dot */}
                    <div className={`w-3 h-3 rounded-full sm:hidden ${
                      displayStatus === 'FINISHED' ? 'bg-green-500' :
                      displayStatus === 'REQUESTED' || displayStatus === 'APPROVED' ? 'bg-blue-500' :
                      displayStatus === 'PENDING_APPROVAL' ? 'bg-yellow-500' :
                      displayStatus === 'BLOCKED' || displayStatus === 'DISAPPROVED' ? 'bg-red-500' : 'bg-gray-500'
                    }`}></div>
                    {/* Desktop: Status badge */}
                    <span className={`hidden sm:inline-flex px-2 py-1 rounded-full text-xs font-medium border ${getStatusColor(displayStatus)}`}>
                      {displayStatus.replace('_', ' ')}
                    </span>
                    {expandedTask === job.id.toString() ? (
                      <ChevronDown className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                    ) : (
                      <ChevronRight className="h-4 w-4 sm:h-5 sm:w-5 text-gray-400" />
                    )}
                  </div>
                </div>
              </button>

              {/* Expanded Task Details */}
              {expandedTask === job.id.toString() && (
                <div className="border-t border-gray-200 p-4 bg-gray-50">
                  <div className="space-y-4">
                    {/* Task Description */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Description</h4>
                      <p className="text-sm text-gray-700 bg-white rounded-lg p-3 border">
                        {job.description || 'No description available'}
                      </p>
                    </div>

                    {/* Task Details Grid */}
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-3">
                        <div>
                          <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Chief Engineer</span>
                          <div className="flex items-center space-x-2 mt-1">
                            <User className="h-4 w-4 text-gray-400" />
                            <span className="text-sm text-gray-900">{job.chiefEngineer?.name || 'Not assigned'}</span>
                          </div>
                        </div>
                        <div>
                          <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Duration</span>
                          <div className="flex items-center space-x-2 mt-1">
                            <Clock className="h-4 w-4 text-gray-400" />
                            <span className="text-sm text-gray-900">{job.timeForCompletion || 'Not specified'}</span>
                          </div>
                        </div>
                      </div>
                      <div className="space-y-3">
                        <div>
                          <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Start Date</span>
                          <div className="text-sm text-gray-900 mt-1">
                            {job.startDate ? new Date(job.startDate).toLocaleDateString() : 'Not set'}
                          </div>
                        </div>
                        <div>
                          <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">Risk Level</span>
                          <div className="mt-1">
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskLevelColor(riskLevel)}`}>
                              {riskLevel} risk
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Required Permits */}
                    {job.requiredPermits && job.requiredPermits.length > 0 && (
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Required Permits</h4>
                        <ul className="space-y-1">
                          {job.requiredPermits.map((permit: string, index: number) => (
                            <li key={index} className="text-sm text-gray-700 flex items-start space-x-2">
                              <span className="text-blue-600 mt-1">•</span>
                              <span>{permit}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* PPE Requirements */}
                    {job.ppEs && job.ppEs.length > 0 && (
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">PPE Requirements</h4>
                        <ul className="space-y-1">
                          {job.ppEs.map((ppe: string, index: number) => (
                            <li key={index} className="text-sm text-gray-700 flex items-start space-x-2">
                              <span className="text-blue-600 mt-1">•</span>
                              <span>{ppe}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Hazards */}
                    {job.hazards && job.hazards.length > 0 && (
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Hazards & Control Measures</h4>
                        <div className="space-y-2">
                          {job.hazards.map((hazard: any, index: number) => (
                            <div key={index} className="bg-white rounded-lg p-3 border">
                              <div className="font-medium text-sm text-gray-900 mb-1">{hazard.description}</div>
                              {hazard.controlMeasures && hazard.controlMeasures.length > 0 && (
                                <ul className="space-y-1 ml-4">
                                  {hazard.controlMeasures.map((measure: any, measureIndex: number) => (
                                    <li key={measureIndex} className="text-xs text-gray-600 flex items-start space-x-2">
                                      <span className="text-green-600 mt-1">→</span>
                                      <span>{measure.description}</span>
                                    </li>
                                  ))}
                                </ul>
                              )}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Status Information */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Status Information</h4>
                      <div className="bg-white rounded-lg p-3 border space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-500">Current Status:</span>
                          <span className="font-medium text-gray-900">{job.status}</span>
                        </div>
                        {job.requestedBy && (
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-500">Requested By:</span>
                            <span className="font-medium text-gray-900">{job.requestedBy.name}</span>
                          </div>
                        )}
                        {job.reviewedBy && (
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-500">Reviewed By:</span>
                            <span className="font-medium text-gray-900">{job.reviewedBy.name}</span>
                          </div>
                        )}
                        {job.approvedBy && (
                          <div className="flex justify-between text-sm">
                            <span className="text-gray-500">Approved By:</span>
                            <span className="font-medium text-gray-900">{job.approvedBy.name}</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
            );
            })}
          </div>
        )}
      </div>
    </SiteEngineerLayout>
  );
};

export default TasksPage;
