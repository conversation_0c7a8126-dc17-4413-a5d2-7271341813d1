import React, { useState } from 'react';

import {
  Sun,
  Cloud,
  CloudRain,
  Wind,
  Droplets,
  Eye,
  AlertTriangle,
  Clock
} from 'lucide-react';
import { SiteInfo } from '../../types';
import { mockSite } from '../../mock/taskData';
import SiteEngineerLayout from '../../components/site-engineer/SiteEngineerLayout';

// Enhanced weather data
const WEATHER_DATA = {
  current: {
    temperature: 24,
    feelsLike: 27,
    humidity: 65,
    windSpeed: 12,
    windDirection: 'NW',
    precipitation: 0,
    visibility: 10,
    uvIndex: 6,
    pressure: 1013,
    conditions: 'Partly Cloudy',
    icon: 'partly-cloudy'
  },
  hourly: [
    { time: '09:00', temp: 22, condition: 'Sunny', precipitation: 0, wind: 10 },
    { time: '10:00', temp: 24, condition: 'Sunny', precipitation: 0, wind: 11 },
    { time: '11:00', temp: 26, condition: 'Partly Cloudy', precipitation: 0, wind: 12 },
    { time: '12:00', temp: 28, condition: 'Partly Cloudy', precipitation: 5, wind: 13 },
    { time: '13:00', temp: 29, condition: 'Cloudy', precipitation: 10, wind: 15 },
    { time: '14:00', temp: 27, condition: 'Light Rain', precipitation: 20, wind: 16 },
    { time: '15:00', temp: 25, condition: 'Rain', precipitation: 40, wind: 18 },
    { time: '16:00', temp: 23, condition: 'Heavy Rain', precipitation: 60, wind: 20 }
  ],
  forecast: [
    {
      date: new Date(Date.now() + 86400000).toISOString().split('T')[0],
      day: 'Tomorrow',
      highTemp: 26,
      lowTemp: 18,
      condition: 'Partly Cloudy',
      precipitationChance: 20,
      windSpeed: 14,
      workImpact: 'none',
      icon: 'partly-cloudy'
    },
    {
      date: new Date(Date.now() + 172800000).toISOString().split('T')[0],
      day: 'Wednesday',
      highTemp: 23,
      lowTemp: 16,
      condition: 'Heavy Rain',
      precipitationChance: 80,
      windSpeed: 22,
      workImpact: 'severe',
      icon: 'heavy-rain'
    },
    {
      date: new Date(Date.now() + 259200000).toISOString().split('T')[0],
      day: 'Thursday',
      highTemp: 25,
      lowTemp: 17,
      condition: 'Cloudy',
      precipitationChance: 30,
      windSpeed: 16,
      workImpact: 'minor',
      icon: 'cloudy'
    },
    {
      date: new Date(Date.now() + 345600000).toISOString().split('T')[0],
      day: 'Friday',
      highTemp: 28,
      lowTemp: 20,
      condition: 'Sunny',
      precipitationChance: 5,
      windSpeed: 12,
      workImpact: 'none',
      icon: 'sunny'
    }
  ],
  alerts: [
    {
      type: 'heavy-rain',
      severity: 'warning',
      title: 'Heavy Rain Warning',
      message: 'Heavy rain expected Wednesday. Outdoor electrical work should be postponed.',
      validFrom: '2024-08-07 06:00',
      validTo: '2024-08-07 18:00'
    }
  ]
};

const WeatherPage: React.FC = () => {
  const [site] = useState<SiteInfo>(mockSite);
  const [weather] = useState(WEATHER_DATA);

  const getWeatherIcon = (condition: string) => {
    switch (condition.toLowerCase()) {
      case 'sunny':
        return <Sun className="h-8 w-8 text-yellow-500" />;
      case 'partly cloudy':
      case 'partly-cloudy':
        return <Cloud className="h-8 w-8 text-gray-500" />;
      case 'cloudy':
        return <Cloud className="h-8 w-8 text-gray-600" />;
      case 'light rain':
      case 'rain':
      case 'heavy rain':
      case 'heavy-rain':
        return <CloudRain className="h-8 w-8 text-blue-500" />;
      default:
        return <Sun className="h-8 w-8 text-yellow-500" />;
    }
  };

  const getWorkImpactColor = (impact: string) => {
    switch (impact) {
      case 'none': return 'text-green-600 bg-green-50 border-green-200';
      case 'minor': return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'moderate': return 'text-orange-600 bg-orange-50 border-orange-200';
      case 'severe': return 'text-red-600 bg-red-50 border-red-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  const getWorkImpactText = (impact: string) => {
    switch (impact) {
      case 'none': return 'Good for work';
      case 'minor': return 'Minor impact';
      case 'moderate': return 'Moderate impact';
      case 'severe': return 'Severe impact';
      default: return 'Unknown impact';
    }
  };

  const getUVIndexColor = (uvIndex: number) => {
    if (uvIndex <= 2) return 'text-green-600 bg-green-50';
    if (uvIndex <= 5) return 'text-yellow-600 bg-yellow-50';
    if (uvIndex <= 7) return 'text-orange-600 bg-orange-50';
    if (uvIndex <= 10) return 'text-red-600 bg-red-50';
    return 'text-purple-600 bg-purple-50';
  };

  const getUVIndexText = (uvIndex: number) => {
    if (uvIndex <= 2) return 'Low';
    if (uvIndex <= 5) return 'Moderate';
    if (uvIndex <= 7) return 'High';
    if (uvIndex <= 10) return 'Very High';
    return 'Extreme';
  };

  return (
    <SiteEngineerLayout site={site} title="Weather" showBackButton={true}>
      <div className="px-6 py-6 space-y-6">
        
        {/* Current Weather */}
        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Current Conditions</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Main Weather Display */}
            <div className="text-center">
              <div className="flex items-center justify-center mb-4">
                {getWeatherIcon(weather.current.conditions)}
              </div>
              <div className="text-4xl font-bold text-gray-900 mb-2">
                {weather.current.temperature}°C
              </div>
              <div className="text-lg text-gray-600 mb-2">{weather.current.conditions}</div>
              <div className="text-sm text-gray-500">
                Feels like {weather.current.feelsLike}°C
              </div>
            </div>

            {/* Weather Details */}
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="flex items-center space-x-2 mb-1">
                  <Droplets className="h-4 w-4 text-blue-500" />
                  <span className="text-sm text-gray-600">Humidity</span>
                </div>
                <div className="text-lg font-semibold text-gray-900">{weather.current.humidity}%</div>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="flex items-center space-x-2 mb-1">
                  <Wind className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Wind</span>
                </div>
                <div className="text-lg font-semibold text-gray-900">
                  {weather.current.windSpeed} km/h {weather.current.windDirection}
                </div>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="flex items-center space-x-2 mb-1">
                  <Eye className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Visibility</span>
                </div>
                <div className="text-lg font-semibold text-gray-900">{weather.current.visibility} km</div>
              </div>
              
              <div className="bg-gray-50 rounded-lg p-3">
                <div className="flex items-center space-x-2 mb-1">
                  <Sun className="h-4 w-4 text-yellow-500" />
                  <span className="text-sm text-gray-600">UV Index</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-lg font-semibold text-gray-900">{weather.current.uvIndex}</span>
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getUVIndexColor(weather.current.uvIndex)}`}>
                    {getUVIndexText(weather.current.uvIndex)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Weather Alerts */}
        {weather.alerts.length > 0 && (
          <div className="bg-red-50 border border-red-200 rounded-xl p-6">
            <h2 className="text-lg font-semibold text-red-800 mb-4 flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2" />
              Weather Alerts
            </h2>
            <div className="space-y-4">
              {weather.alerts.map((alert, index) => (
                <div key={index} className="bg-white border border-red-200 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-2">
                        <h3 className="font-semibold text-red-800">{alert.title}</h3>
                        <span className="px-2 py-1 bg-red-100 text-red-700 text-xs font-medium rounded-full capitalize">
                          {alert.severity}
                        </span>
                      </div>
                      <p className="text-red-700 mb-2">{alert.message}</p>
                      <div className="flex items-center space-x-4 text-sm text-red-600">
                        <div className="flex items-center space-x-1">
                          <Clock className="h-3 w-3" />
                          <span>From: {new Date(alert.validFrom).toLocaleString()}</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <Clock className="h-3 w-3" />
                          <span>To: {new Date(alert.validTo).toLocaleString()}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Hourly Forecast */}
        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Today's Hourly Forecast</h2>
          <div className="overflow-x-auto">
            <div className="flex space-x-4 pb-2">
              {weather.hourly.map((hour, index) => (
                <div key={index} className="flex-shrink-0 text-center bg-gray-50 rounded-lg p-3 min-w-[80px]">
                  <div className="text-sm font-medium text-gray-900 mb-2">{hour.time}</div>
                  <div className="flex justify-center mb-2">
                    {getWeatherIcon(hour.condition)}
                  </div>
                  <div className="text-lg font-semibold text-gray-900 mb-1">{hour.temp}°</div>
                  <div className="text-xs text-blue-600 mb-1">{hour.precipitation}%</div>
                  <div className="text-xs text-gray-500">{hour.wind} km/h</div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 4-Day Forecast */}
        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">4-Day Forecast</h2>
          <div className="space-y-4">
            {weather.forecast.map((day, index) => (
              <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    {getWeatherIcon(day.condition)}
                  </div>
                  <div>
                    <div className="font-semibold text-gray-900">{day.day}</div>
                    <div className="text-sm text-gray-600">{day.condition}</div>
                    <div className="text-xs text-gray-500 flex items-center space-x-3 mt-1">
                      <span>{day.precipitationChance}% rain</span>
                      <span>{day.windSpeed} km/h wind</span>
                    </div>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="font-semibold text-gray-900 text-lg mb-2">
                    {day.highTemp}° / {day.lowTemp}°
                  </div>
                  <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getWorkImpactColor(day.workImpact)}`}>
                    {getWorkImpactText(day.workImpact)}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Work Impact Guidelines */}
        <div className="bg-blue-50 border border-blue-200 rounded-xl p-6">
          <h2 className="text-lg font-semibold text-blue-800 mb-4 flex items-center">
            <Eye className="h-5 w-5 mr-2" />
            Work Impact Guidelines
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <div className="w-4 h-4 bg-green-500 rounded-full mt-1 flex-shrink-0"></div>
                <div>
                  <div className="font-medium text-green-800">Good for work</div>
                  <div className="text-sm text-green-700">All outdoor work can proceed normally. Ideal conditions for construction activities.</div>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="w-4 h-4 bg-yellow-500 rounded-full mt-1 flex-shrink-0"></div>
                <div>
                  <div className="font-medium text-yellow-800">Minor impact</div>
                  <div className="text-sm text-yellow-700">Some work types may be affected. Monitor conditions and take precautions.</div>
                </div>
              </div>
            </div>
            
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <div className="w-4 h-4 bg-orange-500 rounded-full mt-1 flex-shrink-0"></div>
                <div>
                  <div className="font-medium text-orange-800">Moderate impact</div>
                  <div className="text-sm text-orange-700">Outdoor work may be limited. Consider rescheduling non-essential tasks.</div>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="w-4 h-4 bg-red-500 rounded-full mt-1 flex-shrink-0"></div>
                <div>
                  <div className="font-medium text-red-800">Severe impact</div>
                  <div className="text-sm text-red-700">Most outdoor work should be postponed. Safety is the top priority.</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </SiteEngineerLayout>
  );
};

export default WeatherPage;
