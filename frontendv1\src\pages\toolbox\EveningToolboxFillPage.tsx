import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery, useMutation } from '@apollo/client';
import { toast } from 'react-toastify';
import {
  ArrowLeft,
  Moon,
  User,
  Plus,
  Minus,
  Save,
  AlertCircle,
  CheckCircle,
  X
} from 'lucide-react';
import FloatingCard from '../../components/layout/FloatingCard';
import { GET_ALL_WORKERS, GET_VALID_JOBS, GET_TODAYS_JOB_RISK_ASSESSMENT } from '../../graphql/queries';
import { FILL_EVENING_TOOLBOX } from '../../graphql/mutations';

interface Worker {
  id: number;
  name: string;
}

interface Job {
  id: number;
  title: string;
  description: string;
}

interface Hazard {
  id: number;
  description: string;
  controlMeasures: { id: number; description: string; }[];
}

interface NewHazard {
  description: string;
  controlMeasures: string[];
}

interface ExistingHazard {
  hazardId: number;
  controlMeasures: string[];
}

interface JobFormData {
  jobId: number;
  newHazards: NewHazard[];
  existingHazards: ExistingHazard[];
  isClosed: boolean;
}

interface FormData {
  conductorId: number;
  jobs: JobFormData[];
}

const EveningToolboxFillPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId } = useParams<{ siteId: string }>();

  const [formData, setFormData] = useState<FormData>({
    conductorId: 1, // Default as specified in requirements
    jobs: []
  });

  const [selectedJobs, setSelectedJobs] = useState<number[]>([]);

  const { data: workersData, loading: workersLoading } = useQuery(GET_ALL_WORKERS);
  const { data: jobsData, loading: jobsLoading } = useQuery(GET_VALID_JOBS);
  const { data: riskAssessmentData, loading: riskLoading } = useQuery(GET_TODAYS_JOB_RISK_ASSESSMENT);
  const [fillEveningToolbox, { loading: submitting }] = useMutation(FILL_EVENING_TOOLBOX);

  const workers: Worker[] = workersData?.allWorkers || [];
  const jobs: Job[] = jobsData?.validJobs || [];
  const availableHazards: Hazard[] = riskAssessmentData?.todaysJobRiskAssessment?.hazards || [];

  const addJob = () => {
    const newJob: JobFormData = {
      jobId: 0,
      newHazards: [],
      existingHazards: [],
      isClosed: false
    };
    setFormData(prev => ({
      ...prev,
      jobs: [...prev.jobs, newJob]
    }));
  };

  const removeJob = (index: number) => {
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.filter((_, i) => i !== index)
    }));
  };

  const updateJob = (index: number, field: keyof JobFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.map((job, i) =>
        i === index ? { ...job, [field]: value } : job
      )
    }));
  };

  const addNewHazard = (jobIndex: number) => {
    const newHazard: NewHazard = {
      description: '',
      controlMeasures: ['']
    };
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.map((job, i) =>
        i === jobIndex
          ? { ...job, newHazards: [...job.newHazards, newHazard] }
          : job
      )
    }));
  };

  const removeNewHazard = (jobIndex: number, hazardIndex: number) => {
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.map((job, i) =>
        i === jobIndex
          ? { ...job, newHazards: job.newHazards.filter((_, hi) => hi !== hazardIndex) }
          : job
      )
    }));
  };

  const updateNewHazard = (jobIndex: number, hazardIndex: number, field: keyof NewHazard, value: any) => {
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.map((job, i) =>
        i === jobIndex
          ? {
              ...job,
              newHazards: job.newHazards.map((hazard, hi) =>
                hi === hazardIndex ? { ...hazard, [field]: value } : hazard
              )
            }
          : job
      )
    }));
  };

  const addExistingHazard = (jobIndex: number) => {
    const newExistingHazard: ExistingHazard = {
      hazardId: 0,
      controlMeasures: ['']
    };
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.map((job, i) =>
        i === jobIndex
          ? { ...job, existingHazards: [...job.existingHazards, newExistingHazard] }
          : job
      )
    }));
  };

  const removeExistingHazard = (jobIndex: number, hazardIndex: number) => {
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.map((job, i) =>
        i === jobIndex
          ? { ...job, existingHazards: job.existingHazards.filter((_, hi) => hi !== hazardIndex) }
          : job
      )
    }));
  };

  const updateExistingHazard = (jobIndex: number, hazardIndex: number, field: keyof ExistingHazard, value: any) => {
    setFormData(prev => ({
      ...prev,
      jobs: prev.jobs.map((job, i) =>
        i === jobIndex
          ? {
              ...job,
              existingHazards: job.existingHazards.map((hazard, hi) =>
                hi === hazardIndex ? { ...hazard, [field]: value } : hazard
              )
            }
          : job
      )
    }));
  };

  const validateForm = (): boolean => {
    if (!formData.conductorId) {
      toast.error('Please select a conductor');
      return false;
    }

    if (formData.jobs.length === 0) {
      toast.error('Please add at least one job');
      return false;
    }

    for (let i = 0; i < formData.jobs.length; i++) {
      const job = formData.jobs[i];
      if (!job.jobId) {
        toast.error(`Please select a job for job ${i + 1}`);
        return false;
      }

      // Validate new hazards
      for (let j = 0; j < job.newHazards.length; j++) {
        const hazard = job.newHazards[j];
        if (!hazard.description.trim()) {
          toast.error(`Please provide description for new hazard ${j + 1} in job ${i + 1}`);
          return false;
        }
        if (hazard.controlMeasures.some(cm => !cm.trim())) {
          toast.error(`Please provide all control measures for new hazard ${j + 1} in job ${i + 1}`);
          return false;
        }
      }

      // Validate existing hazards
      for (let j = 0; j < job.existingHazards.length; j++) {
        const hazard = job.existingHazards[j];
        if (!hazard.hazardId) {
          toast.error(`Please select a hazard for existing hazard ${j + 1} in job ${i + 1}`);
          return false;
        }
        if (hazard.controlMeasures.some(cm => !cm.trim())) {
          toast.error(`Please provide all control measures for existing hazard ${j + 1} in job ${i + 1}`);
          return false;
        }
      }
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    try {
      const result = await fillEveningToolbox({
        variables: {
          input: {
            jobs: formData.jobs.map(job => ({
              jobId: job.jobId,
              newHazards: job.newHazards,
              existingHazards: job.existingHazards,
              isClosed: job.isClosed
            })),
            conductorId: formData.conductorId
          }
        }
      });

      if (result.data?.fillEveningToolbox) {
        toast.success('Evening toolbox created successfully!');
        navigate(`/sites/${siteId}/toolbox/evening-toolbox`);
      }
    } catch (error) {
      console.error('Error creating evening toolbox:', error);
      toast.error('Failed to create evening toolbox. Please try again.');
    }
  };

  const breadcrumbs = [
    { name: 'Site Dashboard', path: `/sites/${siteId}/dashboard` },
    { name: 'Toolbox', path: `/sites/${siteId}/toolbox` },
    { name: 'Evening Toolbox', path: `/sites/${siteId}/toolbox/evening-toolbox` },
    { name: 'Create', path: `/sites/${siteId}/toolbox/evening-toolbox/fill` }
  ];

  if (workersLoading) {
    return (
      <FloatingCard title="Create Evening Toolbox" breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </FloatingCard>
    );
  }

  return (
    <FloatingCard title="Create Evening Toolbox" breadcrumbs={breadcrumbs}>
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Header */}
        <div className="bg-indigo-50 p-4 rounded-lg">
          <div className="flex items-center space-x-3">
            <Moon className="h-8 w-8 text-indigo-600" />
            <div>
              <h3 className="text-lg font-semibold text-indigo-900">Evening Toolbox Meeting</h3>
              <p className="text-sm text-indigo-700">Record the day's work summary and safety observations</p>
            </div>
          </div>
        </div>

        {/* Date and Conductor */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              <Calendar className="h-4 w-4 inline mr-1" />
              Date *
            </label>
            <input
              type="date"
              value={formData.date}
              onChange={(e) => handleInputChange('date', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              required
            />
          </div>

          <div className="space-y-2">
            <label className="block text-sm font-medium text-gray-700">
              <User className="h-4 w-4 inline mr-1" />
              Conductor *
            </label>
            <select
              value={formData.conductorId}
              onChange={(e) => handleInputChange('conductorId', parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
              required
            >
              <option value={0}>Select Conductor</option>
              {workers.map(worker => (
                <option key={worker.id} value={worker.id}>
                  {worker.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Work Completed */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            <FileText className="h-4 w-4 inline mr-1" />
            Work Completed Today *
          </label>
          <textarea
            value={formData.workCompleted}
            onChange={(e) => handleInputChange('workCompleted', e.target.value)}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            placeholder="Describe the work that was completed today..."
            required
          />
          <p className="text-sm text-gray-500">
            Provide a detailed summary of all work activities completed during the day.
          </p>
        </div>

        {/* Work Planned */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            <FileText className="h-4 w-4 inline mr-1" />
            Work Planned for Tomorrow *
          </label>
          <textarea
            value={formData.workPlanned}
            onChange={(e) => handleInputChange('workPlanned', e.target.value)}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            placeholder="Describe the work planned for tomorrow..."
            required
          />
          <p className="text-sm text-gray-500">
            Outline the work activities and tasks planned for the next working day.
          </p>
        </div>

        {/* Safety Observations */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            <AlertCircle className="h-4 w-4 inline mr-1 text-orange-500" />
            Safety Observations *
          </label>
          <textarea
            value={formData.safetyObservations}
            onChange={(e) => handleInputChange('safetyObservations', e.target.value)}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            placeholder="Record any safety observations, near misses, or safety improvements..."
            required
          />
          <p className="text-sm text-gray-500">
            Document any safety observations, near misses, hazards identified, or safety improvements made.
          </p>
        </div>

        {/* Incidents */}
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">
            <AlertCircle className="h-4 w-4 inline mr-1 text-red-500" />
            Incidents (Optional)
          </label>
          <textarea
            value={formData.incidents}
            onChange={(e) => handleInputChange('incidents', e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
            placeholder="Record any incidents that occurred during the day (leave blank if none)..."
          />
          <p className="text-sm text-gray-500">
            Document any incidents, accidents, or safety events that occurred. Leave blank if no incidents occurred.
          </p>
        </div>

        {/* Form Validation Info */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h4 className="font-medium text-yellow-800 mb-2">Required Information</h4>
          <ul className="text-sm text-yellow-700 space-y-1">
            <li>• Select the date for this evening toolbox</li>
            <li>• Choose the conductor who led the meeting</li>
            <li>• Provide a summary of work completed today</li>
            <li>• Outline work planned for tomorrow</li>
            <li>• Record safety observations and any concerns</li>
            <li>• Document incidents only if any occurred</li>
          </ul>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between pt-6 border-t border-gray-200">
          <button
            type="button"
            onClick={() => navigate(`/sites/${siteId}/toolbox/evening-toolbox`)}
            className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Dashboard</span>
          </button>

          <button
            type="submit"
            disabled={submitting}
            className="flex items-center space-x-2 px-6 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {submitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Creating...</span>
              </>
            ) : (
              <>
                <Save className="h-4 w-4" />
                <span>Create Evening Toolbox</span>
              </>
            )}
          </button>
        </div>
      </form>
    </FloatingCard>
  );
};

export default EveningToolboxFillPage;
