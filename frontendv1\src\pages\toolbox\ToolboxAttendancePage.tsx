import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery, useMutation } from '@apollo/client';
import { toast } from 'react-toastify';
import {
  ArrowLeft,
  CheckCircle,
  AlertCircle,
  Search,
  Users,
  CheckSquare,
  Square
} from 'lucide-react';
import FloatingCard from '../../components/layout/FloatingCard';
import { GET_TOOLBOX_BY_ID, GET_ALL_WORKERS } from '../../graphql/queries';
import { ADD_ATTENDEES } from '../../graphql/mutations';

interface Worker {
  id: number;
  name: string;
  company: string;
  phoneNumber: string;
}

interface Toolbox {
  id: number;
  status: string;
  conductor?: {
    id: number;
    name: string;
  };
  attendees?: {
    id: number;
    name: string;
  }[];
  jobs: {
    id: number;
    title: string;
  }[];
}

const ToolboxAttendancePage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId, toolboxId } = useParams<{ siteId: string; toolboxId: string }>();

  const [selectedWorkerIds, setSelectedWorkerIds] = useState<number[]>([]);
  const [searchTerm, setSearchTerm] = useState('');

  const { data: toolboxData, loading: toolboxLoading, error: toolboxError } = useQuery(GET_TOOLBOX_BY_ID, {
    variables: { id: parseInt(toolboxId || '0') },
    skip: !toolboxId
  });

  const { data: workersData, loading: workersLoading } = useQuery(GET_ALL_WORKERS);
  const [addAttendees, { loading: submitting }] = useMutation(ADD_ATTENDEES);

  const toolbox: Toolbox | undefined = toolboxData?.toolboxById?.[0];
  const workers: Worker[] = workersData?.allWorkers || [];

  // Filter workers based on search term
  const filteredWorkers = workers.filter(worker =>
    worker.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    worker.company.toLowerCase().includes(searchTerm.toLowerCase()) ||
    worker.phoneNumber.includes(searchTerm)
  );

  const handleWorkerToggle = (workerId: number) => {
    setSelectedWorkerIds(prev =>
      prev.includes(workerId)
        ? prev.filter(id => id !== workerId)
        : [...prev, workerId]
    );
  };

  const handleSelectAll = () => {
    if (selectedWorkerIds.length === filteredWorkers.length) {
      setSelectedWorkerIds([]);
    } else {
      setSelectedWorkerIds(filteredWorkers.map(worker => worker.id));
    }
  };

  const handleSubmit = async () => {
    if (selectedWorkerIds.length === 0) {
      toast.error('Please select at least one attendee');
      return;
    }

    try {
      await addAttendees({
        variables: {
          input: {
            toolboxId: parseInt(toolboxId || '0'),
            attendeeIds: selectedWorkerIds,
            closedById: 1 // Default as specified in requirements
          }
        }
      });

      toast.success('Attendance recorded successfully!');
      navigate(`/sites/${siteId}/toolbox`);
    } catch (error) {
      console.error('Error recording attendance:', error);
      toast.error('Failed to record attendance. Please try again.');
    }
  };

  const breadcrumbs = [
    { name: 'Site Dashboard', path: `/sites/${siteId}/dashboard` },
    { name: 'Toolbox', path: `/sites/${siteId}/toolbox` },
    { name: 'Record Attendance', path: `/sites/${siteId}/toolbox/attendance/${toolboxId}` }
  ];

  if (toolboxLoading || workersLoading) {
    return (
      <FloatingCard title="Record Attendance" breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </FloatingCard>
    );
  }

  if (toolboxError || !toolbox) {
    return (
      <FloatingCard title="Record Attendance" breadcrumbs={breadcrumbs}>
        <div className="text-center py-8">
          <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
          <p className="text-red-600">
            {toolboxError ? `Error loading toolbox: ${toolboxError.message}` : 'Toolbox not found'}
          </p>
        </div>
      </FloatingCard>
    );
  }

  return (
    <FloatingCard title="Record Attendance" breadcrumbs={breadcrumbs}>
      <div className="space-y-6">
        {/* Toolbox Info */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <h3 className="text-lg font-semibold text-blue-900 mb-2">Toolbox Information</h3>
          <div className="text-sm text-blue-800">
            <p><strong>Status:</strong> {toolbox.status}</p>
            <p><strong>Conductor:</strong> {toolbox.conductor?.name || 'Not assigned'}</p>
            <p><strong>Jobs:</strong> {toolbox.jobs.map(job => job.title).join(', ')}</p>
          </div>
        </div>

        {/* Search and Selection Controls */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">Select Attendees</h3>
            <div className="flex items-center space-x-4">
              <button
                onClick={handleSelectAll}
                className="flex items-center space-x-2 text-blue-600 hover:text-blue-700"
              >
                {selectedWorkerIds.length === filteredWorkers.length ? (
                  <CheckSquare className="h-5 w-5" />
                ) : (
                  <Square className="h-5 w-5" />
                )}
                <span>Select All</span>
              </button>
              <span className="text-sm text-gray-600">
                {selectedWorkerIds.length} of {filteredWorkers.length} selected
              </span>
            </div>
          </div>

          {/* Search Bar */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search workers by name, company, or phone..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>

        {/* Workers List */}
        {filteredWorkers.length === 0 ? (
          <div className="text-center py-12 bg-gray-50 rounded-lg">
            <Users className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p className="text-gray-600 text-lg">
              {searchTerm ? 'No workers found matching your search' : 'No workers available'}
            </p>
            {searchTerm && (
              <button
                onClick={() => setSearchTerm('')}
                className="text-blue-600 hover:text-blue-700 text-sm mt-2"
              >
                Clear search
              </button>
            )}
          </div>
        ) : (
          <div className="space-y-2 max-h-96 overflow-y-auto">
            {filteredWorkers.map((worker) => (
              <div
                key={worker.id}
                className={`border rounded-lg p-4 cursor-pointer transition-all ${
                  selectedWorkerIds.includes(worker.id)
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => handleWorkerToggle(worker.id)}
              >
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 mt-1">
                    {selectedWorkerIds.includes(worker.id) ? (
                      <CheckSquare className="h-5 w-5 text-blue-600" />
                    ) : (
                      <Square className="h-5 w-5 text-gray-400" />
                    )}
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <h4 className="text-lg font-medium text-gray-900 truncate">
                        {worker.name}
                      </h4>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600 mt-1">
                      <p><strong>Company:</strong> {worker.company}</p>
                      <p><strong>Phone:</strong> {worker.phoneNumber}</p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Existing Attendees */}
        {toolbox.attendees && toolbox.attendees.length > 0 && (
          <div className="space-y-3">
            <h4 className="text-md font-medium text-gray-700">Current Attendees</h4>
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                {toolbox.attendees.map((attendee) => (
                  <div key={attendee.id} className="flex items-center space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-sm text-green-800">{attendee.name}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-between pt-6 border-t border-gray-200">
          <button
            onClick={() => navigate(`/sites/${siteId}/toolbox/fill/${toolboxId}`)}
            className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back to Details</span>
          </button>

          <button
            onClick={handleSubmit}
            disabled={selectedWorkerIds.length === 0 || submitting}
            className="flex items-center space-x-2 px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {submitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Recording...</span>
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4" />
                <span>Complete Toolbox</span>
              </>
            )}
          </button>
        </div>

        {/* Progress Indicator */}
        <div className="bg-gray-100 p-4 rounded-lg">
          <div className="flex items-center justify-between text-sm text-gray-600">
            <span>Step 3 of 3: Record Attendance</span>
            <div className="flex space-x-2">
              <div className="w-3 h-3 bg-green-600 rounded-full"></div>
              <div className="w-3 h-3 bg-green-600 rounded-full"></div>
              <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
            </div>
          </div>
        </div>
      </div>
    </FloatingCard>
  );
};

export default ToolboxAttendancePage;
