import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery } from '@apollo/client';
import { 
  Plus, 
  Clock, 
  Calendar, 
  User, 
  ArrowRight,
  CheckCircle,
  AlertCircle,
  PlayCircle,
  Users
} from 'lucide-react';
import FloatingCard from '../../components/layout/FloatingCard';
import { GET_NON_CLOSED_TOOLBOXES } from '../../graphql/queries';

interface Toolbox {
  id: number;
  startDateTime?: string;
  status: 'DRAFTED' | 'STARTED' | 'PENDING_ATTENDANCE' | 'CLOSED';
  emergencyProcedures: string[];
  toolboxTrainingTopics: string[];
  conductorId?: number;
  conductor?: {
    id: number;
    name: string;
  };
  jobs: {
    id: number;
    title: string;
  }[];
  draftedBy: {
    id: number;
    name: string;
  };
  draftedDate: string;
  createdAt: string;
}

const ToolboxDashboardPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId } = useParams<{ siteId: string }>();
  
  const { data, loading, error } = useQuery(GET_NON_CLOSED_TOOLBOXES);
  
  const toolboxes: Toolbox[] = data?.nonClosedToolbox || [];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'DRAFTED':
        return <AlertCircle className="h-5 w-5 text-yellow-500" />;
      case 'STARTED':
        return <PlayCircle className="h-5 w-5 text-blue-500" />;
      case 'PENDING_ATTENDANCE':
        return <Users className="h-5 w-5 text-orange-500" />;
      case 'CLOSED':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-500" />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'DRAFTED':
        return 'Drafted';
      case 'STARTED':
        return 'Started';
      case 'PENDING_ATTENDANCE':
        return 'Pending Attendance';
      case 'CLOSED':
        return 'Closed';
      default:
        return status;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFTED':
        return 'bg-yellow-100 text-yellow-800';
      case 'STARTED':
        return 'bg-blue-100 text-blue-800';
      case 'PENDING_ATTENDANCE':
        return 'bg-orange-100 text-orange-800';
      case 'CLOSED':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getContinueAction = (toolbox: Toolbox) => {
    switch (toolbox.status) {
      case 'DRAFTED':
        return () => navigate(`/sites/${siteId}/toolbox/photos/${toolbox.id}`);
      case 'STARTED':
        return () => navigate(`/sites/${siteId}/toolbox/fill/${toolbox.id}`);
      case 'PENDING_ATTENDANCE':
        return () => navigate(`/sites/${siteId}/toolbox/attendance/${toolbox.id}`);
      case 'CLOSED':
        return () => navigate(`/sites/${siteId}/toolbox/details/${toolbox.id}`);
      default:
        return () => navigate(`/sites/${siteId}/toolbox/details/${toolbox.id}`);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-GB', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  const breadcrumbs = [
    { name: 'Site Dashboard', path: `/sites/${siteId}/dashboard` },
    { name: 'Toolbox', path: `/sites/${siteId}/toolbox` }
  ];

  if (loading) {
    return (
      <FloatingCard title="Toolbox Dashboard" breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </FloatingCard>
    );
  }

  if (error) {
    return (
      <FloatingCard title="Toolbox Dashboard" breadcrumbs={breadcrumbs}>
        <div className="text-center py-8">
          <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
          <p className="text-red-600">Error loading toolboxes: {error.message}</p>
        </div>
      </FloatingCard>
    );
  }

  return (
    <FloatingCard title="Toolbox Dashboard" breadcrumbs={breadcrumbs}>
      <div className="space-y-6">
        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4">
          <button
            onClick={() => navigate(`/sites/${siteId}/toolbox/jobs`)}
            className="flex-1 bg-blue-600 text-white px-6 py-4 rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2 text-lg font-semibold"
          >
            <Plus className="h-6 w-6" />
            <span>Create Toolbox</span>
          </button>
          
          <button
            onClick={() => navigate(`/sites/${siteId}/toolbox/evening-toolbox`)}
            className="flex-1 bg-purple-600 text-white px-6 py-4 rounded-lg hover:bg-purple-700 transition-colors flex items-center justify-center space-x-2 text-lg font-semibold"
          >
            <Plus className="h-6 w-6" />
            <span>Create Evening Toolbox</span>
          </button>
        </div>

        {/* Toolbox List */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-gray-900">Active Toolboxes</h3>
          
          {toolboxes.length === 0 ? (
            <div className="text-center py-12 bg-gray-50 rounded-lg">
              <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p className="text-gray-600 text-lg">No active toolboxes</p>
              <p className="text-gray-500 text-sm mt-2">Create a new toolbox to get started</p>
            </div>
          ) : (
            <div className="grid gap-4">
              {toolboxes.map((toolbox) => (
                <div
                  key={toolbox.id}
                  className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer"
                  onClick={() => navigate(`/sites/${siteId}/toolbox/details/${toolbox.id}`)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-3">
                        {getStatusIcon(toolbox.status)}
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(toolbox.status)}`}>
                          {getStatusText(toolbox.status)}
                        </span>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4" />
                          <span>{formatDate(toolbox.startDateTime || toolbox.draftedDate)}</span>
                        </div>
                        
                        {toolbox.startDateTime && (
                          <div className="flex items-center space-x-2">
                            <Clock className="h-4 w-4" />
                            <span>{formatTime(toolbox.startDateTime)}</span>
                          </div>
                        )}
                        
                        <div className="flex items-center space-x-2">
                          <User className="h-4 w-4" />
                          <span>{toolbox.conductor?.name || toolbox.draftedBy.name}</span>
                        </div>
                      </div>
                      
                      {toolbox.jobs.length > 0 && (
                        <div className="mt-3">
                          <p className="text-sm text-gray-600">
                            Jobs: {toolbox.jobs.map(job => job.title).join(', ')}
                          </p>
                        </div>
                      )}
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          getContinueAction(toolbox)();
                        }}
                        className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors flex items-center space-x-2"
                      >
                        <span>Continue</span>
                        <ArrowRight className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </FloatingCard>
  );
};

export default ToolboxDashboardPage;
