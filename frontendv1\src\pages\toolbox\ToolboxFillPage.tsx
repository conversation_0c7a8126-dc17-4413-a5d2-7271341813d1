import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useMutation, useQuery } from '@apollo/client';
import { toast } from 'react-toastify';
import {
  Calendar,
  Clock,
  User,
  FileText,
  Plus,
  Trash2,
  Save,
  AlertTriangle,

  Check
} from 'lucide-react';
import FloatingCard from '../../components/layout/FloatingCard';
import { CREATE_TOOLBOX } from '../../graphql/mutations';
import { GET_TODAYS_JOB_RISK_ASSESSMENT, GET_ALL_WORKERS } from '../../graphql/queries';
import {
  CreateToolboxInput,
  CreateToolboxJobInput,
  CreateToolboxExistingHazardInput,
  CreateToolboxNewHazardInput,
  TodaysJobRiskAssessment,
  WorkerForAttendance,
  ToolboxFormData
} from '../../types/toolbox';

const ToolboxFillPage: React.FC = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<ToolboxFormData>({
    conductorId: 0,
    emergencyProcedures: '',
    toolboxTrainingTopics: '',
    jobs: {}
  });

  // State for managing inline editing
  const [editingHazard, setEditingHazard] = useState<{jobId: number, hazardId?: number, hazardIndex?: number} | null>(null);
  const [editingControl, setEditingControl] = useState<{jobId: number, hazardId?: number, hazardIndex?: number, controlId?: number, controlIndex?: number} | null>(null);
  const [newHazardText, setNewHazardText] = useState('');
  const [newControlText, setNewControlText] = useState('');

  // GraphQL hooks
  const { data: jobsData, loading: jobsLoading } = useQuery(GET_TODAYS_JOB_RISK_ASSESSMENT);
  const { data: workersData, loading: workersLoading } = useQuery(GET_ALL_WORKERS);
  const [createToolbox, { loading: creating }] = useMutation(CREATE_TOOLBOX);

  const jobs: TodaysJobRiskAssessment[] = jobsData?.todaysJobRiskAssessment || [];
  const workers: WorkerForAttendance[] = workersData?.allWorkers || [];

  // Get current date and time
  const currentDate = new Date().toLocaleDateString('en-GB'); // dd/mm/yyyy format
  const currentTime = new Date().toLocaleTimeString('en-GB', { 
    hour12: false, 
    hour: '2-digit', 
    minute: '2-digit' 
  }); // hh:mm format

  // Initialize form data when jobs are loaded
  useEffect(() => {
    if (jobs.length > 0) {
      const initialJobs: ToolboxFormData['jobs'] = {};
      jobs.forEach(job => {
        initialJobs[job.id] = {
          existingHazards: {},
          newHazards: []
        };
        
        // Initialize existing hazards
        job.hazards.forEach(hazard => {
          initialJobs[job.id].existingHazards[hazard.id] = {
            description: hazard.description,
            existingControlMeasures: {},
            newControlMeasures: []
          };
          
          // Initialize existing control measures
          hazard.controlMeasures.forEach(controlMeasure => {
            initialJobs[job.id].existingHazards[hazard.id].existingControlMeasures[controlMeasure.id] = {
              description: controlMeasure.description
            };
          });
        });
      });
      
      setFormData(prev => ({ ...prev, jobs: initialJobs }));
    }
  }, [jobs]);

  const addNewHazard = (jobId: number) => {
    setFormData(prev => ({
      ...prev,
      jobs: {
        ...prev.jobs,
        [jobId]: {
          ...prev.jobs[jobId],
          newHazards: [
            ...prev.jobs[jobId].newHazards,
            { description: '', controlMeasures: [{ description: '' }] }
          ]
        }
      }
    }));
  };

  const removeNewHazard = (jobId: number, hazardIndex: number) => {
    setFormData(prev => ({
      ...prev,
      jobs: {
        ...prev.jobs,
        [jobId]: {
          ...prev.jobs[jobId],
          newHazards: prev.jobs[jobId].newHazards.filter((_, index) => index !== hazardIndex)
        }
      }
    }));
  };

  const updateNewHazardDescription = (jobId: number, hazardIndex: number, description: string) => {
    setFormData(prev => ({
      ...prev,
      jobs: {
        ...prev.jobs,
        [jobId]: {
          ...prev.jobs[jobId],
          newHazards: prev.jobs[jobId].newHazards.map((hazard, index) =>
            index === hazardIndex ? { ...hazard, description } : hazard
          )
        }
      }
    }));
  };

  const addNewControlMeasureToNewHazard = (jobId: number, hazardIndex: number) => {
    setFormData(prev => ({
      ...prev,
      jobs: {
        ...prev.jobs,
        [jobId]: {
          ...prev.jobs[jobId],
          newHazards: prev.jobs[jobId].newHazards.map((hazard, index) =>
            index === hazardIndex 
              ? { ...hazard, controlMeasures: [...hazard.controlMeasures, { description: '' }] }
              : hazard
          )
        }
      }
    }));
  };

  const updateNewControlMeasureInNewHazard = (
    jobId: number, 
    hazardIndex: number, 
    controlIndex: number, 
    description: string
  ) => {
    setFormData(prev => ({
      ...prev,
      jobs: {
        ...prev.jobs,
        [jobId]: {
          ...prev.jobs[jobId],
          newHazards: prev.jobs[jobId].newHazards.map((hazard, hIndex) =>
            hIndex === hazardIndex 
              ? {
                  ...hazard,
                  controlMeasures: hazard.controlMeasures.map((control, cIndex) =>
                    cIndex === controlIndex ? { description } : control
                  )
                }
              : hazard
          )
        }
      }
    }));
  };

  const addNewControlMeasureToExistingHazard = (jobId: number, hazardId: number) => {
    setFormData(prev => ({
      ...prev,
      jobs: {
        ...prev.jobs,
        [jobId]: {
          ...prev.jobs[jobId],
          existingHazards: {
            ...prev.jobs[jobId].existingHazards,
            [hazardId]: {
              ...prev.jobs[jobId].existingHazards[hazardId],
              newControlMeasures: [
                ...prev.jobs[jobId].existingHazards[hazardId].newControlMeasures,
                { description: '' }
              ]
            }
          }
        }
      }
    }));
  };

  const updateNewControlMeasureInExistingHazard = (
    jobId: number, 
    hazardId: number, 
    controlIndex: number, 
    description: string
  ) => {
    setFormData(prev => ({
      ...prev,
      jobs: {
        ...prev.jobs,
        [jobId]: {
          ...prev.jobs[jobId],
          existingHazards: {
            ...prev.jobs[jobId].existingHazards,
            [hazardId]: {
              ...prev.jobs[jobId].existingHazards[hazardId],
              newControlMeasures: prev.jobs[jobId].existingHazards[hazardId].newControlMeasures.map((control, index) =>
                index === controlIndex ? { description } : control
              )
            }
          }
        }
      }
    }));
  };

  // Helper functions for table interactions
  const startAddingHazard = (jobId: number) => {
    setEditingHazard({ jobId });
    setNewHazardText('');
  };

  const confirmAddHazard = (jobId: number) => {
    if (newHazardText.trim()) {
      addNewHazard(jobId);
      const newHazardIndex = formData.jobs[jobId]?.newHazards.length || 0;
      updateNewHazardDescription(jobId, newHazardIndex, newHazardText.trim());
      setEditingHazard(null);
      setNewHazardText('');
    }
  };

  const cancelAddHazard = () => {
    setEditingHazard(null);
    setNewHazardText('');
  };

  const startAddingControl = (jobId: number, hazardId?: number, hazardIndex?: number) => {
    setEditingControl({ jobId, hazardId, hazardIndex });
    setNewControlText('');
  };

  const confirmAddControl = (jobId: number, hazardId?: number, hazardIndex?: number) => {
    if (newControlText.trim()) {
      if (hazardId !== undefined) {
        // Adding to existing hazard
        addNewControlMeasureToExistingHazard(jobId, hazardId);
        const newControlIndex = formData.jobs[jobId]?.existingHazards[hazardId]?.newControlMeasures.length || 0;
        updateNewControlMeasureInExistingHazard(jobId, hazardId, newControlIndex, newControlText.trim());
      } else if (hazardIndex !== undefined) {
        // Adding to new hazard
        addNewControlMeasureToNewHazard(jobId, hazardIndex);
        const newControlIndex = formData.jobs[jobId]?.newHazards[hazardIndex]?.controlMeasures.length || 0;
        updateNewControlMeasureInNewHazard(jobId, hazardIndex, newControlIndex - 1, newControlText.trim());
      }
      setEditingControl(null);
      setNewControlText('');
    }
  };

  const cancelAddControl = () => {
    setEditingControl(null);
    setNewControlText('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.conductorId) {
      toast.error('Please select a conductor');
      return;
    }

    if (!formData.emergencyProcedures.trim()) {
      toast.error('Please fill in emergency procedures');
      return;
    }

    if (!formData.toolboxTrainingTopics.trim()) {
      toast.error('Please fill in toolbox training topics');
      return;
    }

    try {
      // Transform form data to GraphQL input format
      const jobInputs: CreateToolboxJobInput[] = Object.entries(formData.jobs).map(([jobIdStr, jobData]) => {
        const jobId = parseInt(jobIdStr);
        
        const existingHazards: CreateToolboxExistingHazardInput[] = Object.entries(jobData.existingHazards).map(([hazardIdStr, hazardData]) => ({
          id: parseInt(hazardIdStr),
          description: hazardData.description,
          existingControlMeasures: Object.entries(hazardData.existingControlMeasures).map(([controlIdStr, controlData]) => ({
            id: parseInt(controlIdStr),
            description: controlData.description
          })),
          newControlMeasures: hazardData.newControlMeasures.filter(control => control.description.trim())
        }));

        const newHazards: CreateToolboxNewHazardInput[] = jobData.newHazards
          .filter(hazard => hazard.description.trim())
          .map(hazard => ({
            description: hazard.description,
            controlMeasures: hazard.controlMeasures.filter(control => control.description.trim())
          }));

        return {
          jobId,
          existingHazards,
          newHazards
        };
      });

      const input: CreateToolboxInput = {
        conductorId: formData.conductorId,
        jobs: jobInputs,
        emergencyProcedures: formData.emergencyProcedures,
        toolboxTrainingTopics: formData.toolboxTrainingTopics
      };

      const result = await createToolbox({ variables: { input } });
      
      if (result.data?.createToolbox) {
        toast.success('Toolbox created successfully!');
        navigate(`/sites/:siteId/toolbox/attendance?toolboxId=${result.data.createToolbox.id}`);
      }
    } catch (error) {
      console.error('Error creating toolbox:', error);
      toast.error('Failed to create toolbox. Please try again.');
    }
  };

  const breadcrumbs = [
    { name: 'Toolbox', path: '/toolbox' },
    { name: 'Fill Toolbox', path: '/toolbox/fill' }
  ];

  if (jobsLoading || workersLoading) {
    return (
      <FloatingCard title="Fill Toolbox" breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </FloatingCard>
    );
  }

  return (
    <FloatingCard title="Fill Toolbox" breadcrumbs={breadcrumbs}>
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Header Section */}
        <div className="bg-gray-50 p-6 rounded-lg">
          <h3 className="text-lg font-semibold mb-4 flex items-center">
            <FileText className="h-5 w-5 mr-2" />
            Toolbox Meeting Details
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <Calendar className="h-4 w-4 inline mr-1" />
                Date
              </label>
              <input
                type="text"
                value={currentDate}
                readOnly
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <Clock className="h-4 w-4 inline mr-1" />
                Time
              </label>
              <input
                type="text"
                value={currentTime}
                readOnly
                className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                <User className="h-4 w-4 inline mr-1" />
                Conducted By
              </label>
              <select
                value={formData.conductorId}
                onChange={(e) => setFormData(prev => ({ ...prev, conductorId: parseInt(e.target.value) }))}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                required
              >
                <option value={0}>Select Conductor</option>
                {workers.map(worker => (
                  <option key={worker.id} value={worker.id}>
                    {worker.name}
                  </option>
                ))}
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Signature
              </label>
              <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-100 text-gray-500 text-sm">
                (Reserved for signature)
              </div>
            </div>
          </div>
        </div>

        {/* Jobs and Risk Assessment Section */}
        <div className="space-y-6">
          <h3 className="text-lg font-semibold flex items-center">
            <AlertTriangle className="h-5 w-5 mr-2 text-orange-500" />
            Jobs and Risk Assessment
          </h3>

          {jobs.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-gray-400" />
              <p>No jobs scheduled for today</p>
            </div>
          ) : (
            jobs.map(job => (
              <div key={job.id} className="border border-gray-200 rounded-lg overflow-hidden">
                <div className="bg-blue-50 px-6 py-3 border-b border-gray-200">
                  <h4 className="text-lg font-medium text-blue-600">{job.title}</h4>
                </div>

                <div className="overflow-x-auto">
                  <table className="min-w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                          Job
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-200">
                          Hazard
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          Control Measure
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-gray-200">
                      {/* Existing Hazards */}
                      {job.hazards.map((hazard, hazardIndex) => (
                        <React.Fragment key={hazard.id}>
                          {hazard.controlMeasures.map((control, controlIndex) => (
                            <tr key={`${hazard.id}-${control.id}`} className="hover:bg-gray-50">
                              {/* Job column - only show for first control measure of first hazard */}
                              {hazardIndex === 0 && controlIndex === 0 && (
                                <td
                                  className="px-6 py-4 text-sm font-medium text-gray-900 border-r border-gray-200 relative"
                                  rowSpan={job.hazards.reduce((total, h) => total + Math.max(h.controlMeasures.length, 1), 0) +
                                    (formData.jobs[job.id]?.newHazards.reduce((total, nh) => total + Math.max(nh.controlMeasures.length, 1), 0) || 0)}
                                >
                                  <div className="flex items-center justify-between">
                                    <span>{job.title}</span>
                                    <button
                                      type="button"
                                      onClick={() => startAddingHazard(job.id)}
                                      className="ml-2 p-1 text-blue-600 hover:bg-blue-50 rounded"
                                      title="Add new hazard"
                                    >
                                      <Plus className="h-4 w-4" />
                                    </button>
                                  </div>
                                </td>
                              )}

                              {/* Hazard column - only show for first control measure of each hazard */}
                              {controlIndex === 0 && (
                                <td
                                  className="px-6 py-4 text-sm text-gray-900 border-r border-gray-200 relative"
                                  rowSpan={Math.max(hazard.controlMeasures.length, 1)}
                                >
                                  <div className="flex items-center justify-between">
                                    <span>{hazard.description}</span>
                                    <button
                                      type="button"
                                      onClick={() => startAddingControl(job.id, hazard.id)}
                                      className="ml-2 p-1 text-green-600 hover:bg-green-50 rounded"
                                      title="Add control measure"
                                    >
                                      <Plus className="h-4 w-4" />
                                    </button>
                                  </div>
                                </td>
                              )}

                              {/* Control Measure column */}
                              <td className="px-6 py-4 text-sm text-gray-900">
                                <input
                                  type="text"
                                  value={formData.jobs[job.id]?.existingHazards[hazard.id]?.existingControlMeasures[control.id]?.description || control.description}
                                  onChange={(e) => {
                                    const newFormData = { ...formData };
                                    if (!newFormData.jobs[job.id]) newFormData.jobs[job.id] = { existingHazards: {}, newHazards: [] };
                                    if (!newFormData.jobs[job.id].existingHazards[hazard.id]) {
                                      newFormData.jobs[job.id].existingHazards[hazard.id] = {
                                        description: hazard.description,
                                        existingControlMeasures: {},
                                        newControlMeasures: []
                                      };
                                    }
                                    newFormData.jobs[job.id].existingHazards[hazard.id].existingControlMeasures[control.id] = {
                                      description: e.target.value
                                    };
                                    setFormData(newFormData);
                                  }}
                                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                  placeholder="Control measure description"
                                />
                              </td>
                            </tr>
                          ))}

                          {/* New Control Measures for Existing Hazard */}
                          {formData.jobs[job.id]?.existingHazards[hazard.id]?.newControlMeasures.map((control, controlIndex) => (
                            <tr key={`new-control-${hazard.id}-${controlIndex}`} className="hover:bg-gray-50 bg-green-50">
                              <td className="px-6 py-4 text-sm text-gray-900">
                                <div className="flex items-center">
                                  <input
                                    type="text"
                                    value={control.description}
                                    onChange={(e) => updateNewControlMeasureInExistingHazard(job.id, hazard.id, controlIndex, e.target.value)}
                                    className="flex-1 px-3 py-2 border border-green-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                    placeholder="New control measure"
                                  />
                                  <button
                                    type="button"
                                    onClick={() => {
                                      const newFormData = { ...formData };
                                      newFormData.jobs[job.id].existingHazards[hazard.id].newControlMeasures =
                                        newFormData.jobs[job.id].existingHazards[hazard.id].newControlMeasures.filter((_, i) => i !== controlIndex);
                                      setFormData(newFormData);
                                    }}
                                    className="ml-2 p-2 text-red-600 hover:bg-red-50 rounded-md"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </button>
                                </div>
                              </td>
                            </tr>
                          ))}
                        </React.Fragment>
                      ))}

                      {/* New Hazards */}
                      {formData.jobs[job.id]?.newHazards.map((hazard, hazardIndex) => (
                        <React.Fragment key={`new-hazard-${hazardIndex}`}>
                          {hazard.controlMeasures.map((control, controlIndex) => (
                            <tr key={`new-hazard-${hazardIndex}-control-${controlIndex}`} className="hover:bg-gray-50 bg-blue-50">
                              {/* Hazard column - only show for first control measure */}
                              {controlIndex === 0 && (
                                <td
                                  className="px-6 py-4 text-sm text-gray-900 border-r border-gray-200 relative"
                                  rowSpan={Math.max(hazard.controlMeasures.length, 1)}
                                >
                                  <div className="flex items-center justify-between">
                                    <input
                                      type="text"
                                      value={hazard.description}
                                      onChange={(e) => updateNewHazardDescription(job.id, hazardIndex, e.target.value)}
                                      className="flex-1 px-3 py-2 border border-blue-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                      placeholder="New hazard description"
                                    />
                                    <div className="flex ml-2">
                                      <button
                                        type="button"
                                        onClick={() => startAddingControl(job.id, undefined, hazardIndex)}
                                        className="p-1 text-green-600 hover:bg-green-50 rounded mr-1"
                                        title="Add control measure"
                                      >
                                        <Plus className="h-4 w-4" />
                                      </button>
                                      <button
                                        type="button"
                                        onClick={() => removeNewHazard(job.id, hazardIndex)}
                                        className="p-1 text-red-600 hover:bg-red-50 rounded"
                                        title="Remove hazard"
                                      >
                                        <Trash2 className="h-4 w-4" />
                                      </button>
                                    </div>
                                  </div>
                                </td>
                              )}

                              {/* Control Measure column */}
                              <td className="px-6 py-4 text-sm text-gray-900">
                                <div className="flex items-center">
                                  <input
                                    type="text"
                                    value={control.description}
                                    onChange={(e) => updateNewControlMeasureInNewHazard(job.id, hazardIndex, controlIndex, e.target.value)}
                                    className="flex-1 px-3 py-2 border border-blue-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                    placeholder="Control measure description"
                                  />
                                  <button
                                    type="button"
                                    onClick={() => {
                                      const newFormData = { ...formData };
                                      newFormData.jobs[job.id].newHazards[hazardIndex].controlMeasures =
                                        newFormData.jobs[job.id].newHazards[hazardIndex].controlMeasures.filter((_, i) => i !== controlIndex);
                                      setFormData(newFormData);
                                    }}
                                    className="ml-2 p-2 text-red-600 hover:bg-red-50 rounded-md"
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </button>
                                </div>
                              </td>
                            </tr>
                          ))}
                        </React.Fragment>
                      ))}

                      {/* Inline editing rows */}
                      {editingHazard?.jobId === job.id && (
                        <tr className="bg-yellow-50">
                          <td className="px-6 py-4 text-sm text-gray-900 border-r border-gray-200">
                            <div className="flex items-center">
                              <input
                                type="text"
                                value={newHazardText}
                                onChange={(e) => setNewHazardText(e.target.value)}
                                className="flex-1 px-3 py-2 border border-yellow-300 rounded-md focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
                                placeholder="Enter new hazard description"
                                autoFocus
                              />
                              <div className="flex ml-2">
                                <button
                                  type="button"
                                  onClick={() => confirmAddHazard(job.id)}
                                  className="p-2 text-green-600 hover:bg-green-50 rounded-md mr-1"
                                  title="Confirm"
                                >
                                  <Check className="h-4 w-4" />
                                </button>
                                <button
                                  type="button"
                                  onClick={cancelAddHazard}
                                  className="p-2 text-red-600 hover:bg-red-50 rounded-md"
                                  title="Cancel"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 text-sm text-gray-500">
                            Enter hazard first
                          </td>
                        </tr>
                      )}

                      {editingControl?.jobId === job.id && (
                        <tr className="bg-yellow-50">
                          {editingControl.hazardId === undefined && editingControl.hazardIndex === undefined && (
                            <td className="px-6 py-4 text-sm text-gray-500 border-r border-gray-200">
                              Select a hazard first
                            </td>
                          )}
                          <td className="px-6 py-4 text-sm text-gray-900">
                            <div className="flex items-center">
                              <input
                                type="text"
                                value={newControlText}
                                onChange={(e) => setNewControlText(e.target.value)}
                                className="flex-1 px-3 py-2 border border-yellow-300 rounded-md focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500"
                                placeholder="Enter new control measure"
                                autoFocus
                              />
                              <div className="flex ml-2">
                                <button
                                  type="button"
                                  onClick={() => confirmAddControl(job.id, editingControl.hazardId, editingControl.hazardIndex)}
                                  className="p-2 text-green-600 hover:bg-green-50 rounded-md mr-1"
                                  title="Confirm"
                                >
                                  <Check className="h-4 w-4" />
                                </button>
                                <button
                                  type="button"
                                  onClick={cancelAddControl}
                                  className="p-2 text-red-600 hover:bg-red-50 rounded-md"
                                  title="Cancel"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </button>
                              </div>
                            </div>
                          </td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Footer Section */}
        <div className="bg-gray-50 p-6 rounded-lg space-y-4">
          <h3 className="text-lg font-semibold mb-4">Additional Information</h3>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Emergency Procedure Specific to the Task of the Day
            </label>
            <textarea
              value={formData.emergencyProcedures}
              onChange={(e) => setFormData(prev => ({ ...prev, emergencyProcedures: e.target.value }))}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Describe emergency procedures specific to today's tasks..."
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Toolbox Training Topic
            </label>
            <textarea
              value={formData.toolboxTrainingTopics}
              onChange={(e) => setFormData(prev => ({ ...prev, toolboxTrainingTopics: e.target.value }))}
              rows={4}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Describe the training topics covered in this toolbox meeting..."
              required
            />
          </div>
        </div>

        {/* Submit Button */}
        <div className="flex justify-end space-x-4">
          <button
            type="button"
            onClick={() => navigate('/sites/:siteId/toolbox')}
            className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            type="submit"
            disabled={creating}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {creating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Creating...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Create Toolbox
              </>
            )}
          </button>
        </div>
      </form>
    </FloatingCard>
  );
};

export default ToolboxFillPage;
