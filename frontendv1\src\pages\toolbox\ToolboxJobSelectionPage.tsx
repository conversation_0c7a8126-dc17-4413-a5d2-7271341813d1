import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery, useMutation } from '@apollo/client';
import { toast } from 'react-toastify';
import { 
  CheckSquare, 
  Square, 
  MapPin, 
  Calendar, 
  Clock,
  ArrowLeft,
  Save,
  AlertCircle
} from 'lucide-react';
import FloatingCard from '../../components/layout/FloatingCard';
import { GET_VALID_JOBS } from '../../graphql/queries';
import { CREATE_TOOLBOX } from '../../graphql/mutations';

interface Job {
  id: number;
  title: string;
  description?: string;
  location?: string;
  status: string;
  startDate?: string;
  dueDate?: string;
}

const ToolboxJobSelectionPage: React.FC = () => {
  const navigate = useNavigate();
  const { siteId } = useParams<{ siteId: string }>();
  const [selectedJobIds, setSelectedJobIds] = useState<number[]>([]);
  
  const { data, loading, error } = useQuery(GET_VALID_JOBS);
  const [createToolbox, { loading: creating }] = useMutation(CREATE_TOOLBOX);
  
  const jobs: Job[] = data?.validJobs || [];

  const handleJobToggle = (jobId: number) => {
    setSelectedJobIds(prev => 
      prev.includes(jobId) 
        ? prev.filter(id => id !== jobId)
        : [...prev, jobId]
    );
  };

  const handleSelectAll = () => {
    if (selectedJobIds.length === jobs.length) {
      setSelectedJobIds([]);
    } else {
      setSelectedJobIds(jobs.map(job => job.id));
    }
  };

  const handleSubmit = async () => {
    if (selectedJobIds.length === 0) {
      toast.error('Please select at least one job');
      return;
    }

    try {
      const result = await createToolbox({
        variables: {
          input: {
            jobIds: selectedJobIds,
            draftedById: 1 // Default as specified in requirements
          }
        }
      });

      if (result.data?.createToolbox) {
        toast.success('Toolbox created successfully!');
        navigate(`/sites/${siteId}/toolbox`);
      }
    } catch (error) {
      console.error('Error creating toolbox:', error);
      toast.error('Failed to create toolbox. Please try again.');
    }
  };

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString('en-GB', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'requested':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const breadcrumbs = [
    { name: 'Site Dashboard', path: `/sites/${siteId}/dashboard` },
    { name: 'Toolbox', path: `/sites/${siteId}/toolbox` },
    { name: 'Select Jobs', path: `/sites/${siteId}/toolbox/jobs` }
  ];

  if (loading) {
    return (
      <FloatingCard title="Select Jobs for Toolbox" breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </FloatingCard>
    );
  }

  if (error) {
    return (
      <FloatingCard title="Select Jobs for Toolbox" breadcrumbs={breadcrumbs}>
        <div className="text-center py-8">
          <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
          <p className="text-red-600">Error loading jobs: {error.message}</p>
        </div>
      </FloatingCard>
    );
  }

  return (
    <FloatingCard title="Select Jobs for Toolbox" breadcrumbs={breadcrumbs}>
      <div className="space-y-6">
        {/* Header with selection info */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={handleSelectAll}
              className="flex items-center space-x-2 text-blue-600 hover:text-blue-700"
            >
              {selectedJobIds.length === jobs.length ? (
                <CheckSquare className="h-5 w-5" />
              ) : (
                <Square className="h-5 w-5" />
              )}
              <span>Select All</span>
            </button>
            <span className="text-sm text-gray-600">
              {selectedJobIds.length} of {jobs.length} jobs selected
            </span>
          </div>
        </div>

        {/* Jobs List */}
        {jobs.length === 0 ? (
          <div className="text-center py-12 bg-gray-50 rounded-lg">
            <AlertCircle className="h-12 w-12 mx-auto mb-4 text-gray-400" />
            <p className="text-gray-600 text-lg">No valid jobs available</p>
            <p className="text-gray-500 text-sm mt-2">Please check back later or contact your supervisor</p>
          </div>
        ) : (
          <div className="space-y-3">
            {jobs.map((job) => (
              <div
                key={job.id}
                className={`border rounded-lg p-4 cursor-pointer transition-all ${
                  selectedJobIds.includes(job.id)
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => handleJobToggle(job.id)}
              >
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 mt-1">
                    {selectedJobIds.includes(job.id) ? (
                      <CheckSquare className="h-5 w-5 text-blue-600" />
                    ) : (
                      <Square className="h-5 w-5 text-gray-400" />
                    )}
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between mb-2">
                      <h3 className="text-lg font-medium text-gray-900 truncate">
                        {job.title}
                      </h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(job.status)}`}>
                        {job.status}
                      </span>
                    </div>
                    
                    {job.description && (
                      <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                        {job.description}
                      </p>
                    )}
                    
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-500">
                      {job.location && (
                        <div className="flex items-center space-x-1">
                          <MapPin className="h-4 w-4" />
                          <span>{job.location}</span>
                        </div>
                      )}
                      
                      <div className="flex items-center space-x-1">
                        <Calendar className="h-4 w-4" />
                        <span>Start: {formatDate(job.startDate)}</span>
                      </div>
                      
                      <div className="flex items-center space-x-1">
                        <Clock className="h-4 w-4" />
                        <span>Due: {formatDate(job.dueDate)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-between pt-6 border-t border-gray-200">
          <button
            onClick={() => navigate(`/sites/${siteId}/toolbox`)}
            className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back</span>
          </button>
          
          <button
            onClick={handleSubmit}
            disabled={selectedJobIds.length === 0 || creating}
            className="flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {creating ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Creating...</span>
              </>
            ) : (
              <>
                <Save className="h-4 w-4" />
                <span>Create Toolbox</span>
              </>
            )}
          </button>
        </div>
      </div>
    </FloatingCard>
  );
};

export default ToolboxJobSelectionPage;
