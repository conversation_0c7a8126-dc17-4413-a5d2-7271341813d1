import React, { useEffect, useMemo, useState } from 'react';
import { Link, useParams, useNavigate } from 'react-router-dom';
import { Search, Filter, CheckCircle, XCircle, Clock, AlertTriangle, ArrowLeft, Upload } from 'lucide-react';
import FloatingCard from '../../components/layout/FloatingCard';
import UniversalFilterModal, { FilterValues } from '../../components/common/UniversalFilterModal';
import ActiveFiltersBar from '../../components/common/ActiveFiltersBar';
import { mockCompanyWorkers } from '../../data/workers';

const SiteImportWorkersPage: React.FC = () => {
  const { siteId } = useParams<{ siteId: string }>();
  const navigate = useNavigate();

  // Toolbar and filters
  const [searchTerm, setSearchTerm] = useState('');
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [activeFilters, setActiveFilters] = useState<FilterValues>({});
  const [selectedIds, setSelectedIds] = useState<number[]>([]);

  // Persist selection, filters and search per site using sessionStorage
  const storageSelKey = `siteImportSelection:${siteId || 'default'}`;
  const storageFiltersKey = `siteImportFilters:${siteId || 'default'}`;
  const storageSearchKey = `siteImportSearch:${siteId || 'default'}`;

  // Restore on mount
  useEffect(() => {
    try {
      const rawSel = sessionStorage.getItem(storageSelKey);
      if (rawSel) {
        const parsed = JSON.parse(rawSel);
        if (Array.isArray(parsed)) {
          setSelectedIds(parsed.filter((n: any) => typeof n === 'number'));
        }
      }
      const rawFilters = sessionStorage.getItem(storageFiltersKey);
      if (rawFilters) {
        const parsed = JSON.parse(rawFilters);
        if (parsed && typeof parsed === 'object') setActiveFilters(parsed);
      }
      const rawSearch = sessionStorage.getItem(storageSearchKey);
      if (rawSearch) setSearchTerm(rawSearch);
    } catch (e) {
      // no-op
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [siteId]);

  // Persist on change
  useEffect(() => {
    try { sessionStorage.setItem(storageSelKey, JSON.stringify(selectedIds)); } catch {}
  }, [selectedIds, storageSelKey]);
  useEffect(() => {
    try { sessionStorage.setItem(storageFiltersKey, JSON.stringify(activeFilters)); } catch {}
  }, [activeFilters, storageFiltersKey]);
  useEffect(() => {
    try { sessionStorage.setItem(storageSearchKey, searchTerm); } catch {}
  }, [searchTerm, storageSearchKey]);

  // Basic filter config (trade and availability)
  const filterConfig = useMemo(() => {
    const uniqueTrades = new Set<string>();
    mockCompanyWorkers.forEach(w => w.trades.forEach(t => uniqueTrades.add(t.name)));

    return [
      {
        id: 'trade',
        label: 'Trade',
        type: 'multiselect' as const,
        options: Array.from(uniqueTrades).map(trade => ({
          value: trade,
          label: trade,
          count: mockCompanyWorkers.filter(w => w.trades.some(t => t.name === trade)).length,
        })),
      },
      {
        id: 'availability',
        label: 'Availability',
        type: 'dropdown' as const,
        placeholder: 'Select availability',
        options: [
          { value: 'available', label: 'Available (No Assignment)', count: mockCompanyWorkers.filter(w => !w.currentSiteId).length },
          { value: 'assigned', label: 'Assigned to any Site', count: mockCompanyWorkers.filter(w => !!w.currentSiteId).length },
        ],
      },
    ];
  }, []);

  // Compute filtered workers from company list
  const filteredWorkers = useMemo(() => {
    let list = mockCompanyWorkers;

    // Search by name, employee no, ID
    const term = searchTerm.trim().toLowerCase();
    if (term) {
      list = list.filter(w =>
        w.name.toLowerCase().includes(term) ||
        w.employeeNumber.toLowerCase().includes(term) ||
        w.nationalId.includes(term)
      );
    }

    // Trade filter (multiselect)
    if (activeFilters.trade && Array.isArray(activeFilters.trade) && activeFilters.trade.length > 0) {
      list = list.filter(w => activeFilters.trade!.some((t: string) => w.trades.some(tt => tt.name === t)));
    }

    // Availability filter
    if (activeFilters.availability === 'available') {
      list = list.filter(w => !w.currentSiteId);
    } else if (activeFilters.availability === 'assigned') {
      list = list.filter(w => !!w.currentSiteId);
    }

    return list;
  }, [searchTerm, activeFilters]);

  // Sort so that selected appear on top (stable order)
  const displayWorkers = useMemo(() => {
    const selectedSet = new Set(selectedIds);
    return [...filteredWorkers].sort((a, b) => {
      const aSel = selectedSet.has(a.id);
      const bSel = selectedSet.has(b.id);
      if (aSel && !bSel) return -1;
      if (!aSel && bSel) return 1;
      return 0; // keep existing relative order
    });
  }, [filteredWorkers, selectedIds]);

  const isAllSelected = displayWorkers.length > 0 && selectedIds.length === displayWorkers.length;
  const toggleSelectAll = () => {
    if (isAllSelected) setSelectedIds([]);
    else setSelectedIds(displayWorkers.map(w => w.id));
  };
  const toggleSelectOne = (id: number) => {
    setSelectedIds(prev => prev.includes(id) ? prev.filter(x => x !== id) : [...prev, id]);
  };

  const activeFilterCount = Object.values(activeFilters).filter(value => {
    if (Array.isArray(value)) return value.length > 0;
    if (typeof value === 'object' && value !== null) {
      return Object.values(value).some(v => v !== '' && v !== null);
    }
    return value !== '' && value !== null && value !== false;
  }).length;

  const breadcrumbs = [
    { name: 'Dashboard', path: '/' },
    { name: `Site ${siteId}`, path: `/sites/${siteId}/dashboard` },
    { name: 'Workers', path: `/sites/${siteId}/workers` },
    { name: 'Import', path: `/sites/${siteId}/workers/import` },
  ];

  const handleImportSelected = () => {
    // Placeholder: in real app, call API to attach selected workers to the site
    alert(`Importing ${selectedIds.length} workers to site ${siteId} (placeholder)`);
    navigate(`/sites/${siteId}/workers`);
  };

  const complianceIcon = (status: string) => {
    switch (status) {
      case 'compliant': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'pending_training': return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'non_compliant': return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'expired': return <XCircle className="w-4 h-4 text-red-600" />;
      default: return <AlertTriangle className="w-4 h-4 text-gray-400" />;
    }
  };

  return (
    <FloatingCard title={`Import Workers`} breadcrumbs={breadcrumbs}>
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        {/* Toolbar */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-3">
          <div className="flex items-center gap-2">
            <button
              onClick={() => navigate(-1)}
              className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm text-gray-700 bg-white hover:bg-gray-50"
            >
              <ArrowLeft className="h-4 w-4 mr-1" /> Back
            </button>
          </div>
          <div className="flex items-center w-full md:w-auto gap-3">
            <div className="relative flex-1 md:flex-initial md:w-80">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-gray-500" />
              </div>
              <input
                type="text"
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm"
                placeholder="Search company workers..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <button
              onClick={() => setIsFilterOpen(true)}
              className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-green-500 focus:border-green-500"
            >
              <Filter className="h-4 w-4 mr-2" /> Filters
              {activeFilterCount > 0 && (
                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  {activeFilterCount}
                </span>
              )}
            </button>
            <button
              onClick={handleImportSelected}
              disabled={selectedIds.length === 0}
              className={`inline-flex items-center px-4 py-2 rounded-md shadow-sm text-sm font-medium text-white ${selectedIds.length === 0 ? 'bg-gray-300 cursor-not-allowed' : 'bg-green-600 hover:bg-green-700'}`}
            >
              <Upload className="h-4 w-4 mr-2" /> Import Selected
            </button>
          </div>
        </div>

        {/* Active Filters */}
        <ActiveFiltersBar
          values={activeFilters}
          config={filterConfig}
          onRemove={(filterId) => {
            const newFilters = { ...activeFilters } as Record<string, any>;
            delete newFilters[filterId];
            setActiveFilters(newFilters);
          }}
          onClear={() => setActiveFilters({})}
        />

        {/* Table */}
        <div className="bg-white shadow overflow-hidden border border-gray-200 rounded-lg mt-4">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left">
                  <input
                    type="checkbox"
                    checked={isAllSelected}
                    onChange={toggleSelectAll}
                    aria-label="Select all"
                    className="h-4 w-4 text-green-600 border-gray-300 rounded"
                  />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Worker</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Trade(s)</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Compliance</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {displayWorkers.map(worker => (
                <tr key={worker.id} className="hover:bg-gray-50">
                  <td className="px-4 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      checked={selectedIds.includes(worker.id)}
                      onChange={() => toggleSelectOne(worker.id)}
                      aria-label={`Select ${worker.name}`}
                      className="h-4 w-4 text-green-600 border-gray-300 rounded"
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <img
                          className="h-10 w-10 rounded-full"
                          src={worker.photoUrl || `https://ui-avatars.com/api/?name=${encodeURIComponent(worker.name)}&background=3B82F6&color=fff`}
                          alt={worker.name}
                        />
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          <Link to={`/sites/${siteId}/workers/${worker.id}`} className="hover:text-green-500">
                            {worker.name}
                          </Link>
                        </div>
                        <div className="text-xs text-gray-500">{worker.employeeNumber}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {worker.trades.map(t => t.name).join(', ')}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                      {complianceIcon(worker.complianceStatus)}
                      <span className="ml-1 capitalize">{String(worker.complianceStatus).replace('_', ' ')}</span>
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex items-center justify-end space-x-2">
                      <Link
                        to={`/sites/${siteId}/workers/${worker.id}`}
                        className="px-3 py-1 text-xs border border-blue-300 text-blue-700 rounded hover:bg-blue-50 transition-colors"
                        style={{ borderRadius: '5px' }}
                      >
                        View
                      </Link>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          {displayWorkers.length === 0 && (
            <div className="px-6 py-12 text-center text-sm text-gray-600">No company workers match your filters.</div>
          )}
        </div>
      </div>

      {/* Filters Modal */}
      <UniversalFilterModal
        isOpen={isFilterOpen}
        onClose={() => setIsFilterOpen(false)}
        title="Filter Company Workers"
        filters={filterConfig}
        initialValues={activeFilters}
        onApplyFilters={(values) => setActiveFilters(values)}
        onClearFilters={() => setActiveFilters({})}
        size="xl"
      />
    </FloatingCard>
  );
};

export default SiteImportWorkersPage;
