import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import FloatingCard from '../../components/layout/FloatingCard';
import UpdateWorkerForm from '../../components/workers/UpdateWorkerForm';

const UpdateWorkerPage: React.FC = () => {
  const { siteId, workerId } = useParams<{ siteId: string; workerId: string }>();
  const navigate = useNavigate();

  const siteName = `Site ${siteId}`;

  const handleCancel = () => { navigate(`/sites/${siteId}/workers/${workerId}`); };
  const handleDelete = () => { navigate(`/sites/${siteId}/workers`); };

  const breadcrumbs = [
    { name: 'Dashboard', path: '/' },
    { name: siteName, path: `/sites/${siteId}/dashboard` },
    { name: 'Workers', path: `/sites/${siteId}/workers` },
    { name: 'Edit Worker', path: `/sites/${siteId}/workers/${workerId}/edit` },
  ];

  return (
    <FloatingCard title="Edit Worker" breadcrumbs={breadcrumbs}>
      <div className="mb-4 p-3 bg-yellow-50 border-l-4 border-yellow-400 text-yellow-800 rounded">
        <strong>Note:</strong> Profile photo and documents cannot be updated here. To update the profile photo, go to the worker profile page. Document update is not yet supported.
      </div>
      <UpdateWorkerForm workerId={parseInt(workerId || '1')} onCancel={handleCancel} onDelete={handleDelete} />
    </FloatingCard>
  );
};

export default UpdateWorkerPage;
