import { Apollo<PERSON><PERSON>, InMemoryCache, createHttpLink, from } from '@apollo/client';
import { setContext } from '@apollo/client/link/context';
import { onError } from '@apollo/client/link/error';
import {
  User,
  LoginCredentials,
  RegisterData,
  AuthSession,
  LoginPayload,
  UserSession,
  UpdateUserRequest,
  ChangePasswordRequest,
  ResetPasswordRequest,
  CreateUserRequest,
  PermissionLevel
} from '../types/auth';
import {
  LOGIN_MUTATION,
  REFRESH_TOKEN_MUTATION,
  LOGOUT_MUTATION,
  LOGOUT_ALL_SESSIONS_MUTATION,
  CREATE_USER_MUTATION,
  UPDATE_USER_MUTATION,
  DELETE_USER_MUTATION,
  CHANGE_PASSWORD_MUTATION,
  // RESET_PASSWORD_MUTATION,
  REQUEST_PASSWORD_RESET_MUTATION,
  LOCK_USER_MUTATION,
  UNLOCK_USER_MUTATION
} from '../graphql/mutations';
import {
  ME_QUERY,
  GET_ACTIVE_SESSIONS_QUERY,
  // VALIDATE_SESSION_QUERY,
  GET_USERS_QUERY,
  GET_USER_BY_ID_QUERY,
  // GET_ROLES_QUERY,
  // GET_USERS_BY_ROLE_QUERY
} from '../graphql/queries';

class GraphQLAuthService {
  private static instance: GraphQLAuthService;
  private readonly SESSION_KEY = 'auth_session';
  private readonly TOKEN_KEY = 'access_token';
  private readonly REFRESH_TOKEN_KEY = 'refresh_token';
  private client: ApolloClient<any>;

  constructor() {
    this.client = this.createApolloClient();
  }

  static getInstance(): GraphQLAuthService {
    if (!GraphQLAuthService.instance) {
      GraphQLAuthService.instance = new GraphQLAuthService();
    }
    return GraphQLAuthService.instance;
  }

  private createApolloClient() {
    // HTTP Link
    const httpLink = createHttpLink({
      uri: import.meta.env.VITE_GRAPHQL_URI_1,
    });

    // Auth Link
    const authLink = setContext((_, { headers }) => {
      const token = this.getStoredToken();
      const authHeaders: Record<string, string> = {
        ...headers,
        'user-agent': navigator.userAgent,
      };

      // Only add authorization header if we have a valid token
      if (token && token.trim() !== '') {
        authHeaders.authorization = `Bearer ${token}`;
      }

      return {
        headers: authHeaders
      };
    });

    // Error Link
    const errorLink = onError(({ graphQLErrors, networkError }) => {
      if (graphQLErrors) {
        graphQLErrors.forEach(({ message, locations, path }) => {
          console.error(`GraphQL error: Message: ${message}, Location: ${locations}, Path: ${path}`);

          // Handle authentication errors
          if (message.includes('Unauthorized') || message.includes('Invalid token')) {
            this.clearSession();
            window.location.href = '/login';
          }
        });
      }

      if (networkError) {
        console.error(`Network error: ${networkError}`);

        // Handle network errors
        if ('statusCode' in networkError && networkError.statusCode === 401) {
          this.clearSession();
          window.location.href = '/login';
        }
      }
    });

    return new ApolloClient({
      link: from([errorLink, authLink, httpLink]),
      cache: new InMemoryCache(),
      defaultOptions: {
        watchQuery: {
          errorPolicy: 'all',
        },
        query: {
          errorPolicy: 'all',
        },
      },
    });
  }

  private getClientIpAddress(): string {
    // In a real application, you might want to get this from a service
    // For now, we'll use a placeholder
    return 'unknown';
  }

  private getUserAgent(): string {
    return navigator.userAgent;
  }

  // Login with credentials
  async login(credentials: LoginCredentials): Promise<LoginPayload> {
    try {
      const { data } = await this.client.mutate({
        mutation: LOGIN_MUTATION,
        variables: {
          input: {
            email: credentials.email,
            password: credentials.password,
            tenantId: credentials.tenantId,
            rememberMe: credentials.rememberMe || false,
          }
        }
      });

      const result = data.login;

      if (result.success && result.accessToken) {
        // Store tokens and session
        this.storeTokens(result.accessToken, undefined); // Backend uses HTTP-only cookies for refresh tokens
        this.storeSession({
          user: result.user,
          accessToken: result.accessToken,
          refreshToken: undefined, // Refresh token is in HTTP-only cookie
          expiresAt: result.expiresAt,
          session: undefined // Session info is managed by backend cookies
        });
      }

      return result;
    } catch (error) {
      console.error('Login error:', error);
      throw new Error(error instanceof Error ? error.message : 'Login failed');
    }
  }

  // Register new user (using createUser mutation)
  async register(_data: RegisterData): Promise<LoginPayload> {
    try {
      // Since there's no public register mutation, we'll use createUser
      // This requires admin permissions, so for now we'll return an error
      // In a real app, you'd either:
      // 1. Create a public register mutation on the backend
      // 2. Have an admin create users
      // 3. Use a different registration flow

      throw new Error('User registration is not available. Please contact an administrator to create your account.');

      // If you want to use createUser (requires admin auth):
      /*
      const { data: result } = await this.client.mutate({
        mutation: CREATE_USER_MUTATION,
        variables: {
          input: {
            email: data.email,
            password: data.password,
            firstName: data.firstName,
            lastName: data.lastName,
            phone: data.phone,
            roleId: data.roleId || 4, // Default to Worker role
          }
        }
      });

      // createUser doesn't return login payload, so we'd need to login after creation
      if (result.createUser) {
        // Auto-login after user creation
        return await this.login({
          email: data.email,
          password: data.password,
          tenantId: data.tenantId,
          rememberMe: false
        });
      }

      throw new Error('User creation failed');
      */
    } catch (error) {
      console.error('Registration error:', error);
      throw new Error(error instanceof Error ? error.message : 'Registration failed');
    }
  }

  // Refresh token
  async refreshToken(): Promise<boolean> {
    try {
      const refreshToken = this.getStoredRefreshToken();
      if (!refreshToken) {
        return false;
      }

      const { data } = await this.client.mutate({
        mutation: REFRESH_TOKEN_MUTATION,
        variables: {
          input: {
            refreshToken,
            ipAddress: this.getClientIpAddress(),
            userAgent: this.getUserAgent(),
          }
        }
      });

      const result = data.refreshToken;

      if (result.success && result.accessToken) {
        // Store new tokens (refresh token is in HTTP-only cookie)
        this.storeTokens(result.accessToken);

        // Update session
        const currentSession = this.getStoredSession();
        if (currentSession) {
          this.storeSession({
            ...currentSession,
            accessToken: result.accessToken,
            expiresAt: result.expiresAt,
            user: result.user
          });
        }

        return true;
      }

      return false;
    } catch (error) {
      console.error('Token refresh error:', error);
      this.clearSession();
      return false;
    }
  }

  // Logout
  async logout(): Promise<void> {
    try {
      const session = this.getStoredSession();
      if (session) {
        await this.client.mutate({
          mutation: LOGOUT_MUTATION,
          variables: {
            input: {
              sessionId: session.session?.id || '',
              refreshToken: this.getStoredRefreshToken() || undefined
            }
          }
        });
      }
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      this.clearSession();
    }
  }

  // Logout all sessions
  async logoutAllSessions(userId: number): Promise<void> {
    try {
      await this.client.mutate({
        mutation: LOGOUT_ALL_SESSIONS_MUTATION,
        variables: { userId }
      });
    } catch (error) {
      console.error('Logout all sessions error:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to logout all sessions');
    } finally {
      this.clearSession();
    }
  }

  // Get current user
  async getCurrentUser(): Promise<User | null> {
    try {
      const { data } = await this.client.query({
        query: ME_QUERY,
        fetchPolicy: 'network-only'
      });

      return data.me;
    } catch (error) {
      console.error('Get current user error:', error);
      return null;
    }
  }

  // Get active sessions
  async getActiveSessions(userId: number): Promise<UserSession[]> {
    try {
      const { data } = await this.client.query({
        query: GET_ACTIVE_SESSIONS_QUERY,
        variables: { userId },
        fetchPolicy: 'network-only'
      });

      return data.activeSessions;
    } catch (error) {
      console.error('Get active sessions error:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to get active sessions');
    }
  }

  // End session
  async endSession(sessionId: string, _reason: string = 'User requested'): Promise<void> {
    try {
      // Note: EndSession mutation doesn't exist in backend yet
      // For now, we'll use logout with sessionId to end the session
      await this.client.mutate({
        mutation: LOGOUT_MUTATION,
        variables: {
          input: {
            sessionId: sessionId
          }
        }
      });
    } catch (error) {
      console.error('End session error:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to end session');
    }
  }

  // Store tokens
  private storeTokens(accessToken: string, refreshToken?: string): void {
    localStorage.setItem(this.TOKEN_KEY, accessToken);
    if (refreshToken) {
      localStorage.setItem(this.REFRESH_TOKEN_KEY, refreshToken);
    }
  }

  // Store session
  private storeSession(session: AuthSession): void {
    localStorage.setItem(this.SESSION_KEY, JSON.stringify(session));
  }

  // Get stored session
  getStoredSession(): AuthSession | null {
    try {
      const stored = localStorage.getItem(this.SESSION_KEY);
      if (!stored) return null;

      const session: AuthSession = JSON.parse(stored);

      // Check if session is expired
      if (session.expiresAt && new Date() > new Date(session.expiresAt)) {
        this.clearSession();
        return null;
      }

      return session;
    } catch (error) {
      console.error('Error parsing stored session:', error);
      this.clearSession();
      return null;
    }
  }

  // Get stored token
  getStoredToken(): string | null {
    return localStorage.getItem(this.TOKEN_KEY);
  }

  // Get stored refresh token
  getStoredRefreshToken(): string | null {
    return localStorage.getItem(this.REFRESH_TOKEN_KEY);
  }

  // Clear session
  clearSession(): void {
    localStorage.removeItem(this.SESSION_KEY);
    localStorage.removeItem(this.TOKEN_KEY);
    localStorage.removeItem(this.REFRESH_TOKEN_KEY);
  }

  // Validate session token
  async validateToken(token: string): Promise<boolean> {
    try {
      // In a real implementation, you might want to validate with the backend
      // For now, we'll do a simple check
      if (!token) return false;

      // Try to get current user to validate token
      const user = await this.getCurrentUser();
      return user !== null;
    } catch (error) {
      console.error('Token validation error:', error);
      return false;
    }
  }

  // User Management Methods

  // Update user profile
  async updateProfile(userId: number, data: UpdateUserRequest): Promise<User> {
    try {
      const { data: result } = await this.client.mutate({
        mutation: UPDATE_USER_MUTATION,
        variables: { userId, input: data }
      });

      // Update stored session if it's the current user
      const session = this.getStoredSession();
      if (session && session.user && session.user.id === userId) {
        session.user = result.updateUser.user;
        this.storeSession(session);
      }

      return result.updateUser;
    } catch (error) {
      console.error('Update profile error:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to update profile');
    }
  }

  // Change password
  async changePassword(_userId: number, data: ChangePasswordRequest): Promise<void> {
    try {
      const { data: result } = await this.client.mutate({
        mutation: CHANGE_PASSWORD_MUTATION,
        variables: {
          input: {
            currentPassword: data.currentPassword,
            newPassword: data.newPassword
          }
        }
      });

      if (!result.changePassword.success) {
        throw new Error(result.changePassword.errorMessage || 'Password change failed');
      }
    } catch (error) {
      console.error('Change password error:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to change password');
    }
  }

  // Request password reset
  async requestPasswordReset(email: string): Promise<void> {
    try {
      await this.client.mutate({
        mutation: REQUEST_PASSWORD_RESET_MUTATION,
        variables: { email }
      });
    } catch (error) {
      console.error('Request password reset error:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to request password reset');
    }
  }

  // Reset password
  async resetPassword(data: ResetPasswordRequest): Promise<void> {
    try {
      // This would typically use a different mutation that accepts a reset token
      // For now, we'll use the admin reset password mutation
      console.log('Reset password with token:', data.token);
      // Implementation would depend on how the backend handles password reset tokens
    } catch (error) {
      console.error('Reset password error:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to reset password');
    }
  }

  // Create user (admin function)
  async createUser(data: CreateUserRequest): Promise<User> {
    try {
      const { data: result } = await this.client.mutate({
        mutation: CREATE_USER_MUTATION,
        variables: { input: data }
      });

      return result.createUser;
    } catch (error) {
      console.error('Create user error:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to create user');
    }
  }

  // Delete user (admin function)
  async deleteUser(userId: number, deletedBy: string): Promise<boolean> {
    try {
      const { data } = await this.client.mutate({
        mutation: DELETE_USER_MUTATION,
        variables: { userId, deletedBy }
      });

      return data.deleteUser;
    } catch (error) {
      console.error('Delete user error:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to delete user');
    }
  }

  // Get users by tenant
  async getUsersByTenant(tenantId: number, skip: number = 0, take: number = 50): Promise<User[]> {
    try {
      const { data } = await this.client.query({
        query: GET_USERS_QUERY,
        variables: { tenantId, skip, take },
        fetchPolicy: 'network-only'
      });

      return data.usersByTenant;
    } catch (error) {
      console.error('Get users by tenant error:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to get users');
    }
  }

  // Get user by ID
  async getUserById(userId: number): Promise<User | null> {
    try {
      const { data } = await this.client.query({
        query: GET_USER_BY_ID_QUERY,
        variables: { userId },
        fetchPolicy: 'network-only'
      });

      return data.userById;
    } catch (error) {
      console.error('Get user by ID error:', error);
      return null;
    }
  }

  // Lock user (admin function)
  async lockUser(userId: number, lockDuration: string, reason: string, lockedBy: string): Promise<boolean> {
    try {
      const { data } = await this.client.mutate({
        mutation: LOCK_USER_MUTATION,
        variables: { userId, lockDuration, reason, lockedBy }
      });

      return data.lockUser;
    } catch (error) {
      console.error('Lock user error:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to lock user');
    }
  }

  // Unlock user (admin function)
  async unlockUser(userId: number, unlockedBy: string): Promise<boolean> {
    try {
      const { data } = await this.client.mutate({
        mutation: UNLOCK_USER_MUTATION,
        variables: { userId, unlockedBy }
      });

      return data.unlockUser;
    } catch (error) {
      console.error('Unlock user error:', error);
      throw new Error(error instanceof Error ? error.message : 'Failed to unlock user');
    }
  }

  // Permission checking
  hasPermission(user: User | null, resource: string, action: string, level: PermissionLevel = PermissionLevel.Site): boolean {
    if (!user || !user.role || !user.role.permissions) {
      return false;
    }

    return user.role.permissions.some(permission =>
      permission.resource === resource &&
      permission.action === action &&
      permission.level === level
    );
  }
}

export const graphqlAuthService = GraphQLAuthService.getInstance();
