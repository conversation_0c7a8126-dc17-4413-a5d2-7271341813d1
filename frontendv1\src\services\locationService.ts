// Location service for OpenStreetMap integration
// Based on the drawarea.md documentation requirements

export interface OSMSearchResult {
  osm_id: string;
  osm_type: 'way' | 'node' | 'relation';
  display_name: string;
  name: string;
  lat: number;
  lon: number;
  type: string;
  address: {
    road?: string;
    city?: string;
    county?: string;
    country?: string;
    country_code?: string;
    [key: string]: any;
  };
}

export interface OSMBoundaryResult {
  type: 'Feature';
  properties: {
    osm_id: string;
    osm_type: string;
    name: string;
  };
  geometry: {
    type: 'Polygon';
    coordinates: number[][][];
  };
}

export interface GeoCoordinate {
  latitude: number;
  longitude: number;
}

class LocationService {
  private readonly nominatimBaseUrl = 'https://nominatim.openstreetmap.org';
  private readonly overpassBaseUrls = [
    'https://overpass-api.de/api/interpreter',
    'https://overpass.kumi.systems/api/interpreter',
    'https://overpass.openstreetmap.ru/api/interpreter'
  ];
  private readonly userAgent = 'Workforce-Management-System/1.0';

  /**
   * Search for locations using OSM Nominatim API
   */
  async searchLocations(query: string, countryCode: string = 'ke', limit: number = 5): Promise<OSMSearchResult[]> {
    if (!query.trim()) {
      throw new Error('Search query cannot be empty');
    }

    try {
      const params = new URLSearchParams({
        q: query,
        format: 'json',
        limit: limit.toString(),
        addressdetails: '1',
        countrycodes: countryCode
      });

      const response = await fetch(`${this.nominatimBaseUrl}/search?${params}`, {
        headers: {
          'User-Agent': this.userAgent
        }
      });

      if (!response.ok) {
        throw new Error(`Nominatim API error: ${response.status}`);
      }

      const results = await response.json();
      
      return results.map((result: any) => ({
        osm_id: result.osm_id?.toString() || '',
        osm_type: result.osm_type || 'node',
        display_name: result.display_name || '',
        name: result.name || result.display_name?.split(',')[0]?.trim() || '',
        lat: parseFloat(result.lat),
        lon: parseFloat(result.lon),
        type: result.type || '',
        address: result.address || {}
      }));
    } catch (error) {
      console.error('Location search failed:', error);
      throw new Error('Failed to search locations. Please try again.');
    }
  }

  /**
   * Get boundary coordinates for a location using OSM Overpass API
   */
  async getBoundary(osmId: string, osmType: string): Promise<OSMBoundaryResult | null> {
    if (!osmId || !osmType) {
      throw new Error('OSM ID and type are required');
    }

    try {
      let query: string;
      
      if (osmType === 'way') {
        query = `[out:json][timeout:90];way(${osmId});out body;>;out skel qt;`;
      } else if (osmType === 'relation') {
        query = `[out:json][timeout:90];relation(${osmId});out body;>>;out skel qt;relation(${osmId});way(r);out geom qt;`;
      } else if (osmType === 'node') {
        // For nodes, create a small circular area around the point
        query = `[out:json];node(${osmId});out;`;
      } else {
        throw new Error(`Unsupported OSM type: ${osmType}`);
      }

      // Try each Overpass endpoint until one works
      let response: Response | null = null;
      for (const endpoint of this.overpassBaseUrls) {
        try {
          response = await fetch(endpoint, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
              'User-Agent': this.userAgent
            },
            body: `data=${encodeURIComponent(query)}`
          });
          
          if (response.ok) {
            break;
          }
        } catch (error) {
          console.warn(`Overpass endpoint ${endpoint} failed:`, error);
          continue;
        }
      }

      if (!response || !response.ok) {
        throw new Error('All Overpass API endpoints failed');
      }

      const result = await response.json();

      if (osmType === 'way') {
        return this.processWayResponse(result, osmId, osmType);
      } else if (osmType === 'relation') {
        return this.processRelationResponse(result, osmId, osmType);
      } else if (osmType === 'node') {
        return this.processNodeResponse(result, osmId, osmType);
      }

      return null;
    } catch (error) {
      console.error('Boundary retrieval failed:', error);
      throw new Error('Failed to retrieve boundary data. Please try again.');
    }
  }

  /**
   * Reverse geocode coordinates to get address information
   */
  async reverseGeocode(lat: number, lon: number): Promise<OSMSearchResult | null> {
    try {
      const params = new URLSearchParams({
        lat: lat.toString(),
        lon: lon.toString(),
        format: 'json',
        addressdetails: '1'
      });

      const response = await fetch(`${this.nominatimBaseUrl}/reverse?${params}`, {
        headers: {
          'User-Agent': this.userAgent
        }
      });

      if (!response.ok) {
        throw new Error(`Reverse geocoding error: ${response.status}`);
      }

      const result = await response.json();
      
      if (!result) {
        return null;
      }

      return {
        osm_id: result.osm_id?.toString() || '',
        osm_type: result.osm_type || 'node',
        display_name: result.display_name || '',
        name: result.name || result.display_name?.split(',')[0]?.trim() || '',
        lat: parseFloat(result.lat),
        lon: parseFloat(result.lon),
        type: result.type || '',
        address: result.address || {}
      };
    } catch (error) {
      console.error('Reverse geocoding failed:', error);
      return null;
    }
  }

  /**
   * Calculate the center point (centroid) of a polygon
   */
  calculatePolygonCenter(coordinates: number[][]): GeoCoordinate {
    if (!coordinates || coordinates.length === 0) {
      throw new Error('Invalid coordinates for center calculation');
    }

    const lats = coordinates.map(coord => coord[1]);
    const lngs = coordinates.map(coord => coord[0]);

    const centerLat = lats.reduce((sum, lat) => sum + lat, 0) / lats.length;
    const centerLng = lngs.reduce((sum, lng) => sum + lng, 0) / lngs.length;

    return {
      latitude: centerLat,
      longitude: centerLng
    };
  }

  /**
   * Calculate area of a polygon in square meters using the shoelace formula
   */
  calculatePolygonArea(coordinates: number[][]): number {
    if (!coordinates || coordinates.length < 3) {
      return 0;
    }

    // Convert to radians and calculate area using spherical geometry
    const R = 6371000; // Earth's radius in meters
    let area = 0;

    for (let i = 0; i < coordinates.length - 1; i++) {
      const [lon1, lat1] = coordinates[i];
      const [lon2, lat2] = coordinates[i + 1];
      
      const lat1Rad = lat1 * Math.PI / 180;
      const lat2Rad = lat2 * Math.PI / 180;
      const deltaLon = (lon2 - lon1) * Math.PI / 180;
      
      area += deltaLon * (2 + Math.sin(lat1Rad) + Math.sin(lat2Rad));
    }

    area = Math.abs(area * R * R / 2);
    return area;
  }

  /**
   * Validate GeoJSON polygon structure
   */
  validateGeoJSON(geojson: any): boolean {
    if (!geojson || geojson.type !== 'Feature') {
      return false;
    }

    if (!geojson.geometry || geojson.geometry.type !== 'Polygon') {
      return false;
    }

    if (!geojson.geometry.coordinates || !Array.isArray(geojson.geometry.coordinates)) {
      return false;
    }

    const coordinates = geojson.geometry.coordinates[0];
    if (!Array.isArray(coordinates) || coordinates.length < 4) {
      return false;
    }

    // Check if polygon is closed
    const first = coordinates[0];
    const last = coordinates[coordinates.length - 1];
    if (first[0] !== last[0] || first[1] !== last[1]) {
      return false;
    }

    return true;
  }

  /**
   * Process Overpass API response for ways
   */
  private processWayResponse(result: any, osmId: string, osmType: string): OSMBoundaryResult | null {
    const nodes: { [key: string]: any } = {};
    const ways: any[] = [];

    // Extract nodes and ways from response
    result.elements?.forEach((element: any) => {
      if (element.type === 'node') {
        nodes[element.id] = element;
      } else if (element.type === 'way') {
        ways.push(element);
      }
    });

    if (ways.length === 0) {
      return null;
    }

    const way = ways[0];
    const coordinates: number[][] = [];

    // Build coordinate array from node references
    way.nodes?.forEach((nodeId: string) => {
      if (nodes[nodeId]) {
        const node = nodes[nodeId];
        coordinates.push([node.lon, node.lat]);
      }
    });

    // Close the polygon if not already closed
    if (coordinates.length > 0 && coordinates[0] !== coordinates[coordinates.length - 1]) {
      coordinates.push(coordinates[0]);
    }

    if (coordinates.length < 4) {
      return null; // Invalid polygon
    }

    return {
      type: 'Feature',
      properties: {
        osm_id: osmId,
        osm_type: osmType,
        name: way.tags?.name || 'Unknown'
      },
      geometry: {
        type: 'Polygon',
        coordinates: [coordinates]
      }
    };
  }

  /**
   * Process Overpass API response for relations (complex multipolygons)
   */
  private processRelationResponse(result: any, osmId: string, osmType: string): OSMBoundaryResult | null {
    // This is a simplified implementation - full multipolygon processing is complex
    // For now, we'll try to extract the first valid way from the relation
    const ways = result.elements?.filter((element: any) => element.type === 'way' && element.geometry);

    if (!ways || ways.length === 0) {
      return null;
    }

    const way = ways[0];
    const coordinates: number[][] = [];

    // Extract coordinates from geometry
    way.geometry?.forEach((point: any) => {
      coordinates.push([point.lon, point.lat]);
    });

    // Close the polygon if not already closed
    if (coordinates.length > 0 && coordinates[0] !== coordinates[coordinates.length - 1]) {
      coordinates.push(coordinates[0]);
    }

    if (coordinates.length < 4) {
      return null;
    }

    return {
      type: 'Feature',
      properties: {
        osm_id: osmId,
        osm_type: osmType,
        name: way.tags?.name || 'Unknown'
      },
      geometry: {
        type: 'Polygon',
        coordinates: [coordinates]
      }
    };
  }

  /**
   * Process Overpass API response for nodes (create small circular area)
   */
  private processNodeResponse(result: any, osmId: string, osmType: string): OSMBoundaryResult | null {
    const nodes = result.elements?.filter((element: any) => element.type === 'node');

    if (!nodes || nodes.length === 0) {
      return null;
    }

    const node = nodes[0];
    const centerLat = node.lat;
    const centerLon = node.lon;

    // Create a small circular polygon around the node (approximately 50m radius)
    const radius = 0.0005; // Approximately 50 meters in degrees
    const points = 16; // Number of points in the circle
    const coordinates: number[][] = [];

    for (let i = 0; i <= points; i++) {
      const angle = (i * 2 * Math.PI) / points;
      const lat = centerLat + radius * Math.cos(angle);
      const lon = centerLon + radius * Math.sin(angle);
      coordinates.push([lon, lat]);
    }

    return {
      type: 'Feature',
      properties: {
        osm_id: osmId,
        osm_type: osmType,
        name: node.tags?.name || 'Point Location'
      },
      geometry: {
        type: 'Polygon',
        coordinates: [coordinates]
      }
    };
  }
}

// Export singleton instance
export const locationService = new LocationService();
export default locationService;
