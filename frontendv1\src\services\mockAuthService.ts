import {
  User,
  LoginCredentials,
  RegisterData,
  LoginPayload,
  UserSession,
  UpdateUserRequest,
  ChangePasswordRequest,
  ResetPasswordRequest,
  CreateUserRequest,
  PermissionLevel,
  UserStatus,
  Role,
  Tenant
} from '../types/auth';

// Mock data
const mockTenant: Tenant = {
  id: 1,
  name: 'Demo Company',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z'
};

const mockRole: Role = {
  id: 1,
  name: 'Administrator',
  description: 'Full system access',
  isSystemRole: true
};

const mockUser: User = {
  id: 1,
  email: '<EMAIL>',
  firstName: 'Admin',
  lastName: 'User',
  phone: '+1234567890',
  status: UserStatus.Active,
  emailConfirmed: true,
  phoneConfirmed: true,
  twoFactorEnabled: false,
  lastLoginAt: new Date().toISOString(),
  lastLoginIp: '127.0.0.1',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: new Date().toISOString(),
  createdBy: 'System',
  updatedBy: 'System',
  role: mockRole,
  tenant: mockTenant
};

class MockAuthService {
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private generateToken(): string {
    return 'mock-jwt-token-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
  }

  private storeToken(token: string): void {
    localStorage.setItem('authToken', token);
  }

  private getStoredToken(): string | null {
    return localStorage.getItem('authToken');
  }

  private removeToken(): void {
    localStorage.removeItem('authToken');
  }

  // Login with credentials
  async login(credentials: LoginCredentials): Promise<LoginPayload> {
    await this.delay(800); // Simulate API call

    // Simple validation - accept demo credentials or any email with "password"
    const validEmails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
    const validPassword = 'Admin@123!';

    if (!validEmails.includes(credentials.email) && !credentials.email.includes('@')) {
      return {
        success: false,
        errorMessage: 'Invalid email format'
      };
    }

    if (credentials.password !== validPassword && credentials.password !== 'password') {
      return {
        success: false,
        errorMessage: 'Invalid email or password'
      };
    }

    // Generate and store token
    const token = this.generateToken();
    this.storeToken(token);

    // Update user data based on email
    const user = {
      ...mockUser,
      email: credentials.email,
      firstName: credentials.email.split('@')[0].charAt(0).toUpperCase() + credentials.email.split('@')[0].slice(1),
      lastLoginAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      tenant: {
        ...mockTenant,
        id: credentials.tenantId || 1
      }
    };

    return {
      success: true,
      accessToken: token,
      expiresAt: new Date(Date.now() + (credentials.rememberMe ? 30 : 1) * 24 * 60 * 60 * 1000).toISOString(),
      user
    };
  }

  // Register new user
  async register(data: RegisterData): Promise<LoginPayload> {
    await this.delay(1000);

    if (data.password !== data.confirmPassword) {
      return {
        success: false,
        errorMessage: 'Passwords do not match'
      };
    }

    // Generate and store token
    const token = this.generateToken();
    this.storeToken(token);

    const user: User = {
      ...mockUser,
      id: Math.floor(Math.random() * 1000) + 100,
      email: data.email,
      firstName: data.firstName,
      lastName: data.lastName,
      phone: data.phone || '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      tenant: {
        ...mockTenant,
        id: data.tenantId
      }
    };

    return {
      success: true,
      accessToken: token,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      user
    };
  }

  // Get current user
  async getCurrentUser(): Promise<User | null> {
    await this.delay(200);
    const token = this.getStoredToken();
    return token ? mockUser : null;
  }

  // Refresh token
  async refreshToken(): Promise<{ success: boolean; accessToken?: string; expiresAt?: string; user?: User }> {
    await this.delay(300);
    const token = this.getStoredToken();
    
    if (!token) {
      return { success: false };
    }

    const newToken = this.generateToken();
    this.storeToken(newToken);

    return {
      success: true,
      accessToken: newToken,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
      user: mockUser
    };
  }

  // Logout
  async logout(): Promise<void> {
    await this.delay(200);
    this.removeToken();
  }

  // Logout all sessions
  async logoutAllSessions(): Promise<void> {
    await this.delay(300);
    this.removeToken();
  }

  // Update user profile
  async updateProfile(data: UpdateUserRequest): Promise<User> {
    await this.delay(500);
    return {
      ...mockUser,
      ...data,
      updatedAt: new Date().toISOString()
    };
  }

  // Change password
  async changePassword(data: ChangePasswordRequest): Promise<void> {
    await this.delay(600);
    // Mock implementation - just simulate success
  }

  // Request password reset
  async requestPasswordReset(email: string): Promise<void> {
    await this.delay(800);
    console.log(`Mock: Password reset email sent to ${email}`);
  }

  // Reset password
  async resetPassword(data: ResetPasswordRequest): Promise<void> {
    await this.delay(600);
    // Mock implementation - just simulate success
  }

  // Get active sessions
  async getActiveSessions(): Promise<UserSession[]> {
    await this.delay(400);
    return [
      {
        id: '1',
        deviceInfo: 'Chrome on Windows',
        ipAddress: '127.0.0.1',
        userAgent: navigator.userAgent,
        isActive: true,
        lastActivity: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
        createdAt: new Date().toISOString()
      }
    ];
  }

  // End session
  async endSession(sessionId: string): Promise<void> {
    await this.delay(300);
    console.log(`Mock: Ended session ${sessionId}`);
  }

  // Create user
  async createUser(data: CreateUserRequest): Promise<{ success: boolean; user?: User; errorMessage?: string }> {
    await this.delay(700);
    
    const user: User = {
      ...mockUser,
      id: Math.floor(Math.random() * 1000) + 100,
      email: data.email,
      firstName: data.firstName,
      lastName: data.lastName,
      phone: data.phone || '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    return {
      success: true,
      user
    };
  }

  // Update user
  async updateUser(userId: number, data: UpdateUserRequest): Promise<{ success: boolean; user?: User; errorMessage?: string }> {
    await this.delay(500);
    
    const user: User = {
      ...mockUser,
      id: userId,
      ...data,
      updatedAt: new Date().toISOString()
    };

    return {
      success: true,
      user
    };
  }

  // Delete user
  async deleteUser(userId: number, deletedBy: string): Promise<{ success: boolean; errorMessage?: string }> {
    await this.delay(400);
    console.log(`Mock: User ${userId} deleted by ${deletedBy}`);
    return { success: true };
  }

  // Check permissions
  hasPermission(user: User | null, resource: string, action: string, level: PermissionLevel = PermissionLevel.Site): boolean {
    // Mock implementation - admin has all permissions
    return user?.role?.name === 'Administrator';
  }
}

export const mockAuthService = new MockAuthService();
