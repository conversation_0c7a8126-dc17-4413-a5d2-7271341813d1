import {
  mockTenants,
  mockSites,
  mockWorkers,
  mockTrades,
  mockSkills,
  mockTrainings,
  mockWorkerSiteAssignments,
  mockWorkerTrades,
  mockWorkerSkills
} from '../data/mockTenantData';
import { 
  Tenant,
  SiteInfo,
  Worker, 
  Trade, 
  Skill, 
  Training,
  WorkerSiteAssignment,
  WorkerTrade,
  WorkerSkill
} from '../types';

// Simulate network delay
const delay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms));

// Mock GraphQL response wrapper
interface MockGraphQLResponse<T> {
  data: T;
  loading: boolean;
  error?: Error;
}

// Tenant context for all operations
interface TenantContext {
  tenantId: string;
  userId?: string;
}

// Mock Tenant-based GraphQL Client
export class MockTenantGraphQLClient {
  private static instance: MockTenantGraphQLClient;
  
  // In-memory data store (simulating backend state with tenant isolation)
  private tenants: Tenant[] = [...mockTenants];
  private sites: SiteInfo[] = [...mockSites];
  private workers: Worker[] = [...mockWorkers];
  private trades: Trade[] = [...mockTrades];
  private skills: Skill[] = [...mockSkills];
  private trainings: Training[] = [...mockTrainings];
  private workerSiteAssignments: WorkerSiteAssignment[] = [...mockWorkerSiteAssignments];
  private workerTrades: WorkerTrade[] = [...mockWorkerTrades];
  private workerSkills: WorkerSkill[] = [...mockWorkerSkills];

  private constructor() {}

  static getInstance(): MockTenantGraphQLClient {
    if (!MockTenantGraphQLClient.instance) {
      MockTenantGraphQLClient.instance = new MockTenantGraphQLClient();
    }
    return MockTenantGraphQLClient.instance;
  }

  // Tenant Operations
  async getTenant(tenantId: string): Promise<MockGraphQLResponse<Tenant | null>> {
    await delay();
    const tenant = this.tenants.find(t => t.id === tenantId);
    return {
      data: tenant || null,
      loading: false
    };
  }

  // Site Operations (filtered by tenant)
  async getSites(context: TenantContext): Promise<MockGraphQLResponse<SiteInfo[]>> {
    await delay();
    const sites = this.sites.filter(site => site.tenantId === context.tenantId);
    return {
      data: sites,
      loading: false
    };
  }

  async getSite(siteId: string, context: TenantContext): Promise<MockGraphQLResponse<SiteInfo | null>> {
    await delay();
    const site = this.sites.find(s => s.id === siteId && s.tenantId === context.tenantId);
    return {
      data: site || null,
      loading: false
    };
  }

  // Worker Operations (tenant-based with site assignments)
  async getWorkers(context: TenantContext, siteId?: string): Promise<MockGraphQLResponse<Worker[]>> {
    await delay();
    
    // Get all workers for the tenant
    let workers = this.workers.filter(worker => worker.tenantId === context.tenantId);
    
    // If siteId is provided, filter by site assignments
    if (siteId) {
      const siteAssignments = this.workerSiteAssignments.filter(
        assignment => assignment.siteId === siteId && assignment.status === 'active'
      );
      const workerIds = siteAssignments.map(assignment => assignment.workerId);
      workers = workers.filter(worker => workerIds.includes(worker.id));
    }
    
    // Populate navigation properties
    workers = workers.map(worker => this.populateWorkerNavigationProperties(worker));
    
    return {
      data: workers,
      loading: false
    };
  }

  async getWorker(workerId: number, context: TenantContext): Promise<MockGraphQLResponse<Worker | null>> {
    await delay();
    const worker = this.workers.find(w => w.id === workerId && w.tenantId === context.tenantId);
    
    if (!worker) {
      return { data: null, loading: false };
    }
    
    const populatedWorker = this.populateWorkerNavigationProperties(worker);
    return {
      data: populatedWorker,
      loading: false
    };
  }

  // Get workers available for site assignment (not currently assigned to the site)
  async getAvailableWorkersForSite(siteId: string, context: TenantContext): Promise<MockGraphQLResponse<Worker[]>> {
    await delay();
    
    // Get all workers for the tenant
    const allWorkers = this.workers.filter(worker => worker.tenantId === context.tenantId);
    
    // Get workers already assigned to this site
    const siteAssignments = this.workerSiteAssignments.filter(
      assignment => assignment.siteId === siteId && assignment.status === 'active'
    );
    const assignedWorkerIds = siteAssignments.map(assignment => assignment.workerId);
    
    // Filter out already assigned workers
    const availableWorkers = allWorkers.filter(worker => !assignedWorkerIds.includes(worker.id));
    
    // Populate navigation properties
    const populatedWorkers = availableWorkers.map(worker => this.populateWorkerNavigationProperties(worker));
    
    return {
      data: populatedWorkers,
      loading: false
    };
  }

  // Worker Site Assignment Operations
  async getWorkerSiteAssignments(workerId: number, context: TenantContext): Promise<MockGraphQLResponse<WorkerSiteAssignment[]>> {
    await delay();
    
    // Verify worker belongs to tenant
    const worker = this.workers.find(w => w.id === workerId && w.tenantId === context.tenantId);
    if (!worker) {
      return { data: [], loading: false };
    }
    
    const assignments = this.workerSiteAssignments.filter(assignment => assignment.workerId === workerId);
    return {
      data: assignments,
      loading: false
    };
  }

  async assignWorkerToSite(
    workerId: number, 
    siteId: string, 
    role: string, 
    context: TenantContext
  ): Promise<MockGraphQLResponse<WorkerSiteAssignment>> {
    await delay();
    
    // Verify worker belongs to tenant
    const worker = this.workers.find(w => w.id === workerId && w.tenantId === context.tenantId);
    if (!worker) {
      throw new Error('Worker not found or access denied');
    }
    
    // Verify site belongs to tenant
    const site = this.sites.find(s => s.id === siteId && s.tenantId === context.tenantId);
    if (!site) {
      throw new Error('Site not found or access denied');
    }
    
    const newAssignment: WorkerSiteAssignment = {
      id: Math.max(...this.workerSiteAssignments.map(a => a.id)) + 1,
      workerId,
      siteId,
      role,
      startDate: new Date().toISOString(),
      status: 'active',
      createdAt: new Date().toISOString(),
      createdBy: context.userId || 'system',
      updatedAt: new Date().toISOString(),
      updatedBy: context.userId || 'system'
    };
    
    this.workerSiteAssignments.push(newAssignment);
    
    return {
      data: newAssignment,
      loading: false
    };
  }

  // Trade Operations (tenant-based)
  async getTrades(context: TenantContext): Promise<MockGraphQLResponse<Trade[]>> {
    await delay();
    const trades = this.trades.filter(trade => trade.tenantId === context.tenantId);
    return {
      data: trades,
      loading: false
    };
  }

  // Skill Operations (tenant-based)
  async getSkills(context: TenantContext): Promise<MockGraphQLResponse<Skill[]>> {
    await delay();
    const skills = this.skills.filter(skill => skill.tenantId === context.tenantId);
    return {
      data: skills,
      loading: false
    };
  }

  // Training Operations (tenant-based)
  async getTrainings(context: TenantContext): Promise<MockGraphQLResponse<Training[]>> {
    await delay();
    const trainings = this.trainings.filter(training => training.tenantId === context.tenantId);
    return {
      data: trainings,
      loading: false
    };
  }

  // Cross-site resource queries
  async getWorkerUtilizationAcrossSites(context: TenantContext): Promise<MockGraphQLResponse<any[]>> {
    await delay();
    
    const workers = this.workers.filter(worker => worker.tenantId === context.tenantId);
    const utilization = workers.map(worker => {
      const assignments = this.workerSiteAssignments.filter(
        assignment => assignment.workerId === worker.id && assignment.status === 'active'
      );
      
      return {
        workerId: worker.id,
        workerName: worker.name,
        activeSites: assignments.length,
        siteAssignments: assignments.map(assignment => ({
          siteId: assignment.siteId,
          siteName: this.sites.find(s => s.id === assignment.siteId)?.name || 'Unknown',
          role: assignment.role,
          startDate: assignment.startDate
        }))
      };
    });
    
    return {
      data: utilization,
      loading: false
    };
  }

  // Helper method to populate worker navigation properties
  private populateWorkerNavigationProperties(worker: Worker): Worker {
    // Get trades for this worker
    const workerTradeAssignments = this.workerTrades.filter(wt => wt.workerId === worker.id);
    const trades = workerTradeAssignments.map(wt => 
      this.trades.find(t => t.id === wt.tradeId)
    ).filter(Boolean) as Trade[];
    
    // Get skills for this worker
    const workerSkillAssignments = this.workerSkills.filter(ws => ws.workerId === worker.id);
    const skills = workerSkillAssignments.map(ws => 
      this.skills.find(s => s.id === ws.skillId)
    ).filter(Boolean) as Skill[];
    
    // Get site assignments for this worker
    const siteAssignments = this.workerSiteAssignments.filter(wsa => wsa.workerId === worker.id);
    
    return {
      ...worker,
      trades,
      skills,
      siteAssignments
    };
  }

  // Utility method to get current tenant context (would come from auth in real app)
  getCurrentTenantContext(): TenantContext {
    // In a real application, this would come from authentication/authorization
    return {
      tenantId: 'tenant-1', // Default to first tenant for demo
      userId: 'current-user'
    };
  }
}

// Export singleton instance
export const mockTenantGraphQLClient = MockTenantGraphQLClient.getInstance();
