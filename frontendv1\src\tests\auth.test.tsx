// import React from 'react';
// import { render, screen, fireEvent, waitFor } from '@testing-library/react';
// import { <PERSON><PERSON><PERSON><PERSON>outer } from 'react-router-dom';
// import { AuthProvider, useAuth } from '../hooks/useAuthContext';
// import { graphqlAuthService } from '../services/graphqlAuthService';
// import LoginPage from '../pages/LoginPage';
// import RoleProtectedComponent from '../components/auth/RoleProtectedComponent';
// import { PermissionLevel, UserStatus } from '../types/auth';

// // Mock the GraphQL auth service
// jest.mock('../services/graphqlAuthService');
// const mockGraphqlAuthService = graphqlAuthService as jest.Mocked<typeof graphqlAuthService>;

// // Mock user data
// const mockUser = {
//   id: 1,
//   email: '<EMAIL>',
//   firstName: 'Test',
//   lastName: 'User',
//   phone: '+**********',
//   avatar: undefined,
//   role: {
//     id: 1,
//     name: 'Administrator',
//     description: 'Full system access',
//     tenantId: 1,
//     permissions: [
//       {
//         id: 1,
//         resource: 'Workers',
//         action: 'Create',
//         level: PermissionLevel.Site,
//         description: 'Create workers'
//       },
//       {
//         id: 2,
//         resource: 'Workers',
//         action: 'Read',
//         level: PermissionLevel.Site,
//         description: 'Read workers'
//       }
//     ],
//     isSystemRole: true,
//     createdAt: '2024-01-01T00:00:00Z',
//     updatedAt: '2024-01-01T00:00:00Z'
//   },
//   tenant: {
//     id: 1,
//     name: 'Test Company',
//     subdomain: 'test',
//     subscriptionPlan: 'Premium',
//     maxSites: 10,
//     status: 'Active' as any,
//     createdAt: '2024-01-01T00:00:00Z',
//     updatedAt: '2024-01-01T00:00:00Z'
//   },
//   tenantId: 1,
//   status: UserStatus.Active,
//   lastLogin: '2024-01-01T00:00:00Z',
//   lastLoginIp: '127.0.0.1',
//   failedLoginAttempts: 0,
//   lockedUntil: undefined,
//   emailVerified: true,
//   emailVerificationToken: undefined,
//   passwordResetToken: undefined,
//   passwordResetExpires: undefined,
//   createdAt: '2024-01-01T00:00:00Z',
//   updatedAt: '2024-01-01T00:00:00Z',
//   createdBy: 1,
//   updatedBy: 1
// };

// const mockSession = {
//   id: 'session-1',
//   userId: 1,
//   deviceInfo: 'Test Device',
//   ipAddress: '127.0.0.1',
//   userAgent: 'Test Agent',
//   isActive: true,
//   lastActivity: '2024-01-01T00:00:00Z',
//   expiresAt: '2024-01-02T00:00:00Z',
//   createdAt: '2024-01-01T00:00:00Z'
// };

// // Test component that uses auth
// const TestAuthComponent: React.FC = () => {
//   const { user, isAuthenticated, login, logout } = useAuth();

//   return (
//     <div>
//       <div data-testid="auth-status">
//         {isAuthenticated ? 'Authenticated' : 'Not Authenticated'}
//       </div>
//       {user && (
//         <div data-testid="user-info">
//           {user.firstName} {user.lastName} - {user.role.name}
//         </div>
//       )}
//       <button
//         data-testid="login-button"
//         onClick={() => login({ email: '<EMAIL>', password: 'password123' })}
//       >
//         Login
//       </button>
//       <button data-testid="logout-button" onClick={logout}>
//         Logout
//       </button>
//     </div>
//   );
// };

// const renderWithAuth = (component: React.ReactElement) => {
//   return render(
//     <BrowserRouter>
//       <AuthProvider>
//         {component}
//       </AuthProvider>
//     </BrowserRouter>
//   );
// };

// describe('Authentication System', () => {
//   beforeEach(() => {
//     jest.clearAllMocks();
//   });

//   describe('AuthProvider', () => {
//     it('should provide authentication context', () => {
//       renderWithAuth(<TestAuthComponent />);
      
//       expect(screen.getByTestId('auth-status')).toHaveTextContent('Not Authenticated');
//     });

//     it('should handle successful login', async () => {
//       mockGraphqlAuthService.login.mockResolvedValue({
//         success: true,
//         accessToken: 'mock-token',
//         refreshToken: 'mock-refresh-token',
//         expiresAt: '2024-01-02T00:00:00Z',
//         user: mockUser,
//         session: mockSession
//       });

//       renderWithAuth(<TestAuthComponent />);
      
//       fireEvent.click(screen.getByTestId('login-button'));
      
//       await waitFor(() => {
//         expect(screen.getByTestId('auth-status')).toHaveTextContent('Authenticated');
//         expect(screen.getByTestId('user-info')).toHaveTextContent('Test User - Administrator');
//       });
//     });

//     it('should handle logout', async () => {
//       mockGraphqlAuthService.login.mockResolvedValue({
//         success: true,
//         accessToken: 'mock-token',
//         refreshToken: 'mock-refresh-token',
//         expiresAt: '2024-01-02T00:00:00Z',
//         user: mockUser,
//         session: mockSession
//       });

//       mockGraphqlAuthService.logout.mockResolvedValue();

//       renderWithAuth(<TestAuthComponent />);
      
//       // Login first
//       fireEvent.click(screen.getByTestId('login-button'));
//       await waitFor(() => {
//         expect(screen.getByTestId('auth-status')).toHaveTextContent('Authenticated');
//       });

//       // Then logout
//       fireEvent.click(screen.getByTestId('logout-button'));
//       await waitFor(() => {
//         expect(screen.getByTestId('auth-status')).toHaveTextContent('Not Authenticated');
//       });
//     });
//   });

//   describe('RoleProtectedComponent', () => {
//     it('should show content for users with allowed roles', () => {
//       const TestComponent = () => (
//         <AuthProvider>
//           <RoleProtectedComponent allowedRoles={['Administrator']}>
//             <div data-testid="protected-content">Protected Content</div>
//           </RoleProtectedComponent>
//         </AuthProvider>
//       );

//       // Mock the auth context to return authenticated user
//       jest.spyOn(require('../hooks/useAuthContext'), 'useAuth').mockReturnValue({
//         user: mockUser,
//         isAuthenticated: true,
//         hasPermission: jest.fn().mockReturnValue(true)
//       });

//       render(<TestComponent />);
      
//       expect(screen.getByTestId('protected-content')).toBeInTheDocument();
//     });

//     it('should hide content for users without allowed roles', () => {
//       const TestComponent = () => (
//         <AuthProvider>
//           <RoleProtectedComponent allowedRoles={['Manager']}>
//             <div data-testid="protected-content">Protected Content</div>
//           </RoleProtectedComponent>
//         </AuthProvider>
//       );

//       // Mock the auth context to return authenticated user with different role
//       jest.spyOn(require('../hooks/useAuthContext'), 'useAuth').mockReturnValue({
//         user: mockUser,
//         isAuthenticated: true,
//         hasPermission: jest.fn().mockReturnValue(false)
//       });

//       render(<TestComponent />);
      
//       expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
//     });

//     it('should show content for users with required permissions', () => {
//       const TestComponent = () => (
//         <AuthProvider>
//           <RoleProtectedComponent 
//             requiredPermissions={[
//               { resource: 'Workers', action: 'Create', level: PermissionLevel.Site }
//             ]}
//           >
//             <div data-testid="protected-content">Protected Content</div>
//           </RoleProtectedComponent>
//         </AuthProvider>
//       );

//       // Mock the auth context to return authenticated user with permissions
//       jest.spyOn(require('../hooks/useAuthContext'), 'useAuth').mockReturnValue({
//         user: mockUser,
//         isAuthenticated: true,
//         hasPermission: jest.fn().mockReturnValue(true)
//       });

//       render(<TestComponent />);
      
//       expect(screen.getByTestId('protected-content')).toBeInTheDocument();
//     });

//     it('should hide content for users without required permissions', () => {
//       const TestComponent = () => (
//         <AuthProvider>
//           <RoleProtectedComponent 
//             requiredPermissions={[
//               { resource: 'Sites', action: 'Delete', level: PermissionLevel.Company }
//             ]}
//           >
//             <div data-testid="protected-content">Protected Content</div>
//           </RoleProtectedComponent>
//         </AuthProvider>
//       );

//       // Mock the auth context to return authenticated user without permissions
//       jest.spyOn(require('../hooks/useAuthContext'), 'useAuth').mockReturnValue({
//         user: mockUser,
//         isAuthenticated: true,
//         hasPermission: jest.fn().mockReturnValue(false)
//       });

//       render(<TestComponent />);
      
//       expect(screen.queryByTestId('protected-content')).not.toBeInTheDocument();
//     });
//   });

//   describe('Permission System', () => {
//     it('should correctly check permissions', () => {
//       const hasPermission = mockGraphqlAuthService.hasPermission(
//         mockUser,
//         'Workers',
//         'Create',
//         PermissionLevel.Site
//       );

//       expect(hasPermission).toBe(true);
//     });

//     it('should return false for permissions user does not have', () => {
//       const hasPermission = mockGraphqlAuthService.hasPermission(
//         mockUser,
//         'Sites',
//         'Delete',
//         PermissionLevel.Company
//       );

//       expect(hasPermission).toBe(false);
//     });

//     it('should return false for null user', () => {
//       const hasPermission = mockGraphqlAuthService.hasPermission(
//         null,
//         'Workers',
//         'Create',
//         PermissionLevel.Site
//       );

//       expect(hasPermission).toBe(false);
//     });
//   });
// });

// describe('Login Page', () => {
//   it('should render login form', () => {
//     render(
//       <BrowserRouter>
//         <AuthProvider>
//           <LoginPage />
//         </AuthProvider>
//       </BrowserRouter>
//     );

//     expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
//     expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
//     expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
//   });

//   it('should handle form submission', async () => {
//     mockGraphqlAuthService.login.mockResolvedValue({
//       success: true,
//       accessToken: 'mock-token',
//       refreshToken: 'mock-refresh-token',
//       expiresAt: '2024-01-02T00:00:00Z',
//       user: mockUser,
//       session: mockSession
//     });

//     render(
//       <BrowserRouter>
//         <AuthProvider>
//           <LoginPage />
//         </AuthProvider>
//       </BrowserRouter>
//     );

//     fireEvent.change(screen.getByLabelText(/email address/i), {
//       target: { value: '<EMAIL>' }
//     });
//     fireEvent.change(screen.getByLabelText(/password/i), {
//       target: { value: 'password123' }
//     });

//     fireEvent.click(screen.getByRole('button', { name: /sign in/i }));

//     await waitFor(() => {
//       expect(mockGraphqlAuthService.login).toHaveBeenCalledWith({
//         email: '<EMAIL>',
//         password: 'password123',
//         tenantId: undefined,
//         rememberMe: false
//       });
//     });
//   });
// });
