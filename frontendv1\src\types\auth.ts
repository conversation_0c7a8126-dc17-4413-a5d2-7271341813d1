export interface User {
  id: number;
  email: string;
  firstName: string;
  lastName: string;
  phone?: string;
  role: Role;
  tenant?: Tenant;
  tenantId: number;
  status: UserStatus;
  emailConfirmed: boolean;
  phoneConfirmed: boolean;
  twoFactorEnabled: boolean;
  avatar?: string;
  lastLoginAt?: string;
  lastLoginIp?: string;
  createdAt: string;
  updatedAt?: string;
  createdBy: string;
  updatedBy?: string;
}

export interface Role {
  id: number;
  name: string;
  description?: string;
  tenantId: number;
  permissions: Permission[];
  isSystemRole: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Permission {
  id: number;
  resource: string;
  action: string;
  level: PermissionLevel;
  description?: string;
}

export interface Tenant {
  id: number;
  name: string;
  subdomain: string;
  subscriptionPlan: string;
  maxSites: number;
  status: TenantStatus;
  createdAt: string;
  updatedAt: string;
}

export enum UserStatus {
  Active = 'Active',
  Inactive = 'Inactive',
  Suspended = 'Suspended',
  Locked = 'Locked'
}

export enum TenantStatus {
  Active = 'Active',
  Inactive = 'Inactive',
  Suspended = 'Suspended'
}

export enum PermissionLevel {
  Site = 'Site',
  Company = 'Company'
}

export interface UserSession {
  id: string;
  userId: number;
  deviceInfo: string;
  ipAddress: string;
  userAgent: string;
  isActive: boolean;
  lastActivity: string;
  expiresAt: string;
  createdAt: string;
}

export interface AuthState {
  user: User | null;
  session: UserSession | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface LoginCredentials {
  email: string;
  password: string;
  tenantId?: number;
  rememberMe?: boolean;
}

export interface RegisterData {
  email: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  phone?: string;
  tenantId: number;
  roleId?: number;
}

export interface LoginRequest {
  email: string;
  password: string;
  tenantId?: number;
  ipAddress: string;
  userAgent: string;
  rememberMe: boolean;
}

export interface LoginPayload {
  success: boolean;
  accessToken?: string;
  expiresAt?: string;
  user?: User;
  errorMessage?: string;
}

export interface RefreshTokenRequest {
  refreshToken: string;
  ipAddress: string;
  userAgent: string;
}

export interface LogoutRequest {
  sessionId?: string;
  userId: number;
  ipAddress: string;
  userAgent: string;
}

export interface CreateUserRequest {
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  password: string;
  roleId: number;
  tenantId: number;
  createdBy: number;
}

export interface UpdateUserRequest {
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  roleId?: number;
  status?: UserStatus;
  updatedBy: number;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface ResetPasswordRequest {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

export interface AuthContextValue {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  session: UserSession | null;
  login: (credentials: LoginCredentials) => Promise<LoginPayload>;
  register: (data: RegisterData) => Promise<LoginPayload>;
  logout: () => Promise<void>;
  logoutAllSessions: () => Promise<void>;
  refreshToken: () => Promise<boolean>;
  updateProfile: (data: UpdateUserRequest) => Promise<User>;
  changePassword: (data: ChangePasswordRequest) => Promise<void>;
  requestPasswordReset: (email: string) => Promise<void>;
  resetPassword: (data: ResetPasswordRequest) => Promise<void>;
  getActiveSessions: () => Promise<UserSession[]>;
  endSession: (sessionId: string) => Promise<void>;
  hasPermission: (resource: string, action: string, level?: PermissionLevel) => boolean;
  clearError: () => void;
}

export interface AuthSession {
  user?: User;
  accessToken: string;
  refreshToken?: string;
  expiresAt?: string;
  session?: UserSession;
}

export enum RolesType {
  ADMIN,
  SITE_SAFETY_OFFICER,
  SITE_AGENT,
}

export type UserType = {
  role: RolesType;
};