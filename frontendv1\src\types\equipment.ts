// Equipment & PPE Management Types

// Company-Level Equipment Management Types
export type EquipmentOwnershipType = "company" | "rented" | "contracted";

export interface CompanyEquipment {
	id: string;
	equipmentNumber: string;
	companyId: string;
	name: string;
	category: string;
	manufacturer: string;
	model: string;
	serialNumber?: string;
	yearOfManufacture: number;
	purchaseDate: string;
	purchasePrice: number;
	currentBookValue: number;
	ownershipType: EquipmentOwnershipType;
	overallStatus: "active" | "inactive" | "retired" | "maintenance";
	isAvailableForAssignment: boolean;
	currentSiteId?: string;
	currentSiteName?: string;
	assignedOperator?: string;
	totalHours: number;
	lastMaintenanceDate?: string;
	nextMaintenanceDate?: string;
	nextInspectionDate?: string;
	complianceStatus: "compliant" | "warning" | "critical" | "overdue";
	specifications: Record<string, string>;
	images: string[];
	documents: EquipmentDocument[];
	rentalInfo?: EquipmentRentalInfo;
	contractorInfo?: EquipmentContractorInfo;
	createdAt: string;
	updatedAt: string;
	createdBy: string;
	updatedBy: string;
}

export interface EquipmentRentalInfo {
	rentalCompany: string;
	rentalStartDate: string;
	rentalEndDate: string;
	monthlyRate: number;
	contactPerson: string;
	contactPhone: string;
	contractNumber?: string;
	insuranceProvider?: string;
	insurancePolicyNumber?: string;
}

export interface EquipmentContractorInfo {
	contractorCompany: string;
	contractStartDate: string;
	contractEndDate: string;
	contactPerson: string;
	contactPhone: string;
	serviceType: string;
	contractNumber?: string;
	insuranceProvider?: string;
	insurancePolicyNumber?: string;
}

export interface EquipmentDocument {
	id: string;
	equipmentId: string;
	documentType: "manual" | "warranty" | "insurance" | "certification" | "inspection" | "maintenance" | "other";
	fileName: string;
	fileUrl: string;
	uploadDate: string;
	expiryDate?: string;
	uploadedBy: string;
}

export interface EquipmentSiteAssignment {
	id: string;
	companyEquipmentId: string;
	siteId: string;
	siteName: string;
	assignmentDate: string;
	expectedReturnDate?: string;
	actualReturnDate?: string;
	assignedBy: string;
	assignedByName: string;
	conditionOnAssignment: "excellent" | "good" | "fair" | "poor" | "damaged";
	conditionOnReturn?: "excellent" | "good" | "fair" | "poor" | "damaged";
	assignmentNotes?: string;
	returnNotes?: string;
	status: "active" | "returned" | "overdue";
}

export interface EquipmentTransfer {
	id: string;
	companyEquipmentId: string;
	fromSiteId?: string;
	fromSiteName?: string;
	toSiteId: string;
	toSiteName: string;
	transferDate: string;
	requestedBy: string;
	requestedByName: string;
	approvedBy?: string;
	approvedByName?: string;
	transferReason: string;
	conditionBeforeTransfer: "excellent" | "good" | "fair" | "poor" | "damaged";
	conditionAfterTransfer?: "excellent" | "good" | "fair" | "poor" | "damaged";
	transferNotes?: string;
	status: "pending" | "approved" | "in_transit" | "completed" | "cancelled";
}

// Site-Level Equipment Management Types (existing, preserved for backward compatibility)

export interface EquipmentMaster {
	id: string;
	name: string;
	sku: string;
	category: EquipmentCategory;
	description: string;
	defaultCost: number;
	supplier?: Supplier;
	expectedLifespanDays: number;
	defaultInspectionIntervalDays: number;
	defaultMaintenanceIntervalDays: number;
	isPPE: boolean;
	requiresCertification: boolean;
	requiredTrainingIds: string[];
	qrCode?: string;
	safetyStandards: string[];
	specifications: Record<string, string>;
	images: string[];
	status: "active" | "discontinued" | "draft";
	createdAt: Date;
	updatedAt: Date;
}

export interface EquipmentCategory {
	id: string;
	name: string;
	description: string;
	parentCategoryId?: string;
	safetyRequirements: Record<string, any>;
	isPPECategory: boolean;
}

export interface SiteEquipmentInventory {
	id: string;
	equipmentMaster: EquipmentMaster;
	siteId: string;
	serialNumber?: string;
	status: EquipmentStatus;
	purchaseDate: Date;
	lastInspectionDate?: Date;
	nextInspectionDueDate?: Date;
	lastMaintenanceDate?: Date;
	nextMaintenanceDueDate?: Date;
	currentAssigneeType?: "worker" | "project" | "task";
	currentAssigneeId?: string;
	currentAssigneeName?: string;
	locationOnSite?: string;
	gpsCoordinates?: { lat: number; lng: number };
	totalUsageHours: number;
	acquisitionCost: number;
	currentValue: number;
	condition: "excellent" | "good" | "fair" | "poor" | "damaged";
}

export interface EquipmentStatus {
	id: string;
	name: string;
	isAvailableForAssignment: boolean;
	color: string;
}

export interface SitePPEStock {
	id: string;
	ppeMaster: EquipmentMaster;
	siteId: string;
	quantityOnHand: number;
	quantityAvailable: number;
	quantityReserved: number;
	reorderLevel: number;
	maxStockLevel: number;
	lastStockUpdate: Date;
	batchNumber?: string;
	expiryDate?: Date;
	unitCost: number;
	// Lifespan tracking per worker
	assignedDate?: Date;
	assignedToWorkerId?: string;
}

export interface EquipmentAssignment {
	id: string;
	siteEquipment: SiteEquipmentInventory;
	assignedToWorkerId?: string;
	assignedToWorkerName?: string;
	assignedToProjectId?: string;
	assignedToTaskId?: string;
	assignmentDate: Date;
	expectedReturnDate?: Date;
	actualReturnDate?: Date;
	conditionOnAssignment: string;
	conditionOnReturn?: string;
	usageHours: number;
	assignmentCost: number;
	notes?: string;
	assignedByUserId: string;
	assignedByUserName: string;
	status: "active" | "returned" | "overdue";
}

export interface PPETransaction {
	id: string;
	sitePPEStock: SitePPEStock;
	workerId: string;
	workerName: string;
	transactionType: "assign" | "return" | "lost" | "damaged";
	quantity: number;
	transactionDate: Date;
	conditionOnReturn?: string;
	unitCost: number;
	totalCost: number;
	notes?: string;
	processedByUserId: string;
	processedByUserName: string;
}

export interface MaintenanceWorkOrder {
	id: string;
	siteEquipment: SiteEquipmentInventory;
	workOrderType: "scheduled" | "emergency" | "inspection-triggered";
	priority: "low" | "medium" | "high" | "critical";
	description: string;
	scheduledDate: Date;
	actualStartDate?: Date;
	actualCompletionDate?: Date;
	assignedToWorkerId?: string;
	assignedToWorkerName?: string;
	status: "pending" | "in-progress" | "completed" | "cancelled";
	laborCost: number;
	partsCost: number;
	totalCost: number;
	downtimeHours: number;
	notes?: string;
	partsUsed: MaintenancePart[];
}

export interface MaintenancePart {
	id: string;
	name: string;
	partNumber: string;
	quantity: number;
	unitCost: number;
	supplier?: string;
}

export interface EquipmentInspection {
	id: string;
	siteEquipment?: SiteEquipmentInventory;
	templateId: string;
	templateName: string;
	conductedByWorkerId: string;
	conductedByWorkerName: string;
	completionDate: Date;
	overallStatus: "pass" | "fail" | "pass-with-issues";
	gpsLocation?: { lat: number; lng: number };
	weatherConditions?: string;
	offlineSyncStatus: "synced" | "pending" | "failed";
	photos: string[];
	notes?: string;
}

export interface EquipmentStats {
	totalEquipment: number;
	availableEquipment: number;
	inUseEquipment: number;
	maintenanceRequired: number;
	overdueInspections: number;
	totalPPEItems: number;
	lowStockPPE: number;
	totalMaintenanceCost: number;
	averageUtilization: number;
}

export interface Supplier {
	id: string;
	name: string;
	contactInfo: {
		email: string;
		phone: string;
		address: string;
	};
	rating?: number;
	isPreferred: boolean;
}

// Filter and search types
export interface EquipmentFilters {
	category?: string;
	status?: string;
	assignee?: string;
	location?: string;
	maintenanceStatus?: "due" | "overdue" | "up-to-date";
	inspectionStatus?: "due" | "overdue" | "up-to-date";
}

export interface PPEFilters {
	category?: string;
	stockLevel?: "low" | "normal" | "high";
	expiryStatus?: "expired" | "expiring-soon" | "valid";
}

// Dashboard types
export interface EquipmentDashboardData {
	stats: EquipmentStats;
	recentAssignments: EquipmentAssignment[];
	upcomingMaintenance: MaintenanceWorkOrder[];
	overdueInspections: EquipmentInspection[];
	lowStockPPE: SitePPEStock[];
	utilizationTrends: UtilizationData[];
}

export interface UtilizationData {
	date: string;
	utilization: number;
	equipmentCount: number;
}

// Form types
export interface EquipmentFormData {
	name: string;
	sku: string;
	categoryId: string;
	description: string;
	defaultCost: number;
	supplierId?: string;
	expectedLifespanDays: number;
	defaultInspectionIntervalDays: number;
	defaultMaintenanceIntervalDays: number;
	isPPE: boolean;
	requiresCertification: boolean;
	requiredTrainingIds: string[];
	safetyStandards: string[];
	specifications: Record<string, string>;
}

export interface PPEAssignmentFormData {
	workerId: string;
	ppeItems: {
		stockId: string;
		quantity: number;
		notes?: string;
	}[];
	assignmentDate: Date;
}

export interface EquipmentAssignmentFormData {
	equipmentId: string;
	assigneeType: "worker" | "project" | "task";
	assigneeId: string;
	expectedReturnDate?: Date;
	notes?: string;
}
