// Enhanced Forms Type System
// Based on existing patterns from permits, inspections, and master data

export type FieldType =
	| "text"
	| "textarea"
	| "number"
	| "email"
	| "phone"
	| "date"
	| "time"
	| "datetime"
	| "select"
	| "radio"
	| "checkbox"
	| "multiselect"
	| "file"
	| "signature"
	| "photo"
	| "rating"
	| "yesno"
	| "location";

export interface FormFieldValidation {
	required?: boolean;
	min?: number;
	max?: number;
	minLength?: number;
	maxLength?: number;
	pattern?: string;
	message?: string;
	fileTypes?: string[];
	maxFileSize?: number; // in MB
}

export interface FormField {
	id: string;
	type: FieldType;
	label: string;
	placeholder?: string;
	description?: string;
	required: boolean;
	options?: string[];
	validation?: FormFieldValidation;
	order: number;
	defaultValue?: any;
	conditional?: {
		dependsOn: string;
		value: any;
		operator:
			| "equals"
			| "not_equals"
			| "contains"
			| "greater_than"
			| "less_than";
	};
}

export interface FormSection {
	id: string;
	title: string;
	description?: string;
	fields: FormField[];
	order: number;
	collapsible?: boolean;
	defaultCollapsed?: boolean;
}

export interface FormCategory {
	id: string;
	name: string;
	description: string;
	color: string;
	icon: string;
}

export interface FormTemplate {
	id: string;
	name: string;
	description: string;
	category: FormCategory;
	version: string;
	sections: FormSection[];
	status: "draft" | "active" | "archived";
	isPublic: boolean;
	siteIds?: string[]; // If not public, specific sites
	roleIds?: string[]; // Role-based access
	estimatedDuration?: number; // in minutes
	approvedBy?: string;
	approvedAt?: Date;
	createdBy: string;
	createdAt: Date;
	updatedAt: Date;
	tags: string[];
	instructions?: string;
	completionMessage?: string;
}

export interface FormSubmissionData {
	[fieldId: string]: any;
}

export interface FormSubmission {
	id: string;
	templateId: string;
	templateName: string;
	templateVersion: string;
	siteId: string;
	siteName: string;
	submittedBy: string;
	submittedByName: string;
	submissionDate: Date;
	status:
		| "draft"
		| "submitted"
		| "under_review"
		| "approved"
		| "rejected"
		| "requires_action";
	data: FormSubmissionData;
	attachments?: FormAttachment[];
	reviewedBy?: string;
	reviewedByName?: string;
	reviewedAt?: Date;
	reviewComments?: string;
	score?: number; // For forms with scoring
	completionTime?: number; // in minutes
	location?: {
		latitude: number;
		longitude: number;
		accuracy?: number;
	};
	createdAt: Date;
	updatedAt: Date;
}

export interface FormAttachment {
	id: string;
	fieldId: string;
	fileName: string;
	fileType: string;
	fileSize: number;
	url: string;
	uploadedAt: Date;
}

export interface FormValidationError {
	fieldId: string;
	message: string;
}

export interface FormState {
	data: FormSubmissionData;
	errors: FormValidationError[];
	isValid: boolean;
	isDirty: boolean;
	isSubmitting: boolean;
	currentSection: number;
	completedSections: Set<number>;
}

// Predefined form categories
export const FORM_CATEGORIES: FormCategory[] = [
  {
    id: 'safety',
    name: 'Safety',
    description: 'Safety inspections, incident reports, and compliance forms',
    color: 'red',
    icon: 'ShieldCheck'
  },
  {
    id: 'equipment',
    name: 'Equipment',
    description: 'Equipment inspections, maintenance logs, and checklists',
    color: 'blue',
    icon: 'HardHat'
  },
  {
    id: 'progress',
    name: 'Progress',
    description: 'Daily logs, progress reports, and project updates',
    color: 'green',
    icon: 'TrendingUp'
  },
  {
    id: 'quality',
    name: 'Quality',
    description: 'Quality control, inspections, and compliance checks',
    color: 'purple',
    icon: 'CheckCircle'
  },
  {
    id: 'environmental',
    name: 'Environmental',
    description: 'Environmental monitoring and compliance forms',
    color: 'emerald',
    icon: 'Leaf'
  },
  {
    id: 'training',
    name: 'Training',
    description: 'Training records, assessments, and certifications',
    color: 'orange',
    icon: 'GraduationCap'
  },
  {
    id: 'administrative',
    name: 'Administrative',
    description: 'General administrative forms and requests',
    color: 'gray',
    icon: 'FileText'
  },
  {
    id: 'hr',
    name: 'Human Resources',
    description: 'Worker registration, onboarding, and HR processes',
    color: 'indigo',
    icon: 'Users'
  },
  {
    id: 'inspection',
    name: 'Inspections',
    description: 'Equipment inspections, safety checks, and compliance audits',
    color: 'blue',
    icon: 'ClipboardCheck'
  },
  {
    id: 'permit',
    name: 'Permits',
    description: 'Work permits, safety authorizations, and compliance permits',
    color: 'red',
    icon: 'FileText'
  }
];

// Form submission statistics
export interface FormStats {
	totalSubmissions: number;
	pendingReview: number;
	completedToday: number;
	averageCompletionTime: number;
	topCategories: Array<{
		category: string;
		count: number;
	}>;
	recentActivity: Array<{
		id: string;
		templateName: string;
		submittedBy: string;
		submittedAt: Date;
		status: FormSubmission["status"];
	}>;
}

// Form template creation/editing
export interface CreateFormTemplateRequest {
	name: string;
	description: string;
	categoryId: string;
	sections: Omit<FormSection, "id">[];
	isPublic: boolean;
	siteIds?: string[];
	roleIds?: string[];
	estimatedDuration?: number;
	instructions?: string;
	completionMessage?: string;
	tags: string[];
}

export interface UpdateFormTemplateRequest
	extends Partial<CreateFormTemplateRequest> {
	id: string;
}

// Form submission creation
export interface CreateFormSubmissionRequest {
	templateId: string;
	data: FormSubmissionData;
	status: "draft" | "submitted";
	location?: {
		latitude: number;
		longitude: number;
		accuracy?: number;
	};
}

export interface UpdateFormSubmissionRequest {
	id: string;
	data?: FormSubmissionData;
	status?: FormSubmission["status"];
	reviewComments?: string;
}
