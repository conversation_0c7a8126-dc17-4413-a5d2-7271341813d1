// GraphQL types for worker creation
export type Gender = "MALE" | "FEMALE";

export type PermitType = "GENERAL_WORK_PERMIT" | "HOT_WORK_PERMIT" | "CONFINED_SPACE_ENTRY_PERMIT" | "WORK_AT_HEIGHT_PERMIT" | "EXCAVATION_PERMIT";
export interface HazardInput {
  description: string;
  controlMeasures: string[];
}


export interface DocumentFileInput {
  name: string;
  file: File; // Upload type maps to File in browser
  description?: string | null;
  folderPath?: string | null;
  isPublic: boolean;
  expiresAt?: string | null; // ISO string (DateTime)
  additionalMetadata?: string | null;
}

export type WorkerTrainingInput = {
  trainingId: number;
  notes?: string;
  documents?: DocumentFileInput[];
};

export interface CreateWorkerInput {
  name: string;
  company: string;
  nationalId: string;
  gender: Gender;
  phoneNumber: string;
  dateOfBirth?: string | null; // ISO string (Date)
  trainingIds?: number[];
  tradeIds?: number[];
  skillIds?: number[];
  mpesaNumber: string;
  email?: string | null;
  inductionDate?: string | null; // ISO string (DateTime)
  medicalCheckDate?: string | null; // ISO string (DateTime)
  profilePicture?: File | null; // Upload
  signature?: File | null; // Upload
  documents?: DocumentFileInput[];
}

export type CreateWorkerWithTrainingInput = {
  name: string;
  company: string;
  nationalId: string;
  gender: Gender;
  phoneNumber: string;
  dateOfBirth?: string; // Use ISO string, e.g. "2025-07-21"
  trainings?: WorkerTrainingInput[];
  tradeIds: number[];
  skillIds: number[];
  mpesaNumber: string;
  email?: string;
  inductionDate?: string; // ISO 8601
  medicalCheckDate?: string; // ISO 8601
  profilePicture?: File | null; // Upload file as a stream
  signature?: File | null;
  documents?: DocumentFileInput[];
};
export interface UpdateWorkerWithTrainingInput {
  id: number;
  name?: string;
  company?: string;
  dateOfBirth?: string; // ISO format, e.g. "1990-06-12"
  trainings?: WorkerTrainingInput[];
  tradeIds?: number[];
  skillIds?: number[];
  manhours?: number[];
  rating? : number;
  gender?: string;
  phoneNumber?: string;
  mpesaNumber?: string;
  email?: string;
  inductionDate?: string;
  medicalCheckDate?: string;
  profilePicture? : File | null;
  nationalId?: string;
  documents?: DocumentFileInput[];
}

export interface ReviewJobInput {
  jobId: number;
  reviewedById: number;
  hazards: HazardInput[];
  requiredPermits: PermitType[];
  ppEs: string[];
  precautionsRequired: string[];
  trainingIds: number[];
  documents?: DocumentFileInput[];
  modesOfAccessToBeUsed?: string[];
  fireExtinguishers?: string[];
  excavationProtectionSystems?: string[];
  depthOfExcavation?: string;
  excavationEquipmentsToBeUsed?: string[];
  natureOfHotWork?: string[];
}