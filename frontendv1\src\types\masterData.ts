// Master Data Types for Data Page
export interface DataTab {
	id: string;
	label: string;
	icon: React.ComponentType;
	component: React.ComponentType<any>;
	permissions: string[];
}

export interface TrainingProgram {
	id: string;
	name: string;
	description: string;
	validityPeriod: number; // in months
	associatedTrades: string[]; // Trade IDs
	isMandatory: boolean;
	materials: TrainingMaterial[];
	createdAt: Date;
	updatedAt: Date;
	createdBy: string;
	status: "active" | "inactive" | "draft";
}

export interface TrainingMaterial {
	id: string;
	name: string;
	type: "document" | "video" | "presentation";
	url: string;
	size: number;
}

export interface PPEItem {
	id: string;
	name: string;
	sku: string;
	category: PPECategory;
	description: string;
	specifications: Record<string, string>;
	supplier: Supplier;
	unitCost: number;
	reorderLevel: number;
	safetyStandards: string[];
	images: string[];
	status: "active" | "discontinued";
	createdAt: Date;
	updatedAt: Date;
}

export interface PPECategory {
	id: string;
	name: string;
	description: string;
}

export interface Supplier {
	id: string;
	name: string;
	contactInfo: ContactInfo;
}

export interface ContactInfo {
	email: string;
	phone: string;
	address: string;
}

export interface FormTemplate {
	id: string;
	name: string;
	category: FormCategory;
	version: string;
	description: string;
	sections: FormSection[];
	status: "draft" | "active" | "archived";
	approvedBy?: string;
	approvedAt?: Date;
	createdAt: Date;
	updatedAt: Date;
}

export interface FormCategory {
	id: string;
	name: string;
	description: string;
}

export interface FormSection {
	id: string;
	title: string;
	description?: string;
	fields: FormField[];
	order: number;
}

export interface FormField {
	id: string;
	type: FieldType;
	label: string;
	placeholder?: string;
	required: boolean;
	options?: string[]; // for select/radio/checkbox
	validation?: FieldValidation;
	order: number;
}

export type FieldType =
	| "text"
	| "textarea"
	| "number"
	| "date"
	| "time"
	| "select"
	| "radio"
	| "checkbox"
	| "file"
	| "signature"
	| "photo";

export interface FieldValidation {
	min?: number;
	max?: number;
	pattern?: string;
	message?: string;
}

export interface MasterDataStats {
	trainingPrograms: number;
	ppeItems: number;
	permitTypes: number;
	formTemplates: number;
	incidentTypes: number;
	tradesSkills: number;
}

export interface RecentActivity {
	id: string;
	type: "created" | "updated" | "deleted";
	entityType: "training-program" | "ppe-item" | "form-template" | "permit-type";
	entityName: string;
	userName: string;
	timestamp: Date;
}
