// Task Template Types - Simplified for Permit-focused system
// Tasks are now predefined templates/categories that users select when creating permits

// Task Category Types - Extended for permit-related activities
export type TaskCategory =
	| "construction"
	| "electrical"
	| "plumbing"
	| "hvac"
	| "safety"
	| "inspection"
	| "maintenance"
	| "demolition"
	| "excavation"
	| "painting"
	| "roofing"
	| "flooring"
	| "hot-work"
	| "confined-space"
	| "working-at-height"
	| "chemical-handling"
	| "lifting-operations"
	| "scaffolding"
	| "trenching"
	| "asbestos-work"
	| "radiation-work"
	| "other";

// Risk Level Types
export type RiskLevel = "low" | "medium" | "high" | "critical";

// Task Template - Predefined work activities for permit creation
export interface TaskTemplate {
	id: string;
	name: string;
	description: string;
	category: TaskCategory;
	
	// Risk and Safety Requirements
	riskLevel: RiskLevel;
	requiredPermitTypes: string[];
	requiredPPE: string[];
	requiredTrainings: string[];
	requiredCertifications: string[];
	safetyRequirements: string[];
	
	// Estimated parameters
	estimatedDuration: number; // in hours
	typicalLocations: string[];
	
	// Additional requirements
	requiresSupervision: boolean;
	minimumWorkers: number;
	maximumWorkers: number;
	specialEquipment: string[];
	
	// Metadata
	isActive: boolean;
	createdAt: Date;
	updatedAt: Date;
}

// Task Template Categories for UI grouping
export interface TaskTemplateCategory {
	id: string;
	name: string;
	description: string;
	icon: string;
	color: string;
	templates: TaskTemplate[];
}

// Predefined Task Templates Data
export const TASK_TEMPLATE_CATEGORIES: TaskTemplateCategory[] = [
	{
		id: "high-risk",
		name: "High Risk Activities",
		description: "Activities requiring special permits and safety measures",
		icon: "AlertTriangle",
		color: "red",
		templates: []
	},
	{
		id: "construction",
		name: "Construction Work",
		description: "General construction and building activities",
		icon: "Hammer",
		color: "blue",
		templates: []
	},
	{
		id: "maintenance",
		name: "Maintenance & Repair",
		description: "Routine maintenance and repair activities",
		icon: "Wrench",
		color: "green",
		templates: []
	},
	{
		id: "inspection",
		name: "Inspection & Testing",
		description: "Quality control and safety inspections",
		icon: "Search",
		color: "purple",
		templates: []
	}
];

// Predefined Task Templates
export const PREDEFINED_TASK_TEMPLATES: TaskTemplate[] = [
	// High Risk Activities
	{
		id: "hot-work-welding",
		name: "Hot Work - Welding",
		description: "Welding operations requiring hot work permit",
		category: "hot-work",
		riskLevel: "high",
		requiredPermitTypes: ["Hot Work Permit"],
		requiredPPE: ["Welding helmet", "Fire-resistant clothing", "Safety boots", "Gloves"],
		requiredTrainings: ["Hot Work Safety", "Fire Prevention", "Welding Certification"],
		requiredCertifications: ["Welding Certificate", "Hot Work Permit"],
		safetyRequirements: ["Fire watch", "Fire extinguisher nearby", "Clear combustibles"],
		estimatedDuration: 4,
		typicalLocations: ["Workshop", "Construction site", "Maintenance area"],
		requiresSupervision: true,
		minimumWorkers: 2,
		maximumWorkers: 4,
		specialEquipment: ["Welding machine", "Fire extinguisher", "Fire blanket"],
		isActive: true,
		createdAt: new Date(),
		updatedAt: new Date()
	},
	{
		id: "confined-space-entry",
		name: "Confined Space Entry",
		description: "Entry into confined spaces requiring special permits",
		category: "confined-space",
		riskLevel: "critical",
		requiredPermitTypes: ["Confined Space Entry Permit"],
		requiredPPE: ["Full body harness", "Gas monitor", "Emergency breathing apparatus"],
		requiredTrainings: ["Confined Space Entry", "Gas Detection", "Emergency Response"],
		requiredCertifications: ["Confined Space Certificate", "Gas Monitor Certificate"],
		safetyRequirements: ["Atmospheric testing", "Continuous monitoring", "Standby person"],
		estimatedDuration: 6,
		typicalLocations: ["Tanks", "Vessels", "Underground spaces"],
		requiresSupervision: true,
		minimumWorkers: 3,
		maximumWorkers: 6,
		specialEquipment: ["Gas monitor", "Ventilation fan", "Emergency equipment"],
		isActive: true,
		createdAt: new Date(),
		updatedAt: new Date()
	},
	{
		id: "working-at-height",
		name: "Working at Height",
		description: "Work activities above 2 meters requiring height permits",
		category: "working-at-height",
		riskLevel: "high",
		requiredPermitTypes: ["Working at Height Permit"],
		requiredPPE: ["Safety harness", "Hard hat", "Safety boots", "High-vis vest"],
		requiredTrainings: ["Working at Height", "Fall Protection", "Scaffold Safety"],
		requiredCertifications: ["Height Work Certificate"],
		safetyRequirements: ["Fall protection system", "Secure anchor points", "Safety barriers"],
		estimatedDuration: 8,
		typicalLocations: ["Scaffolding", "Roof", "Platform", "Ladder work"],
		requiresSupervision: true,
		minimumWorkers: 2,
		maximumWorkers: 8,
		specialEquipment: ["Scaffolding", "Safety lines", "Fall arrest system"],
		isActive: true,
		createdAt: new Date(),
		updatedAt: new Date()
	},
	// Construction Activities
	{
		id: "concrete-pouring",
		name: "Concrete Pouring",
		description: "Concrete placement and finishing operations",
		category: "construction",
		riskLevel: "medium",
		requiredPermitTypes: [],
		requiredPPE: ["Hard hat", "Safety boots", "Gloves", "High-vis vest"],
		requiredTrainings: ["Concrete Safety", "Manual Handling"],
		requiredCertifications: [],
		safetyRequirements: ["Clear work area", "Proper lifting techniques"],
		estimatedDuration: 8,
		typicalLocations: ["Foundation", "Slab", "Column", "Beam"],
		requiresSupervision: true,
		minimumWorkers: 4,
		maximumWorkers: 12,
		specialEquipment: ["Concrete mixer", "Vibrator", "Finishing tools"],
		isActive: true,
		createdAt: new Date(),
		updatedAt: new Date()
	},
	{
		id: "electrical-installation",
		name: "Electrical Installation",
		description: "Electrical wiring and equipment installation",
		category: "electrical",
		riskLevel: "high",
		requiredPermitTypes: ["Electrical Work Permit"],
		requiredPPE: ["Insulated gloves", "Safety boots", "Hard hat", "Arc flash suit"],
		requiredTrainings: ["Electrical Safety", "LOTO Procedures", "Arc Flash Safety"],
		requiredCertifications: ["Electrical License", "Safety Certificate"],
		safetyRequirements: ["Lockout/Tagout", "Voltage testing", "Isolation verification"],
		estimatedDuration: 6,
		typicalLocations: ["Electrical room", "Panel board", "Cable tray", "Conduit"],
		requiresSupervision: true,
		minimumWorkers: 2,
		maximumWorkers: 4,
		specialEquipment: ["Voltage tester", "Insulated tools", "LOTO devices"],
		isActive: true,
		createdAt: new Date(),
		updatedAt: new Date()
	},
	// Maintenance Activities
	{
		id: "equipment-maintenance",
		name: "Equipment Maintenance",
		description: "Routine maintenance of machinery and equipment",
		category: "maintenance",
		riskLevel: "medium",
		requiredPermitTypes: [],
		requiredPPE: ["Safety glasses", "Gloves", "Safety boots", "Hard hat"],
		requiredTrainings: ["Equipment Safety", "LOTO Procedures", "Maintenance Procedures"],
		requiredCertifications: ["Maintenance Certificate"],
		safetyRequirements: ["Equipment isolation", "Proper tools", "Clear procedures"],
		estimatedDuration: 4,
		typicalLocations: ["Machine shop", "Equipment room", "Production area"],
		requiresSupervision: false,
		minimumWorkers: 1,
		maximumWorkers: 3,
		specialEquipment: ["Hand tools", "Lubricants", "Spare parts"],
		isActive: true,
		createdAt: new Date(),
		updatedAt: new Date()
	},
	// Inspection Activities
	{
		id: "safety-inspection",
		name: "Safety Inspection",
		description: "Regular safety inspections and audits",
		category: "inspection",
		riskLevel: "low",
		requiredPermitTypes: [],
		requiredPPE: ["Hard hat", "Safety boots", "High-vis vest"],
		requiredTrainings: ["Safety Inspection", "Hazard Identification"],
		requiredCertifications: ["Safety Inspector Certificate"],
		safetyRequirements: ["Inspection checklist", "Proper documentation"],
		estimatedDuration: 2,
		typicalLocations: ["All work areas", "Equipment locations", "Safety systems"],
		requiresSupervision: false,
		minimumWorkers: 1,
		maximumWorkers: 2,
		specialEquipment: ["Inspection tools", "Camera", "Measuring devices"],
		isActive: true,
		createdAt: new Date(),
		updatedAt: new Date()
	}
];

// Task Template Filters
export interface TaskTemplateFilters {
	search: string;
	category: TaskCategory | "all";
	requiresPermit: boolean | "all";
	isActive: boolean | "all";
}
