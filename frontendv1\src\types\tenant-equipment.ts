// Tenant-based Equipment Management Types
// Following industry best practices for resource ownership

export interface Equipment {
  id: number;
  tenantId: string; // Equipment belongs to tenant/organization
  name: string;
  type: string;
  serialNumber?: string;
  purchaseDate: string;
  status: 'available' | 'in-use' | 'maintenance' | 'retired';
  condition: 'excellent' | 'good' | 'fair' | 'poor' | 'damaged';
  acquisitionCost: number;
  currentValue: number;
  expectedLifespanDays: number;
  totalUsageHours: number;
  lastMaintenanceDate?: string;
  nextMaintenanceDueDate?: string;
  lastInspectionDate?: string;
  nextInspectionDueDate?: string;
  qrCode?: string;
  specifications: Record<string, string>;
  images: string[];
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
  // Navigation properties
  siteAllocations: EquipmentSiteAllocation[];
  maintenanceRecords: MaintenanceRecord[];
  inspectionRecords: InspectionRecord[];
}

export interface EquipmentSiteAllocation {
  id: number;
  equipmentId: number;
  siteId: string;
  allocatedDate: string;
  returnDate?: string;
  purpose: string;
  condition: 'excellent' | 'good' | 'fair' | 'poor' | 'damaged';
  notes?: string;
  status: 'active' | 'returned' | 'transferred';
  allocatedBy: string;
  returnedBy?: string;
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
  // Navigation properties
  equipment?: Equipment;
  site?: any; // SiteInfo
}

export interface MaintenanceRecord {
  id: number;
  equipmentId: number;
  siteId?: string; // Where maintenance was performed
  maintenanceType: 'preventive' | 'corrective' | 'emergency';
  description: string;
  performedBy: string;
  performedDate: string;
  cost: number;
  partsUsed?: string[];
  nextMaintenanceDate?: string;
  status: 'scheduled' | 'in-progress' | 'completed' | 'cancelled';
  notes?: string;
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
  // Navigation properties
  equipment?: Equipment;
}

export interface InspectionRecord {
  id: number;
  equipmentId: number;
  siteId?: string; // Where inspection was performed
  inspectionType: 'daily' | 'weekly' | 'monthly' | 'annual' | 'pre-use';
  inspectedBy: string;
  inspectionDate: string;
  overallStatus: 'pass' | 'fail' | 'pass-with-issues';
  findings: InspectionFinding[];
  nextInspectionDate?: string;
  notes?: string;
  photos: string[];
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
  // Navigation properties
  equipment?: Equipment;
}

export interface InspectionFinding {
  id: number;
  category: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'resolved' | 'deferred';
  actionRequired?: string;
  resolvedDate?: string;
  resolvedBy?: string;
}

// PPE Management with tenant-based architecture
export interface PPEMaster {
  id: number;
  tenantId: string; // PPE belongs to tenant
  name: string;
  sku: string;
  category: PPECategory;
  description: string;
  unitCost: number;
  supplier?: Supplier;
  reorderLevel: number;
  safetyStandards: string[];
  specifications: Record<string, string>;
  images: string[];
  status: 'active' | 'discontinued';
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
  // Navigation properties
  siteStock: SitePPEStock[];
  assignments: PPEAssignment[];
}

export interface SitePPEStock {
  id: number;
  ppeMasterId: number;
  siteId: string;
  currentStock: number;
  minStock: number;
  maxStock: number;
  lastRestockDate?: string;
  nextRestockDate?: string;
  averageUsagePerMonth: number;
  status: 'in-stock' | 'low-stock' | 'out-of-stock';
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
  // Navigation properties
  ppeMaster?: PPEMaster;
  site?: any; // SiteInfo
}

export interface PPEAssignment {
  id: number;
  ppeMasterId: number;
  workerId: number;
  siteId: string;
  quantity: number;
  assignedDate: string;
  returnDate?: string;
  condition: 'new' | 'good' | 'fair' | 'damaged' | 'lost';
  notes?: string;
  status: 'assigned' | 'returned' | 'lost' | 'damaged';
  assignedBy: string;
  returnedBy?: string;
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
  // Navigation properties
  ppeMaster?: PPEMaster;
  worker?: any; // Worker
  site?: any; // SiteInfo
}

export interface PPECategory {
  id: number;
  tenantId: string;
  name: string;
  description: string;
  safetyRequirements: Record<string, any>;
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
}

export interface Supplier {
  id: number;
  tenantId: string;
  name: string;
  contactInfo: ContactInfo;
  status: 'active' | 'inactive';
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
}

export interface ContactInfo {
  email: string;
  phone: string;
  address: string;
  contactPerson?: string;
}

// Dashboard and reporting types
export interface EquipmentStats {
  totalEquipment: number;
  availableEquipment: number;
  inUseEquipment: number;
  maintenanceRequired: number;
  overdueInspections: number;
  totalPPEItems: number;
  lowStockPPE: number;
  totalMaintenanceCost: number;
  averageUtilization: number;
}

export interface EquipmentUtilization {
  equipmentId: number;
  equipmentName: string;
  totalHours: number;
  utilizationPercentage: number;
  currentSite?: string;
  status: string;
}

export interface CrossSiteEquipmentView {
  equipmentId: number;
  equipmentName: string;
  type: string;
  currentSite?: string;
  status: string;
  lastMoved?: string;
  utilizationRate: number;
  nextMaintenance?: string;
}
