export interface TimeLog {
  id: string;
  workerId: string;
  workerName: string;
  workerPhoto?: string;
  workerTrade: string;
  date: string;
  clockIn?: string;
  clockOut?: string;
  breakDuration?: number; // in minutes
  totalHours?: number;
  overtime?: number;
  status: 'on-site' | 'late' | 'absent' | 'off-site';
  toolboxTalkAttended: boolean;
  isManuallyEdited?: boolean;
  editReason?: string;
  editedBy?: string;
  editedAt?: string;
  isVerifiedByHikvision?: boolean; // Face recognition verification
  hikvisionPersonId?: string; // Hikvision person ID for face recognition
  terminalId?: string; // Terminal used for check-in/out
}

export interface Worker {
	id: string;
	name: string;
	photo?: string;
	primaryTrade: string;
	employeeId: string;
	status: "active" | "inactive";
	scheduledStartTime?: string;
}

export interface TerminalStatus {
	id: string;
	name: string;
	location: string;
	status: "online" | "offline" | "error";
	lastSync?: string;
	totalCheckInsToday: number;
	ipAddress?: string;
}

export interface TimeLogFilters {
	search: string;
	status: "all" | "on-site" | "late" | "absent" | "off-site";
	trade: string;
	date: string;
}

export interface AttendanceSummary {
  totalWorkers: number;
  presentToday: number;
  late: number;
  absent: number;
  onSite: number;
  verifiedByHikvision: number;
  manualEntries: number;
}

export interface OvertimeRequest {
	id: string;
	workerId: string;
	workerName: string;
	date: string;
	requestedHours: number;
	reason: string;
	status: "pending" | "approved" | "rejected";
	requestedBy: string;
	requestedAt: string;
	reviewedBy?: string;
	reviewedAt?: string;
	reviewNotes?: string;
}

export interface TimeCalculation {
	regularHours: number;
	overtimeHours: number;
	totalHours: number;
	breakDuration: number;
}

export interface EditTimeLogData {
	clockIn?: string;
	clockOut?: string;
	breakDuration?: number;
	reason: string;
}

// Hikvision Integration Types
export interface HikvisionSyncResult {
  success: boolean;
  syncedCount: number;
  message: string;
  attendanceRecords: {
    workerId: string;
    checkInTime: string;
    checkOutTime?: string;
    isVerified: boolean;
    terminalId: string;
  }[];
}

export interface FaceRegistrationResult {
  success: boolean;
  hikvisionPersonId?: string;
  message: string;
}

export interface TerminalConfiguration {
  id: string;
  name: string;
  location: string;
  ipAddress: string;
  port: number;
  username: string;
  password: string;
  deviceType: 'access-control' | 'time-attendance';
  isActive: boolean;
}

export interface SyncOperation {
  id: string;
  siteId: string;
  date: string;
  status: 'pending' | 'in-progress' | 'completed' | 'failed';
  startedAt: string;
  completedAt?: string;
  syncedCount: number;
  errorMessage?: string;
}
