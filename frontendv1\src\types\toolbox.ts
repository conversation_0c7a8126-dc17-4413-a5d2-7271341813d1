// Toolbox System Types
// Based on GraphQL schema and specification requirements

export interface ToolboxConductor {
  workerId: number;
  name: string;
  signatureFileId: string;
}

export interface ToolboxAttendee {
  workerId: number;
  name: string;
  designation: string;
  signatureFileId: string;
}

export interface ControlMeasure {
  id: number;
  description: string;
  closed: boolean;
  hazardId: number;
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
  isDeleted: boolean;
  deletedAt?: string;
  deletedBy?: string;
}

export interface Hazard {
  id: number;
  description: string;
  jobId: number;
  controlMeasures: ControlMeasure[];
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
  isDeleted: boolean;
  deletedAt?: string;
  deletedBy?: string;
}

export interface Job {
  id: number;
  title: string;
  description?: string;
  status: JobStatus;
  requiredPermits?: PermitType[];
  timeForCompletion?: string;
  startDate: string;
  dueDate?: string;
  calculatedDueDate: string;
  hazards: Hazard[];
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
  isDeleted: boolean;
  deletedAt?: string;
  deletedBy?: string;
}

export interface Toolbox {
  id: number;
  date: string;
  status: ToolboxStatus;
  emergencyProcedures: string;
  toolboxTrainingTopics: string;
  closedDate?: string;
  conductor?: ToolboxConductor;
  conductedBy?: ToolboxConductor;
  attendees?: ToolboxAttendee[];
  jobs?: Job[];
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
  isDeleted: boolean;
  deletedAt?: string;
  deletedBy?: string;
}

// Enums
export enum ToolboxStatus {
  OPEN = 'OPEN',
  CLOSED = 'CLOSED',
  DRAFT = 'DRAFT'
}

export enum JobStatus {
  REQUESTED = 'REQUESTED',
  BLOCKED = 'BLOCKED',
  PENDING_APPROVAL = 'PENDING_APPROVAL',
  APPROVED = 'APPROVED',
  DISAPPROVED = 'DISAPPROVED',
  FINISHED = 'FINISHED'
}

export enum PermitType {
  GENERAL_WORK = 'GENERAL_WORK',
  HOT_WORK = 'HOT_WORK',
  CONFINED_SPACE = 'CONFINED_SPACE',
  EXCAVATION = 'EXCAVATION',
  WORK_AT_HEIGHT = 'WORK_AT_HEIGHT'
}

// Input Types for Mutations
export interface CreateToolboxExistingControlMeasureInput {
  id: number;
  description: string;
}

export interface CreateToolboxNewControlMeasureInput {
  description: string;
}

export interface CreateToolboxExistingHazardInput {
  id: number;
  description: string;
  existingControlMeasures: CreateToolboxExistingControlMeasureInput[];
  newControlMeasures: CreateToolboxNewControlMeasureInput[];
}

export interface CreateToolboxNewHazardInput {
  description: string;
  controlMeasures: CreateToolboxNewControlMeasureInput[];
}

export interface CreateToolboxJobInput {
  jobId: number;
  existingHazards: CreateToolboxExistingHazardInput[];
  newHazards: CreateToolboxNewHazardInput[];
}

export interface CreateToolboxInput {
  conductorId: number;
  jobs: CreateToolboxJobInput[];
  emergencyProcedures: string;
  toolboxTrainingTopics: string;
}

export interface SummarizeToolboxInput {
  toolboxId: number;
  jobs: CreateToolboxJobInput[];
}

export interface AddHazardInput {
  jobId: number;
  description: string;
  controlMeasures: string[];
}

export interface AddControlMeasureInput {
  hazardId: number;
  description: string;
}

// Risk Assessment Types
export interface TodaysJobControlMeasure {
  id: number;
  description: string;
}

export interface TodaysJobHazard {
  id: number;
  description: string;
  controlMeasures: TodaysJobControlMeasure[];
}

export interface TodaysJobRiskAssessment {
  id: number;
  title: string;
  hazards: TodaysJobHazard[];
}

export interface ToolboxRiskAssessmentControlMeasure {
  id: number;
  description: string;
}

export interface ToolboxRiskAssessmentHazard {
  id: number;
  description: string;
  controlMeasures: ToolboxRiskAssessmentControlMeasure[];
}

export interface ToolboxRiskAssessment {
  id: number;
  title: string;
  hazards: ToolboxRiskAssessmentHazard;
}

// Form State Types
export interface ToolboxFormData {
  conductorId: number;
  emergencyProcedures: string;
  toolboxTrainingTopics: string;
  jobs: {
    [jobId: number]: {
      existingHazards: {
        [hazardId: number]: {
          description: string;
          existingControlMeasures: {
            [controlMeasureId: number]: {
              description: string;
            };
          };
          newControlMeasures: {
            description: string;
          }[];
        };
      };
      newHazards: {
        description: string;
        controlMeasures: {
          description: string;
        }[];
      }[];
    };
  };
}

export interface AttendanceFormData {
  toolboxId: number;
  selectedWorkers: number[];
}

// Worker types for attendance
export interface WorkerForAttendance {
  id: number;
  name: string;
  company: string;
  signatureFileId?: number;
  trainings?: {
    id: number;
    name: string;
  }[];
}
