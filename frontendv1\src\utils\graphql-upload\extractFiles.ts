// extractFiles.ts

type FilesMap = Map<any, string[]>;

function isExtractableFile(value: any): value is File | Blob {
  return (
    (typeof File !== 'undefined' && value instanceof File) ||
    (typeof Blob !== 'undefined' && value instanceof Blob)
  );
}

export function extractFiles(
  value: any,
  path = 'variables',
  filesMap: FilesMap = new Map(),
  fileIndex = 0
): { clone: any; filesMap: FilesMap } {
  if (isExtractableFile(value)) {
    filesMap.set(fileIndex, [path]);
    return { clone: null, filesMap };
  }

  if (Array.isArray(value)) {
    const clone = value.map((item, index) =>
      extractFiles(item, `${path}.${index}`, filesMap, filesMap.size).clone
    );
    return { clone, filesMap };
  }

  if (value !== null && typeof value === 'object') {
    const clone: Record<string, any> = {};
    for (const key in value) {
      const { clone: nestedClone } = extractFiles(
        value[key],
        `${path}.${key}`,
        filesMap,
        filesMap.size
      );
      clone[key] = nestedClone;
    }
    return { clone, filesMap };
  }

  return { clone: value, filesMap };
}
