// Regulatory compliance validation utilities

import { RegulatoryDocument } from '../components/regulatory/RegulatoryDocumentUpload';

export interface RegulatoryData {
  // Building Permit
  buildingPermitRequired: boolean;
  buildingPermitStatus: string;
  buildingPermitNumber: string;
  buildingPermitAuthority: string;
  buildingPermitDate: string;
  buildingPermitExpiry: string;
  buildingPermitDocument: RegulatoryDocument | null;

  // NEMA EIA
  nemaEiaRequired: boolean;
  nemaEiaStatus: string;
  nemaEiaNumber: string;
  nemaEiaDate: string;
  nemaEiaDocument: RegulatoryDocument | null;

  // NCA Registration
  ncaRegistrationRequired: boolean;
  ncaRegistrationStatus: string;
  ncaRegistrationNumber: string;
  ncaRegistrationDate: string;
  ncaRegistrationDocument: RegulatoryDocument | null;

  // Fire Safety
  fireSafetyRequired: boolean;
  fireSafetyStatus: string;
  fireSafetyNumber: string;
  fireSafetyDate: string;
  fireSafetyDocument: RegulatoryDocument | null;

  // Overall status
  overallComplianceStatus: string;

  // Additional approvals
  additionalApprovals: Array<{
    type: string;
    authority: string;
    status: string;
    referenceNumber: string;
    date: string;
    notes: string;
    document: RegulatoryDocument | null;
  }>;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

/**
 * Validate building permit data
 */
export const validateBuildingPermit = (data: RegulatoryData): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (data.buildingPermitRequired) {
    if (data.buildingPermitStatus === 'Approved') {
      if (!data.buildingPermitNumber.trim()) {
        errors.push('Building permit number is required when status is Approved');
      }
      if (!data.buildingPermitAuthority.trim()) {
        errors.push('Building permit authority is required when status is Approved');
      }
      if (!data.buildingPermitDate) {
        errors.push('Building permit issue date is required when status is Approved');
      }
      if (!data.buildingPermitDocument) {
        errors.push('Building permit document is required when status is Approved');
      }
    }

    if (data.buildingPermitExpiry && data.buildingPermitDate) {
      const issueDate = new Date(data.buildingPermitDate);
      const expiryDate = new Date(data.buildingPermitExpiry);
      
      if (expiryDate <= issueDate) {
        errors.push('Building permit expiry date must be after issue date');
      }
      
      if (expiryDate < new Date()) {
        warnings.push('Building permit has expired');
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Validate NEMA EIA data
 */
export const validateNemaEia = (data: RegulatoryData): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (data.nemaEiaRequired) {
    if (data.nemaEiaStatus === 'Approved') {
      if (!data.nemaEiaNumber.trim()) {
        errors.push('NEMA EIA number is required when status is Approved');
      }
      if (!data.nemaEiaDate) {
        errors.push('NEMA EIA issue date is required when status is Approved');
      }
      if (!data.nemaEiaDocument) {
        errors.push('NEMA EIA document is required when status is Approved');
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Validate NCA Registration data
 */
export const validateNcaRegistration = (data: RegulatoryData): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (data.ncaRegistrationRequired) {
    if (data.ncaRegistrationStatus === 'Approved') {
      if (!data.ncaRegistrationNumber.trim()) {
        errors.push('NCA registration number is required when status is Approved');
      }
      if (!data.ncaRegistrationDate) {
        errors.push('NCA registration date is required when status is Approved');
      }
      if (!data.ncaRegistrationDocument) {
        errors.push('NCA registration document is required when status is Approved');
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Validate Fire Safety data
 */
export const validateFireSafety = (data: RegulatoryData): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  if (data.fireSafetyRequired) {
    if (data.fireSafetyStatus === 'Approved') {
      if (!data.fireSafetyNumber.trim()) {
        errors.push('Fire safety certificate number is required when status is Approved');
      }
      if (!data.fireSafetyDate) {
        errors.push('Fire safety certificate issue date is required when status is Approved');
      }
      if (!data.fireSafetyDocument) {
        errors.push('Fire safety certificate document is required when status is Approved');
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Validate additional approvals
 */
export const validateAdditionalApprovals = (data: RegulatoryData): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  data.additionalApprovals.forEach((approval, index) => {
    if (!approval.type.trim()) {
      errors.push(`Additional approval ${index + 1}: Type is required`);
    }
    if (!approval.authority.trim()) {
      errors.push(`Additional approval ${index + 1}: Authority is required`);
    }
    
    if (approval.status === 'Approved') {
      if (!approval.referenceNumber.trim()) {
        warnings.push(`Additional approval ${index + 1}: Reference number recommended when approved`);
      }
      if (!approval.date) {
        warnings.push(`Additional approval ${index + 1}: Issue date recommended when approved`);
      }
      if (!approval.document) {
        warnings.push(`Additional approval ${index + 1}: Document recommended when approved`);
      }
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Comprehensive validation of all regulatory data
 */
export const validateRegulatoryData = (data: RegulatoryData): ValidationResult => {
  const allErrors: string[] = [];
  const allWarnings: string[] = [];

  // Validate each section
  const buildingPermitValidation = validateBuildingPermit(data);
  const nemaEiaValidation = validateNemaEia(data);
  const ncaRegistrationValidation = validateNcaRegistration(data);
  const fireSafetyValidation = validateFireSafety(data);
  const additionalApprovalsValidation = validateAdditionalApprovals(data);

  // Combine all errors and warnings
  allErrors.push(...buildingPermitValidation.errors);
  allErrors.push(...nemaEiaValidation.errors);
  allErrors.push(...ncaRegistrationValidation.errors);
  allErrors.push(...fireSafetyValidation.errors);
  allErrors.push(...additionalApprovalsValidation.errors);

  allWarnings.push(...buildingPermitValidation.warnings);
  allWarnings.push(...nemaEiaValidation.warnings);
  allWarnings.push(...ncaRegistrationValidation.warnings);
  allWarnings.push(...fireSafetyValidation.warnings);
  allWarnings.push(...additionalApprovalsValidation.warnings);

  // Check if any required approvals are missing
  const hasRequiredApprovals = data.buildingPermitRequired || 
                               data.nemaEiaRequired || 
                               data.ncaRegistrationRequired || 
                               data.fireSafetyRequired ||
                               data.additionalApprovals.length > 0;

  if (!hasRequiredApprovals) {
    allWarnings.push('No regulatory approvals are marked as required. Please verify this is correct.');
  }

  return {
    isValid: allErrors.length === 0,
    errors: allErrors,
    warnings: allWarnings
  };
};

/**
 * Check if regulatory compliance is ready for site creation
 */
export const isRegulatoryCompliant = (data: RegulatoryData): boolean => {
  const validation = validateRegulatoryData(data);
  
  // Must have no validation errors
  if (!validation.isValid) {
    return false;
  }

  // Check that all required approvals are at least in progress
  const requiredStatuses: string[] = [];
  
  if (data.buildingPermitRequired) requiredStatuses.push(data.buildingPermitStatus);
  if (data.nemaEiaRequired) requiredStatuses.push(data.nemaEiaStatus);
  if (data.ncaRegistrationRequired) requiredStatuses.push(data.ncaRegistrationStatus);
  if (data.fireSafetyRequired) requiredStatuses.push(data.fireSafetyStatus);
  
  data.additionalApprovals.forEach(approval => {
    requiredStatuses.push(approval.status);
  });

  // All required approvals should be at least "In Progress" or "Approved"
  const validStatuses = ['In Progress', 'Approved'];
  return requiredStatuses.every(status => validStatuses.includes(status));
};

/**
 * Get completion percentage for regulatory compliance
 */
export const getRegulatoryCompletionPercentage = (data: RegulatoryData): number => {
  let totalItems = 0;
  let completedItems = 0;

  // Count required approvals
  if (data.buildingPermitRequired) {
    totalItems++;
    if (data.buildingPermitStatus === 'Approved') completedItems++;
  }
  
  if (data.nemaEiaRequired) {
    totalItems++;
    if (data.nemaEiaStatus === 'Approved') completedItems++;
  }
  
  if (data.ncaRegistrationRequired) {
    totalItems++;
    if (data.ncaRegistrationStatus === 'Approved') completedItems++;
  }
  
  if (data.fireSafetyRequired) {
    totalItems++;
    if (data.fireSafetyStatus === 'Approved') completedItems++;
  }

  // Count additional approvals
  data.additionalApprovals.forEach(approval => {
    totalItems++;
    if (approval.status === 'Approved') completedItems++;
  });

  if (totalItems === 0) return 100; // No approvals required
  
  return Math.round((completedItems / totalItems) * 100);
};
