import { NavigationContext } from "../types";

/**
 * Determines the navigation context based on the current pathname
 */
export const getNavigationContext = (pathname: string): NavigationContext => {
	// Check if we're in a site-specific route
	const siteRouteMatch = pathname.match(/^\/sites\/([^\/]+)/);

	if (siteRouteMatch) {
		const siteId = siteRouteMatch[1];
		return {
			isCompanyLevel: false,
			isSiteLevel: true,
			siteId,
			siteName: undefined, // Will be populated by the hook
		};
	}

	// Default to company level
	return {
		isCompanyLevel: true,
		isSiteLevel: false,
		siteId: undefined,
		siteName: undefined,
	};
};

/**
 * Determines if a route should use minimal TopBar mode
 */
export const isMinimalRoute = (pathname: string): boolean => {
	const minimalPatterns = [
		/^\/sites\/new$/,
		/^\/sites\/[^\/]+\/workers\/new$/,
		/^\/sites\/[^\/]+\/workers\/edit\/[^\/]+$/,
		/^\/sites\/[^\/]+\/permits\/new$/,
		/^\/sites\/[^\/]+\/permits\/edit\/[^\/]+$/,
		/^\/sites\/[^\/]+\/incidents\/new$/,
		/^\/sites\/[^\/]+\/incidents\/edit\/[^\/]+$/,
		/^\/sites\/[^\/]+\/equipment\/new$/,
		/^\/sites\/[^\/]+\/equipment\/edit\/[^\/]+$/,
		/^\/demo\/ptw-form$/,
		/^\/demo\/hot-work-form$/,
		/^\/demo\/confined-space-form$/,
		/^\/demo\/work-at-height-form$/,
	];

	return minimalPatterns.some(pattern => pattern.test(pathname));
};

/**
 * Determines if a route should show the back button
 */
export const shouldShowBackButton = (pathname: string): boolean => {
	// Show back button for creation/edit workflows and demo forms
	if (isMinimalRoute(pathname)) return true;

	// Show back button for secondary/detail pages
	const secondaryPagePatterns = [
		/^\/sites\/[^\/]+\/tasks\/[^\/]+$/,
		/^\/sites\/[^\/]+\/tasks\/request\/[^\/]+$/,
		/^\/sites\/[^\/]+\/workers\/[^\/]+$/,
		/^\/sites\/[^\/]+\/permits\/[^\/]+\/[^\/]+$/,
		/^\/sites\/[^\/]+\/engineer\/permits\/[^\/]+$/,
		/^\/sites\/[^\/]+\/inspections\/form\/[^\/]+$/,
		/^\/sites\/[^\/]+\/toolbox\/fill$/,
		/^\/sites\/[^\/]+\/toolbox\/attendance$/,
		/^\/sites\/[^\/]+\/toolbox\/summarize$/,
		/^\/sites\/[^\/]+\/training\/new$/,
		/^\/sites\/[^\/]+\/tasks\/review$/,
		/^\/sites\/[^\/]+\/tasks\/approve$/,
		/^\/sites\/[^\/]+\/tasks\/close$/,
		/^\/sites\/[^\/]+\/tasks\/all$/,
		/^\/sites\/[^\/]+\/permits\/approve$/,
	];

	return secondaryPagePatterns.some(pattern => pattern.test(pathname));
};

/**
 * Gets the appropriate page title based on the route
 */
export const getPageTitle = (pathname: string, siteName?: string): string => {
	// Creation workflows
	if (pathname.includes('/sites/new')) return 'Create New Site';
	if (pathname.includes('/workers/new')) return 'Add New Worker';
	if (pathname.includes('/workers/edit')) return 'Edit Worker';
	if (pathname.includes('/permits/new')) return 'Create New Permit';
	if (pathname.includes('/permits/edit')) return 'Edit Permit';
	if (pathname.includes('/incidents/new')) return 'Report New Incident';
	if (pathname.includes('/incidents/edit')) return 'Edit Incident';
	if (pathname.includes('/equipment/new')) return 'Add New Equipment';
	if (pathname.includes('/equipment/edit')) return 'Edit Equipment';

	// Demo forms
	if (pathname.includes('/demo/ptw-form')) return 'Permit to Work Form';
	if (pathname.includes('/demo/hot-work-form')) return 'Hot Work Permit Form';
	if (pathname.includes('/demo/confined-space-form')) return 'Confined Space Entry System Demo';
	if (pathname.includes('/demo/work-at-height-form')) return 'Work at Height Permit Form';

	// Site dashboard
	if (pathname.match(/^\/sites\/[^\/]+\/(dashboard)?$/)) {
		return siteName ? `${siteName} Dashboard` : 'Site Dashboard';
	}

	// Secondary/Detail pages (more specific patterns first)
	if (pathname.match(/\/tasks\/request\/[^\/]+$/)) return 'Task Request';
	if (pathname.match(/\/tasks\/[^\/]+$/)) return 'Task Details';
	if (pathname.match(/\/workers\/[^\/]+$/)) return 'Worker Profile';
	if (pathname.match(/\/permits\/[^\/]+\/[^\/]+$/)) return 'Permit Details';
	if (pathname.match(/\/engineer\/permits\/[^\/]+$/)) return 'Permit Details';
	if (pathname.match(/\/inspections\/form\/[^\/]+$/)) return 'Inspection Form';
	if (pathname.includes('/toolbox/fill')) return 'Fill Toolbox Talk';
	if (pathname.includes('/toolbox/attendance')) return 'Toolbox Attendance';
	if (pathname.includes('/toolbox/summarize')) return 'Toolbox Summary';
	if (pathname.includes('/training/new')) return 'New Training';
	if (pathname.includes('/tasks/review')) return 'Review Tasks';
	if (pathname.includes('/tasks/approve')) return 'Approve Tasks';
	if (pathname.includes('/tasks/close')) return 'Close Tasks';
	if (pathname.includes('/tasks/all')) return 'All Tasks';
	if (pathname.includes('/permits/approve')) return 'Approve Permits';

	// Main section pages
	if (pathname.includes('/workers')) return 'Workers';
	if (pathname.includes('/permits')) return 'Permits';
	if (pathname.includes('/incidents')) return 'Incidents';
	if (pathname.includes('/equipment')) return 'Equipment';
	if (pathname.includes('/training')) return 'Training';
	if (pathname.includes('/safety')) return 'Safety';
	if (pathname.includes('/data')) return 'Data';
	if (pathname.includes('/info')) return 'Site Information';
	if (pathname.includes('/tasks')) return 'Tasks';
	if (pathname.includes('/inspections')) return 'Inspections';
	if (pathname.includes('/toolbox')) return 'Toolbox Talks';
	if (pathname.includes('/engineer')) return 'Engineer Dashboard';

	// Company level
	if (pathname === '/') return 'Dashboard';
	if (pathname.includes('/settings')) return 'Settings';
	if (pathname.includes('/account')) return 'Account';
	if (pathname.includes('/reports')) return 'Reports';

	return 'Page';
};

/**
 * Checks if a given path is active based on current location
 */
export const isPathActive = (
	currentPath: string,
	targetPath: string,
): boolean => {
	// Handle hash-based navigation
	if (targetPath.includes("#")) {
		const currentFullPath = currentPath + window.location.hash;
		return currentFullPath === targetPath;
	}

	// Exact match for root path
	if (targetPath === "/" && currentPath === "/") {
		return true;
	}

	// For non-root paths, check if current path starts with target path
	if (targetPath !== "/" && currentPath.startsWith(targetPath)) {
		return true;
	}

	return false;
};

/**
 * Checks if any submenu item is active
 */
export const isSubmenuActive = (
	currentPath: string,
	submenuItems: { path: string }[],
): boolean => {
	return submenuItems.some((item) => isPathActive(currentPath, item.path));
};

/**
 * Generates site-specific path
 */
export const getSitePath = (siteId: string, path: string): string => {
	return `/sites/${siteId}${path}`;
};

/**
 * Extracts site ID from current path
 */
export const extractSiteId = (pathname: string): string | null => {
	const match = pathname.match(/^\/sites\/([^\/]+)/);
	return match ? match[1] : null;
};

/**
 * Generates equivalent path for a different site
 * Maps current site-specific path to equivalent path in target site
 */
export const getEquivalentSitePath = (
	currentPath: string,
	targetSiteId: string,
): string => {
	// Extract the current site ID and the rest of the path
	const siteRouteMatch = currentPath.match(/^\/sites\/([^\/]+)(.*)$/);

	if (!siteRouteMatch) {
		// If not a site route, default to site dashboard
		return `/sites/${targetSiteId}/dashboard`;
	}

	const [, /*currentSiteId*/, restOfPath] = siteRouteMatch;

	// If no additional path, go to dashboard
	if (!restOfPath || restOfPath === "/dashboard") {
		return `/sites/${targetSiteId}/dashboard`;
	}

	// Map the rest of the path to the new site
	return `/sites/${targetSiteId}${restOfPath}`;
};

/**
 * Checks if a breadcrumb item is the site name (second item in site breadcrumbs)
 */
export const isSiteBreadcrumb = (
	breadcrumbs: { name: string; path: string }[],
	index: number,
): boolean => {
	// Site name is typically the second breadcrumb (index 1) in site views
	// Pattern: Dashboard / [Site Name] / ...
	return (
		index === 1 &&
		breadcrumbs.length >= 2 &&
		breadcrumbs[0].name === "Dashboard"
	);
};
