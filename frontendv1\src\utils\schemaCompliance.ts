// Schema compliance testing utilities
// Validates that our site boundary data matches the schema requirements

export interface SiteSchemaLocation {
  searchQuery: string;
  displayName: string;
  addressStreet: string;
  addressCity: string;
  addressCounty: string;
  addressPostalCode: string;
  addressCountry: string;
  latitude: number;
  longitude: number;
  accuracy: string;
  osmPlaceId: string;
  osmType: string;
  osmId: string;
}

export interface SiteSchemaBoundary {
  geometry: {
    type: 'Polygon';
    coordinates: number[][][];
  };
  drawingComplete: boolean;
  lastModified: string;
}

export interface SchemaComplianceResult {
  isCompliant: boolean;
  errors: string[];
  warnings: string[];
  missingFields: string[];
  extraFields: string[];
}

/**
 * Validate that site boundary data complies with the schema
 */
export const validateSchemaCompliance = (data: any): SchemaComplianceResult => {
  const errors: string[] = [];
  const warnings: string[] = [];
  const missingFields: string[] = [];
  const extraFields: string[] = [];

  // Required location fields from schema
  const requiredLocationFields = [
    'searchQuery',
    'displayName', 
    'addressStreet',
    'addressCity',
    'addressCounty',
    'addressPostalCode',
    'addressCountry',
    'latitude',
    'longitude',
    'accuracy',
    'osmPlaceId',
    'osmType',
    'osmId'
  ];

  // Required boundary fields from schema
  const requiredBoundaryFields = [
    'geometry',
    'drawingComplete',
    'lastModified'
  ];

  // Check required location fields
  requiredLocationFields.forEach(field => {
    if (!(field in data)) {
      missingFields.push(`Location.${field}`);
    } else if (data[field] === null || data[field] === undefined) {
      warnings.push(`Location.${field} is null or undefined`);
    }
  });

  // Check required boundary fields
  requiredBoundaryFields.forEach(field => {
    if (!(field in data)) {
      missingFields.push(`SiteBoundary.${field}`);
    }
  });

  // Validate geometry structure if present
  if (data.geometry) {
    if (data.geometry.type !== 'Polygon') {
      errors.push('SiteBoundary.geometry.type must be "Polygon"');
    }

    if (!Array.isArray(data.geometry.coordinates)) {
      errors.push('SiteBoundary.geometry.coordinates must be an array');
    } else if (!Array.isArray(data.geometry.coordinates[0])) {
      errors.push('SiteBoundary.geometry.coordinates[0] must be an array of coordinates');
    } else if (data.geometry.coordinates[0].length < 4) {
      errors.push('SiteBoundary.geometry.coordinates[0] must have at least 4 coordinate pairs (closed polygon)');
    }
  }

  // Validate coordinate types
  if (typeof data.latitude !== 'number' || isNaN(data.latitude)) {
    errors.push('Location.latitude must be a valid number');
  }

  if (typeof data.longitude !== 'number' || isNaN(data.longitude)) {
    errors.push('Location.longitude must be a valid number');
  }

  // Validate boolean fields
  if (typeof data.drawingComplete !== 'boolean') {
    errors.push('SiteBoundary.drawingComplete must be a boolean');
  }

  // Validate string fields are strings
  const stringFields = ['searchQuery', 'displayName', 'addressStreet', 'addressCity', 'addressCounty', 'addressPostalCode', 'addressCountry', 'accuracy', 'osmPlaceId', 'osmType', 'osmId', 'lastModified'];
  stringFields.forEach(field => {
    if (data[field] !== undefined && typeof data[field] !== 'string') {
      errors.push(`${field} must be a string`);
    }
  });

  // Check for extra fields that aren't in the schema
  const allowedFields = [
    ...requiredLocationFields,
    ...requiredBoundaryFields,
    'calculatedArea',
    'calculatedPerimeter'
  ];

  Object.keys(data).forEach(field => {
    if (!allowedFields.includes(field)) {
      extraFields.push(field);
    }
  });

  return {
    isCompliant: errors.length === 0 && missingFields.length === 0,
    errors,
    warnings,
    missingFields,
    extraFields
  };
};

/**
 * Transform our component data to match the exact schema structure
 */
export const transformToSchemaFormat = (data: any): { Location: SiteSchemaLocation; SiteBoundary: SiteSchemaBoundary } => {
  return {
    Location: {
      searchQuery: data.searchQuery || '',
      displayName: data.displayName || '',
      addressStreet: data.addressStreet || '',
      addressCity: data.addressCity || '',
      addressCounty: data.addressCounty || '',
      addressPostalCode: data.addressPostalCode || '',
      addressCountry: data.addressCountry || 'Kenya',
      latitude: data.latitude || 0,
      longitude: data.longitude || 0,
      accuracy: data.accuracy || '',
      osmPlaceId: data.osmPlaceId || '',
      osmType: data.osmType || '',
      osmId: data.osmId || ''
    },
    SiteBoundary: {
      geometry: data.geometry || {
        type: 'Polygon',
        coordinates: [[[0, 0]]]
      },
      drawingComplete: data.drawingComplete || false,
      lastModified: data.lastModified || new Date().toISOString()
    }
  };
};

/**
 * Generate a sample valid data structure for testing
 */
export const generateSampleData = (): any => {
  return {
    searchQuery: 'Westlands, Nairobi',
    displayName: 'Westlands, Nairobi County, Kenya',
    addressStreet: 'Waiyaki Way',
    addressCity: 'Nairobi',
    addressCounty: 'Nairobi County',
    addressPostalCode: '00100',
    addressCountry: 'Kenya',
    latitude: -1.2634,
    longitude: 36.8078,
    accuracy: 'osm_search',
    osmPlaceId: '123456',
    osmType: 'way',
    osmId: '123456',
    geometry: {
      type: 'Polygon',
      coordinates: [[
        [36.8078, -1.2634],
        [36.8088, -1.2634],
        [36.8088, -1.2644],
        [36.8078, -1.2644],
        [36.8078, -1.2634]
      ]]
    },
    drawingComplete: true,
    lastModified: new Date().toISOString(),
    calculatedArea: 12345.67,
    calculatedPerimeter: 456.78
  };
};

/**
 * Run comprehensive schema compliance tests
 */
export const runSchemaComplianceTests = (): { passed: number; failed: number; results: any[] } => {
  const tests = [
    {
      name: 'Valid complete data',
      data: generateSampleData(),
      shouldPass: true
    },
    {
      name: 'Missing required fields',
      data: {
        searchQuery: 'Test',
        latitude: -1.2634,
        longitude: 36.8078
      },
      shouldPass: false
    },
    {
      name: 'Invalid geometry type',
      data: {
        ...generateSampleData(),
        geometry: {
          type: 'Point',
          coordinates: [36.8078, -1.2634]
        }
      },
      shouldPass: false
    },
    {
      name: 'Invalid coordinate types',
      data: {
        ...generateSampleData(),
        latitude: 'invalid',
        longitude: 'invalid'
      },
      shouldPass: false
    }
  ];

  const results = tests.map(test => {
    const compliance = validateSchemaCompliance(test.data);
    const passed = test.shouldPass ? compliance.isCompliant : !compliance.isCompliant;
    
    return {
      name: test.name,
      passed,
      expected: test.shouldPass,
      actual: compliance.isCompliant,
      compliance
    };
  });

  const passed = results.filter(r => r.passed).length;
  const failed = results.filter(r => !r.passed).length;

  return { passed, failed, results };
};
