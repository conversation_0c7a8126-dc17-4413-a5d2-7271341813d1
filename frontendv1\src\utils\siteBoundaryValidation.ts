// Site boundary validation utilities
// Based on the site schema requirements

import { validateGeoJSON, isPolygonSelfIntersecting, calculatePolygonArea } from './geoJsonUtils';

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}

export interface SiteBoundaryData {
  searchQuery: string;
  displayName: string;
  addressStreet: string;
  addressCity: string;
  addressCounty: string;
  addressPostalCode: string;
  addressCountry: string;
  latitude: number;
  longitude: number;
  accuracy: string;
  osmPlaceId: string;
  osmType: string;
  osmId: string;
  geometry: {
    type: 'Polygon';
    coordinates: number[][][];
  } | null;
  drawingComplete: boolean;
  lastModified: string;
  calculatedArea?: number;
  calculatedPerimeter?: number;
}

/**
 * Validate site boundary data according to schema requirements
 */
export const validateSiteBoundaryData = (data: SiteBoundaryData): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required field validation
  if (!data.searchQuery?.trim()) {
    errors.push('Search query is required');
  }

  if (!data.displayName?.trim()) {
    errors.push('Location display name is required');
  }

  // Coordinate validation
  if (!isValidLatitude(data.latitude)) {
    errors.push('Invalid latitude coordinate');
  }

  if (!isValidLongitude(data.longitude)) {
    errors.push('Invalid longitude coordinate');
  }

  // Kenya bounds check (approximate)
  if (!isWithinKenyaBounds(data.latitude, data.longitude)) {
    warnings.push('Location appears to be outside Kenya');
  }

  // Geometry validation
  if (data.geometry) {
    const geometryValidation = validateGeometry(data.geometry);
    errors.push(...geometryValidation.errors);
    warnings.push(...geometryValidation.warnings);
  } else if (data.drawingComplete) {
    errors.push('Site boundary geometry is required when drawing is marked complete');
  }

  // Address validation
  if (!data.addressCity?.trim()) {
    warnings.push('City information is missing');
  }

  if (!data.addressCounty?.trim()) {
    warnings.push('County information is missing');
  }

  // OSM data validation
  if (data.osmPlaceId && !data.osmType) {
    warnings.push('OSM type is missing for OSM place');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Validate geometry structure and properties
 */
export const validateGeometry = (geometry: any): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Basic GeoJSON validation
  if (!validateGeoJSON({ type: 'Feature', properties: {}, geometry })) {
    errors.push('Invalid GeoJSON geometry structure');
    return { isValid: false, errors, warnings };
  }

  const coordinates = geometry.coordinates[0];

  // Minimum points check
  if (coordinates.length < 4) {
    errors.push('Polygon must have at least 3 points (4 including closure)');
  }

  // Self-intersection check
  if (isPolygonSelfIntersecting(coordinates)) {
    errors.push('Polygon edges cannot cross each other');
  }

  // Area validation
  const area = calculatePolygonArea(coordinates);
  if (area < 100) {
    errors.push('Site area must be at least 100 square meters');
  }

  if (area > 1000000000) { // 1000 km²
    warnings.push('Site area is very large - please verify the boundary is correct');
  }

  // Coordinate bounds validation
  for (const [lon, lat] of coordinates) {
    if (!isValidLatitude(lat)) {
      errors.push(`Invalid latitude coordinate: ${lat}`);
    }
    if (!isValidLongitude(lon)) {
      errors.push(`Invalid longitude coordinate: ${lon}`);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Check if latitude is valid
 */
export const isValidLatitude = (lat: number): boolean => {
  return typeof lat === 'number' && !isNaN(lat) && lat >= -90 && lat <= 90;
};

/**
 * Check if longitude is valid
 */
export const isValidLongitude = (lon: number): boolean => {
  return typeof lon === 'number' && !isNaN(lon) && lon >= -180 && lon <= 180;
};

/**
 * Check if coordinates are within Kenya's approximate bounds
 */
export const isWithinKenyaBounds = (lat: number, lon: number): boolean => {
  // Kenya's approximate bounding box
  const kenyaBounds = {
    north: 5.0,
    south: -4.7,
    east: 41.9,
    west: 33.9
  };

  return lat >= kenyaBounds.south && 
         lat <= kenyaBounds.north && 
         lon >= kenyaBounds.west && 
         lon <= kenyaBounds.east;
};

/**
 * Validate coordinate precision (should not be too precise for practical use)
 */
export const validateCoordinatePrecision = (lat: number, lon: number): ValidationResult => {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check for excessive precision (more than 8 decimal places)
  const latStr = lat.toString();
  const lonStr = lon.toString();

  const latDecimals = latStr.includes('.') ? latStr.split('.')[1].length : 0;
  const lonDecimals = lonStr.includes('.') ? lonStr.split('.')[1].length : 0;

  if (latDecimals > 8) {
    warnings.push('Latitude has excessive precision - consider rounding to 8 decimal places');
  }

  if (lonDecimals > 8) {
    warnings.push('Longitude has excessive precision - consider rounding to 8 decimal places');
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
};

/**
 * Sanitize and normalize site boundary data
 */
export const sanitizeSiteBoundaryData = (data: SiteBoundaryData): SiteBoundaryData => {
  return {
    ...data,
    searchQuery: data.searchQuery?.trim() || '',
    displayName: data.displayName?.trim() || '',
    addressStreet: data.addressStreet?.trim() || '',
    addressCity: data.addressCity?.trim() || '',
    addressCounty: data.addressCounty?.trim() || '',
    addressPostalCode: data.addressPostalCode?.trim() || '',
    addressCountry: data.addressCountry?.trim() || 'Kenya',
    latitude: Math.round(data.latitude * 100000000) / 100000000, // Round to 8 decimal places
    longitude: Math.round(data.longitude * 100000000) / 100000000,
    accuracy: data.accuracy?.trim() || '',
    osmPlaceId: data.osmPlaceId?.trim() || '',
    osmType: data.osmType?.trim() || '',
    osmId: data.osmId?.trim() || '',
    lastModified: data.lastModified || new Date().toISOString()
  };
};

/**
 * Check if site boundary data is complete for submission
 */
export const isSiteBoundaryComplete = (data: SiteBoundaryData): boolean => {
  const validation = validateSiteBoundaryData(data);
  
  return validation.isValid && 
         data.drawingComplete && 
         data.geometry !== null &&
         data.displayName.trim() !== '' &&
         data.latitude !== 0 &&
         data.longitude !== 0;
};

/**
 * Get completion percentage for site boundary step
 */
export const getSiteBoundaryCompletionPercentage = (data: SiteBoundaryData): number => {
  let completed = 0;
  const total = 5;

  // Location search completed
  if (data.searchQuery?.trim() && data.displayName?.trim()) {
    completed += 1;
  }

  // Location selected
  if (data.latitude !== 0 && data.longitude !== 0) {
    completed += 1;
  }

  // Address information
  if (data.addressCity?.trim() && data.addressCounty?.trim()) {
    completed += 1;
  }

  // Boundary drawing started
  if (data.geometry) {
    completed += 1;
  }

  // Boundary drawing completed and valid
  if (data.drawingComplete && data.geometry) {
    const geometryValidation = validateGeometry(data.geometry);
    if (geometryValidation.isValid) {
      completed += 1;
    }
  }

  return Math.round((completed / total) * 100);
};
