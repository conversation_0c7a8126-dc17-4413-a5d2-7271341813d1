import { TimeLog } from "../types";
import { TimeCalculation } from "../types/time";

/**
 * Converts time string (HH:MM) to minutes since midnight
 */
export const timeToMinutes = (timeStr: string): number => {
	if (!timeStr) return 0;
	const [hours, minutes] = timeStr.split(":").map(Number);
	return hours * 60 + minutes;
};

/**
 * Converts minutes since midnight to time string (HH:MM)
 */
export const minutesToTime = (minutes: number): string => {
	const hours = Math.floor(minutes / 60);
	const mins = minutes % 60;
	return `${hours.toString().padStart(2, "0")}:${mins.toString().padStart(2, "0")}`;
};

/**
 * Calculates total hours worked including break deduction
 */
export const calculateTotalHours = (
	clockIn: string,
	clockOut: string,
	breakDuration: number = 0,
): number => {
	if (!clockIn || !clockOut) return 0;

	const startMinutes = timeToMinutes(clockIn);
	const endMinutes = timeToMinutes(clockOut);

	// Handle overnight shifts
	let totalMinutes =
		endMinutes >= startMinutes
			? endMinutes - startMinutes
			: 24 * 60 - startMinutes + endMinutes;

	// Subtract break duration
	totalMinutes -= breakDuration;

	// Convert to hours with 2 decimal places
	return Math.round((totalMinutes / 60) * 100) / 100;
};

/**
 * Calculates overtime hours based on standard work hours
 */
export const calculateOvertimeHours = (
	totalHours: number,
	standardHours: number = 8,
): number => {
	return Math.max(0, totalHours - standardHours);
};

/**
 * Determines worker status based on clock-in time and scheduled start
 */
export const determineWorkerStatus = (
	clockIn?: string,
	clockOut?: string,
	scheduledStart: string = "08:00",
): "on-site" | "late" | "absent" | "off-site" => {
	if (!clockIn) return "absent";
	if (clockOut) return "off-site";

	const clockInMinutes = timeToMinutes(clockIn);
	const scheduledMinutes = timeToMinutes(scheduledStart);

	// Consider late if more than 15 minutes after scheduled start
	return clockInMinutes > scheduledMinutes + 15 ? "late" : "on-site";
};

/**
 * Formats duration in minutes to human-readable format
 */
export const formatDuration = (minutes: number): string => {
	if (minutes === 0) return "0 min";

	const hours = Math.floor(minutes / 60);
	const mins = minutes % 60;

	if (hours === 0) return `${mins} min`;
	if (mins === 0) return `${hours}h`;
	return `${hours}h ${mins}m`;
};

/**
 * Formats hours to display format (e.g., 8.5 -> "8h 30m")
 */
export const formatHours = (hours: number): string => {
	if (hours === 0) return "0h";

	const wholeHours = Math.floor(hours);
	const minutes = Math.round((hours - wholeHours) * 60);

	if (minutes === 0) return `${wholeHours}h`;
	return `${wholeHours}h ${minutes}m`;
};

/**
 * Validates time string format (HH:MM)
 */
export const isValidTimeFormat = (timeStr: string): boolean => {
	const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
	return timeRegex.test(timeStr);
};

/**
 * Validates that clock-out is after clock-in
 */
export const isValidTimeRange = (
	clockIn: string,
	clockOut: string,
): boolean => {
	if (!isValidTimeFormat(clockIn) || !isValidTimeFormat(clockOut)) return false;

	// const startMinutes = timeToMinutes(clockIn);
	// const endMinutes = timeToMinutes(clockOut);

	// Allow overnight shifts
	return true;
};

/**
 * Gets current date in YYYY-MM-DD format
 */
export const getCurrentDate = (): string => {
	return new Date().toISOString().split("T")[0];
};

/**
 * Formats date for display
 */
export const formatDate = (dateStr: string): string => {
	const date = new Date(dateStr);
	return date.toLocaleDateString("en-GB", {
		weekday: "long",
		year: "numeric",
		month: "long",
		day: "numeric",
	});
};

/**
 * Calculates comprehensive time data for a time log
 */
export const calculateTimeData = (timeLog: TimeLog): TimeCalculation => {
	const totalHours = calculateTotalHours(
		timeLog.clockIn || "",
		timeLog.clockOut || "",
		timeLog.breakDuration || 0,
	);

	const overtimeHours = calculateOvertimeHours(totalHours);
	const regularHours = totalHours - overtimeHours;

	return {
		regularHours,
		overtimeHours,
		totalHours,
		breakDuration: timeLog.breakDuration || 0,
	};
};

/**
 * Generates time options for dropdowns (e.g., 06:00, 06:15, 06:30, ...)
 */
export const generateTimeOptions = (interval: number = 15): string[] => {
	const options: string[] = [];
	for (let hour = 0; hour < 24; hour++) {
		for (let minute = 0; minute < 60; minute += interval) {
			options.push(minutesToTime(hour * 60 + minute));
		}
	}
	return options;
};
