// Integration utilities for connecting worker creation with training management workflow
// Based on the training management workflow documentation

import {
  EnhancedWorkerCreationInput,
  WorkerCredential,
  TemporaryCredential,
  PermanentCredential
} from '../types/credentials';

// Training requirement types from the workflow
export interface TrainingRequirement {
  id: string;
  name: string;
  code: string;
  category: 'critical' | 'operational' | 'foundation' | 'administrative';
  validityPeriodMonths?: number;
  requiresFullRecertification: boolean;
  applicableTrades: string[];
  isMandatory: boolean;
  description: string;
  providerRequirements?: string[];
}

// Worker compliance status calculation
export interface WorkerComplianceStatus {
  workerId: string;
  overallStatus: 'compliant' | 'expiring' | 'non_compliant' | 'incomplete';
  siteEligible: boolean;
  compliancePercentage: number;
  requiredTrainings: TrainingRequirement[];
  completedTrainings: string[];
  missingTrainings: string[];
  expiringTrainings: string[];
  expiredTrainings: string[];
  nextExpiryDate?: string;
  blockingIssues: string[];
  recommendations: string[];
}

// Training record for integration with existing system
export interface WorkerTrainingRecord {
  id: string;
  workerId: string;
  trainingModuleId: string;
  trainingModuleName: string;
  completionDate: string;
  expiryDate?: string;
  certificateNumber?: string;
  certificateUrl?: string;
  trainingProvider: string;
  score?: number;
  status: 'valid' | 'expiring' | 'expired';
  renewalRequired: boolean;
  renewalPeriodMonths?: number;
  credentialType: 'permanent' | 'temporary';
  credentialCategory: string;
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Convert worker credentials to training records for integration
 */
export const convertCredentialsToTrainingRecords = (
  workerId: string,
  credentials: WorkerCredential[]
): WorkerTrainingRecord[] => {
  return credentials.map(credential => {
    const baseRecord = {
      id: credential.id || `record_${Date.now()}_${Math.random()}`,
      workerId,
      trainingModuleId: `module_${credential.name.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase()}`,
      trainingModuleName: credential.name,
      completionDate: credential.issueDate,
      certificateNumber: credential.certificateNumber,
      certificateUrl: credential.documentUrl,
      score: undefined,
      notes: credential.notes,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    if (credential.type === 'PERMANENT') {
      const permCred = credential as PermanentCredential;
      return {
        ...baseRecord,
        trainingProvider: permCred.institution,
        expiryDate: undefined,
        status: 'valid' as const,
        renewalRequired: false,
        renewalPeriodMonths: undefined,
        credentialType: 'permanent' as const,
        credentialCategory: permCred.category
      };
    } else {
      const tempCred = credential as TemporaryCredential;
      return {
        ...baseRecord,
        trainingProvider: tempCred.trainingProvider,
        expiryDate: tempCred.expiryDate,
        status: tempCred.status === 'VALID' ? 'valid' as const : 
                tempCred.status === 'EXPIRING' ? 'expiring' as const : 'expired' as const,
        renewalRequired: tempCred.renewalRequired,
        renewalPeriodMonths: tempCred.renewalPeriodMonths,
        credentialType: 'temporary' as const,
        credentialCategory: tempCred.category
      };
    }
  });
};

/**
 * Calculate worker compliance status based on credentials and requirements
 */
export const calculateWorkerCompliance = (
  workerData: EnhancedWorkerCreationInput,
  trainingRequirements: TrainingRequirement[]
): WorkerComplianceStatus => {
  const allCredentials = [...(workerData.permanentCredentials || []), ...(workerData.temporaryCredentials || [])];
  
  // Filter requirements applicable to worker's trades
  const applicableRequirements = trainingRequirements.filter(req => 
    req.applicableTrades.length === 0 || // Universal requirements
    workerData.tradeIds?.some(tradeId => req.applicableTrades.includes(tradeId.toString()))
  );

  const mandatoryRequirements = applicableRequirements.filter(req => req.isMandatory);
  
  // Check which requirements are met
  const completedTrainings: string[] = [];
  const missingTrainings: string[] = [];
  const expiringTrainings: string[] = [];
  const expiredTrainings: string[] = [];
  const blockingIssues: string[] = [];

  applicableRequirements.forEach(requirement => {
    const matchingCredential = allCredentials.find(cred => 
      cred.name.toLowerCase().includes(requirement.name.toLowerCase()) ||
      requirement.name.toLowerCase().includes(cred.name.toLowerCase())
    );

    if (matchingCredential) {
      completedTrainings.push(requirement.id);
      
      // Check expiry for temporary credentials
      if (matchingCredential.type === 'TEMPORARY') {
        const tempCred = matchingCredential as TemporaryCredential;
        const daysUntilExpiry = Math.ceil(
          (new Date(tempCred.expiryDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)
        );
        
        if (daysUntilExpiry < 0) {
          expiredTrainings.push(requirement.id);
          if (requirement.isMandatory) {
            blockingIssues.push(`${requirement.name} has expired and is mandatory for site assignment`);
          }
        } else if (daysUntilExpiry <= 30) {
          expiringTrainings.push(requirement.id);
        }
      }
    } else {
      missingTrainings.push(requirement.id);
      if (requirement.isMandatory) {
        blockingIssues.push(`${requirement.name} is required but not completed`);
      }
    }
  });

  // Calculate compliance percentage
  const totalRequired = applicableRequirements.length;
  const totalCompleted = completedTrainings.length - expiredTrainings.length;
  const compliancePercentage = totalRequired > 0 ? Math.round((totalCompleted / totalRequired) * 100) : 100;

  // Determine overall status
  let overallStatus: WorkerComplianceStatus['overallStatus'];
  if (expiredTrainings.length > 0 || missingTrainings.some(id => 
    mandatoryRequirements.find(req => req.id === id)
  )) {
    overallStatus = 'non_compliant';
  } else if (expiringTrainings.length > 0) {
    overallStatus = 'expiring';
  } else if (missingTrainings.length > 0) {
    overallStatus = 'incomplete';
  } else {
    overallStatus = 'compliant';
  }

  // Site eligibility (no blocking issues)
  const siteEligible = blockingIssues.length === 0;

  // Find next expiry date
  const temporaryCredentials = (workerData.temporaryCredentials || []) as TemporaryCredential[];
  const nextExpiry = temporaryCredentials
    .filter(cred => new Date(cred.expiryDate) > new Date())
    .sort((a, b) => new Date(a.expiryDate).getTime() - new Date(b.expiryDate).getTime())[0];

  // Generate recommendations
  const recommendations: string[] = [];
  if (missingTrainings.length > 0) {
    recommendations.push(`Complete ${missingTrainings.length} missing training requirement(s)`);
  }
  if (expiringTrainings.length > 0) {
    recommendations.push(`Schedule renewal for ${expiringTrainings.length} expiring credential(s)`);
  }
  if (expiredTrainings.length > 0) {
    recommendations.push(`Renew ${expiredTrainings.length} expired credential(s) immediately`);
  }
  if (siteEligible && overallStatus === 'compliant') {
    recommendations.push('Worker is ready for site assignment');
  }

  return {
    workerId: workerData.nationalId, // Use nationalId as temporary worker ID
    overallStatus,
    siteEligible,
    compliancePercentage,
    requiredTrainings: applicableRequirements,
    completedTrainings,
    missingTrainings,
    expiringTrainings,
    expiredTrainings,
    nextExpiryDate: nextExpiry?.expiryDate,
    blockingIssues,
    recommendations
  };
};

/**
 * Generate training schedule for missing requirements
 */
export const generateTrainingSchedule = (
  complianceStatus: WorkerComplianceStatus,
  trainingRequirements: TrainingRequirement[]
): Array<{
  requirementId: string;
  trainingName: string;
  priority: 'critical' | 'high' | 'medium' | 'low';
  suggestedDate: string;
  reason: string;
}> => {
  const schedule: Array<{
    requirementId: string;
    trainingName: string;
    priority: 'critical' | 'high' | 'medium' | 'low';
    suggestedDate: string;
    reason: string;
  }> = [];

  const today = new Date();

  // Handle expired trainings (critical priority)
  complianceStatus.expiredTrainings.forEach(trainingId => {
    const requirement = trainingRequirements.find(req => req.id === trainingId);
    if (requirement) {
      schedule.push({
        requirementId: trainingId,
        trainingName: requirement.name,
        priority: 'critical',
        suggestedDate: new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 1 week
        reason: 'Credential has expired and must be renewed immediately'
      });
    }
  });

  // Handle expiring trainings (high priority)
  complianceStatus.expiringTrainings.forEach(trainingId => {
    const requirement = trainingRequirements.find(req => req.id === trainingId);
    if (requirement) {
      schedule.push({
        requirementId: trainingId,
        trainingName: requirement.name,
        priority: 'high',
        suggestedDate: new Date(today.getTime() + 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 2 weeks
        reason: 'Credential expires within 30 days'
      });
    }
  });

  // Handle missing mandatory trainings (high priority)
  complianceStatus.missingTrainings.forEach(trainingId => {
    const requirement = trainingRequirements.find(req => req.id === trainingId);
    if (requirement && requirement.isMandatory) {
      schedule.push({
        requirementId: trainingId,
        trainingName: requirement.name,
        priority: 'high',
        suggestedDate: new Date(today.getTime() + 21 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 3 weeks
        reason: 'Mandatory training required for site assignment'
      });
    }
  });

  // Handle missing optional trainings (medium priority)
  complianceStatus.missingTrainings.forEach(trainingId => {
    const requirement = trainingRequirements.find(req => req.id === trainingId);
    if (requirement && !requirement.isMandatory) {
      schedule.push({
        requirementId: trainingId,
        trainingName: requirement.name,
        priority: 'medium',
        suggestedDate: new Date(today.getTime() + 60 * 24 * 60 * 60 * 1000).toISOString().split('T')[0], // 2 months
        reason: 'Optional training to improve worker capabilities'
      });
    }
  });

  return schedule.sort((a, b) => {
    const priorityOrder = { critical: 0, high: 1, medium: 2, low: 3 };
    return priorityOrder[a.priority] - priorityOrder[b.priority];
  });
};

/**
 * Prepare worker data for integration with existing training management system
 */
export const prepareWorkerForTrainingIntegration = (
  workerData: EnhancedWorkerCreationInput,
  trainingRequirements: TrainingRequirement[]
) => {
  const trainingRecords = convertCredentialsToTrainingRecords(
    workerData.nationalId,
    [...(workerData.permanentCredentials || []), ...(workerData.temporaryCredentials || [])]
  );

  const complianceStatus = calculateWorkerCompliance(workerData, trainingRequirements);
  const trainingSchedule = generateTrainingSchedule(complianceStatus, trainingRequirements);

  return {
    workerData,
    trainingRecords,
    complianceStatus,
    trainingSchedule,
    integrationMetadata: {
      createdAt: new Date().toISOString(),
      credentialCount: trainingRecords.length,
      compliancePercentage: complianceStatus.compliancePercentage,
      siteEligible: complianceStatus.siteEligible,
      nextAction: trainingSchedule.length > 0 ? trainingSchedule[0] : null
    }
  };
};
