{   // in frontend the term 'task' is used to refer to a job but backend (in graphql schema) will use the term 'job'.
    // use 'graphqlSchema.txt' to get the graphql schema
    "pages": [
        {
            "path": "/fill",
            "actions": [
                "it is a form for filling a toolbox",
                "it will have a table of jobs and for each job there are its corresponding hazards(many hazards for one job) and each hazard has a table of control measures(many controls for one hazard)",
                "a user should be able to add a new hazard to a job (with its corresponding control measure) and update existing hazards",
                "a user should be able to add a new control measure to a hazard and update existing control measures",
                "uses the create toolbox mutation"
            ],
            "table structure": {
                "header": {
                    "date": "todays date in this format 'dd/mm/yyyy'",
                    "time": "current time in this format 'hh:mm' (24 hour format)",
                    "conducted by": "name of the person who conducted the toolbox meeting (leave it blank for now. But reserve space for it)",
                    "signature": "signature of the person who conducted the toolbox meeting (leave it blank for now. But reserve space for it)"
                },
                "body": [
                    "with jobs, and for each job there are its corresponding hazards(many hazards for one job) and each hazard has a table of control measures(many controls for one hazard)",
                    "user should be able to add new hazards (with their corresponding control measures) and update existing hazards",
                    "for existing hazards, user should be able to add new control measures and update existing control measures"
                ],
                "footer": {
                    "emergency procedure specific to the task of the day": "a textarea for filling the emergency procedure specific to the task of the day",
                    "toolbox training topic": "a textarea for filling the toolbox training topic"
                }
            }
        },
        {
            "path":"/attendance",
            "actions":[
                "fills the attendance table",
                "the user should be able to search and add workers to the attendance table",
                "uses the 'addAttendees' mutation"
            ],
            "attendance table structure":{
                "body":{
                    "s/no":"count of the number of workers in the attendance table. It increments by 1 for each worker added to the table",
                    "name":"name of the worker",
                    "training":"training of the worker",
                    "signature":"signature of the worker. This can be null so be blank for null signatures. To get the image src for the signature get the 'signatureFileId' from the graphql 'allworker' query and 'FILE_BASE_URL' constant to make a url of this format `${FILE_BASE_URL}/files/${signatureFileId}`.If no image let it be blank"
                }
            }
        },
        {
            "path":"/summarize",
            "actions":[
                "summarizes the tool box",
                "has the same table as in fill except it does not have a header and the footer has only time(which is auto filled)",
                "all actions in body remains the same as in fill toolbox",
                "uses the 'summarizeToolbox' mutation"
            ]
        }
    ]
}